ARM GAS  /tmp/ccK1isvr.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"sysmem.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/sysmem.c"
  19              		.section	.text._sbrk,"ax",%progbits
  20              		.align	1
  21              		.global	_sbrk
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	_sbrk:
  27              	.LVL0:
  28              	.LFB170:
   1:Core/Src/sysmem.c **** /**
   2:Core/Src/sysmem.c ****  ******************************************************************************
   3:Core/Src/sysmem.c ****  * @file      sysmem.c
   4:Core/Src/sysmem.c ****  * <AUTHOR> by STM32CubeMX
   5:Core/Src/sysmem.c ****  * @brief     System Memory calls file
   6:Core/Src/sysmem.c ****  *
   7:Core/Src/sysmem.c ****  *            For more information about which C functions
   8:Core/Src/sysmem.c ****  *            need which of these lowlevel functions
   9:Core/Src/sysmem.c ****  *            please consult the Newlib or Picolibc libc manual
  10:Core/Src/sysmem.c ****  ******************************************************************************
  11:Core/Src/sysmem.c ****  * @attention
  12:Core/Src/sysmem.c ****  *
  13:Core/Src/sysmem.c ****  * Copyright (c) 2025 STMicroelectronics.
  14:Core/Src/sysmem.c ****  * All rights reserved.
  15:Core/Src/sysmem.c ****  *
  16:Core/Src/sysmem.c ****  * This software is licensed under terms that can be found in the LICENSE file
  17:Core/Src/sysmem.c ****  * in the root directory of this software component.
  18:Core/Src/sysmem.c ****  * If no LICENSE file comes with this software, it is provided AS-IS.
  19:Core/Src/sysmem.c ****  *
  20:Core/Src/sysmem.c ****  ******************************************************************************
  21:Core/Src/sysmem.c ****  */
  22:Core/Src/sysmem.c **** 
  23:Core/Src/sysmem.c **** /* Includes */
  24:Core/Src/sysmem.c **** #include <errno.h>
  25:Core/Src/sysmem.c **** #include <stdint.h>
  26:Core/Src/sysmem.c **** #include <stddef.h>
  27:Core/Src/sysmem.c **** 
  28:Core/Src/sysmem.c **** /**
  29:Core/Src/sysmem.c ****  * Pointer to the current high watermark of the heap usage
  30:Core/Src/sysmem.c ****  */
ARM GAS  /tmp/ccK1isvr.s 			page 2


  31:Core/Src/sysmem.c **** static uint8_t *__sbrk_heap_end = NULL;
  32:Core/Src/sysmem.c **** 
  33:Core/Src/sysmem.c **** /**
  34:Core/Src/sysmem.c ****  * @brief _sbrk() allocates memory to the newlib heap and is used by malloc
  35:Core/Src/sysmem.c ****  *        and others from the C library
  36:Core/Src/sysmem.c ****  *
  37:Core/Src/sysmem.c ****  * @verbatim
  38:Core/Src/sysmem.c ****  * ############################################################################
  39:Core/Src/sysmem.c ****  * #  .data  #  .bss  #       newlib heap       #          MSP stack          #
  40:Core/Src/sysmem.c ****  * #         #        #                         # Reserved by _Min_Stack_Size #
  41:Core/Src/sysmem.c ****  * ############################################################################
  42:Core/Src/sysmem.c ****  * ^-- RAM start      ^-- _end                             _estack, RAM end --^
  43:Core/Src/sysmem.c ****  * @endverbatim
  44:Core/Src/sysmem.c ****  *
  45:Core/Src/sysmem.c ****  * This implementation starts allocating at the '_end' linker symbol
  46:Core/Src/sysmem.c ****  * The '_Min_Stack_Size' linker symbol reserves a memory for the MSP stack
  47:Core/Src/sysmem.c ****  * The implementation considers '_estack' linker symbol to be RAM end
  48:Core/Src/sysmem.c ****  * NOTE: If the MSP stack, at any point during execution, grows larger than the
  49:Core/Src/sysmem.c ****  * reserved size, please increase the '_Min_Stack_Size'.
  50:Core/Src/sysmem.c ****  *
  51:Core/Src/sysmem.c ****  * @param incr Memory size
  52:Core/Src/sysmem.c ****  * @return Pointer to allocated memory
  53:Core/Src/sysmem.c ****  */
  54:Core/Src/sysmem.c **** void *_sbrk(ptrdiff_t incr)
  55:Core/Src/sysmem.c **** {
  29              		.loc 1 55 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  34              		.loc 1 55 1 is_stmt 0 view .LVU1
  35 0000 10B4     		push	{r4}
  36              	.LCFI0:
  37              		.cfi_def_cfa_offset 4
  38              		.cfi_offset 4, -4
  39 0002 0346     		mov	r3, r0
  56:Core/Src/sysmem.c ****   extern uint8_t _end; /* Symbol defined in the linker script */
  40              		.loc 1 56 3 is_stmt 1 view .LVU2
  57:Core/Src/sysmem.c ****   extern uint8_t _estack; /* Symbol defined in the linker script */
  41              		.loc 1 57 3 view .LVU3
  58:Core/Src/sysmem.c ****   extern uint32_t _Min_Stack_Size; /* Symbol defined in the linker script */
  42              		.loc 1 58 3 view .LVU4
  59:Core/Src/sysmem.c ****   const uint32_t stack_limit = (uint32_t)&_estack - (uint32_t)&_Min_Stack_Size;
  43              		.loc 1 59 3 view .LVU5
  44              	.LVL1:
  60:Core/Src/sysmem.c ****   const uint8_t *max_heap = (uint8_t *)stack_limit;
  45              		.loc 1 60 3 view .LVU6
  59:Core/Src/sysmem.c ****   const uint32_t stack_limit = (uint32_t)&_estack - (uint32_t)&_Min_Stack_Size;
  46              		.loc 1 59 51 is_stmt 0 view .LVU7
  47 0004 0C4A     		ldr	r2, .L8
  48 0006 0D49     		ldr	r1, .L8+4
  49              	.LVL2:
  61:Core/Src/sysmem.c ****   uint8_t *prev_heap_end;
  50              		.loc 1 61 3 is_stmt 1 view .LVU8
  62:Core/Src/sysmem.c **** 
  63:Core/Src/sysmem.c ****   /* Initialize heap end at first call */
  64:Core/Src/sysmem.c ****   if (NULL == __sbrk_heap_end)
ARM GAS  /tmp/ccK1isvr.s 			page 3


  51              		.loc 1 64 3 view .LVU9
  52              		.loc 1 64 12 is_stmt 0 view .LVU10
  53 0008 0D48     		ldr	r0, .L8+8
  54              	.LVL3:
  55              		.loc 1 64 12 view .LVU11
  56 000a 0068     		ldr	r0, [r0]
  57              		.loc 1 64 6 view .LVU12
  58 000c 50B1     		cbz	r0, .L6
  59              	.L2:
  65:Core/Src/sysmem.c ****   {
  66:Core/Src/sysmem.c ****     __sbrk_heap_end = &_end;
  67:Core/Src/sysmem.c ****   }
  68:Core/Src/sysmem.c **** 
  69:Core/Src/sysmem.c ****   /* Protect heap from growing into the reserved MSP stack */
  70:Core/Src/sysmem.c ****   if (__sbrk_heap_end + incr > max_heap)
  60              		.loc 1 70 3 is_stmt 1 view .LVU13
  61              		.loc 1 70 23 is_stmt 0 view .LVU14
  62 000e 0C48     		ldr	r0, .L8+8
  63 0010 0068     		ldr	r0, [r0]
  64 0012 0344     		add	r3, r3, r0
  65              	.LVL4:
  66              		.loc 1 70 6 view .LVU15
  67 0014 521A     		subs	r2, r2, r1
  68 0016 9342     		cmp	r3, r2
  69 0018 08D8     		bhi	.L7
  71:Core/Src/sysmem.c ****   {
  72:Core/Src/sysmem.c ****     errno = ENOMEM;
  73:Core/Src/sysmem.c ****     return (void *)-1;
  74:Core/Src/sysmem.c ****   }
  75:Core/Src/sysmem.c **** 
  76:Core/Src/sysmem.c ****   prev_heap_end = __sbrk_heap_end;
  70              		.loc 1 76 3 is_stmt 1 view .LVU16
  71              	.LVL5:
  77:Core/Src/sysmem.c ****   __sbrk_heap_end += incr;
  72              		.loc 1 77 3 view .LVU17
  73              		.loc 1 77 19 is_stmt 0 view .LVU18
  74 001a 094A     		ldr	r2, .L8+8
  75 001c 1360     		str	r3, [r2]
  78:Core/Src/sysmem.c **** 
  79:Core/Src/sysmem.c ****   return (void *)prev_heap_end;
  76              		.loc 1 79 3 is_stmt 1 view .LVU19
  77              	.LVL6:
  78              	.L1:
  80:Core/Src/sysmem.c **** }
  79              		.loc 1 80 1 is_stmt 0 view .LVU20
  80 001e 5DF8044B 		ldr	r4, [sp], #4
  81              	.LCFI1:
  82              		.cfi_remember_state
  83              		.cfi_restore 4
  84              		.cfi_def_cfa_offset 0
  85 0022 7047     		bx	lr
  86              	.LVL7:
  87              	.L6:
  88              	.LCFI2:
  89              		.cfi_restore_state
  66:Core/Src/sysmem.c ****   }
  90              		.loc 1 66 5 is_stmt 1 view .LVU21
ARM GAS  /tmp/ccK1isvr.s 			page 4


  66:Core/Src/sysmem.c ****   }
  91              		.loc 1 66 21 is_stmt 0 view .LVU22
  92 0024 0648     		ldr	r0, .L8+8
  93 0026 074C     		ldr	r4, .L8+12
  94 0028 0460     		str	r4, [r0]
  95 002a F0E7     		b	.L2
  96              	.LVL8:
  97              	.L7:
  72:Core/Src/sysmem.c ****     return (void *)-1;
  98              		.loc 1 72 5 is_stmt 1 view .LVU23
  72:Core/Src/sysmem.c ****     return (void *)-1;
  99              		.loc 1 72 11 is_stmt 0 view .LVU24
 100 002c 064B     		ldr	r3, .L8+16
 101 002e 0C22     		movs	r2, #12
 102 0030 1A60     		str	r2, [r3]
  73:Core/Src/sysmem.c ****   }
 103              		.loc 1 73 5 is_stmt 1 view .LVU25
  73:Core/Src/sysmem.c ****   }
 104              		.loc 1 73 12 is_stmt 0 view .LVU26
 105 0032 4FF0FF30 		mov	r0, #-1
 106 0036 F2E7     		b	.L1
 107              	.L9:
 108              		.align	2
 109              	.L8:
 110 0038 00000000 		.word	_estack
 111 003c 00000000 		.word	_Min_Stack_Size
 112 0040 00000000 		.word	__sbrk_heap_end
 113 0044 00000000 		.word	_end
 114 0048 00000000 		.word	errno
 115              		.cfi_endproc
 116              	.LFE170:
 118              		.section	.bss.__sbrk_heap_end,"aw",%nobits
 119              		.align	2
 122              	__sbrk_heap_end:
 123 0000 00000000 		.space	4
 124              		.text
 125              	.Letext0:
 126              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 127              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 128              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 129              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/errno.h"
ARM GAS  /tmp/ccK1isvr.s 			page 5


DEFINED SYMBOLS
                            *ABS*:00000000 sysmem.c
     /tmp/ccK1isvr.s:20     .text._sbrk:00000000 $t
     /tmp/ccK1isvr.s:26     .text._sbrk:00000000 _sbrk
     /tmp/ccK1isvr.s:110    .text._sbrk:00000038 $d
     /tmp/ccK1isvr.s:122    .bss.__sbrk_heap_end:00000000 __sbrk_heap_end
     /tmp/ccK1isvr.s:119    .bss.__sbrk_heap_end:00000000 $d

UNDEFINED SYMBOLS
_estack
_Min_Stack_Size
_end
errno
