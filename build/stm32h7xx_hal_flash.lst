ARM GAS  /tmp/cczI1n00.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_flash.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c"
  19              		.section	.text.HAL_FLASH_EndOfOperationCallback,"ax",%progbits
  20              		.align	1
  21              		.weak	HAL_FLASH_EndOfOperationCallback
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_FLASH_EndOfOperationCallback:
  27              	.LVL0:
  28              	.LFB147:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @file    stm32h7xx_hal_flash.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief   FLASH HAL module driver.
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          This file provides firmware functions to manage the following
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          functionalities of the internal FLASH memory:
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *           + Program operations functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *           + Memory Control functions
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *           + Peripheral Errors functions
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  @verbatim
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   ==============================================================================
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                         ##### FLASH peripheral features #####
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   ==============================================================================
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   [..] The Flash memory interface manages CPU AXI I-Code and D-Code accesses
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****        to the Flash memory. It implements the erase and program Flash memory operations
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****        and the read and write protection mechanisms.
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   [..] The FLASH main features are:
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (+) Flash memory read operations
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (+) Flash memory program/erase operations
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (+) Read / write protections
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (+) Option bytes programming
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (+) Error code correction (ECC) : Data in flash are 266-bits word
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****           (10 bits added per flash word)
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                         ##### How to use this driver #####
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ==============================================================================
ARM GAS  /tmp/cczI1n00.s 			page 2


  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     [..]
  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       This driver provides functions and macros to configure and program the FLASH
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       memory of all STM32H7xx devices.
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (#) FLASH Memory IO Programming functions:
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Lock and Unlock the FLASH interface using HAL_FLASH_Unlock() and
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                 HAL_FLASH_Lock() functions
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Program functions: 256-bit word only
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) There Two modes of programming :
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****             (+++) Polling mode using HAL_FLASH_Program() function
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****             (+++) Interrupt mode using HAL_FLASH_Program_IT() function
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (#) Interrupts and flags management functions :
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Handle FLASH interrupts by calling HAL_FLASH_IRQHandler()
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Callback functions are called when the flash operations are finished :
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                 HAL_FLASH_EndOfOperationCallback() when everything is ok, otherwise
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                 HAL_FLASH_OperationErrorCallback()
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Get error flag status by calling HAL_FLASH_GetError()
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       (#) Option bytes management functions :
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Lock and Unlock the option bytes using HAL_FLASH_OB_Unlock() and
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                 HAL_FLASH_OB_Lock() functions
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****            (++) Launch the reload of the option bytes using HAL_FLASH_OB_Launch() function.
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                 In this case, a reset is generated
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     [..]
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       In addition to these functions, this driver includes a set of macros allowing
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       to handle the following operations:
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****        (+) Set the latency
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****        (+) Enable/Disable the FLASH interrupts
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****        (+) Monitor the FLASH flags status
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****      [..]
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     (@) For any Flash memory program operation (erase or program), the CPU clock frequency
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         (HCLK) must be at least 1MHz.
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     (@) The contents of the Flash memory are not guaranteed if a device reset occurs during
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         a Flash memory operation.
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     (@) The application can simultaneously request a read and a write operation through each AXI
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         interface.
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         As the Flash memory is divided into two independent banks, the embedded Flash
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         memory interface can drive different operations at the same time on each bank. For
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         example a read, write or erase operation can be executed on bank 1 while another read,
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         write or erase operation is executed on bank 2.
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  @endverbatim
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   ******************************************************************************
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @attention
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * Copyright (c) 2017 STMicroelectronics.
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * All rights reserved.
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * This software is licensed under terms that can be found in the LICENSE file in
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * the root directory of this software component.
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   ******************************************************************************
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Includes ------------------------------------------------------------------*/
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #include "stm32h7xx_hal.h"
ARM GAS  /tmp/cczI1n00.s 			page 3


  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @addtogroup STM32H7xx_HAL_Driver
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @defgroup FLASH FLASH
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief FLASH HAL module driver
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #ifdef HAL_FLASH_MODULE_ENABLED
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Private typedef -----------------------------------------------------------*/
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Private define ------------------------------------------------------------*/
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @addtogroup FLASH_Private_Constants
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #define FLASH_TIMEOUT_VALUE              50000U /* 50 s */
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @}
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Private macro -------------------------------------------------------------*/
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Private variables ---------------------------------------------------------*/
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @addtogroup FLASH_Private_Variables
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** FLASH_ProcessTypeDef pFlash;
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @}
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Private function prototypes -----------------------------------------------*/
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Exported functions ---------------------------------------------------------*/
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @defgroup FLASH_Exported_Functions FLASH Exported functions
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @defgroup FLASH_Exported_Functions_Group1 Programming operation functions
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *  @brief   Programming operation functions
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** @verbatim
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ===============================================================================
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                   ##### Programming operation functions #####
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ===============================================================================
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     [..]
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     This subsection provides a set of functions allowing to manage the FLASH
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     program operations.
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** @endverbatim
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Program a flash word at a specified address
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  TypeProgram Indicate the way to program at a specified address.
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *         This parameter can be a value of @ref FLASH_Type_Program
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  FlashAddress specifies the address to be programmed.
ARM GAS  /tmp/cczI1n00.s 			page 4


 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *         This parameter shall be aligned to the Flash word:
 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          - 256 bits for STM32H74x/5X devices (8x 32bits words)
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          - 128 bits for STM32H7Ax/BX devices (4x 32bits words)
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          - 256 bits for STM32H72x/3X devices (8x 32bits words)
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  DataAddress specifies the address of data to be programmed.
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *         This parameter shall be 32-bit aligned
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL_StatusTypeDef HAL Status
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_Program(uint32_t TypeProgram, uint32_t FlashAddress, uint32_t DataAddre
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *dest_addr = (__IO uint32_t *)FlashAddress;
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *src_addr = (__IO uint32_t*)DataAddress;
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t bank;
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint8_t row_index = FLASH_NB_32BITWORD_IN_FLASHWORD;
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check the parameters */
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_TYPEPROGRAM(TypeProgram));
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_PROGRAM_ADDRESS(FlashAddress));
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Process Locked */
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __HAL_LOCK(&pFlash);
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if((IS_FLASH_PROGRAM_ADDRESS_BANK1(FlashAddress)) || (IS_FLASH_PROGRAM_ADDRESS_OTP(FlashAddress))
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(IS_FLASH_PROGRAM_ADDRESS_BANK1(FlashAddress))
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bank = FLASH_BANK_1;
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Prevent unused argument(s) compilation warning */
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     UNUSED(TypeProgram);
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else if(IS_FLASH_PROGRAM_ADDRESS_BANK2(FlashAddress))
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bank = FLASH_BANK_2;
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Reset error code */
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Wait for last operation to be completed */
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, bank);
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(status == HAL_OK)
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(bank == FLASH_BANK_1)
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
ARM GAS  /tmp/cczI1n00.s 			page 5


 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Set OTP_PG bit */
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         SET_BIT(FLASH->OPTCR, FLASH_OPTCR_PG_OTP);
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Set PG bit */
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         SET_BIT(FLASH->CR1, FLASH_CR_PG);
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Set PG bit */
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       SET_BIT(FLASH->CR2, FLASH_CR_PG);
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else /* Single Bank */
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Set OTP_PG bit */
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         SET_BIT(FLASH->OPTCR, FLASH_OPTCR_PG_OTP);
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Set PG bit */
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         SET_BIT(FLASH->CR1, FLASH_CR_PG);
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __ISB();
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Program an OTP word (16 bits) */
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       *(__IO uint16_t *)FlashAddress = *(__IO uint16_t*)DataAddress;
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Program the flash word */
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       do
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         *dest_addr = *src_addr;
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         src_addr++;
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         row_index--;
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****      } while (row_index != 0U);
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __ISB();
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
ARM GAS  /tmp/cczI1n00.s 			page 6


 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Wait for last operation to be completed */
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, bank);
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* If the program operation is completed, disable the OTP_PG */
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       CLEAR_BIT(FLASH->OPTCR, FLASH_OPTCR_PG_OTP);
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if(bank == FLASH_BANK_1)
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* If the program operation is completed, disable the PG */
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         CLEAR_BIT(FLASH->CR1, FLASH_CR_PG);
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* If the program operation is completed, disable the PG */
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         CLEAR_BIT(FLASH->CR2, FLASH_CR_PG);
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else /* Single Bank */
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* If the program operation is completed, disable the OTP_PG */
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       CLEAR_BIT(FLASH->OPTCR, FLASH_OPTCR_PG_OTP);
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* If the program operation is completed, disable the PG */
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       CLEAR_BIT(FLASH->CR1, FLASH_CR_PG);
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Process Unlocked */
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __HAL_UNLOCK(&pFlash);
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return status;
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Program a flash word at a specified address with interrupt enabled.
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  TypeProgram Indicate the way to program at a specified address.
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                      This parameter can be a value of @ref FLASH_Type_Program
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  FlashAddress specifies the address to be programmed.
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *         This parameter shall be aligned to the Flash word:
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          - 256 bits for STM32H74x/5X devices (8x 32bits words)
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          - 128 bits for STM32H7Ax/BX devices (4x 32bits words)
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *          - 256 bits for STM32H72x/3X devices (8x 32bits words)
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  DataAddress specifies the address of data to be programmed.
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *         This parameter shall be 32-bit aligned
ARM GAS  /tmp/cczI1n00.s 			page 7


 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL Status
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_Program_IT(uint32_t TypeProgram, uint32_t FlashAddress, uint32_t DataAd
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *dest_addr = (__IO uint32_t*)FlashAddress;
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *src_addr = (__IO uint32_t*)DataAddress;
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t bank;
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint8_t row_index = FLASH_NB_32BITWORD_IN_FLASHWORD;
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check the parameters */
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_TYPEPROGRAM(TypeProgram));
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_PROGRAM_ADDRESS(FlashAddress));
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Process Locked */
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __HAL_LOCK(&pFlash);
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Reset error code */
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   pFlash.ErrorCode = HAL_FLASH_ERROR_NONE;
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if((IS_FLASH_PROGRAM_ADDRESS_BANK1(FlashAddress)) || (IS_FLASH_PROGRAM_ADDRESS_OTP(FlashAddress))
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(IS_FLASH_PROGRAM_ADDRESS_BANK1(FlashAddress))
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bank = FLASH_BANK_1;
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Prevent unused argument(s) compilation warning */
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     UNUSED(TypeProgram);
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else if(IS_FLASH_PROGRAM_ADDRESS_BANK2(FlashAddress))
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bank = FLASH_BANK_2;
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Wait for last operation to be completed */
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   status = FLASH_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, bank);
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (status != HAL_OK)
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Process Unlocked */
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_UNLOCK(&pFlash);
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.Address = FlashAddress;
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(bank == FLASH_BANK_1)
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
ARM GAS  /tmp/cczI1n00.s 			page 8


 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Set internal variables used by the IRQ handler */
 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.ProcedureOnGoing = FLASH_PROC_PROGRAM_BANK1;
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Set OTP_PG bit */
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         SET_BIT(FLASH->OPTCR, FLASH_OPTCR_PG_OTP);
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Set PG bit */
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         SET_BIT(FLASH->CR1, FLASH_CR_PG);
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Enable End of Operation and Error interrupts for Bank 1 */
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_CR_OPERRIE)
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_ENABLE_IT_BANK1(FLASH_IT_EOP_BANK1     | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_B
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_ENABLE_IT_BANK1(FLASH_IT_EOP_BANK1     | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1);
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_CR_OPERRIE */
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Set internal variables used by the IRQ handler */
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.ProcedureOnGoing = FLASH_PROC_PROGRAM_BANK2;
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Set PG bit */
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       SET_BIT(FLASH->CR2, FLASH_CR_PG);
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Enable End of Operation and Error interrupts for Bank2 */
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_CR_OPERRIE)
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_ENABLE_IT_BANK2(FLASH_IT_EOP_BANK2     | FLASH_IT_WRPERR_BANK2 | FLASH_IT_PGSERR_
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK2 | FLASH_IT_INCERR_BANK2 | FLASH_IT_OPERR_B
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_ENABLE_IT_BANK2(FLASH_IT_EOP_BANK2     | FLASH_IT_WRPERR_BANK2 | FLASH_IT_PGSERR_
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK2 | FLASH_IT_INCERR_BANK2);
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_CR_OPERRIE */
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else /* Single Bank */
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Set internal variables used by the IRQ handler */
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ProcedureOnGoing = FLASH_PROC_PROGRAM_BANK1;
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Set OTP_PG bit */
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       SET_BIT(FLASH->OPTCR, FLASH_OPTCR_PG_OTP);
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Set PG bit */
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       SET_BIT(FLASH->CR1, FLASH_CR_PG);
ARM GAS  /tmp/cczI1n00.s 			page 9


 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Enable End of Operation and Error interrupts for Bank 1 */
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_CR_OPERRIE)
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_ENABLE_IT_BANK1(FLASH_IT_EOP_BANK1     | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_B
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_ENABLE_IT_BANK1(FLASH_IT_EOP_BANK1     | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1);
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_CR_OPERRIE */
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __ISB();
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_OPTCR_PG_OTP)
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (TypeProgram == FLASH_TYPEPROGRAM_OTPWORD)
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Program an OTP word (16 bits) */
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       *(__IO uint16_t *)FlashAddress = *(__IO uint16_t*)DataAddress;
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Program the flash word */
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       do
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         *dest_addr = *src_addr;
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         src_addr++;
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         row_index--;
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       } while (row_index != 0U);
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __ISB();
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return status;
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief This function handles FLASH interrupt request.
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval None
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** void HAL_FLASH_IRQHandler(void)
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t temp;
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t errorflag;
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   FLASH_ProcedureTypeDef procedure;
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank1 End of Operation flag  */
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(__HAL_FLASH_GET_FLAG_BANK1(FLASH_SR_EOP) != RESET)
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(pFlash.ProcedureOnGoing == FLASH_PROC_SECTERASE_BANK1)
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Nb of sector to erased can be decreased */
ARM GAS  /tmp/cczI1n00.s 			page 10


 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.NbSectorsToErase--;
 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Check if there are still sectors to erase */
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if(pFlash.NbSectorsToErase != 0U)
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Indicate user which sector has been erased */
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(pFlash.Sector);
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear bank 1 End of Operation pending bit */
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_EOP_BANK1);
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Increment sector number */
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.Sector++;
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         temp = pFlash.Sector;
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         FLASH_Erase_Sector(temp, FLASH_BANK_1, pFlash.VoltageForErase);
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* No more sectors to Erase, user callback can be called */
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Reset Sector and stop Erase sectors procedure */
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.Sector = 0xFFFFFFFFU;
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* FLASH EOP interrupt user callback */
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(pFlash.Sector);
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear FLASH End of Operation pending bit */
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_EOP_BANK1);
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       procedure = pFlash.ProcedureOnGoing;
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if((procedure == FLASH_PROC_MASSERASE_BANK1) || (procedure == FLASH_PROC_ALLBANK_MASSERASE))
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* MassErase ended. Return the selected bank */
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* FLASH EOP interrupt user callback */
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(FLASH_BANK_1);
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else if(procedure == FLASH_PROC_PROGRAM_BANK1)
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Program ended. Return the selected address */
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* FLASH EOP interrupt user callback */
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(pFlash.Address);
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Nothing to do */
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if((procedure != FLASH_PROC_SECTERASE_BANK2) && \
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_MASSERASE_BANK2) && \
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_PROGRAM_BANK2))
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear FLASH End of Operation pending bit */
ARM GAS  /tmp/cczI1n00.s 			page 11


 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_EOP_BANK1);
 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  /* Check FLASH Bank2 End of Operation flag  */
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(__HAL_FLASH_GET_FLAG_BANK2(FLASH_SR_EOP) != RESET)
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(pFlash.ProcedureOnGoing == FLASH_PROC_SECTERASE_BANK2)
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /*Nb of sector to erased can be decreased*/
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.NbSectorsToErase--;
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Check if there are still sectors to erase*/
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if(pFlash.NbSectorsToErase != 0U)
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /*Indicate user which sector has been erased*/
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(pFlash.Sector);
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear bank 2 End of Operation pending bit */
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_EOP_BANK2);
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /*Increment sector number*/
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.Sector++;
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         temp = pFlash.Sector;
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         FLASH_Erase_Sector(temp, FLASH_BANK_2, pFlash.VoltageForErase);
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* No more sectors to Erase, user callback can be called */
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Reset Sector and stop Erase sectors procedure */
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.Sector = 0xFFFFFFFFU;
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* FLASH EOP interrupt user callback */
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(pFlash.Sector);
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear FLASH End of Operation pending bit */
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_EOP_BANK2);
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       procedure = pFlash.ProcedureOnGoing;
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if((procedure == FLASH_PROC_MASSERASE_BANK2) || (procedure == FLASH_PROC_ALLBANK_MASSERASE))
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /*MassErase ended. Return the selected bank*/
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* FLASH EOP interrupt user callback */
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(FLASH_BANK_2);
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else if(procedure == FLASH_PROC_PROGRAM_BANK2)
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Program ended. Return the selected address */
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* FLASH EOP interrupt user callback */
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         HAL_FLASH_EndOfOperationCallback(pFlash.Address);
ARM GAS  /tmp/cczI1n00.s 			page 12


 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       else
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Nothing to do */
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if((procedure != FLASH_PROC_SECTERASE_BANK1) && \
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_MASSERASE_BANK1) && \
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_PROGRAM_BANK1))
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear FLASH End of Operation pending bit */
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_EOP_BANK2);
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank1 operation error flags */
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_SR_OPERR)
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR1 & (FLASH_FLAG_WRPERR_BANK1 | FLASH_FLAG_PGSERR_BANK1 | FLASH_FLAG_STRBERR_
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                             FLASH_FLAG_INCERR_BANK1 | FLASH_FLAG_OPERR_BANK1);
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR1 & (FLASH_FLAG_WRPERR_BANK1 | FLASH_FLAG_PGSERR_BANK1 | FLASH_FLAG_STRBERR_
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                             FLASH_FLAG_INCERR_BANK1);
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_SR_OPERR */
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(errorflag != 0U)
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= errorflag;
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear error programming flags */
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG_BANK1(errorflag);
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     procedure = pFlash.ProcedureOnGoing;
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(procedure == FLASH_PROC_SECTERASE_BANK1)
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Return the faulty sector */
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       temp = pFlash.Sector;
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.Sector = 0xFFFFFFFFU;
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else if((procedure == FLASH_PROC_MASSERASE_BANK1) || (procedure == FLASH_PROC_ALLBANK_MASSERASE
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Return the faulty bank */
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       temp = FLASH_BANK_1;
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Return the faulty address */
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       temp = pFlash.Address;
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Stop the procedure ongoing*/
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
ARM GAS  /tmp/cczI1n00.s 			page 13


 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* FLASH error interrupt user callback */
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     HAL_FLASH_OperationErrorCallback(temp);
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if (USE_FLASH_ECC == 1U)
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank1 ECC single correction error flag */
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR1 & FLASH_FLAG_SNECCERR_BANK1;
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(errorflag != 0U)
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= errorflag;
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Call User callback */
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     HAL_FLASHEx_EccCorrectionCallback();
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear FLASH Bank1 ECC single correction error flag in order to allow new ECC error record */
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG_BANK1(errorflag);
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank1 ECC double detection error flag */
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR1 & FLASH_FLAG_DBECCERR_BANK1;
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(errorflag != 0U)
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= errorflag;
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Call User callback */
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     HAL_FLASHEx_EccDetectionCallback();
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear FLASH Bank1 ECC double detection error flag in order to allow new ECC error record */
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG_BANK1(errorflag);
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* USE_FLASH_ECC */
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank2 operation error flags */
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_SR_OPERR)
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR2 & ((FLASH_FLAG_WRPERR_BANK2 | FLASH_FLAG_PGSERR_BANK2 | FLASH_FLAG_STRBERR
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                              FLASH_FLAG_INCERR_BANK2 | FLASH_FLAG_OPERR_BANK2) & 0x7FFFFFFFU);
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR2 & ((FLASH_FLAG_WRPERR_BANK2 | FLASH_FLAG_PGSERR_BANK2 | FLASH_FLAG_STRBERR
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                              FLASH_FLAG_INCERR_BANK2) & 0x7FFFFFFFU);
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_SR_OPERR */
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(errorflag != 0U)
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= (errorflag | 0x80000000U);
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear error programming flags */
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG_BANK2(errorflag);
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     procedure = pFlash.ProcedureOnGoing;
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(procedure== FLASH_PROC_SECTERASE_BANK2)
ARM GAS  /tmp/cczI1n00.s 			page 14


 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /*return the faulty sector*/
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       temp = pFlash.Sector;
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.Sector = 0xFFFFFFFFU;
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else if((procedure == FLASH_PROC_MASSERASE_BANK2) || (procedure == FLASH_PROC_ALLBANK_MASSERASE
 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /*return the faulty bank*/
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       temp = FLASH_BANK_2;
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     else
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /*return the faulty address*/
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       temp = pFlash.Address;
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /*Stop the procedure ongoing*/
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* FLASH error interrupt user callback */
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     HAL_FLASH_OperationErrorCallback(temp);
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if (USE_FLASH_ECC == 1U)
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank2 ECC single correction error flag */
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR2 & FLASH_FLAG_SNECCERR_BANK2;
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(errorflag != 0U)
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= (errorflag | 0x80000000U);
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Call User callback */
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     HAL_FLASHEx_EccCorrectionCallback();
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear FLASH Bank2 ECC single correction error flag in order to allow new ECC error record */
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG_BANK2(errorflag);
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH Bank2 ECC double detection error flag */
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   errorflag = FLASH->SR2 & FLASH_FLAG_DBECCERR_BANK2;
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(errorflag != 0U)
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= (errorflag | 0x80000000U);
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Call User callback */
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     HAL_FLASHEx_EccDetectionCallback();
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear FLASH Bank2 ECC double detection error flag in order to allow new ECC error record */
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG_BANK2(errorflag);
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* USE_FLASH_ECC */
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
ARM GAS  /tmp/cczI1n00.s 			page 15


 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(pFlash.ProcedureOnGoing == FLASH_PROC_NONE)
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (FLASH_CR_OPERRIE)
 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Disable Bank1 Operation and Error source interrupt */
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_DISABLE_IT_BANK1(FLASH_IT_EOP_BANK1    | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_BA
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                  FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_BA
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Disable Bank2 Operation and Error source interrupt */
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_DISABLE_IT_BANK2(FLASH_IT_EOP_BANK2    | FLASH_IT_WRPERR_BANK2 | FLASH_IT_PGSERR_BA
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                  FLASH_IT_STRBERR_BANK2 | FLASH_IT_INCERR_BANK2 | FLASH_IT_OPERR_BA
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #else
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Disable Bank1 Operation and Error source interrupt */
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_DISABLE_IT_BANK1(FLASH_IT_EOP_BANK1    | FLASH_IT_WRPERR_BANK1 | FLASH_IT_PGSERR_BA
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                  FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1);
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Disable Bank2 Operation and Error source interrupt */
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_DISABLE_IT_BANK2(FLASH_IT_EOP_BANK2    | FLASH_IT_WRPERR_BANK2 | FLASH_IT_PGSERR_BA
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                  FLASH_IT_STRBERR_BANK2 | FLASH_IT_INCERR_BANK2);
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_CR_OPERRIE */
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Process Unlocked */
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_UNLOCK(&pFlash);
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  FLASH end of operation interrupt callback
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  ReturnValue The value saved in this parameter depends on the ongoing procedure
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                  Mass Erase: Bank number which has been requested to erase
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                  Sectors Erase: Sector which has been erased
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                    (if 0xFFFFFFFF, it means that all the selected sectors have been erased)
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                  Program: Address which was selected for data program
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval None
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** __weak void HAL_FLASH_EndOfOperationCallback(uint32_t ReturnValue)
 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
  29              		.loc 1 811 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Prevent unused argument(s) compilation warning */
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   UNUSED(ReturnValue);
  34              		.loc 1 813 3 view .LVU1
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* NOTE : This function Should not be modified, when the callback is needed,
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****             the HAL_FLASH_EndOfOperationCallback could be implemented in the user file
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****    */
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
  35              		.loc 1 818 1 is_stmt 0 view .LVU2
  36 0000 7047     		bx	lr
  37              		.cfi_endproc
  38              	.LFE147:
ARM GAS  /tmp/cczI1n00.s 			page 16


  40              		.section	.text.HAL_FLASH_OperationErrorCallback,"ax",%progbits
  41              		.align	1
  42              		.weak	HAL_FLASH_OperationErrorCallback
  43              		.syntax unified
  44              		.thumb
  45              		.thumb_func
  47              	HAL_FLASH_OperationErrorCallback:
  48              	.LVL1:
  49              	.LFB148:
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  FLASH operation error interrupt callback
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  ReturnValue The value saved in this parameter depends on the ongoing procedure
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                 Mass Erase: Bank number which has been requested to erase
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                 Sectors Erase: Sector number which returned an error
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *                 Program: Address which was selected for data program
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval None
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** __weak void HAL_FLASH_OperationErrorCallback(uint32_t ReturnValue)
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
  50              		.loc 1 829 1 is_stmt 1 view -0
  51              		.cfi_startproc
  52              		@ args = 0, pretend = 0, frame = 0
  53              		@ frame_needed = 0, uses_anonymous_args = 0
  54              		@ link register save eliminated.
 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Prevent unused argument(s) compilation warning */
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   UNUSED(ReturnValue);
  55              		.loc 1 831 3 view .LVU4
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* NOTE : This function Should not be modified, when the callback is needed,
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****             the HAL_FLASH_OperationErrorCallback could be implemented in the user file
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****    */
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
  56              		.loc 1 836 1 is_stmt 0 view .LVU5
  57 0000 7047     		bx	lr
  58              		.cfi_endproc
  59              	.LFE148:
  61              		.section	.text.HAL_FLASH_IRQHandler,"ax",%progbits
  62              		.align	1
  63              		.global	HAL_FLASH_IRQHandler
  64              		.syntax unified
  65              		.thumb
  66              		.thumb_func
  68              	HAL_FLASH_IRQHandler:
  69              	.LFB146:
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t temp;
  70              		.loc 1 476 1 is_stmt 1 view -0
  71              		.cfi_startproc
  72              		@ args = 0, pretend = 0, frame = 0
  73              		@ frame_needed = 0, uses_anonymous_args = 0
  74 0000 10B5     		push	{r4, lr}
  75              	.LCFI0:
  76              		.cfi_def_cfa_offset 8
  77              		.cfi_offset 4, -8
  78              		.cfi_offset 14, -4
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t errorflag;
  79              		.loc 1 477 3 view .LVU7
ARM GAS  /tmp/cczI1n00.s 			page 17


 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   FLASH_ProcedureTypeDef procedure;
  80              		.loc 1 478 3 view .LVU8
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  81              		.loc 1 479 3 view .LVU9
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
  82              		.loc 1 482 3 view .LVU10
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
  83              		.loc 1 482 6 is_stmt 0 view .LVU11
  84 0002 3D4B     		ldr	r3, .L19
  85 0004 1B69     		ldr	r3, [r3, #16]
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
  86              		.loc 1 482 5 view .LVU12
  87 0006 13F4803F 		tst	r3, #65536
  88 000a 18D0     		beq	.L4
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
  89              		.loc 1 484 5 is_stmt 1 view .LVU13
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
  90              		.loc 1 484 14 is_stmt 0 view .LVU14
  91 000c 3B4B     		ldr	r3, .L19+4
  92 000e 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
  93 0010 DBB2     		uxtb	r3, r3
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
  94              		.loc 1 484 7 view .LVU15
  95 0012 012B     		cmp	r3, #1
  96 0014 29D0     		beq	.L15
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  97              		.loc 1 519 7 is_stmt 1 view .LVU16
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
  98              		.loc 1 519 17 is_stmt 0 view .LVU17
  99 0016 394B     		ldr	r3, .L19+4
 100 0018 1C78     		ldrb	r4, [r3]	@ zero_extendqisi2
 101 001a E4B2     		uxtb	r4, r4
 102              	.LVL2:
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 103              		.loc 1 521 7 is_stmt 1 view .LVU18
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 104              		.loc 1 521 9 is_stmt 0 view .LVU19
 105 001c 072C     		cmp	r4, #7
 106 001e 18BF     		it	ne
 107 0020 022C     		cmpne	r4, #2
 108 0022 47D0     		beq	.L16
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 109              		.loc 1 527 12 is_stmt 1 view .LVU20
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 110              		.loc 1 527 14 is_stmt 0 view .LVU21
 111 0024 032C     		cmp	r4, #3
 112 0026 49D0     		beq	.L17
 113              	.L8:
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 114              		.loc 1 536 7 is_stmt 1 view .LVU22
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_MASSERASE_BANK2) && \
 115              		.loc 1 538 7 view .LVU23
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_PROGRAM_BANK2))
 116              		.loc 1 539 52 is_stmt 0 view .LVU24
 117 0028 043C     		subs	r4, r4, #4
 118              	.LVL3:
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_PROGRAM_BANK2))
ARM GAS  /tmp/cczI1n00.s 			page 18


 119              		.loc 1 539 52 view .LVU25
 120 002a E4B2     		uxtb	r4, r4
 121              	.LVL4:
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****          (procedure != FLASH_PROC_MASSERASE_BANK2) && \
 122              		.loc 1 538 9 view .LVU26
 123 002c 022C     		cmp	r4, #2
 124 002e 06D9     		bls	.L4
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear FLASH End of Operation pending bit */
 125              		.loc 1 542 9 is_stmt 1 view .LVU27
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         /* Clear FLASH End of Operation pending bit */
 126              		.loc 1 542 33 is_stmt 0 view .LVU28
 127 0030 324B     		ldr	r3, .L19+4
 128 0032 0022     		movs	r2, #0
 129 0034 1A70     		strb	r2, [r3]
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 130              		.loc 1 544 9 is_stmt 1 view .LVU29
 131 0036 304B     		ldr	r3, .L19
 132 0038 4FF48032 		mov	r2, #65536
 133 003c 5A61     		str	r2, [r3, #20]
 134              	.LVL5:
 135              	.L4:
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                             FLASH_FLAG_INCERR_BANK1 | FLASH_FLAG_OPERR_BANK1);
 136              		.loc 1 621 3 view .LVU30
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                             FLASH_FLAG_INCERR_BANK1 | FLASH_FLAG_OPERR_BANK1);
 137              		.loc 1 621 20 is_stmt 0 view .LVU31
 138 003e 2E4B     		ldr	r3, .L19
 139 0040 1B69     		ldr	r3, [r3, #16]
 140              	.LVL6:
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 141              		.loc 1 628 3 is_stmt 1 view .LVU32
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 142              		.loc 1 628 5 is_stmt 0 view .LVU33
 143 0042 13F4DC03 		ands	r3, r3, #7208960
 144              	.LVL7:
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 145              		.loc 1 628 5 view .LVU34
 146 0046 48D0     		beq	.L9
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 147              		.loc 1 631 5 is_stmt 1 view .LVU35
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 148              		.loc 1 631 11 is_stmt 0 view .LVU36
 149 0048 2C4A     		ldr	r2, .L19+4
 150 004a 9169     		ldr	r1, [r2, #24]
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 151              		.loc 1 631 22 view .LVU37
 152 004c 1943     		orrs	r1, r1, r3
 153 004e 9161     		str	r1, [r2, #24]
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 154              		.loc 1 634 5 is_stmt 1 view .LVU38
 155 0050 2949     		ldr	r1, .L19
 156 0052 4B61     		str	r3, [r1, #20]
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 157              		.loc 1 636 5 view .LVU39
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 158              		.loc 1 636 15 is_stmt 0 view .LVU40
 159 0054 1378     		ldrb	r3, [r2]	@ zero_extendqisi2
 160              	.LVL8:
ARM GAS  /tmp/cczI1n00.s 			page 19


 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 161              		.loc 1 636 15 view .LVU41
 162 0056 DBB2     		uxtb	r3, r3
 163              	.LVL9:
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 164              		.loc 1 638 5 is_stmt 1 view .LVU42
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 165              		.loc 1 638 7 is_stmt 0 view .LVU43
 166 0058 012B     		cmp	r3, #1
 167 005a 34D0     		beq	.L18
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 168              		.loc 1 644 10 is_stmt 1 view .LVU44
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 169              		.loc 1 644 12 is_stmt 0 view .LVU45
 170 005c 072B     		cmp	r3, #7
 171 005e 18BF     		it	ne
 172 0060 022B     		cmpne	r3, #2
 173 0062 46D0     		beq	.L13
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 174              		.loc 1 652 7 is_stmt 1 view .LVU46
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 175              		.loc 1 652 12 is_stmt 0 view .LVU47
 176 0064 254B     		ldr	r3, .L19+4
 177              	.LVL10:
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 178              		.loc 1 652 12 view .LVU48
 179 0066 1869     		ldr	r0, [r3, #16]
 180              	.LVL11:
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 181              		.loc 1 652 12 view .LVU49
 182 0068 32E0     		b	.L11
 183              	.LVL12:
 184              	.L15:
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 185              		.loc 1 487 7 is_stmt 1 view .LVU50
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 186              		.loc 1 487 13 is_stmt 0 view .LVU51
 187 006a 244B     		ldr	r3, .L19+4
 188 006c 5A68     		ldr	r2, [r3, #4]
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 189              		.loc 1 487 30 view .LVU52
 190 006e 013A     		subs	r2, r2, #1
 191 0070 5A60     		str	r2, [r3, #4]
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 192              		.loc 1 490 7 is_stmt 1 view .LVU53
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 193              		.loc 1 490 16 is_stmt 0 view .LVU54
 194 0072 5B68     		ldr	r3, [r3, #4]
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 195              		.loc 1 490 9 view .LVU55
 196 0074 83B1     		cbz	r3, .L6
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 197              		.loc 1 493 9 is_stmt 1 view .LVU56
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 198              		.loc 1 493 48 is_stmt 0 view .LVU57
 199 0076 214C     		ldr	r4, .L19+4
 200 0078 E068     		ldr	r0, [r4, #12]
ARM GAS  /tmp/cczI1n00.s 			page 20


 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 201              		.loc 1 493 9 view .LVU58
 202 007a FFF7FEFF 		bl	HAL_FLASH_EndOfOperationCallback
 203              	.LVL13:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 204              		.loc 1 496 9 is_stmt 1 view .LVU59
 205 007e 1E4B     		ldr	r3, .L19
 206 0080 4FF48032 		mov	r2, #65536
 207 0084 5A61     		str	r2, [r3, #20]
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         temp = pFlash.Sector;
 208              		.loc 1 499 9 view .LVU60
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         temp = pFlash.Sector;
 209              		.loc 1 499 15 is_stmt 0 view .LVU61
 210 0086 E368     		ldr	r3, [r4, #12]
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         temp = pFlash.Sector;
 211              		.loc 1 499 22 view .LVU62
 212 0088 0133     		adds	r3, r3, #1
 213 008a E360     		str	r3, [r4, #12]
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         FLASH_Erase_Sector(temp, FLASH_BANK_1, pFlash.VoltageForErase);
 214              		.loc 1 500 9 is_stmt 1 view .LVU63
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         FLASH_Erase_Sector(temp, FLASH_BANK_1, pFlash.VoltageForErase);
 215              		.loc 1 500 14 is_stmt 0 view .LVU64
 216 008c E068     		ldr	r0, [r4, #12]
 217              	.LVL14:
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 218              		.loc 1 501 9 is_stmt 1 view .LVU65
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 219              		.loc 1 501 54 is_stmt 0 view .LVU66
 220 008e A268     		ldr	r2, [r4, #8]
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 221              		.loc 1 501 9 view .LVU67
 222 0090 0121     		movs	r1, #1
 223 0092 FFF7FEFF 		bl	FLASH_Erase_Sector
 224              	.LVL15:
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 225              		.loc 1 501 9 view .LVU68
 226 0096 D2E7     		b	.L4
 227              	.LVL16:
 228              	.L6:
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 229              		.loc 1 507 9 is_stmt 1 view .LVU69
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         pFlash.ProcedureOnGoing = FLASH_PROC_NONE;
 230              		.loc 1 507 23 is_stmt 0 view .LVU70
 231 0098 184B     		ldr	r3, .L19+4
 232 009a 4FF0FF32 		mov	r2, #-1
 233 009e DA60     		str	r2, [r3, #12]
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 234              		.loc 1 508 9 is_stmt 1 view .LVU71
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 235              		.loc 1 508 33 is_stmt 0 view .LVU72
 236 00a0 0022     		movs	r2, #0
 237 00a2 1A70     		strb	r2, [r3]
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 238              		.loc 1 511 9 is_stmt 1 view .LVU73
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 239              		.loc 1 511 48 is_stmt 0 view .LVU74
 240 00a4 D868     		ldr	r0, [r3, #12]
ARM GAS  /tmp/cczI1n00.s 			page 21


 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 241              		.loc 1 511 9 view .LVU75
 242 00a6 FFF7FEFF 		bl	HAL_FLASH_EndOfOperationCallback
 243              	.LVL17:
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 244              		.loc 1 514 9 is_stmt 1 view .LVU76
 245 00aa 134B     		ldr	r3, .L19
 246 00ac 4FF48032 		mov	r2, #65536
 247 00b0 5A61     		str	r2, [r3, #20]
 248 00b2 C4E7     		b	.L4
 249              	.LVL18:
 250              	.L16:
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 251              		.loc 1 525 9 view .LVU77
 252 00b4 0120     		movs	r0, #1
 253 00b6 FFF7FEFF 		bl	HAL_FLASH_EndOfOperationCallback
 254              	.LVL19:
 255 00ba B5E7     		b	.L8
 256              	.L17:
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 257              		.loc 1 531 9 view .LVU78
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 258              		.loc 1 531 48 is_stmt 0 view .LVU79
 259 00bc 0F4B     		ldr	r3, .L19+4
 260 00be 1869     		ldr	r0, [r3, #16]
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 261              		.loc 1 531 9 view .LVU80
 262 00c0 FFF7FEFF 		bl	HAL_FLASH_EndOfOperationCallback
 263              	.LVL20:
 264 00c4 B0E7     		b	.L8
 265              	.LVL21:
 266              	.L18:
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.Sector = 0xFFFFFFFFU;
 267              		.loc 1 641 7 is_stmt 1 view .LVU81
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.Sector = 0xFFFFFFFFU;
 268              		.loc 1 641 12 is_stmt 0 view .LVU82
 269 00c6 1346     		mov	r3, r2
 270              	.LVL22:
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.Sector = 0xFFFFFFFFU;
 271              		.loc 1 641 12 view .LVU83
 272 00c8 D068     		ldr	r0, [r2, #12]
 273              	.LVL23:
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 274              		.loc 1 642 7 is_stmt 1 view .LVU84
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 275              		.loc 1 642 21 is_stmt 0 view .LVU85
 276 00ca 4FF0FF32 		mov	r2, #-1
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 277              		.loc 1 642 21 view .LVU86
 278 00ce DA60     		str	r2, [r3, #12]
 279              	.LVL24:
 280              	.L11:
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 281              		.loc 1 656 5 is_stmt 1 view .LVU87
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 282              		.loc 1 656 29 is_stmt 0 view .LVU88
 283 00d0 0A4B     		ldr	r3, .L19+4
ARM GAS  /tmp/cczI1n00.s 			page 22


 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 284              		.loc 1 656 29 view .LVU89
 285 00d2 0022     		movs	r2, #0
 286 00d4 1A70     		strb	r2, [r3]
 287              	.LVL25:
 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 288              		.loc 1 659 5 is_stmt 1 view .LVU90
 289 00d6 FFF7FEFF 		bl	HAL_FLASH_OperationErrorCallback
 290              	.LVL26:
 291              	.L9:
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 292              		.loc 1 772 3 view .LVU91
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 293              		.loc 1 772 12 is_stmt 0 view .LVU92
 294 00da 084B     		ldr	r3, .L19+4
 295 00dc 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 296              		.loc 1 772 5 view .LVU93
 297 00de 3BB9     		cbnz	r3, .L3
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                  FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_BA
 298              		.loc 1 776 5 is_stmt 1 view .LVU94
 299 00e0 054A     		ldr	r2, .L19
 300 00e2 D368     		ldr	r3, [r2, #12]
 301 00e4 23F4DE03 		bic	r3, r3, #7274496
 302 00e8 D360     		str	r3, [r2, #12]
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 303              		.loc 1 797 5 view .LVU95
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 304              		.loc 1 797 5 view .LVU96
 305 00ea 044B     		ldr	r3, .L19+4
 306 00ec 0022     		movs	r2, #0
 307 00ee 1A75     		strb	r2, [r3, #20]
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 308              		.loc 1 797 5 discriminator 1 view .LVU97
 309              	.L3:
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 310              		.loc 1 799 1 is_stmt 0 view .LVU98
 311 00f0 10BD     		pop	{r4, pc}
 312              	.LVL27:
 313              	.L13:
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 314              		.loc 1 647 12 view .LVU99
 315 00f2 0120     		movs	r0, #1
 316 00f4 ECE7     		b	.L11
 317              	.L20:
 318 00f6 00BF     		.align	2
 319              	.L19:
 320 00f8 ******** 		.word	**********
 321 00fc 00000000 		.word	pFlash
 322              		.cfi_endproc
 323              	.LFE146:
 325              		.section	.text.HAL_FLASH_Unlock,"ax",%progbits
 326              		.align	1
 327              		.global	HAL_FLASH_Unlock
 328              		.syntax unified
 329              		.thumb
 330              		.thumb_func
ARM GAS  /tmp/cczI1n00.s 			page 23


 332              	HAL_FLASH_Unlock:
 333              	.LFB149:
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @}
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @defgroup FLASH_Exported_Functions_Group2 Peripheral Control functions
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *  @brief   Management functions
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** @verbatim
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ===============================================================================
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                       ##### Peripheral Control functions #####
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ===============================================================================
 849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     [..]
 850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     This subsection provides a set of functions allowing to control the FLASH
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     memory operations.
 852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** @endverbatim
 854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
 855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Unlock the FLASH control registers access
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL Status
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_Unlock(void)
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 334              		.loc 1 862 1 is_stmt 1 view -0
 335              		.cfi_startproc
 336              		@ args = 0, pretend = 0, frame = 0
 337              		@ frame_needed = 0, uses_anonymous_args = 0
 338              		@ link register save eliminated.
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(READ_BIT(FLASH->CR1, FLASH_CR_LOCK) != 0U)
 339              		.loc 1 863 3 view .LVU101
 340              		.loc 1 863 6 is_stmt 0 view .LVU102
 341 0000 0A4B     		ldr	r3, .L25
 342 0002 DB68     		ldr	r3, [r3, #12]
 343              		.loc 1 863 5 view .LVU103
 344 0004 13F0010F 		tst	r3, #1
 345 0008 0BD0     		beq	.L23
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Authorize the FLASH Bank1 Registers access */
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     WRITE_REG(FLASH->KEYR1, FLASH_KEY1);
 346              		.loc 1 866 5 is_stmt 1 view .LVU104
 347 000a 084B     		ldr	r3, .L25
 348 000c 084A     		ldr	r2, .L25+4
 349 000e 5A60     		str	r2, [r3, #4]
 867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     WRITE_REG(FLASH->KEYR1, FLASH_KEY2);
 350              		.loc 1 867 5 view .LVU105
 351 0010 02F18832 		add	r2, r2, #-**********
 352 0014 5A60     		str	r2, [r3, #4]
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Verify Flash Bank1 is unlocked */
 870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (READ_BIT(FLASH->CR1, FLASH_CR_LOCK) != 0U)
 353              		.loc 1 870 5 view .LVU106
 354              		.loc 1 870 9 is_stmt 0 view .LVU107
ARM GAS  /tmp/cczI1n00.s 			page 24


 355 0016 DB68     		ldr	r3, [r3, #12]
 356              		.loc 1 870 8 view .LVU108
 357 0018 13F0010F 		tst	r3, #1
 358 001c 03D1     		bne	.L24
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       return HAL_ERROR;
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(READ_BIT(FLASH->CR2, FLASH_CR_LOCK) != 0U)
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Authorize the FLASH Bank2 Registers access */
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     WRITE_REG(FLASH->KEYR2, FLASH_KEY1);
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     WRITE_REG(FLASH->KEYR2, FLASH_KEY2);
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Verify Flash Bank2 is unlocked */
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (READ_BIT(FLASH->CR2, FLASH_CR_LOCK) != 0U)
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       return HAL_ERROR;
 887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
 359              		.loc 1 891 10 view .LVU109
 360 001e 0020     		movs	r0, #0
 361 0020 7047     		bx	lr
 362              	.L23:
 363 0022 0020     		movs	r0, #0
 364 0024 7047     		bx	lr
 365              	.L24:
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 366              		.loc 1 872 14 view .LVU110
 367 0026 0120     		movs	r0, #1
 892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 368              		.loc 1 892 1 view .LVU111
 369 0028 7047     		bx	lr
 370              	.L26:
 371 002a 00BF     		.align	2
 372              	.L25:
 373 002c ******** 		.word	**********
 374 0030 ******** 		.word	**********
 375              		.cfi_endproc
 376              	.LFE149:
 378              		.section	.text.HAL_FLASH_Lock,"ax",%progbits
 379              		.align	1
 380              		.global	HAL_FLASH_Lock
 381              		.syntax unified
 382              		.thumb
 383              		.thumb_func
 385              	HAL_FLASH_Lock:
 386              	.LFB150:
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Locks the FLASH control registers access
 896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL Status
ARM GAS  /tmp/cczI1n00.s 			page 25


 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_Lock(void)
 899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 387              		.loc 1 899 1 is_stmt 1 view -0
 388              		.cfi_startproc
 389              		@ args = 0, pretend = 0, frame = 0
 390              		@ frame_needed = 0, uses_anonymous_args = 0
 391              		@ link register save eliminated.
 900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Set the LOCK Bit to lock the FLASH Bank1 Control Register access */
 901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   SET_BIT(FLASH->CR1, FLASH_CR_LOCK);
 392              		.loc 1 901 3 view .LVU113
 393 0000 064B     		ldr	r3, .L30
 394 0002 DA68     		ldr	r2, [r3, #12]
 395 0004 42F00102 		orr	r2, r2, #1
 396 0008 DA60     		str	r2, [r3, #12]
 902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Verify Flash Bank1 is locked */
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (READ_BIT(FLASH->CR1, FLASH_CR_LOCK) == 0U)
 397              		.loc 1 904 3 view .LVU114
 398              		.loc 1 904 7 is_stmt 0 view .LVU115
 399 000a DB68     		ldr	r3, [r3, #12]
 400              		.loc 1 904 6 view .LVU116
 401 000c 13F0010F 		tst	r3, #1
 402 0010 01D0     		beq	.L29
 905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Set the LOCK Bit to lock the FLASH Bank2 Control Register access */
 911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   SET_BIT(FLASH->CR2, FLASH_CR_LOCK);
 912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Verify Flash Bank2 is locked */
 914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (READ_BIT(FLASH->CR2, FLASH_CR_LOCK) == 0U)
 915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
 403              		.loc 1 920 10 view .LVU117
 404 0012 0020     		movs	r0, #0
 405 0014 7047     		bx	lr
 406              	.L29:
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 407              		.loc 1 906 12 view .LVU118
 408 0016 0120     		movs	r0, #1
 921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 409              		.loc 1 921 1 view .LVU119
 410 0018 7047     		bx	lr
 411              	.L31:
 412 001a 00BF     		.align	2
 413              	.L30:
 414 001c ******** 		.word	**********
 415              		.cfi_endproc
 416              	.LFE150:
 418              		.section	.text.HAL_FLASH_OB_Unlock,"ax",%progbits
ARM GAS  /tmp/cczI1n00.s 			page 26


 419              		.align	1
 420              		.global	HAL_FLASH_OB_Unlock
 421              		.syntax unified
 422              		.thumb
 423              		.thumb_func
 425              	HAL_FLASH_OB_Unlock:
 426              	.LFB151:
 922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Unlock the FLASH Option Control Registers access.
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL Status
 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_OB_Unlock(void)
 928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 427              		.loc 1 928 1 is_stmt 1 view -0
 428              		.cfi_startproc
 429              		@ args = 0, pretend = 0, frame = 0
 430              		@ frame_needed = 0, uses_anonymous_args = 0
 431              		@ link register save eliminated.
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(READ_BIT(FLASH->OPTCR, FLASH_OPTCR_OPTLOCK) != 0U)
 432              		.loc 1 929 3 view .LVU121
 433              		.loc 1 929 6 is_stmt 0 view .LVU122
 434 0000 0A4B     		ldr	r3, .L36
 435 0002 9B69     		ldr	r3, [r3, #24]
 436              		.loc 1 929 5 view .LVU123
 437 0004 13F0010F 		tst	r3, #1
 438 0008 0BD0     		beq	.L34
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Authorizes the Option Byte registers programming */
 932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     WRITE_REG(FLASH->OPTKEYR, FLASH_OPT_KEY1);
 439              		.loc 1 932 5 is_stmt 1 view .LVU124
 440 000a 084B     		ldr	r3, .L36
 441 000c 084A     		ldr	r2, .L36+4
 442 000e 9A60     		str	r2, [r3, #8]
 933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     WRITE_REG(FLASH->OPTKEYR, FLASH_OPT_KEY2);
 443              		.loc 1 933 5 view .LVU125
 444 0010 02F14432 		add	r2, r2, #1145324612
 445 0014 9A60     		str	r2, [r3, #8]
 934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Verify that the Option Bytes are unlocked */
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (READ_BIT(FLASH->OPTCR, FLASH_OPTCR_OPTLOCK) != 0U)
 446              		.loc 1 936 5 view .LVU126
 447              		.loc 1 936 9 is_stmt 0 view .LVU127
 448 0016 9B69     		ldr	r3, [r3, #24]
 449              		.loc 1 936 8 view .LVU128
 450 0018 13F0010F 		tst	r3, #1
 451 001c 03D1     		bne	.L35
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       return HAL_ERROR;
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
 452              		.loc 1 942 10 view .LVU129
 453 001e 0020     		movs	r0, #0
 454 0020 7047     		bx	lr
 455              	.L34:
ARM GAS  /tmp/cczI1n00.s 			page 27


 456 0022 0020     		movs	r0, #0
 457 0024 7047     		bx	lr
 458              	.L35:
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 459              		.loc 1 938 14 view .LVU130
 460 0026 0120     		movs	r0, #1
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 461              		.loc 1 943 1 view .LVU131
 462 0028 7047     		bx	lr
 463              	.L37:
 464 002a 00BF     		.align	2
 465              	.L36:
 466 002c ******** 		.word	**********
 467 0030 3B2A1908 		.word	*********
 468              		.cfi_endproc
 469              	.LFE151:
 471              		.section	.text.HAL_FLASH_OB_Lock,"ax",%progbits
 472              		.align	1
 473              		.global	HAL_FLASH_OB_Lock
 474              		.syntax unified
 475              		.thumb
 476              		.thumb_func
 478              	HAL_FLASH_OB_Lock:
 479              	.LFB152:
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Lock the FLASH Option Control Registers access.
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL Status
 948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_OB_Lock(void)
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 480              		.loc 1 950 1 is_stmt 1 view -0
 481              		.cfi_startproc
 482              		@ args = 0, pretend = 0, frame = 0
 483              		@ frame_needed = 0, uses_anonymous_args = 0
 484              		@ link register save eliminated.
 951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Set the OPTLOCK Bit to lock the FLASH Option Byte Registers access */
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   SET_BIT(FLASH->OPTCR, FLASH_OPTCR_OPTLOCK);
 485              		.loc 1 952 3 view .LVU133
 486 0000 064B     		ldr	r3, .L41
 487 0002 9A69     		ldr	r2, [r3, #24]
 488 0004 42F00102 		orr	r2, r2, #1
 489 0008 9A61     		str	r2, [r3, #24]
 953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Verify that the Option Bytes are locked */
 955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (READ_BIT(FLASH->OPTCR, FLASH_OPTCR_OPTLOCK) == 0U)
 490              		.loc 1 955 3 view .LVU134
 491              		.loc 1 955 7 is_stmt 0 view .LVU135
 492 000a 9B69     		ldr	r3, [r3, #24]
 493              		.loc 1 955 6 view .LVU136
 494 000c 13F0010F 		tst	r3, #1
 495 0010 01D0     		beq	.L40
 956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
ARM GAS  /tmp/cczI1n00.s 			page 28


 496              		.loc 1 960 10 view .LVU137
 497 0012 0020     		movs	r0, #0
 498 0014 7047     		bx	lr
 499              	.L40:
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 500              		.loc 1 957 12 view .LVU138
 501 0016 0120     		movs	r0, #1
 961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 502              		.loc 1 961 1 view .LVU139
 503 0018 7047     		bx	lr
 504              	.L42:
 505 001a 00BF     		.align	2
 506              	.L41:
 507 001c ******** 		.word	**********
 508              		.cfi_endproc
 509              	.LFE152:
 511              		.section	.text.HAL_FLASH_GetError,"ax",%progbits
 512              		.align	1
 513              		.global	HAL_FLASH_GetError
 514              		.syntax unified
 515              		.thumb
 516              		.thumb_func
 518              	HAL_FLASH_GetError:
 519              	.LFB154:
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Launch the option bytes loading.
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL Status
 966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
 967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef HAL_FLASH_OB_Launch(void)
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Wait for CRC computation to be completed */
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (FLASH_CRC_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_1) != HAL_OK)
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     status = HAL_ERROR;
 975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else if (FLASH_CRC_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE, FLASH_BANK_2) != HAL_OK)
 978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     status = HAL_ERROR;
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     status = HAL_OK;
 985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (status == HAL_OK)
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Set OPTSTRT Bit */
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     SET_BIT(FLASH->OPTCR, FLASH_OPTCR_OPTSTART);
 991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Wait for OB change operation to be completed */
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     status = FLASH_OB_WaitForLastOperation((uint32_t)FLASH_TIMEOUT_VALUE);
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
ARM GAS  /tmp/cczI1n00.s 			page 29


 995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return status;
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @}
1001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @defgroup FLASH_Exported_Functions_Group3 Peripheral State and Errors functions
1004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *  @brief   Peripheral Errors functions
1005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** @verbatim
1007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ===============================================================================
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                 ##### Peripheral Errors functions #####
1009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****  ===============================================================================
1010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     [..]
1011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     This subsection permits to get in run-time Errors of the FLASH peripheral.
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** @endverbatim
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Get the specific FLASH error flag.
1019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL_FLASH_ERRORCode The returned value can be:
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_NONE       : No error set
1021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
1022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_WRP_BANK1  : Write Protection Error on Bank 1
1023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_PGS_BANK1  : Program Sequence Error on Bank 1
1024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_STRB_BANK1 : Strobe Error on Bank 1
1025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_INC_BANK1  : Inconsistency Error on Bank 1
1026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_OPE_BANK1  : Operation Error on Bank 1
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_RDP_BANK1  : Read Protection Error on Bank 1
1028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_RDS_BANK1  : Read Secured Error on Bank 1
1029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_SNECC_BANK1: ECC Single Correction Error on Bank 1
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_DBECC_BANK1: ECC Double Detection Error on Bank 1
1031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_CRCRD_BANK1: CRC Read Error on Bank 1
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_WRP_BANK2  : Write Protection Error on Bank 2
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_PGS_BANK2  : Program Sequence Error on Bank 2
1035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_STRB_BANK2 : Strobe Error on Bank 2
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_INC_BANK2  : Inconsistency Error on Bank 2
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_OPE_BANK2  : Operation Error on Bank 2
1038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_RDP_BANK2  : Read Protection Error on Bank 2
1039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_RDS_BANK2  : Read Secured Error on Bank 2
1040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_SNECC_BANK2: SNECC Error on Bank 2
1041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_DBECC_BANK2: Double Detection ECC on Bank 2
1042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   *            @arg HAL_FLASH_ERROR_CRCRD_BANK2: CRC Read Error on Bank 2
1043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** uint32_t HAL_FLASH_GetError(void)
1046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 520              		.loc 1 1046 1 is_stmt 1 view -0
 521              		.cfi_startproc
 522              		@ args = 0, pretend = 0, frame = 0
 523              		@ frame_needed = 0, uses_anonymous_args = 0
 524              		@ link register save eliminated.
ARM GAS  /tmp/cczI1n00.s 			page 30


1047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****    return pFlash.ErrorCode;
 525              		.loc 1 1047 4 view .LVU141
 526              		.loc 1 1047 17 is_stmt 0 view .LVU142
 527 0000 014B     		ldr	r3, .L44
 528 0002 9869     		ldr	r0, [r3, #24]
1048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 529              		.loc 1 1048 1 view .LVU143
 530 0004 7047     		bx	lr
 531              	.L45:
 532 0006 00BF     		.align	2
 533              	.L44:
 534 0008 00000000 		.word	pFlash
 535              		.cfi_endproc
 536              	.LFE154:
 538              		.section	.text.FLASH_WaitForLastOperation,"ax",%progbits
 539              		.align	1
 540              		.global	FLASH_WaitForLastOperation
 541              		.syntax unified
 542              		.thumb
 543              		.thumb_func
 545              	FLASH_WaitForLastOperation:
 546              	.LVL28:
 547              	.LFB155:
1049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @}
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @}
1056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /* Private functions ---------------------------------------------------------*/
1059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /** @addtogroup FLASH_Private_Functions
1061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @{
1062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Wait for a FLASH operation to complete.
1066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  Timeout maximum flash operation timeout
1067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  Bank flash FLASH_BANK_1 or FLASH_BANK_2
1068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL_StatusTypeDef HAL Status
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef FLASH_WaitForLastOperation(uint32_t Timeout, uint32_t Bank)
1071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 548              		.loc 1 1071 1 is_stmt 1 view -0
 549              		.cfi_startproc
 550              		@ args = 0, pretend = 0, frame = 0
 551              		@ frame_needed = 0, uses_anonymous_args = 0
 552              		.loc 1 1071 1 is_stmt 0 view .LVU145
 553 0000 70B5     		push	{r4, r5, r6, lr}
 554              	.LCFI1:
 555              		.cfi_def_cfa_offset 16
 556              		.cfi_offset 4, -16
 557              		.cfi_offset 5, -12
 558              		.cfi_offset 6, -8
ARM GAS  /tmp/cczI1n00.s 			page 31


 559              		.cfi_offset 14, -4
 560 0002 0446     		mov	r4, r0
 561 0004 0D46     		mov	r5, r1
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Wait for the FLASH operation to complete by polling on QW flag to be reset.
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****      Even if the FLASH operation fails, the QW flag will be reset and an error
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****      flag will be set */
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t bsyflag = FLASH_FLAG_QW_BANK1;
 562              		.loc 1 1076 3 is_stmt 1 view .LVU146
 563              	.LVL29:
1077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t errorflag = 0;
 564              		.loc 1 1077 3 view .LVU147
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t tickstart = HAL_GetTick();
 565              		.loc 1 1078 3 view .LVU148
 566              		.loc 1 1078 24 is_stmt 0 view .LVU149
 567 0006 FFF7FEFF 		bl	HAL_GetTick
 568              	.LVL30:
 569              		.loc 1 1078 24 view .LVU150
 570 000a 0646     		mov	r6, r0
 571              	.LVL31:
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_BANK_EXCLUSIVE(Bank));
 572              		.loc 1 1080 3 is_stmt 1 view .LVU151
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (Bank == FLASH_BANK_2)
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Select bsyflag depending on Bank */
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bsyflag = FLASH_FLAG_QW_BANK2;
1088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   while(__HAL_FLASH_GET_FLAG(bsyflag))
 573              		.loc 1 1091 3 view .LVU152
 574              	.L48:
 575              		.loc 1 1091 9 view .LVU153
 576 000c 194B     		ldr	r3, .L60
 577 000e 1B69     		ldr	r3, [r3, #16]
 578 0010 13F0040F 		tst	r3, #4
 579 0014 0BD0     		beq	.L57
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(Timeout != HAL_MAX_DELAY)
 580              		.loc 1 1093 5 view .LVU154
 581              		.loc 1 1093 7 is_stmt 0 view .LVU155
 582 0016 B4F1FF3F 		cmp	r4, #-1
 583 001a F7D0     		beq	.L48
1094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if(((HAL_GetTick() - tickstart) > Timeout) || (Timeout == 0U))
 584              		.loc 1 1095 7 is_stmt 1 view .LVU156
 585              		.loc 1 1095 12 is_stmt 0 view .LVU157
 586 001c FFF7FEFF 		bl	HAL_GetTick
 587              	.LVL32:
 588              		.loc 1 1095 26 discriminator 1 view .LVU158
 589 0020 801B     		subs	r0, r0, r6
 590              		.loc 1 1095 9 discriminator 1 view .LVU159
 591 0022 A042     		cmp	r0, r4
ARM GAS  /tmp/cczI1n00.s 			page 32


 592 0024 21D8     		bhi	.L52
 593              		.loc 1 1095 50 discriminator 1 view .LVU160
 594 0026 002C     		cmp	r4, #0
 595 0028 F0D1     		bne	.L48
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         return HAL_TIMEOUT;
 596              		.loc 1 1097 16 view .LVU161
 597 002a 0320     		movs	r0, #3
 598 002c 04E0     		b	.L49
 599              	.L57:
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Get Error Flags */
1103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if (Bank == FLASH_BANK_1)
 600              		.loc 1 1103 3 is_stmt 1 view .LVU162
 601              		.loc 1 1103 6 is_stmt 0 view .LVU163
 602 002e 012D     		cmp	r5, #1
 603 0030 03D0     		beq	.L58
 604              	.LVL33:
 605              	.L51:
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     errorflag = FLASH->SR1 & FLASH_FLAG_ALL_ERRORS_BANK1;
1106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
1108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
1109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     errorflag = (FLASH->SR2 & FLASH_FLAG_ALL_ERRORS_BANK2) | 0x80000000U;
1111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* In case of error reported in Flash SR1 or SR2 register */
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if((errorflag & 0x7FFFFFFFU) != 0U)
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /*Save the error code*/
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= errorflag;
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear error programming flags */
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __HAL_FLASH_CLEAR_FLAG(errorflag);
1122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
1124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH End of Operation flag  */
1127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(Bank == FLASH_BANK_1)
 606              		.loc 1 1127 3 is_stmt 1 view .LVU164
 607              		.loc 1 1127 5 is_stmt 0 view .LVU165
 608 0032 012D     		cmp	r5, #1
 609 0034 0ED0     		beq	.L59
1128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (__HAL_FLASH_GET_FLAG_BANK1(FLASH_FLAG_EOP_BANK1))
1130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Clear FLASH End of Operation pending bit */
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_EOP_BANK1);
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
ARM GAS  /tmp/cczI1n00.s 			page 33


1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
1137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (__HAL_FLASH_GET_FLAG_BANK2(FLASH_FLAG_EOP_BANK2))
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Clear FLASH End of Operation pending bit */
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_EOP_BANK2);
1142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
 610              		.loc 1 1146 10 view .LVU166
 611 0036 0020     		movs	r0, #0
 612              	.L49:
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 613              		.loc 1 1147 1 view .LVU167
 614 0038 70BD     		pop	{r4, r5, r6, pc}
 615              	.LVL34:
 616              	.L58:
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 617              		.loc 1 1105 5 is_stmt 1 view .LVU168
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 618              		.loc 1 1105 22 is_stmt 0 view .LVU169
 619 003a 0E4B     		ldr	r3, .L60
 620 003c 1B69     		ldr	r3, [r3, #16]
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 621              		.loc 1 1105 15 view .LVU170
 622 003e 0E4A     		ldr	r2, .L60+4
 623              	.LVL35:
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 624              		.loc 1 1115 3 is_stmt 1 view .LVU171
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 625              		.loc 1 1115 5 is_stmt 0 view .LVU172
 626 0040 1340     		ands	r3, r3, r2
 627              	.LVL36:
1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 628              		.loc 1 1115 5 view .LVU173
 629 0042 F6D0     		beq	.L51
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 630              		.loc 1 1118 5 is_stmt 1 view .LVU174
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 631              		.loc 1 1118 11 is_stmt 0 view .LVU175
 632 0044 0D49     		ldr	r1, .L60+8
 633 0046 8A69     		ldr	r2, [r1, #24]
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 634              		.loc 1 1118 22 view .LVU176
 635 0048 1A43     		orrs	r2, r2, r3
 636 004a 8A61     		str	r2, [r1, #24]
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 637              		.loc 1 1121 5 is_stmt 1 view .LVU177
 638 004c 094A     		ldr	r2, .L60
 639 004e 5361     		str	r3, [r2, #20]
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 640              		.loc 1 1123 5 view .LVU178
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 641              		.loc 1 1123 12 is_stmt 0 view .LVU179
ARM GAS  /tmp/cczI1n00.s 			page 34


 642 0050 0120     		movs	r0, #1
 643 0052 F1E7     		b	.L49
 644              	.LVL37:
 645              	.L59:
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 646              		.loc 1 1129 5 is_stmt 1 view .LVU180
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 647              		.loc 1 1129 9 is_stmt 0 view .LVU181
 648 0054 074B     		ldr	r3, .L60
 649 0056 1B69     		ldr	r3, [r3, #16]
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 650              		.loc 1 1129 8 view .LVU182
 651 0058 13F4803F 		tst	r3, #65536
 652 005c 07D0     		beq	.L55
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 653              		.loc 1 1132 7 is_stmt 1 view .LVU183
 654 005e 054B     		ldr	r3, .L60
 655 0060 4FF48032 		mov	r2, #65536
 656 0064 5A61     		str	r2, [r3, #20]
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 657              		.loc 1 1146 10 is_stmt 0 view .LVU184
 658 0066 0020     		movs	r0, #0
 659 0068 E6E7     		b	.L49
 660              	.LVL38:
 661              	.L52:
1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 662              		.loc 1 1097 16 view .LVU185
 663 006a 0320     		movs	r0, #3
 664 006c E4E7     		b	.L49
 665              	.LVL39:
 666              	.L55:
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 667              		.loc 1 1146 10 view .LVU186
 668 006e 0020     		movs	r0, #0
 669 0070 E2E7     		b	.L49
 670              	.L61:
 671 0072 00BF     		.align	2
 672              	.L60:
 673 0074 ******** 		.word	**********
 674 0078 0000EE17 		.word	*********
 675 007c 00000000 		.word	pFlash
 676              		.cfi_endproc
 677              	.LFE155:
 679              		.section	.text.HAL_FLASH_Program,"ax",%progbits
 680              		.align	1
 681              		.global	HAL_FLASH_Program
 682              		.syntax unified
 683              		.thumb
 684              		.thumb_func
 686              	HAL_FLASH_Program:
 687              	.LVL40:
 688              	.LFB144:
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 689              		.loc 1 155 1 is_stmt 1 view -0
 690              		.cfi_startproc
 691              		@ args = 0, pretend = 0, frame = 0
 692              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  /tmp/cczI1n00.s 			page 35


 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 693              		.loc 1 155 1 is_stmt 0 view .LVU188
 694 0000 38B5     		push	{r3, r4, r5, lr}
 695              	.LCFI2:
 696              		.cfi_def_cfa_offset 16
 697              		.cfi_offset 3, -16
 698              		.cfi_offset 4, -12
 699              		.cfi_offset 5, -8
 700              		.cfi_offset 14, -4
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *dest_addr = (__IO uint32_t *)FlashAddress;
 701              		.loc 1 156 3 is_stmt 1 view .LVU189
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *src_addr = (__IO uint32_t*)DataAddress;
 702              		.loc 1 157 3 view .LVU190
 703              	.LVL41:
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t bank;
 704              		.loc 1 158 3 view .LVU191
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint8_t row_index = FLASH_NB_32BITWORD_IN_FLASHWORD;
 705              		.loc 1 159 3 view .LVU192
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 706              		.loc 1 160 3 view .LVU193
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_PROGRAM_ADDRESS(FlashAddress));
 707              		.loc 1 163 3 view .LVU194
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 708              		.loc 1 164 3 view .LVU195
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 709              		.loc 1 167 3 view .LVU196
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 710              		.loc 1 167 3 view .LVU197
 711 0002 1F4B     		ldr	r3, .L70
 712 0004 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 713 0006 012B     		cmp	r3, #1
 714 0008 37D0     		beq	.L66
 715 000a 0D46     		mov	r5, r1
 716 000c 1446     		mov	r4, r2
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 717              		.loc 1 167 3 discriminator 2 view .LVU198
 718 000e 1C4B     		ldr	r3, .L70
 719 0010 0122     		movs	r2, #1
 720              	.LVL42:
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 721              		.loc 1 167 3 is_stmt 0 discriminator 2 view .LVU199
 722 0012 1A75     		strb	r2, [r3, #20]
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 723              		.loc 1 167 3 is_stmt 1 discriminator 2 view .LVU200
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 724              		.loc 1 172 3 view .LVU201
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 725              		.loc 1 172 6 is_stmt 0 view .LVU202
 726 0014 01F17841 		add	r1, r1, #-134217728
 727              	.LVL43:
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 728              		.loc 1 172 5 view .LVU203
 729 0018 B1F5801F 		cmp	r1, #1048576
 730 001c 01D3     		bcc	.L69
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 731              		.loc 1 187 12 view .LVU204
 732 001e 0120     		movs	r0, #1
ARM GAS  /tmp/cczI1n00.s 			page 36


 733              	.LVL44:
 734              	.L63:
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 735              		.loc 1 303 1 view .LVU205
 736 0020 38BD     		pop	{r3, r4, r5, pc}
 737              	.LVL45:
 738              	.L69:
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Prevent unused argument(s) compilation warning */
 739              		.loc 1 175 5 is_stmt 1 view .LVU206
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 740              		.loc 1 177 5 view .LVU207
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 741              		.loc 1 191 3 view .LVU208
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 742              		.loc 1 191 20 is_stmt 0 view .LVU209
 743 0022 0022     		movs	r2, #0
 744 0024 9A61     		str	r2, [r3, #24]
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 745              		.loc 1 194 3 is_stmt 1 view .LVU210
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 746              		.loc 1 194 12 is_stmt 0 view .LVU211
 747 0026 0121     		movs	r1, #1
 748 0028 4CF25030 		movw	r0, #50000
 749              	.LVL46:
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 750              		.loc 1 194 12 view .LVU212
 751 002c FFF7FEFF 		bl	FLASH_WaitForLastOperation
 752              	.LVL47:
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 753              		.loc 1 196 3 is_stmt 1 view .LVU213
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 754              		.loc 1 196 5 is_stmt 0 view .LVU214
 755 0030 F8B9     		cbnz	r0, .L64
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 756              		.loc 1 230 9 is_stmt 1 view .LVU215
 757 0032 144A     		ldr	r2, .L70+4
 758 0034 D368     		ldr	r3, [r2, #12]
 759 0036 43F00203 		orr	r3, r3, #2
 760 003a D360     		str	r3, [r2, #12]
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 761              		.loc 1 234 5 view .LVU216
 762              	.LBB18:
 763              	.LBI18:
 764              		.file 2 "Drivers/CMSIS/Include/cmsis_gcc.h"
   1:Drivers/CMSIS/Include/cmsis_gcc.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @file     cmsis_gcc.h
   3:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @brief    CMSIS compiler GCC header file
   4:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @version  V5.2.0
   5:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @date     08. May 2019
   6:Drivers/CMSIS/Include/cmsis_gcc.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/cmsis_gcc.h **** /*
   8:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  10:Drivers/CMSIS/Include/cmsis_gcc.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  12:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/cmsis_gcc.h ****  * not use this file except in compliance with the License.
ARM GAS  /tmp/cczI1n00.s 			page 37


  14:Drivers/CMSIS/Include/cmsis_gcc.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  16:Drivers/CMSIS/Include/cmsis_gcc.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  18:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/cmsis_gcc.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/cmsis_gcc.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/cmsis_gcc.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/cmsis_gcc.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
  24:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  25:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __CMSIS_GCC_H
  26:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_H
  27:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  28:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ignore some GCC warnings */
  29:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic push
  30:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wsign-conversion"
  31:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wconversion"
  32:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wunused-parameter"
  33:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  34:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Fallback for __has_builtin */
  35:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __has_builtin
  36:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __has_builtin(x) (0)
  37:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  38:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  39:Drivers/CMSIS/Include/cmsis_gcc.h **** /* CMSIS compiler specific defines */
  40:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ASM
  41:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ASM                                  __asm
  42:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  43:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __INLINE
  44:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __INLINE                               inline
  45:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  46:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_INLINE
  47:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_INLINE                        static inline
  48:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  49:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_FORCEINLINE                 
  50:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
  51:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif                                           
  52:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __NO_RETURN
  53:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __NO_RETURN                            __attribute__((__noreturn__))
  54:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  55:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __USED
  56:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __USED                                 __attribute__((used))
  57:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  58:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __WEAK
  59:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __WEAK                                 __attribute__((weak))
  60:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  61:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED
  62:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED                               __attribute__((packed, aligned(1)))
  63:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  64:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_STRUCT
  65:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_STRUCT                        struct __attribute__((packed, aligned(1)))
  66:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  67:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_UNION
  68:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_UNION                         union __attribute__((packed, aligned(1)))
  69:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  70:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32        /* deprecated */
ARM GAS  /tmp/cczI1n00.s 			page 38


  71:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  72:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  73:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  74:Drivers/CMSIS/Include/cmsis_gcc.h ****   struct __attribute__((packed)) T_UINT32 { uint32_t v; };
  75:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  76:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32 *)(x))->v)
  77:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  78:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_WRITE
  79:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  80:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  81:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  82:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_WRITE { uint16_t v; };
  83:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  84:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_WRITE(addr, val)    (void)((((struct T_UINT16_WRITE *)(void *)(addr))-
  85:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  86:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_READ
  87:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  88:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  89:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  90:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_READ { uint16_t v; };
  91:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  92:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_READ(addr)          (((const struct T_UINT16_READ *)(const void *)(add
  93:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  94:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_WRITE
  95:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  96:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  97:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  98:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_WRITE { uint32_t v; };
  99:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 100:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_WRITE(addr, val)    (void)((((struct T_UINT32_WRITE *)(void *)(addr))-
 101:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 102:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_READ
 103:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
 104:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
 105:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
 106:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_READ { uint32_t v; };
 107:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 108:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_READ(addr)          (((const struct T_UINT32_READ *)(const void *)(add
 109:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 110:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ALIGNED
 111:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ALIGNED(x)                           __attribute__((aligned(x)))
 112:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 113:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __RESTRICT
 114:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __RESTRICT                             __restrict
 115:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 116:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __COMPILER_BARRIER
 117:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __COMPILER_BARRIER()                   __ASM volatile("":::"memory")
 118:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 119:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 120:Drivers/CMSIS/Include/cmsis_gcc.h **** /* #########################  Startup and Lowlevel Init  ######################## */
 121:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 122:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __PROGRAM_START
 123:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 124:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 125:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Initializes data and bss sections
 126:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details This default implementations initialized all data and additional bss
 127:Drivers/CMSIS/Include/cmsis_gcc.h ****            sections relying on .copy.table and .zero.table specified properly
ARM GAS  /tmp/cczI1n00.s 			page 39


 128:Drivers/CMSIS/Include/cmsis_gcc.h ****            in the used linker script.
 129:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 130:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 131:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE __NO_RETURN void __cmsis_start(void)
 132:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 133:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern void _start(void) __NO_RETURN;
 134:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 135:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 136:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t const* src;
 137:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 138:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 139:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __copy_table_t;
 140:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 141:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 142:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 143:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 144:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __zero_table_t;
 145:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 146:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_start__;
 147:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_end__;
 148:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_start__;
 149:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_end__;
 150:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 151:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__copy_table_t const* pTable = &__copy_table_start__; pTable < &__copy_table_end__; ++pTable
 152:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 153:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = pTable->src[i];
 154:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 155:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 156:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 157:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__zero_table_t const* pTable = &__zero_table_start__; pTable < &__zero_table_end__; ++pTable
 158:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 159:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = 0u;
 160:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 161:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 162:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 163:Drivers/CMSIS/Include/cmsis_gcc.h ****   _start();
 164:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 165:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 166:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __PROGRAM_START           __cmsis_start
 167:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 168:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 169:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __INITIAL_SP
 170:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __INITIAL_SP              __StackTop
 171:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 172:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 173:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __STACK_LIMIT
 174:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __STACK_LIMIT             __StackLimit
 175:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 176:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 177:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE
 178:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE            __Vectors
 179:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 180:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 181:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE_ATTRIBUTE
 182:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE_ATTRIBUTE  __attribute((used, section(".vectors")))
 183:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 184:Drivers/CMSIS/Include/cmsis_gcc.h **** 
ARM GAS  /tmp/cczI1n00.s 			page 40


 185:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ###########################  Core Function Access  ########################### */
 186:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \ingroup  CMSIS_Core_FunctionInterface
 187:Drivers/CMSIS/Include/cmsis_gcc.h ****     \defgroup CMSIS_Core_RegAccFunctions CMSIS Core Register Access Functions
 188:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 189:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 190:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 191:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 192:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable IRQ Interrupts
 193:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables IRQ interrupts by clearing the I-bit in the CPSR.
 194:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 195:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 196:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_irq(void)
 197:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 198:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie i" : : : "memory");
 199:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 200:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 201:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 202:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 203:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable IRQ Interrupts
 204:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables IRQ interrupts by setting the I-bit in the CPSR.
 205:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 206:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 207:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_irq(void)
 208:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 209:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid i" : : : "memory");
 210:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 211:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 212:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 213:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 214:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register
 215:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the Control Register.
 216:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Control Register value
 217:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 218:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_CONTROL(void)
 219:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 220:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 221:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 222:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control" : "=r" (result) );
 223:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 224:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 225:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 226:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 227:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 228:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 229:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Control Register (non-secure)
 230:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the non-secure Control Register when in secure mode.
 231:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               non-secure Control Register value
 232:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 233:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_CONTROL_NS(void)
 234:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 235:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 236:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 237:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, control_ns" : "=r" (result) );
 238:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 239:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 240:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 241:Drivers/CMSIS/Include/cmsis_gcc.h **** 
ARM GAS  /tmp/cczI1n00.s 			page 41


 242:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 243:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 244:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register
 245:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the Control Register.
 246:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 247:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 248:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_CONTROL(uint32_t control)
 249:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 250:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control, %0" : : "r" (control) : "memory");
 251:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 252:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 253:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 254:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 255:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 256:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Control Register (non-secure)
 257:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Writes the given value to the non-secure Control Register when in secure state.
 258:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    control  Control Register value to set
 259:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 260:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_CONTROL_NS(uint32_t control)
 261:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 262:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR control_ns, %0" : : "r" (control) : "memory");
 263:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 264:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 265:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 266:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 267:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 268:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get IPSR Register
 269:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the IPSR Register.
 270:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               IPSR Register value
 271:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 272:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_IPSR(void)
 273:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 274:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 275:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 276:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, ipsr" : "=r" (result) );
 277:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 278:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 279:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 280:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 281:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 282:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get APSR Register
 283:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the APSR Register.
 284:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               APSR Register value
 285:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 286:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_APSR(void)
 287:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 288:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 289:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 290:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, apsr" : "=r" (result) );
 291:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 292:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 293:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 294:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 295:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 296:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get xPSR Register
 297:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the content of the xPSR Register.
 298:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               xPSR Register value
ARM GAS  /tmp/cczI1n00.s 			page 42


 299:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 300:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_xPSR(void)
 301:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 302:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 303:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 304:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, xpsr" : "=r" (result) );
 305:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 306:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 307:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 308:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 309:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 310:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer
 311:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer (PSP).
 312:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 313:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 314:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSP(void)
 315:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 316:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 317:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 318:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp"  : "=r" (result) );
 319:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 320:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 321:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 322:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 323:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 324:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 325:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer (non-secure)
 326:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer (PSP) when in secure s
 327:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSP Register value
 328:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 329:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSP_NS(void)
 330:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 331:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 332:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 333:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psp_ns"  : "=r" (result) );
 334:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 335:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 336:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 337:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 338:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 339:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 340:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer
 341:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer (PSP).
 342:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 343:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 344:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSP(uint32_t topOfProcStack)
 345:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 346:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp, %0" : : "r" (topOfProcStack) : );
 347:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 348:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 349:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 350:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 351:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 352:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 353:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer (PSP) when in secure sta
 354:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfProcStack  Process Stack Pointer value to set
 355:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
ARM GAS  /tmp/cczI1n00.s 			page 43


 356:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSP_NS(uint32_t topOfProcStack)
 357:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 358:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psp_ns, %0" : : "r" (topOfProcStack) : );
 359:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 360:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 361:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 362:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 363:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 364:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer
 365:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer (MSP).
 366:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 367:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 368:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSP(void)
 369:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 370:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 371:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 372:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp" : "=r" (result) );
 373:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 374:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 375:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 376:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 377:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 378:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 379:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer (non-secure)
 380:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer (MSP) when in secure stat
 381:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSP Register value
 382:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 383:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSP_NS(void)
 384:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 385:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 386:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 387:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msp_ns" : "=r" (result) );
 388:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 389:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 390:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 391:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 392:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 393:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 394:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer
 395:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer (MSP).
 396:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 397:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 398:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSP(uint32_t topOfMainStack)
 399:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 400:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp, %0" : : "r" (topOfMainStack) : );
 401:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 402:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 403:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 404:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 405:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 406:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer (non-secure)
 407:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer (MSP) when in secure state.
 408:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfMainStack  Main Stack Pointer value to set
 409:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 410:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSP_NS(uint32_t topOfMainStack)
 411:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 412:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msp_ns, %0" : : "r" (topOfMainStack) : );
ARM GAS  /tmp/cczI1n00.s 			page 44


 413:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 414:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 415:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 416:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 417:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 418:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 419:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Stack Pointer (non-secure)
 420:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Stack Pointer (SP) when in secure state.
 421:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               SP Register value
 422:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 423:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_SP_NS(void)
 424:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 425:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 426:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 427:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, sp_ns" : "=r" (result) );
 428:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 429:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 430:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 431:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 432:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 433:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Stack Pointer (non-secure)
 434:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Stack Pointer (SP) when in secure state.
 435:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    topOfStack  Stack Pointer value to set
 436:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 437:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_SP_NS(uint32_t topOfStack)
 438:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 439:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR sp_ns, %0" : : "r" (topOfStack) : );
 440:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 441:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 442:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 443:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 444:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 445:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask
 446:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the priority mask bit from the Priority Mask Register.
 447:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 448:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 449:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PRIMASK(void)
 450:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 451:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 452:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 453:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask" : "=r" (result) :: "memory");
 454:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 455:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 456:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 457:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 458:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 459:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 460:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Priority Mask (non-secure)
 461:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current state of the non-secure priority mask bit from the Priority Mask Reg
 462:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Priority Mask value
 463:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 464:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PRIMASK_NS(void)
 465:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 466:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 467:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 468:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, primask_ns" : "=r" (result) :: "memory");
 469:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
ARM GAS  /tmp/cczI1n00.s 			page 45


 470:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 471:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 472:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 473:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 474:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 475:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask
 476:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Priority Mask Register.
 477:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 478:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 479:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PRIMASK(uint32_t priMask)
 480:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 481:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask, %0" : : "r" (priMask) : "memory");
 482:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 483:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 484:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 485:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 486:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 487:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Priority Mask (non-secure)
 488:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Priority Mask Register when in secure state.
 489:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    priMask  Priority Mask
 490:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 491:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PRIMASK_NS(uint32_t priMask)
 492:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 493:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR primask_ns, %0" : : "r" (priMask) : "memory");
 494:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 495:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 496:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 497:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 498:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 499:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 500:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    )
 501:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 502:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable FIQ
 503:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables FIQ interrupts by clearing the F-bit in the CPSR.
 504:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 505:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 506:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_fault_irq(void)
 507:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 508:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie f" : : : "memory");
 509:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 510:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 511:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 512:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 513:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable FIQ
 514:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables FIQ interrupts by setting the F-bit in the CPSR.
 515:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 516:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 517:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_fault_irq(void)
 518:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 519:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid f" : : : "memory");
 520:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 521:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 522:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 523:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 524:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority
 525:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Base Priority register.
 526:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
ARM GAS  /tmp/cczI1n00.s 			page 46


 527:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 528:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_BASEPRI(void)
 529:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 530:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 531:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 532:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri" : "=r" (result) );
 533:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 534:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 535:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 536:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 537:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 538:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 539:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Base Priority (non-secure)
 540:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Base Priority register when in secure state.
 541:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Base Priority register value
 542:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 543:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_BASEPRI_NS(void)
 544:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 545:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 546:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 547:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, basepri_ns" : "=r" (result) );
 548:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 549:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 550:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 551:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 552:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 553:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 554:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority
 555:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register.
 556:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 557:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 558:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI(uint32_t basePri)
 559:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 560:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri, %0" : : "r" (basePri) : "memory");
 561:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 562:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 563:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 564:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 565:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 566:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority (non-secure)
 567:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Base Priority register when in secure state.
 568:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 569:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 570:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_BASEPRI_NS(uint32_t basePri)
 571:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 572:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_ns, %0" : : "r" (basePri) : "memory");
 573:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 574:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 575:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 576:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 577:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 578:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Base Priority with condition
 579:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Base Priority register only if BASEPRI masking is disable
 580:Drivers/CMSIS/Include/cmsis_gcc.h ****            or the new value increases the BASEPRI priority level.
 581:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    basePri  Base Priority value to set
 582:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 583:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_BASEPRI_MAX(uint32_t basePri)
ARM GAS  /tmp/cczI1n00.s 			page 47


 584:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 585:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR basepri_max, %0" : : "r" (basePri) : "memory");
 586:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 587:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 588:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 589:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 590:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask
 591:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Fault Mask register.
 592:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 593:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 594:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FAULTMASK(void)
 595:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 596:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 597:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 598:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask" : "=r" (result) );
 599:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 600:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 601:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 602:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 603:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 604:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 605:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Fault Mask (non-secure)
 606:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Fault Mask register when in secure state.
 607:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Fault Mask register value
 608:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 609:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_FAULTMASK_NS(void)
 610:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 611:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 612:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 613:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, faultmask_ns" : "=r" (result) );
 614:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 615:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 616:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 617:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 618:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 619:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 620:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask
 621:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Fault Mask register.
 622:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 623:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 624:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FAULTMASK(uint32_t faultMask)
 625:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 626:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask, %0" : : "r" (faultMask) : "memory");
 627:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 628:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 629:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 630:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE ) && (__ARM_FEATURE_CMSE == 3))
 631:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 632:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Fault Mask (non-secure)
 633:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Fault Mask register when in secure state.
 634:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    faultMask  Fault Mask value to set
 635:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 636:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_FAULTMASK_NS(uint32_t faultMask)
 637:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 638:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR faultmask_ns, %0" : : "r" (faultMask) : "memory");
 639:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 640:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
ARM GAS  /tmp/cczI1n00.s 			page 48


 641:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 642:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
 643:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
 644:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    ) */
 645:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 646:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 647:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 648:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    )
 649:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 650:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 651:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit
 652:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 653:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 654:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 655:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 656:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Process Stack Pointer Limit (PSPLIM).
 657:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 658:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 659:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_PSPLIM(void)
 660:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 661:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 662:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 663:Drivers/CMSIS/Include/cmsis_gcc.h ****     // without main extensions, the non-secure PSPLIM is RAZ/WI
 664:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 665:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 666:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 667:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim"  : "=r" (result) );
 668:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 669:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 670:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 671:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 672:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE) && (__ARM_FEATURE_CMSE == 3))
 673:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 674:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Process Stack Pointer Limit (non-secure)
 675:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 676:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 677:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 678:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Process Stack Pointer Limit (PSPLIM) when in
 679:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               PSPLIM Register value
 680:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 681:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_PSPLIM_NS(void)
 682:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 683:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 684:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 685:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 686:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 687:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 688:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, psplim_ns"  : "=r" (result) );
 689:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 690:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 691:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 692:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 693:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 694:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 695:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 696:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer Limit
 697:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
ARM GAS  /tmp/cczI1n00.s 			page 49


 698:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 699:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 700:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 701:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Process Stack Pointer Limit (PSPLIM).
 702:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 703:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 704:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_PSPLIM(uint32_t ProcStackPtrLimit)
 705:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 706:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 707:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 708:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 709:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 710:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 711:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim, %0" : : "r" (ProcStackPtrLimit));
 712:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 713:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 714:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 715:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 716:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 717:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 718:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Process Stack Pointer (non-secure)
 719:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 720:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 721:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 722:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Process Stack Pointer Limit (PSPLIM) when in s
 723:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    ProcStackPtrLimit  Process Stack Pointer Limit value to set
 724:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 725:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_PSPLIM_NS(uint32_t ProcStackPtrLimit)
 726:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 727:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 728:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure PSPLIM is RAZ/WI
 729:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)ProcStackPtrLimit;
 730:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 731:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR psplim_ns, %0\n" : : "r" (ProcStackPtrLimit));
 732:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 733:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 734:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 735:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 736:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 737:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 738:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit
 739:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 740:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always in non-secure
 741:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 742:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 743:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Main Stack Pointer Limit (MSPLIM).
 744:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 745:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 746:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_MSPLIM(void)
 747:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 748:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 749:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 750:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 751:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 752:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 753:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 754:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim" : "=r" (result) );
ARM GAS  /tmp/cczI1n00.s 			page 50


 755:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 756:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 757:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 758:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 759:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 760:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 761:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 762:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get Main Stack Pointer Limit (non-secure)
 763:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 764:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence zero is returned always.
 765:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 766:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the non-secure Main Stack Pointer Limit(MSPLIM) when in sec
 767:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               MSPLIM Register value
 768:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 769:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __TZ_get_MSPLIM_NS(void)
 770:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 771:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 772:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 773:Drivers/CMSIS/Include/cmsis_gcc.h ****   return 0U;
 774:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 775:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 776:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MRS %0, msplim_ns" : "=r" (result) );
 777:Drivers/CMSIS/Include/cmsis_gcc.h ****   return result;
 778:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 779:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 780:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 781:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 782:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 783:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 784:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit
 785:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 786:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored in non-secure
 787:Drivers/CMSIS/Include/cmsis_gcc.h ****   mode.
 788:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 789:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Main Stack Pointer Limit (MSPLIM).
 790:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer Limit value to set
 791:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 792:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_MSPLIM(uint32_t MainStackPtrLimit)
 793:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 794:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) && \
 795:Drivers/CMSIS/Include/cmsis_gcc.h ****     (!defined (__ARM_FEATURE_CMSE) || (__ARM_FEATURE_CMSE < 3)))
 796:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 797:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 798:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 799:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim, %0" : : "r" (MainStackPtrLimit));
 800:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 801:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 802:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 803:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 804:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (defined (__ARM_FEATURE_CMSE  ) && (__ARM_FEATURE_CMSE   == 3))
 805:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 806:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set Main Stack Pointer Limit (non-secure)
 807:Drivers/CMSIS/Include/cmsis_gcc.h ****   Devices without ARMv8-M Main Extensions (i.e. Cortex-M23) lack the non-secure
 808:Drivers/CMSIS/Include/cmsis_gcc.h ****   Stack Pointer Limit register hence the write is silently ignored.
 809:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 810:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the non-secure Main Stack Pointer Limit (MSPLIM) when in secu
 811:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    MainStackPtrLimit  Main Stack Pointer value to set
ARM GAS  /tmp/cczI1n00.s 			page 51


 812:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 813:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __TZ_set_MSPLIM_NS(uint32_t MainStackPtrLimit)
 814:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 815:Drivers/CMSIS/Include/cmsis_gcc.h **** #if (!(defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)))
 816:Drivers/CMSIS/Include/cmsis_gcc.h ****   // without main extensions, the non-secure MSPLIM is RAZ/WI
 817:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)MainStackPtrLimit;
 818:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 819:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("MSR msplim_ns, %0" : : "r" (MainStackPtrLimit));
 820:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 821:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 822:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 823:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 824:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif /* ((defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1)) || \
 825:Drivers/CMSIS/Include/cmsis_gcc.h ****            (defined (__ARM_ARCH_8M_BASE__ ) && (__ARM_ARCH_8M_BASE__ == 1))    ) */
 826:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 827:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 828:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 829:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Get FPSCR
 830:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Returns the current value of the Floating Point Status/Control register.
 831:Drivers/CMSIS/Include/cmsis_gcc.h ****   \return               Floating Point Status/Control register value
 832:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 833:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE uint32_t __get_FPSCR(void)
 834:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 835:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 836:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 837:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_get_fpscr) 
 838:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 839:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 840:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 841:Drivers/CMSIS/Include/cmsis_gcc.h ****   return __builtin_arm_get_fpscr();
 842:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 843:Drivers/CMSIS/Include/cmsis_gcc.h ****   uint32_t result;
 844:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 845:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMRS %0, fpscr" : "=r" (result) );
 846:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(result);
 847:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 848:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 849:Drivers/CMSIS/Include/cmsis_gcc.h ****   return(0U);
 850:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 851:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 852:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 853:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 854:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 855:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Set FPSCR
 856:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Assigns the given value to the Floating Point Status/Control register.
 857:Drivers/CMSIS/Include/cmsis_gcc.h ****   \param [in]    fpscr  Floating Point Status/Control value to set
 858:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 859:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __set_FPSCR(uint32_t fpscr)
 860:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 861:Drivers/CMSIS/Include/cmsis_gcc.h **** #if ((defined (__FPU_PRESENT) && (__FPU_PRESENT == 1U)) && \
 862:Drivers/CMSIS/Include/cmsis_gcc.h ****      (defined (__FPU_USED   ) && (__FPU_USED    == 1U))     )
 863:Drivers/CMSIS/Include/cmsis_gcc.h **** #if __has_builtin(__builtin_arm_set_fpscr)
 864:Drivers/CMSIS/Include/cmsis_gcc.h **** // Re-enable using built-in when GCC has been fixed
 865:Drivers/CMSIS/Include/cmsis_gcc.h **** // || (__GNUC__ > 7) || (__GNUC__ == 7 && __GNUC_MINOR__ >= 2)
 866:Drivers/CMSIS/Include/cmsis_gcc.h ****   /* see https://gcc.gnu.org/ml/gcc-patches/2017-04/msg00443.html */
 867:Drivers/CMSIS/Include/cmsis_gcc.h ****   __builtin_arm_set_fpscr(fpscr);
 868:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
ARM GAS  /tmp/cczI1n00.s 			page 52


 869:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("VMSR fpscr, %0" : : "r" (fpscr) : "vfpcc", "memory");
 870:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 871:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 872:Drivers/CMSIS/Include/cmsis_gcc.h ****   (void)fpscr;
 873:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 874:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 875:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 876:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 877:Drivers/CMSIS/Include/cmsis_gcc.h **** /*@} end of CMSIS_Core_RegAccFunctions */
 878:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 879:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 880:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ##########################  Core Instruction Access  ######################### */
 881:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \defgroup CMSIS_Core_InstructionInterface CMSIS Core Instruction Interface
 882:Drivers/CMSIS/Include/cmsis_gcc.h ****   Access to dedicated instructions
 883:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 884:Drivers/CMSIS/Include/cmsis_gcc.h **** */
 885:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 886:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Define macros for porting to both thumb1 and thumb2.
 887:Drivers/CMSIS/Include/cmsis_gcc.h ****  * For thumb1, use low register (r0-r7), specified by constraint "l"
 888:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Otherwise, use general registers, specified by constraint "r" */
 889:Drivers/CMSIS/Include/cmsis_gcc.h **** #if defined (__thumb__) && !defined (__thumb2__)
 890:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=l" (r)
 891:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+l" (r)
 892:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "l" (r)
 893:Drivers/CMSIS/Include/cmsis_gcc.h **** #else
 894:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_OUT_REG(r) "=r" (r)
 895:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_RW_REG(r) "+r" (r)
 896:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_USE_REG(r) "r" (r)
 897:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 898:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 899:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 900:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   No Operation
 901:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details No Operation does nothing. This instruction can be used for code alignment purposes.
 902:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 903:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __NOP()                             __ASM volatile ("nop")
 904:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 905:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 906:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Interrupt
 907:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Interrupt is a hint instruction that suspends execution until one of a number o
 908:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 909:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFI()                             __ASM volatile ("wfi")
 910:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 911:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 912:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 913:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Wait For Event
 914:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Wait For Event is a hint instruction that permits the processor to enter
 915:Drivers/CMSIS/Include/cmsis_gcc.h ****            a low-power state until one of a number of events occurs.
 916:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 917:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __WFE()                             __ASM volatile ("wfe")
 918:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 919:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 920:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 921:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Send Event
 922:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Send Event is a hint instruction. It causes an event to be signaled to the CPU.
 923:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 924:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __SEV()                             __ASM volatile ("sev")
 925:Drivers/CMSIS/Include/cmsis_gcc.h **** 
ARM GAS  /tmp/cczI1n00.s 			page 53


 926:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 927:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 928:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Instruction Synchronization Barrier
 929:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Instruction Synchronization Barrier flushes the pipeline in the processor,
 930:Drivers/CMSIS/Include/cmsis_gcc.h ****            so that all instructions following the ISB are fetched from cache or memory,
 931:Drivers/CMSIS/Include/cmsis_gcc.h ****            after the instruction has been completed.
 932:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __ISB(void)
 765              		.loc 2 933 27 view .LVU217
 766              	.LBB19:
 934:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 935:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("isb 0xF":::"memory");
 767              		.loc 2 935 3 view .LVU218
 768              		.syntax unified
 769              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 770 003c BFF36F8F 		isb 0xF
 771              	@ 0 "" 2
 772              		.thumb
 773              		.syntax unified
 774              	.LBE19:
 775              	.LBE18:
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 776              		.loc 1 235 5 view .LVU219
 777              	.LBB20:
 778              	.LBI20:
 936:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 937:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 938:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 939:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 940:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Data Synchronization Barrier
 941:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Acts as a special kind of Data Memory Barrier.
 942:Drivers/CMSIS/Include/cmsis_gcc.h ****            It completes when all explicit memory accesses before this instruction complete.
 943:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __DSB(void)
 779              		.loc 2 944 27 view .LVU220
 780              	.LBB21:
 945:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 946:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("dsb 0xF":::"memory");
 781              		.loc 2 946 3 view .LVU221
 782              		.syntax unified
 783              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 784 0040 BFF34F8F 		dsb 0xF
 785              	@ 0 "" 2
 786              		.thumb
 787              		.syntax unified
 788              	.LBE21:
 789              	.LBE20:
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 790              		.loc 1 160 11 is_stmt 0 view .LVU222
 791 0044 0823     		movs	r3, #8
 792              	.LVL48:
 793              	.L65:
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
 794              		.loc 1 247 7 is_stmt 1 view .LVU223
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 795              		.loc 1 249 9 view .LVU224
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
ARM GAS  /tmp/cczI1n00.s 			page 54


 796              		.loc 1 249 22 is_stmt 0 view .LVU225
 797 0046 54F8042B 		ldr	r2, [r4], #4
 798              	.LVL49:
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 799              		.loc 1 249 20 view .LVU226
 800 004a 45F8042B 		str	r2, [r5], #4
 801              	.LVL50:
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         src_addr++;
 802              		.loc 1 250 9 is_stmt 1 view .LVU227
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         row_index--;
 803              		.loc 1 251 9 view .LVU228
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****      } while (row_index != 0U);
 804              		.loc 1 252 9 view .LVU229
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****      } while (row_index != 0U);
 805              		.loc 1 252 18 is_stmt 0 view .LVU230
 806 004e 013B     		subs	r3, r3, #1
 807              	.LVL51:
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 808              		.loc 1 253 25 is_stmt 1 discriminator 1 view .LVU231
 809 0050 13F0FF03 		ands	r3, r3, #255
 810              	.LVL52:
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 811              		.loc 1 253 25 is_stmt 0 discriminator 1 view .LVU232
 812 0054 F7D1     		bne	.L65
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 813              		.loc 1 256 5 is_stmt 1 view .LVU233
 814              	.LBB22:
 815              	.LBI22:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 816              		.loc 2 933 27 view .LVU234
 817              	.LBB23:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 818              		.loc 2 935 3 view .LVU235
 819              		.syntax unified
 820              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 821 0056 BFF36F8F 		isb 0xF
 822              	@ 0 "" 2
 823              		.thumb
 824              		.syntax unified
 825              	.LBE23:
 826              	.LBE22:
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 827              		.loc 1 257 5 view .LVU236
 828              	.LBB24:
 829              	.LBI24:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 830              		.loc 2 944 27 view .LVU237
 831              	.LBB25:
 832              		.loc 2 946 3 view .LVU238
 833              		.syntax unified
 834              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 835 005a BFF34F8F 		dsb 0xF
 836              	@ 0 "" 2
 837              		.thumb
 838              		.syntax unified
 839              	.LBE25:
 840              	.LBE24:
ARM GAS  /tmp/cczI1n00.s 			page 55


 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 841              		.loc 1 260 5 view .LVU239
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 842              		.loc 1 260 14 is_stmt 0 view .LVU240
 843 005e 0121     		movs	r1, #1
 844 0060 4CF25030 		movw	r0, #50000
 845              	.LVL53:
 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 846              		.loc 1 260 14 view .LVU241
 847 0064 FFF7FEFF 		bl	FLASH_WaitForLastOperation
 848              	.LVL54:
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 849              		.loc 1 294 7 is_stmt 1 view .LVU242
 850 0068 064A     		ldr	r2, .L70+4
 851 006a D368     		ldr	r3, [r2, #12]
 852 006c 23F00203 		bic	r3, r3, #2
 853 0070 D360     		str	r3, [r2, #12]
 854              	.LVL55:
 855              	.L64:
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 856              		.loc 1 300 3 view .LVU243
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 857              		.loc 1 300 3 view .LVU244
 858 0072 034B     		ldr	r3, .L70
 859 0074 0022     		movs	r2, #0
 860 0076 1A75     		strb	r2, [r3, #20]
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 861              		.loc 1 300 3 view .LVU245
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 862              		.loc 1 302 3 view .LVU246
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 863              		.loc 1 302 10 is_stmt 0 view .LVU247
 864 0078 D2E7     		b	.L63
 865              	.LVL56:
 866              	.L66:
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 867              		.loc 1 167 3 discriminator 1 view .LVU248
 868 007a 0220     		movs	r0, #2
 869              	.LVL57:
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 870              		.loc 1 167 3 discriminator 1 view .LVU249
 871 007c D0E7     		b	.L63
 872              	.L71:
 873 007e 00BF     		.align	2
 874              	.L70:
 875 0080 00000000 		.word	pFlash
 876 0084 ******** 		.word	**********
 877              		.cfi_endproc
 878              	.LFE144:
 880              		.section	.text.HAL_FLASH_Program_IT,"ax",%progbits
 881              		.align	1
 882              		.global	HAL_FLASH_Program_IT
 883              		.syntax unified
 884              		.thumb
 885              		.thumb_func
 887              	HAL_FLASH_Program_IT:
 888              	.LVL58:
ARM GAS  /tmp/cczI1n00.s 			page 56


 889              	.LFB145:
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 890              		.loc 1 320 1 is_stmt 1 view -0
 891              		.cfi_startproc
 892              		@ args = 0, pretend = 0, frame = 0
 893              		@ frame_needed = 0, uses_anonymous_args = 0
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *dest_addr = (__IO uint32_t*)FlashAddress;
 894              		.loc 1 321 3 view .LVU251
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   __IO uint32_t *src_addr = (__IO uint32_t*)DataAddress;
 895              		.loc 1 322 3 view .LVU252
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t bank;
 896              		.loc 1 323 3 view .LVU253
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint8_t row_index = FLASH_NB_32BITWORD_IN_FLASHWORD;
 897              		.loc 1 324 3 view .LVU254
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 898              		.loc 1 325 3 view .LVU255
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_PROGRAM_ADDRESS(FlashAddress));
 899              		.loc 1 328 3 view .LVU256
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 900              		.loc 1 329 3 view .LVU257
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 901              		.loc 1 332 3 view .LVU258
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 902              		.loc 1 332 3 view .LVU259
 903 0000 1F4B     		ldr	r3, .L83
 904 0002 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 905 0004 012B     		cmp	r3, #1
 906 0006 38D0     		beq	.L76
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 907              		.loc 1 320 1 is_stmt 0 view .LVU260
 908 0008 70B5     		push	{r4, r5, r6, lr}
 909              	.LCFI3:
 910              		.cfi_def_cfa_offset 16
 911              		.cfi_offset 4, -16
 912              		.cfi_offset 5, -12
 913              		.cfi_offset 6, -8
 914              		.cfi_offset 14, -4
 915 000a 0D46     		mov	r5, r1
 916 000c 0E46     		mov	r6, r1
 917 000e 1446     		mov	r4, r2
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 918              		.loc 1 332 3 is_stmt 1 discriminator 2 view .LVU261
 919 0010 1B4B     		ldr	r3, .L83
 920 0012 0122     		movs	r2, #1
 921              	.LVL59:
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 922              		.loc 1 332 3 is_stmt 0 discriminator 2 view .LVU262
 923 0014 1A75     		strb	r2, [r3, #20]
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 924              		.loc 1 332 3 is_stmt 1 discriminator 2 view .LVU263
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 925              		.loc 1 335 3 view .LVU264
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 926              		.loc 1 335 20 is_stmt 0 view .LVU265
 927 0016 0022     		movs	r2, #0
 928 0018 9A61     		str	r2, [r3, #24]
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
ARM GAS  /tmp/cczI1n00.s 			page 57


 929              		.loc 1 340 3 is_stmt 1 view .LVU266
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 930              		.loc 1 340 6 is_stmt 0 view .LVU267
 931 001a 01F17843 		add	r3, r1, #-134217728
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* FLASH_OPTCR_PG_OTP */
 932              		.loc 1 340 5 view .LVU268
 933 001e B3F5801F 		cmp	r3, #1048576
 934 0022 01D3     		bcc	.L82
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 935              		.loc 1 355 12 view .LVU269
 936 0024 0120     		movs	r0, #1
 937              	.LVL60:
 938              	.L73:
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 939              		.loc 1 469 1 view .LVU270
 940 0026 70BD     		pop	{r4, r5, r6, pc}
 941              	.LVL61:
 942              	.L82:
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Prevent unused argument(s) compilation warning */
 943              		.loc 1 343 5 is_stmt 1 view .LVU271
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 944              		.loc 1 345 5 view .LVU272
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 945              		.loc 1 359 3 view .LVU273
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 946              		.loc 1 359 12 is_stmt 0 view .LVU274
 947 0028 0121     		movs	r1, #1
 948              	.LVL62:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 949              		.loc 1 359 12 view .LVU275
 950 002a 4CF25030 		movw	r0, #50000
 951              	.LVL63:
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 952              		.loc 1 359 12 view .LVU276
 953 002e FFF7FEFF 		bl	FLASH_WaitForLastOperation
 954              	.LVL64:
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 955              		.loc 1 361 3 is_stmt 1 view .LVU277
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 956              		.loc 1 361 6 is_stmt 0 view .LVU278
 957 0032 18B1     		cbz	r0, .L74
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 958              		.loc 1 364 5 is_stmt 1 view .LVU279
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 959              		.loc 1 364 5 view .LVU280
 960 0034 124B     		ldr	r3, .L83
 961 0036 0022     		movs	r2, #0
 962 0038 1A75     		strb	r2, [r3, #20]
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 963              		.loc 1 364 5 view .LVU281
 964 003a F4E7     		b	.L73
 965              	.L74:
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 966              		.loc 1 368 5 view .LVU282
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 967              		.loc 1 368 20 is_stmt 0 view .LVU283
 968 003c 104B     		ldr	r3, .L83
ARM GAS  /tmp/cczI1n00.s 			page 58


 969 003e 1D61     		str	r5, [r3, #16]
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 970              		.loc 1 417 5 is_stmt 1 view .LVU284
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 971              		.loc 1 417 29 is_stmt 0 view .LVU285
 972 0040 0322     		movs	r2, #3
 973 0042 1A70     		strb	r2, [r3]
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 974              		.loc 1 429 7 is_stmt 1 view .LVU286
 975 0044 0F4B     		ldr	r3, .L83+4
 976 0046 DA68     		ldr	r2, [r3, #12]
 977 0048 42F00202 		orr	r2, r2, #2
 978 004c DA60     		str	r2, [r3, #12]
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****                                   FLASH_IT_STRBERR_BANK1 | FLASH_IT_INCERR_BANK1 | FLASH_IT_OPERR_B
 979              		.loc 1 434 7 view .LVU287
 980 004e DA68     		ldr	r2, [r3, #12]
 981 0050 42F4DE02 		orr	r2, r2, #7274496
 982 0054 DA60     		str	r2, [r3, #12]
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 983              		.loc 1 442 5 view .LVU288
 984              	.LBB26:
 985              	.LBI26:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 986              		.loc 2 933 27 view .LVU289
 987              	.LBB27:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 988              		.loc 2 935 3 view .LVU290
 989              		.syntax unified
 990              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 991 0056 BFF36F8F 		isb 0xF
 992              	@ 0 "" 2
 993              		.thumb
 994              		.syntax unified
 995              	.LBE27:
 996              	.LBE26:
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 997              		.loc 1 443 5 view .LVU291
 998              	.LBB28:
 999              	.LBI28:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 1000              		.loc 2 944 27 view .LVU292
 1001              	.LBB29:
 1002              		.loc 2 946 3 view .LVU293
 1003              		.syntax unified
 1004              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 1005 005a BFF34F8F 		dsb 0xF
 1006              	@ 0 "" 2
 1007              		.thumb
 1008              		.syntax unified
 1009              	.LBE29:
 1010              	.LBE28:
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1011              		.loc 1 325 11 is_stmt 0 view .LVU294
 1012 005e 0823     		movs	r3, #8
 1013              	.LVL65:
 1014              	.L75:
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
ARM GAS  /tmp/cczI1n00.s 			page 59


 1015              		.loc 1 455 7 is_stmt 1 view .LVU295
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 1016              		.loc 1 457 9 view .LVU296
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 1017              		.loc 1 457 22 is_stmt 0 view .LVU297
 1018 0060 54F8042B 		ldr	r2, [r4], #4
 1019              	.LVL66:
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         dest_addr++;
 1020              		.loc 1 457 20 view .LVU298
 1021 0064 46F8042B 		str	r2, [r6], #4
 1022              	.LVL67:
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         src_addr++;
 1023              		.loc 1 458 9 is_stmt 1 view .LVU299
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         row_index--;
 1024              		.loc 1 459 9 view .LVU300
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       } while (row_index != 0U);
 1025              		.loc 1 460 9 view .LVU301
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       } while (row_index != 0U);
 1026              		.loc 1 460 18 is_stmt 0 view .LVU302
 1027 0068 013B     		subs	r3, r3, #1
 1028              	.LVL68:
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 1029              		.loc 1 461 26 is_stmt 1 discriminator 1 view .LVU303
 1030 006a 13F0FF03 		ands	r3, r3, #255
 1031              	.LVL69:
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 1032              		.loc 1 461 26 is_stmt 0 discriminator 1 view .LVU304
 1033 006e F7D1     		bne	.L75
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     __DSB();
 1034              		.loc 1 464 5 is_stmt 1 view .LVU305
 1035              	.LBB30:
 1036              	.LBI30:
 933:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 1037              		.loc 2 933 27 view .LVU306
 1038              	.LBB31:
 935:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 1039              		.loc 2 935 3 view .LVU307
 1040              		.syntax unified
 1041              	@ 935 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 1042 0070 BFF36F8F 		isb 0xF
 1043              	@ 0 "" 2
 1044              		.thumb
 1045              		.syntax unified
 1046              	.LBE31:
 1047              	.LBE30:
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1048              		.loc 1 465 5 view .LVU308
 1049              	.LBB32:
 1050              	.LBI32:
 944:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 1051              		.loc 2 944 27 view .LVU309
 1052              	.LBB33:
 1053              		.loc 2 946 3 view .LVU310
 1054              		.syntax unified
 1055              	@ 946 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
 1056 0074 BFF34F8F 		dsb 0xF
 1057              	@ 0 "" 2
ARM GAS  /tmp/cczI1n00.s 			page 60


 947:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 1058              		.loc 2 947 1 is_stmt 0 view .LVU311
 1059              		.thumb
 1060              		.syntax unified
 1061 0078 D5E7     		b	.L73
 1062              	.LVL70:
 1063              	.L76:
 1064              	.LCFI4:
 1065              		.cfi_def_cfa_offset 0
 1066              		.cfi_restore 4
 1067              		.cfi_restore 5
 1068              		.cfi_restore 6
 1069              		.cfi_restore 14
 1070              		.loc 2 947 1 view .LVU312
 1071              	.LBE33:
 1072              	.LBE32:
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1073              		.loc 1 332 3 discriminator 1 view .LVU313
 1074 007a 0220     		movs	r0, #2
 1075              	.LVL71:
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1076              		.loc 1 469 1 view .LVU314
 1077 007c 7047     		bx	lr
 1078              	.L84:
 1079 007e 00BF     		.align	2
 1080              	.L83:
 1081 0080 00000000 		.word	pFlash
 1082 0084 ******** 		.word	**********
 1083              		.cfi_endproc
 1084              	.LFE145:
 1086              		.section	.text.FLASH_OB_WaitForLastOperation,"ax",%progbits
 1087              		.align	1
 1088              		.global	FLASH_OB_WaitForLastOperation
 1089              		.syntax unified
 1090              		.thumb
 1091              		.thumb_func
 1093              	FLASH_OB_WaitForLastOperation:
 1094              	.LVL72:
 1095              	.LFB156:
1148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Wait for a FLASH Option Bytes change operation to complete.
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  Timeout maximum flash operation timeout
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL_StatusTypeDef HAL Status
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef FLASH_OB_WaitForLastOperation(uint32_t Timeout)
1155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 1096              		.loc 1 1155 1 is_stmt 1 view -0
 1097              		.cfi_startproc
 1098              		@ args = 0, pretend = 0, frame = 0
 1099              		@ frame_needed = 0, uses_anonymous_args = 0
 1100              		.loc 1 1155 1 is_stmt 0 view .LVU316
 1101 0000 38B5     		push	{r3, r4, r5, lr}
 1102              	.LCFI5:
 1103              		.cfi_def_cfa_offset 16
 1104              		.cfi_offset 3, -16
 1105              		.cfi_offset 4, -12
ARM GAS  /tmp/cczI1n00.s 			page 61


 1106              		.cfi_offset 5, -8
 1107              		.cfi_offset 14, -4
 1108 0002 0446     		mov	r4, r0
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Get timeout */
1157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t tickstart = HAL_GetTick();
 1109              		.loc 1 1157 3 is_stmt 1 view .LVU317
 1110              		.loc 1 1157 24 is_stmt 0 view .LVU318
 1111 0004 FFF7FEFF 		bl	HAL_GetTick
 1112              	.LVL73:
 1113              		.loc 1 1157 24 view .LVU319
 1114 0008 0546     		mov	r5, r0
 1115              	.LVL74:
1158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Wait for the FLASH Option Bytes change operation to complete by polling on OPT_BUSY flag to be
1160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   while(READ_BIT(FLASH->OPTSR_CUR, FLASH_OPTSR_OPT_BUSY) != 0U)
 1116              		.loc 1 1160 3 is_stmt 1 view .LVU320
 1117              	.L87:
 1118              		.loc 1 1160 58 view .LVU321
 1119              		.loc 1 1160 9 is_stmt 0 view .LVU322
 1120 000a 134B     		ldr	r3, .L96
 1121 000c DB69     		ldr	r3, [r3, #28]
 1122              		.loc 1 1160 58 view .LVU323
 1123 000e 13F0010F 		tst	r3, #1
 1124 0012 0BD0     		beq	.L94
1161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(Timeout != HAL_MAX_DELAY)
 1125              		.loc 1 1162 5 is_stmt 1 view .LVU324
 1126              		.loc 1 1162 7 is_stmt 0 view .LVU325
 1127 0014 B4F1FF3F 		cmp	r4, #-1
 1128 0018 F7D0     		beq	.L87
1163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if(((HAL_GetTick() - tickstart) > Timeout) || (Timeout == 0U))
 1129              		.loc 1 1164 7 is_stmt 1 view .LVU326
 1130              		.loc 1 1164 12 is_stmt 0 view .LVU327
 1131 001a FFF7FEFF 		bl	HAL_GetTick
 1132              	.LVL75:
 1133              		.loc 1 1164 26 discriminator 1 view .LVU328
 1134 001e 401B     		subs	r0, r0, r5
 1135              		.loc 1 1164 9 discriminator 1 view .LVU329
 1136 0020 A042     		cmp	r0, r4
 1137 0022 16D8     		bhi	.L90
 1138              		.loc 1 1164 50 discriminator 1 view .LVU330
 1139 0024 002C     		cmp	r4, #0
 1140 0026 F0D1     		bne	.L87
1165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         return HAL_TIMEOUT;
 1141              		.loc 1 1166 16 view .LVU331
 1142 0028 0320     		movs	r0, #3
 1143 002a 05E0     		b	.L88
 1144              	.L94:
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
1168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check option byte change error */
1172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(READ_BIT(FLASH->OPTSR_CUR, FLASH_OPTSR_OPTCHANGEERR) != 0U)
 1145              		.loc 1 1172 3 is_stmt 1 view .LVU332
ARM GAS  /tmp/cczI1n00.s 			page 62


 1146              		.loc 1 1172 6 is_stmt 0 view .LVU333
 1147 002c 0A4B     		ldr	r3, .L96
 1148 002e DB69     		ldr	r3, [r3, #28]
 1149              		.loc 1 1172 5 view .LVU334
 1150 0030 13F0804F 		tst	r3, #1073741824
 1151 0034 01D1     		bne	.L95
1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Save the error code */
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     pFlash.ErrorCode |= HAL_FLASH_ERROR_OB_CHANGE;
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     /* Clear the OB error flag */
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     FLASH->OPTCCR |= FLASH_OPTCCR_CLR_OPTCHANGEERR;
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     return HAL_ERROR;
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* If there is no error flag set */
1184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
 1152              		.loc 1 1184 10 view .LVU335
 1153 0036 0020     		movs	r0, #0
 1154              	.L88:
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 1155              		.loc 1 1185 1 view .LVU336
 1156 0038 38BD     		pop	{r3, r4, r5, pc}
 1157              	.LVL76:
 1158              	.L95:
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1159              		.loc 1 1175 5 is_stmt 1 view .LVU337
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1160              		.loc 1 1175 11 is_stmt 0 view .LVU338
 1161 003a 084A     		ldr	r2, .L96+4
 1162 003c 9369     		ldr	r3, [r2, #24]
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1163              		.loc 1 1175 22 view .LVU339
 1164 003e 43F08043 		orr	r3, r3, #1073741824
 1165 0042 9361     		str	r3, [r2, #24]
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1166              		.loc 1 1178 5 is_stmt 1 view .LVU340
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1167              		.loc 1 1178 10 is_stmt 0 view .LVU341
 1168 0044 044A     		ldr	r2, .L96
 1169 0046 536A     		ldr	r3, [r2, #36]
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1170              		.loc 1 1178 19 view .LVU342
 1171 0048 43F08043 		orr	r3, r3, #1073741824
 1172 004c 5362     		str	r3, [r2, #36]
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1173              		.loc 1 1180 5 is_stmt 1 view .LVU343
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1174              		.loc 1 1180 12 is_stmt 0 view .LVU344
 1175 004e 0120     		movs	r0, #1
 1176 0050 F2E7     		b	.L88
 1177              	.L90:
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 1178              		.loc 1 1166 16 view .LVU345
 1179 0052 0320     		movs	r0, #3
 1180 0054 F0E7     		b	.L88
ARM GAS  /tmp/cczI1n00.s 			page 63


 1181              	.L97:
 1182 0056 00BF     		.align	2
 1183              	.L96:
 1184 0058 ******** 		.word	**********
 1185 005c 00000000 		.word	pFlash
 1186              		.cfi_endproc
 1187              	.LFE156:
 1189              		.section	.text.FLASH_CRC_WaitForLastOperation,"ax",%progbits
 1190              		.align	1
 1191              		.global	FLASH_CRC_WaitForLastOperation
 1192              		.syntax unified
 1193              		.thumb
 1194              		.thumb_func
 1196              	FLASH_CRC_WaitForLastOperation:
 1197              	.LVL77:
 1198              	.LFB157:
1186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** /**
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @brief  Wait for a FLASH CRC computation to complete.
1189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  Timeout maximum flash operation timeout
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @param  Bank flash FLASH_BANK_1 or FLASH_BANK_2
1191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   * @retval HAL_StatusTypeDef HAL Status
1192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   */
1193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** HAL_StatusTypeDef FLASH_CRC_WaitForLastOperation(uint32_t Timeout, uint32_t Bank)
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** {
 1199              		.loc 1 1194 1 is_stmt 1 view -0
 1200              		.cfi_startproc
 1201              		@ args = 0, pretend = 0, frame = 0
 1202              		@ frame_needed = 0, uses_anonymous_args = 0
 1203              		.loc 1 1194 1 is_stmt 0 view .LVU347
 1204 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 1205              	.LCFI6:
 1206              		.cfi_def_cfa_offset 24
 1207              		.cfi_offset 3, -24
 1208              		.cfi_offset 4, -20
 1209              		.cfi_offset 5, -16
 1210              		.cfi_offset 6, -12
 1211              		.cfi_offset 7, -8
 1212              		.cfi_offset 14, -4
 1213 0002 0446     		mov	r4, r0
 1214 0004 0E46     		mov	r6, r1
1195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t bsyflag;
 1215              		.loc 1 1195 3 is_stmt 1 view .LVU348
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   uint32_t tickstart = HAL_GetTick();
 1216              		.loc 1 1196 3 view .LVU349
 1217              		.loc 1 1196 24 is_stmt 0 view .LVU350
 1218 0006 FFF7FEFF 		bl	HAL_GetTick
 1219              	.LVL78:
 1220              		.loc 1 1196 24 view .LVU351
 1221 000a 0746     		mov	r7, r0
 1222              	.LVL79:
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   assert_param(IS_FLASH_BANK_EXCLUSIVE(Bank));
 1223              		.loc 1 1198 3 is_stmt 1 view .LVU352
1199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Select bsyflag depending on Bank */
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(Bank == FLASH_BANK_1)
ARM GAS  /tmp/cczI1n00.s 			page 64


 1224              		.loc 1 1201 3 view .LVU353
 1225              		.loc 1 1201 5 is_stmt 0 view .LVU354
 1226 000c 012E     		cmp	r6, #1
 1227 000e 11D0     		beq	.L99
1202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bsyflag = FLASH_FLAG_CRC_BUSY_BANK1;
1204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     bsyflag = FLASH_FLAG_CRC_BUSY_BANK2;
 1228              		.loc 1 1207 13 view .LVU355
 1229 0010 154D     		ldr	r5, .L113
 1230              	.LVL80:
 1231              	.L101:
1208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Wait for the FLASH CRC computation to complete by polling on CRC_BUSY flag to be reset */
1211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   while(__HAL_FLASH_GET_FLAG(bsyflag))
 1232              		.loc 1 1211 9 is_stmt 1 view .LVU356
 1233 0012 164B     		ldr	r3, .L113+4
 1234 0014 1B69     		ldr	r3, [r3, #16]
 1235 0016 35EA0303 		bics	r3, r5, r3
 1236 001a 0DD1     		bne	.L110
1212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if(Timeout != HAL_MAX_DELAY)
 1237              		.loc 1 1213 5 view .LVU357
 1238              		.loc 1 1213 7 is_stmt 0 view .LVU358
 1239 001c B4F1FF3F 		cmp	r4, #-1
 1240 0020 F7D0     		beq	.L101
1214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       if(((HAL_GetTick() - tickstart) > Timeout) || (Timeout == 0U))
 1241              		.loc 1 1215 7 is_stmt 1 view .LVU359
 1242              		.loc 1 1215 12 is_stmt 0 view .LVU360
 1243 0022 FFF7FEFF 		bl	HAL_GetTick
 1244              	.LVL81:
 1245              		.loc 1 1215 26 discriminator 1 view .LVU361
 1246 0026 C01B     		subs	r0, r0, r7
 1247              		.loc 1 1215 9 discriminator 1 view .LVU362
 1248 0028 A042     		cmp	r0, r4
 1249 002a 1BD8     		bhi	.L105
 1250              		.loc 1 1215 50 discriminator 1 view .LVU363
 1251 002c 002C     		cmp	r4, #0
 1252 002e F0D1     		bne	.L101
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       {
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****         return HAL_TIMEOUT;
 1253              		.loc 1 1217 16 view .LVU364
 1254 0030 0320     		movs	r0, #3
 1255 0032 04E0     		b	.L102
 1256              	.LVL82:
 1257              	.L99:
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1258              		.loc 1 1203 13 view .LVU365
 1259 0034 0825     		movs	r5, #8
 1260 0036 ECE7     		b	.L101
 1261              	.LVL83:
 1262              	.L110:
1218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
ARM GAS  /tmp/cczI1n00.s 			page 65


1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* Check FLASH CRC read error flag  */
1223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   if(Bank == FLASH_BANK_1)
 1263              		.loc 1 1223 3 is_stmt 1 view .LVU366
 1264              		.loc 1 1223 5 is_stmt 0 view .LVU367
 1265 0038 012E     		cmp	r6, #1
 1266 003a 01D0     		beq	.L111
1224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (__HAL_FLASH_GET_FLAG_BANK1(FLASH_FLAG_CRCRDERR_BANK1))
1226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Save the error code */
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.ErrorCode |= HAL_FLASH_ERROR_CRCRD_BANK1;
1229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Clear FLASH CRC read error pending bit */
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_CLEAR_FLAG_BANK1(FLASH_FLAG_CRCRDERR_BANK1);
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       return HAL_ERROR;
1234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #if defined (DUAL_BANK)
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   else
1238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     if (__HAL_FLASH_GET_FLAG_BANK2(FLASH_FLAG_CRCRDERR_BANK2))
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
1241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Save the error code */
1242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       pFlash.ErrorCode |= HAL_FLASH_ERROR_CRCRD_BANK2;
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       /* Clear FLASH CRC read error pending bit */
1245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       __HAL_FLASH_CLEAR_FLAG_BANK2(FLASH_FLAG_CRCRDERR_BANK2);
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       return HAL_ERROR;
1248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
1250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** #endif /* DUAL_BANK */
1251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   /* If there is no error flag set */
1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   return HAL_OK;
 1267              		.loc 1 1253 10 view .LVU368
 1268 003c 0020     		movs	r0, #0
 1269              	.L102:
1254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 1270              		.loc 1 1254 1 view .LVU369
 1271 003e F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 1272              	.LVL84:
 1273              	.L111:
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 1274              		.loc 1 1225 5 is_stmt 1 view .LVU370
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 1275              		.loc 1 1225 9 is_stmt 0 view .LVU371
 1276 0040 0A4B     		ldr	r3, .L113+4
 1277 0042 1B69     		ldr	r3, [r3, #16]
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     {
 1278              		.loc 1 1225 8 view .LVU372
 1279 0044 13F0805F 		tst	r3, #268435456
 1280 0048 01D1     		bne	.L112
ARM GAS  /tmp/cczI1n00.s 			page 66


1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 1281              		.loc 1 1253 10 view .LVU373
 1282 004a 0020     		movs	r0, #0
 1283 004c F7E7     		b	.L102
 1284              	.L112:
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1285              		.loc 1 1228 7 is_stmt 1 view .LVU374
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1286              		.loc 1 1228 13 is_stmt 0 view .LVU375
 1287 004e 084A     		ldr	r2, .L113+8
 1288 0050 9369     		ldr	r3, [r2, #24]
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1289              		.loc 1 1228 24 view .LVU376
 1290 0052 43F08053 		orr	r3, r3, #268435456
 1291 0056 9361     		str	r3, [r2, #24]
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1292              		.loc 1 1231 7 is_stmt 1 view .LVU377
 1293 0058 044B     		ldr	r3, .L113+4
 1294 005a 4FF08052 		mov	r2, #268435456
 1295 005e 5A61     		str	r2, [r3, #20]
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 1296              		.loc 1 1233 7 view .LVU378
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****     }
 1297              		.loc 1 1233 14 is_stmt 0 view .LVU379
 1298 0060 0120     		movs	r0, #1
 1299 0062 ECE7     		b	.L102
 1300              	.L105:
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****       }
 1301              		.loc 1 1217 16 view .LVU380
 1302 0064 0320     		movs	r0, #3
 1303 0066 EAE7     		b	.L102
 1304              	.L114:
 1305              		.align	2
 1306              	.L113:
 1307 0068 08000080 		.word	-2147483640
 1308 006c ******** 		.word	**********
 1309 0070 00000000 		.word	pFlash
 1310              		.cfi_endproc
 1311              	.LFE157:
 1313              		.section	.text.HAL_FLASH_OB_Launch,"ax",%progbits
 1314              		.align	1
 1315              		.global	HAL_FLASH_OB_Launch
 1316              		.syntax unified
 1317              		.thumb
 1318              		.thumb_func
 1320              	HAL_FLASH_OB_Launch:
 1321              	.LFB153:
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   HAL_StatusTypeDef status;
 1322              		.loc 1 968 1 is_stmt 1 view -0
 1323              		.cfi_startproc
 1324              		@ args = 0, pretend = 0, frame = 0
 1325              		@ frame_needed = 0, uses_anonymous_args = 0
 1326 0000 08B5     		push	{r3, lr}
 1327              	.LCFI7:
 1328              		.cfi_def_cfa_offset 8
 1329              		.cfi_offset 3, -8
 1330              		.cfi_offset 14, -4
ARM GAS  /tmp/cczI1n00.s 			page 67


 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1331              		.loc 1 969 3 view .LVU382
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 1332              		.loc 1 972 3 view .LVU383
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 1333              		.loc 1 972 7 is_stmt 0 view .LVU384
 1334 0002 0121     		movs	r1, #1
 1335 0004 4CF25030 		movw	r0, #50000
 1336 0008 FFF7FEFF 		bl	FLASH_CRC_WaitForLastOperation
 1337              	.LVL85:
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 1338              		.loc 1 972 6 discriminator 1 view .LVU385
 1339 000c 08B1     		cbz	r0, .L119
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1340              		.loc 1 974 12 view .LVU386
 1341 000e 0120     		movs	r0, #1
 1342              	.L116:
 1343              	.LVL86:
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** }
 1344              		.loc 1 996 3 is_stmt 1 view .LVU387
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1345              		.loc 1 997 1 is_stmt 0 view .LVU388
 1346 0010 08BD     		pop	{r3, pc}
 1347              	.LVL87:
 1348              	.L119:
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1349              		.loc 1 984 5 is_stmt 1 view .LVU389
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   {
 1350              		.loc 1 987 3 view .LVU390
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c **** 
 1351              		.loc 1 990 5 view .LVU391
 1352 0012 054A     		ldr	r2, .L120
 1353 0014 9369     		ldr	r3, [r2, #24]
 1354 0016 43F00203 		orr	r3, r3, #2
 1355 001a 9361     		str	r3, [r2, #24]
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1356              		.loc 1 993 5 view .LVU392
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1357              		.loc 1 993 14 is_stmt 0 view .LVU393
 1358 001c 4CF25030 		movw	r0, #50000
 1359 0020 FFF7FEFF 		bl	FLASH_OB_WaitForLastOperation
 1360              	.LVL88:
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c ****   }
 1361              		.loc 1 993 14 view .LVU394
 1362 0024 F4E7     		b	.L116
 1363              	.L121:
 1364 0026 00BF     		.align	2
 1365              	.L120:
 1366 0028 ******** 		.word	**********
 1367              		.cfi_endproc
 1368              	.LFE153:
 1370              		.global	pFlash
 1371              		.section	.bss.pFlash,"aw",%nobits
 1372              		.align	2
 1375              	pFlash:
 1376 0000 00000000 		.space	28
 1376      00000000 
ARM GAS  /tmp/cczI1n00.s 			page 68


 1376      00000000 
 1376      00000000 
 1376      00000000 
 1377              		.text
 1378              	.Letext0:
 1379              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1380              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1381              		.file 5 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 1382              		.file 6 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h7xx.h"
 1383              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 1384              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_flash.h"
 1385              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
 1386              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_flash_ex.h"
ARM GAS  /tmp/cczI1n00.s 			page 69


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_flash.c
     /tmp/cczI1n00.s:20     .text.HAL_FLASH_EndOfOperationCallback:00000000 $t
     /tmp/cczI1n00.s:26     .text.HAL_FLASH_EndOfOperationCallback:00000000 HAL_FLASH_EndOfOperationCallback
     /tmp/cczI1n00.s:41     .text.HAL_FLASH_OperationErrorCallback:00000000 $t
     /tmp/cczI1n00.s:47     .text.HAL_FLASH_OperationErrorCallback:00000000 HAL_FLASH_OperationErrorCallback
     /tmp/cczI1n00.s:62     .text.HAL_FLASH_IRQHandler:00000000 $t
     /tmp/cczI1n00.s:68     .text.HAL_FLASH_IRQHandler:00000000 HAL_FLASH_IRQHandler
     /tmp/cczI1n00.s:320    .text.HAL_FLASH_IRQHandler:000000f8 $d
     /tmp/cczI1n00.s:1375   .bss.pFlash:00000000 pFlash
     /tmp/cczI1n00.s:326    .text.HAL_FLASH_Unlock:00000000 $t
     /tmp/cczI1n00.s:332    .text.HAL_FLASH_Unlock:00000000 HAL_FLASH_Unlock
     /tmp/cczI1n00.s:373    .text.HAL_FLASH_Unlock:0000002c $d
     /tmp/cczI1n00.s:379    .text.HAL_FLASH_Lock:00000000 $t
     /tmp/cczI1n00.s:385    .text.HAL_FLASH_Lock:00000000 HAL_FLASH_Lock
     /tmp/cczI1n00.s:414    .text.HAL_FLASH_Lock:0000001c $d
     /tmp/cczI1n00.s:419    .text.HAL_FLASH_OB_Unlock:00000000 $t
     /tmp/cczI1n00.s:425    .text.HAL_FLASH_OB_Unlock:00000000 HAL_FLASH_OB_Unlock
     /tmp/cczI1n00.s:466    .text.HAL_FLASH_OB_Unlock:0000002c $d
     /tmp/cczI1n00.s:472    .text.HAL_FLASH_OB_Lock:00000000 $t
     /tmp/cczI1n00.s:478    .text.HAL_FLASH_OB_Lock:00000000 HAL_FLASH_OB_Lock
     /tmp/cczI1n00.s:507    .text.HAL_FLASH_OB_Lock:0000001c $d
     /tmp/cczI1n00.s:512    .text.HAL_FLASH_GetError:00000000 $t
     /tmp/cczI1n00.s:518    .text.HAL_FLASH_GetError:00000000 HAL_FLASH_GetError
     /tmp/cczI1n00.s:534    .text.HAL_FLASH_GetError:00000008 $d
     /tmp/cczI1n00.s:539    .text.FLASH_WaitForLastOperation:00000000 $t
     /tmp/cczI1n00.s:545    .text.FLASH_WaitForLastOperation:00000000 FLASH_WaitForLastOperation
     /tmp/cczI1n00.s:673    .text.FLASH_WaitForLastOperation:00000074 $d
     /tmp/cczI1n00.s:680    .text.HAL_FLASH_Program:00000000 $t
     /tmp/cczI1n00.s:686    .text.HAL_FLASH_Program:00000000 HAL_FLASH_Program
     /tmp/cczI1n00.s:875    .text.HAL_FLASH_Program:00000080 $d
     /tmp/cczI1n00.s:881    .text.HAL_FLASH_Program_IT:00000000 $t
     /tmp/cczI1n00.s:887    .text.HAL_FLASH_Program_IT:00000000 HAL_FLASH_Program_IT
     /tmp/cczI1n00.s:1081   .text.HAL_FLASH_Program_IT:00000080 $d
     /tmp/cczI1n00.s:1087   .text.FLASH_OB_WaitForLastOperation:00000000 $t
     /tmp/cczI1n00.s:1093   .text.FLASH_OB_WaitForLastOperation:00000000 FLASH_OB_WaitForLastOperation
     /tmp/cczI1n00.s:1184   .text.FLASH_OB_WaitForLastOperation:00000058 $d
     /tmp/cczI1n00.s:1190   .text.FLASH_CRC_WaitForLastOperation:00000000 $t
     /tmp/cczI1n00.s:1196   .text.FLASH_CRC_WaitForLastOperation:00000000 FLASH_CRC_WaitForLastOperation
     /tmp/cczI1n00.s:1307   .text.FLASH_CRC_WaitForLastOperation:00000068 $d
     /tmp/cczI1n00.s:1314   .text.HAL_FLASH_OB_Launch:00000000 $t
     /tmp/cczI1n00.s:1320   .text.HAL_FLASH_OB_Launch:00000000 HAL_FLASH_OB_Launch
     /tmp/cczI1n00.s:1366   .text.HAL_FLASH_OB_Launch:00000028 $d
     /tmp/cczI1n00.s:1372   .bss.pFlash:00000000 $d

UNDEFINED SYMBOLS
FLASH_Erase_Sector
HAL_GetTick
