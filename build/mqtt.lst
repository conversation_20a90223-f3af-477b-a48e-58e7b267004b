ARM GAS  /tmp/ccBURsTh.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"mqtt.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c"
  19              		.section	.text.msg_generate_packet_id,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	msg_generate_packet_id:
  26              	.LVL0:
  27              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
   2:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT client
   4:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
   5:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @defgroup mqtt MQTT client
   6:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup apps
   7:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @verbinclude mqtt_client.txt
   8:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
   9:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  10:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /*
  11:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Copyright (c) 2016 Erik Andersson <<EMAIL>>
  12:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * All rights reserved.
  13:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  14:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Redistribution and use in source and binary forms, with or without modification,
  15:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * are permitted provided that the following conditions are met:
  16:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  17:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  18:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *    this list of conditions and the following disclaimer.
  19:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  20:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *    this list of conditions and the following disclaimer in the documentation
  21:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *    and/or other materials provided with the distribution.
  22:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * 3. The name of the author may not be used to endorse or promote products
  23:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *    derived from this software without specific prior written permission.
  24:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  25:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  26:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  27:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  28:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  29:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  30:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  31:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
ARM GAS  /tmp/ccBURsTh.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  33:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  34:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * OF SUCH DAMAGE.
  35:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  36:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * This file is part of the lwIP TCP/IP stack
  37:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  38:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Author: Erik Andersson <<EMAIL>>
  39:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  40:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  41:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @todo:
  42:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * - Handle large outgoing payloads for PUBLISH messages
  43:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * - Fix restriction of a single topic in each (UN)SUBSCRIBE message (protocol has support for mult
  44:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * - Add support for legacy MQTT protocol version
  45:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  46:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Please coordinate changes and requests with Erik Andersson
  47:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Erik Andersson <<EMAIL>>
  48:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
  49:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
  50:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/apps/mqtt.h"
  51:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/apps/mqtt_priv.h"
  52:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/timeouts.h"
  53:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/ip_addr.h"
  54:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/mem.h"
  55:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/err.h"
  56:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/pbuf.h"
  57:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/altcp.h"
  58:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/altcp_tcp.h"
  59:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include "lwip/altcp_tls.h"
  60:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #include <string.h>
  61:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  62:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #if LWIP_TCP && LWIP_CALLBACK_API
  63:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  64:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
  65:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT_DEBUG: Default is off.
  66:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
  67:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #if !defined MQTT_DEBUG || defined __DOXYGEN__
  68:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_DEBUG                  LWIP_DBG_OFF
  69:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #endif
  70:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  71:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_DEBUG_TRACE        (MQTT_DEBUG | LWIP_DBG_TRACE)
  72:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_DEBUG_STATE        (MQTT_DEBUG | LWIP_DBG_STATE)
  73:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_DEBUG_WARN         (MQTT_DEBUG | LWIP_DBG_LEVEL_WARNING)
  74:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_DEBUG_WARN_STATE   (MQTT_DEBUG | LWIP_DBG_LEVEL_WARNING | LWIP_DBG_STATE)
  75:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_DEBUG_SERIOUS      (MQTT_DEBUG | LWIP_DBG_LEVEL_SERIOUS)
  76:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  77:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  78:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
  79:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
  80:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT client connection states
  81:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
  82:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** enum {
  83:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   TCP_DISCONNECTED,
  84:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   TCP_CONNECTING,
  85:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECTING,
  86:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECTED
  87:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** };
  88:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
  90:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT control message types
  91:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
  92:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** enum mqtt_message_type {
  93:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_CONNECT = 1,
  94:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_CONNACK = 2,
  95:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PUBLISH = 3,
  96:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PUBACK = 4,
  97:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PUBREC = 5,
  98:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PUBREL = 6,
  99:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PUBCOMP = 7,
 100:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_SUBSCRIBE = 8,
 101:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_SUBACK = 9,
 102:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_UNSUBSCRIBE = 10,
 103:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_UNSUBACK = 11,
 104:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PINGREQ = 12,
 105:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_PINGRESP = 13,
 106:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_MSG_TYPE_DISCONNECT = 14
 107:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** };
 108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 109:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /** Helpers to extract control packet type and qos from first byte in fixed header */
 110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_CTL_PACKET_TYPE(fixed_hdr_byte0) ((fixed_hdr_byte0 & 0xf0) >> 4)
 111:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define MQTT_CTL_PACKET_QOS(fixed_hdr_byte0) ((fixed_hdr_byte0 & 0x6) >> 1)
 112:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 114:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT connect flags, only used in CONNECT message
 115:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 116:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** enum mqtt_connect_flag {
 117:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECT_FLAG_USERNAME = 1 << 7,
 118:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECT_FLAG_PASSWORD = 1 << 6,
 119:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECT_FLAG_WILL_RETAIN = 1 << 5,
 120:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECT_FLAG_WILL = 1 << 2,
 121:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   MQTT_CONNECT_FLAG_CLEAN_SESSION = 1 << 1
 122:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** };
 123:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 124:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void mqtt_cyclic_timer(void *arg);
 126:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 127:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #if defined(LWIP_DEBUG)
 128:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static const char *const mqtt_message_type_str[15] = {
 129:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "UNDEFINED",
 130:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "CONNECT",
 131:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "CONNACK",
 132:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PUBLISH",
 133:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PUBACK",
 134:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PUBREC",
 135:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PUBREL",
 136:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PUBCOMP",
 137:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "SUBSCRIBE",
 138:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "SUBACK",
 139:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "UNSUBSCRIBE",
 140:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "UNSUBACK",
 141:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PINGREQ",
 142:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "PINGRESP",
 143:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   "DISCONNECT"
 144:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** };
 145:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 147:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Message type value to string
 148:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param msg_type see enum mqtt_message_type
 149:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *
 150:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return Control message type text string
 151:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 152:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static const char *
 153:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_msg_type_to_str(u8_t msg_type)
 154:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 155:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (msg_type >= LWIP_ARRAYSIZE(mqtt_message_type_str)) {
 156:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     msg_type = 0;
 157:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 158:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return mqtt_message_type_str[msg_type];
 159:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 160:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 161:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #endif
 162:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 163:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 164:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 165:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Generate MQTT packet identifier
 166:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
 167:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return New packet identifier, range 1 to 65535
 168:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 169:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static u16_t
 170:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** msg_generate_packet_id(mqtt_client_t *client)
 171:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
  28              		.loc 1 171 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 172:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->pkt_id_seq++;
  33              		.loc 1 172 3 view .LVU1
  34              		.loc 1 172 9 is_stmt 0 view .LVU2
  35 0000 C288     		ldrh	r2, [r0, #6]
  36              		.loc 1 172 21 view .LVU3
  37 0002 531C     		adds	r3, r2, #1
  38 0004 9BB2     		uxth	r3, r3
  39 0006 C380     		strh	r3, [r0, #6]	@ movhi
 173:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->pkt_id_seq == 0) {
  40              		.loc 1 173 3 is_stmt 1 view .LVU4
  41              		.loc 1 173 6 is_stmt 0 view .LVU5
  42 0008 0BB9     		cbnz	r3, .L2
 174:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->pkt_id_seq++;
  43              		.loc 1 174 5 is_stmt 1 view .LVU6
  44              		.loc 1 174 23 is_stmt 0 view .LVU7
  45 000a 0232     		adds	r2, r2, #2
  46 000c C280     		strh	r2, [r0, #6]	@ movhi
  47              	.L2:
 175:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 176:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return client->pkt_id_seq;
  48              		.loc 1 176 3 is_stmt 1 view .LVU8
 177:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
  49              		.loc 1 177 1 is_stmt 0 view .LVU9
  50 000e C088     		ldrh	r0, [r0, #6]
  51              	.LVL1:
  52              		.loc 1 177 1 view .LVU10
ARM GAS  /tmp/ccBURsTh.s 			page 5


  53 0010 7047     		bx	lr
  54              		.cfi_endproc
  55              	.LFE174:
  57              		.section	.text.mqtt_ringbuf_put,"ax",%progbits
  58              		.align	1
  59              		.syntax unified
  60              		.thumb
  61              		.thumb_func
  63              	mqtt_ringbuf_put:
  64              	.LVL2:
  65              	.LFB175:
 178:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 179:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /*-------------------------------------------------------------------------------------------------
 180:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /* Output ring buffer */
 181:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 182:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /** Add single item to ring buffer */
 183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 184:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_ringbuf_put(struct mqtt_ringbuf_t *rb, u8_t item)
 185:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
  66              		.loc 1 185 1 is_stmt 1 view -0
  67              		.cfi_startproc
  68              		@ args = 0, pretend = 0, frame = 0
  69              		@ frame_needed = 0, uses_anonymous_args = 0
  70              		@ link register save eliminated.
 186:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   rb->buf[rb->put] = item;
  71              		.loc 1 186 3 view .LVU12
  72              		.loc 1 186 13 is_stmt 0 view .LVU13
  73 0000 0388     		ldrh	r3, [r0]
  74              		.loc 1 186 20 view .LVU14
  75 0002 C218     		adds	r2, r0, r3
  76 0004 1171     		strb	r1, [r2, #4]
 187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   rb->put++;
  77              		.loc 1 187 3 is_stmt 1 view .LVU15
  78              		.loc 1 187 10 is_stmt 0 view .LVU16
  79 0006 0133     		adds	r3, r3, #1
  80 0008 9BB2     		uxth	r3, r3
  81 000a 0380     		strh	r3, [r0]	@ movhi
 188:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (rb->put >= MQTT_OUTPUT_RINGBUF_SIZE) {
  82              		.loc 1 188 3 is_stmt 1 view .LVU17
  83              		.loc 1 188 6 is_stmt 0 view .LVU18
  84 000c FF2B     		cmp	r3, #255
  85 000e 01D9     		bls	.L3
 189:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     rb->put = 0;
  86              		.loc 1 189 5 is_stmt 1 view .LVU19
  87              		.loc 1 189 13 is_stmt 0 view .LVU20
  88 0010 0023     		movs	r3, #0
  89 0012 0380     		strh	r3, [r0]	@ movhi
  90              	.L3:
 190:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
  91              		.loc 1 191 1 view .LVU21
  92 0014 7047     		bx	lr
  93              		.cfi_endproc
  94              	.LFE175:
  96              		.section	.text.mqtt_ringbuf_get_ptr,"ax",%progbits
  97              		.align	1
  98              		.syntax unified
ARM GAS  /tmp/ccBURsTh.s 			page 6


  99              		.thumb
 100              		.thumb_func
 102              	mqtt_ringbuf_get_ptr:
 103              	.LVL3:
 104              	.LFB176:
 192:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 193:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /** Return pointer to ring buffer get position */
 194:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static u8_t *
 195:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_ringbuf_get_ptr(struct mqtt_ringbuf_t *rb)
 196:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 105              		.loc 1 196 1 is_stmt 1 view -0
 106              		.cfi_startproc
 107              		@ args = 0, pretend = 0, frame = 0
 108              		@ frame_needed = 0, uses_anonymous_args = 0
 109              		@ link register save eliminated.
 197:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return &rb->buf[rb->get];
 110              		.loc 1 197 3 view .LVU23
 111              		.loc 1 197 21 is_stmt 0 view .LVU24
 112 0000 4388     		ldrh	r3, [r0, #2]
 113              		.loc 1 197 10 view .LVU25
 114 0002 1844     		add	r0, r0, r3
 115              	.LVL4:
 198:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 116              		.loc 1 198 1 view .LVU26
 117 0004 0430     		adds	r0, r0, #4
 118 0006 7047     		bx	lr
 119              		.cfi_endproc
 120              	.LFE176:
 122              		.section	.text.mqtt_ringbuf_len,"ax",%progbits
 123              		.align	1
 124              		.syntax unified
 125              		.thumb
 126              		.thumb_func
 128              	mqtt_ringbuf_len:
 129              	.LVL5:
 130              	.LFB178:
 199:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 200:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 201:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_ringbuf_advance_get_idx(struct mqtt_ringbuf_t *rb, u16_t len)
 202:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_ringbuf_advance_get_idx: len < MQTT_OUTPUT_RINGBUF_SIZE", len < MQTT_OUTPUT_RIN
 204:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 205:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   rb->get += len;
 206:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (rb->get >= MQTT_OUTPUT_RINGBUF_SIZE) {
 207:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     rb->get = rb->get - MQTT_OUTPUT_RINGBUF_SIZE;
 208:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 209:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 210:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 211:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /** Return number of bytes in ring buffer */
 212:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static u16_t
 213:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_ringbuf_len(struct mqtt_ringbuf_t *rb)
 214:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 131              		.loc 1 214 1 is_stmt 1 view -0
 132              		.cfi_startproc
 133              		@ args = 0, pretend = 0, frame = 0
 134              		@ frame_needed = 0, uses_anonymous_args = 0
 135              		@ link register save eliminated.
ARM GAS  /tmp/ccBURsTh.s 			page 7


 215:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u32_t len = rb->put - rb->get;
 136              		.loc 1 215 3 view .LVU28
 137              		.loc 1 215 17 is_stmt 0 view .LVU29
 138 0000 0388     		ldrh	r3, [r0]
 139              		.loc 1 215 27 view .LVU30
 140 0002 4288     		ldrh	r2, [r0, #2]
 141              		.loc 1 215 23 view .LVU31
 142 0004 981A     		subs	r0, r3, r2
 143              	.LVL6:
 216:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (len > 0xFFFF) {
 144              		.loc 1 216 3 is_stmt 1 view .LVU32
 145              		.loc 1 216 6 is_stmt 0 view .LVU33
 146 0006 B0F5803F 		cmp	r0, #65536
 147 000a 01D3     		bcc	.L7
 217:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len += MQTT_OUTPUT_RINGBUF_SIZE;
 148              		.loc 1 217 5 is_stmt 1 view .LVU34
 149              		.loc 1 217 9 is_stmt 0 view .LVU35
 150 000c 00F58070 		add	r0, r0, #256
 151              	.LVL7:
 152              	.L7:
 218:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 219:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return (u16_t)len;
 153              		.loc 1 219 3 is_stmt 1 view .LVU36
 220:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 154              		.loc 1 220 1 is_stmt 0 view .LVU37
 155 0010 80B2     		uxth	r0, r0
 156              	.LVL8:
 157              		.loc 1 220 1 view .LVU38
 158 0012 7047     		bx	lr
 159              		.cfi_endproc
 160              	.LFE178:
 162              		.section	.text.mqtt_delete_request,"ax",%progbits
 163              		.align	1
 164              		.syntax unified
 165              		.thumb
 166              		.thumb_func
 168              	mqtt_delete_request:
 169              	.LVL9:
 170              	.LFB182:
 221:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 222:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /** Return number of bytes free in ring buffer */
 223:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define mqtt_ringbuf_free(rb) (MQTT_OUTPUT_RINGBUF_SIZE - mqtt_ringbuf_len(rb))
 224:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 225:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /** Return number of bytes possible to read without wrapping around */
 226:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #define mqtt_ringbuf_linear_read_length(rb) LWIP_MIN(mqtt_ringbuf_len(rb), (MQTT_OUTPUT_RINGBUF_SIZ
 227:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 228:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 229:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Try send as many bytes as possible from output ring buffer
 230:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param rb Output ring buffer
 231:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tpcb TCP connection handle
 232:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 233:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 234:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_output_send(struct mqtt_ringbuf_t *rb, struct altcp_pcb *tpcb)
 235:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 236:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err;
 237:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t wrap = 0;
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t ringbuf_lin_len = mqtt_ringbuf_linear_read_length(rb);
ARM GAS  /tmp/ccBURsTh.s 			page 8


 239:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_output_send: tpcb != NULL", tpcb != NULL);
 241:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (send_len == 0 || ringbuf_lin_len == 0) {
 243:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return;
 244:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 245:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 246:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_output_send: tcp_sndbuf: %d bytes, ringbuf_linear_available:
 247:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                                  send_len, ringbuf_lin_len, rb->get, rb->put));
 248:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 249:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (send_len > ringbuf_lin_len) {
 250:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Space in TCP output buffer is larger than available in ring buffer linear portion */
 251:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     send_len = ringbuf_lin_len;
 252:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Wrap around if more data in ring buffer after linear portion */
 253:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     wrap = (mqtt_ringbuf_len(rb) > ringbuf_lin_len);
 254:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY | (wrap ? TCP_WRI
 256:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((err == ERR_OK) && wrap) {
 257:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 258:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Use the lesser one of ring buffer linear length and TCP send buffer size */
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     send_len = LWIP_MIN(altcp_sndbuf(tpcb), mqtt_ringbuf_linear_read_length(rb));
 260:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 261:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 262:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 263:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err == ERR_OK) {
 264:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 265:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Flush */
 266:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_output(tpcb);
 267:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 268:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_output_send: Send failed with err %d (\"%s\")\n", err, lwip
 269:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 270:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 271:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 272:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 273:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 274:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /*-------------------------------------------------------------------------------------------------
 275:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /* Request queue */
 276:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 277:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 278:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Create request item
 279:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r_objs Pointer to request objects
 280:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r_objs_len Number of array entries
 281:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param pkt_id Packet identifier of request
 282:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param cb Packet callback to call when requests lifetime ends
 283:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg Parameter following callback
 284:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return Request or NULL if failed to create
 285:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 286:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static struct mqtt_request_t *
 287:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_create_request(struct mqtt_request_t *r_objs, size_t r_objs_len, u16_t pkt_id, mqtt_request_cb
 288:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 289:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r = NULL;
 290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_create_request: r_objs != NULL", r_objs != NULL);
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 293:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 294:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (r_objs[n].next == &r_objs[n]) {
 295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r = &r_objs[n];
ARM GAS  /tmp/ccBURsTh.s 			page 9


 296:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->next = NULL;
 297:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->cb = cb;
 298:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->arg = arg;
 299:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->pkt_id = pkt_id;
 300:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 301:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 302:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 303:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return r;
 304:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 305:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 306:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 307:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 308:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Append request to pending request queue
 309:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tail Pointer to request queue tail pointer
 310:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r Request to append
 311:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 312:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 313:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_append_request(struct mqtt_request_t **tail, struct mqtt_request_t *r)
 314:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 315:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *head = NULL;
 316:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   s16_t time_before = 0;
 317:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter;
 318:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_append_request: tail != NULL", tail != NULL);
 320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Iterate trough queue to find head, and count total timeout time */
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = iter->next) {
 323:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     head = iter;
 325:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 326:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_append_request: time_before <= MQTT_REQ_TIMEOUT", time_before <= MQTT_REQ_TIMEO
 328:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 329:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (head == NULL) {
 330:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     *tail = r;
 331:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 332:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     head->next = r;
 333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 335:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 336:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 338:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Delete request item
 339:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r Request item to delete
 340:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 341:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 342:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_delete_request(struct mqtt_request_t *r)
 343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 171              		.loc 1 343 1 is_stmt 1 view -0
 172              		.cfi_startproc
 173              		@ args = 0, pretend = 0, frame = 0
 174              		@ frame_needed = 0, uses_anonymous_args = 0
 175              		@ link register save eliminated.
 344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (r != NULL) {
 176              		.loc 1 344 3 view .LVU40
 177              		.loc 1 344 6 is_stmt 0 view .LVU41
 178 0000 0346     		mov	r3, r0
ARM GAS  /tmp/ccBURsTh.s 			page 10


 179 0002 00B1     		cbz	r0, .L8
 345:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r->next = r;
 180              		.loc 1 345 5 is_stmt 1 view .LVU42
 181              		.loc 1 345 13 is_stmt 0 view .LVU43
 182 0004 1860     		str	r0, [r3]
 183              	.L8:
 346:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 184              		.loc 1 347 1 view .LVU44
 185 0006 7047     		bx	lr
 186              		.cfi_endproc
 187              	.LFE182:
 189              		.section	.text.mqtt_output_append_u8,"ax",%progbits
 190              		.align	1
 191              		.syntax unified
 192              		.thumb
 193              		.thumb_func
 195              	mqtt_output_append_u8:
 196              	.LVL10:
 197              	.LFB187:
 348:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 349:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 350:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Remove a request item with a specific packet identifier from request queue
 351:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tail Pointer to request queue tail pointer
 352:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param pkt_id Packet identifier of request to take
 353:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return Request item if found, NULL if not
 354:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static struct mqtt_request_t *
 356:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_take_request(struct mqtt_request_t **tail, u16_t pkt_id)
 357:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter = NULL, *prev = NULL;
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_take_request: tail != NULL", tail != NULL);
 360:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = iter->next) {
 362:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 363:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 364:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 365:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     prev = iter;
 366:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 367:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 368:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* If request was found */
 369:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (iter != NULL) {
 370:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* unchain */
 371:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (prev == NULL) {
 372:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       *tail = iter->next;
 373:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 374:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       prev->next = iter->next;
 375:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 376:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* If exists, add remaining timeout time for the request to next */
 377:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->next != NULL) {
 378:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       iter->next->timeout_diff += iter->timeout_diff;
 379:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 380:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     iter->next = NULL;
 381:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 382:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return iter;
 383:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 384:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 11


 385:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 386:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Handle requests timeout
 387:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tail Pointer to request queue tail pointer
 388:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param t Time since last call in seconds
 389:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 390:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 391:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_request_time_elapsed(struct mqtt_request_t **tail, u8_t t)
 392:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 393:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r;
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_request_time_elapsed: tail != NULL", tail != NULL);
 395:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   while (t > 0 && r != NULL) {
 397:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 398:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t -= (u8_t)r->timeout_diff;
 399:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Unchain */
 400:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       *tail = r->next;
 401:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Notify upper layer about timeout */
 402:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (r->cb != NULL) {
 403:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_TIMEOUT);
 404:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 405:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       mqtt_delete_request(r);
 406:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Tail might be be modified in callback, so re-read it in every iteration */
 407:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r = *(struct mqtt_request_t *const volatile *)tail;
 408:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 409:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->timeout_diff -= t;
 410:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t = 0;
 411:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 412:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 413:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 414:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 415:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 416:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Free all request items
 417:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tail Pointer to request queue tail pointer
 418:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 419:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 420:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_clear_requests(struct mqtt_request_t **tail)
 421:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 422:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter, *next;
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_clear_requests: tail != NULL", tail != NULL);
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 425:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 426:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_delete_request(iter);
 427:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 428:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   *tail = NULL;
 429:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 430:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 431:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Initialize all request items
 432:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r_objs Pointer to request objects
 433:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r_objs_len Number of array entries
 434:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 435:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 436:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_init_requests(struct mqtt_request_t *r_objs, size_t r_objs_len)
 437:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 438:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_init_requests: r_objs != NULL", r_objs != NULL);
 440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 441:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item pointing to itself indicates unused */
ARM GAS  /tmp/ccBURsTh.s 			page 12


 442:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_objs[n].next = &r_objs[n];
 443:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 444:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 445:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 446:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /*-------------------------------------------------------------------------------------------------
 447:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /* Output message build helpers */
 448:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 449:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 450:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 451:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_output_append_u8(struct mqtt_ringbuf_t *rb, u8_t value)
 452:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 198              		.loc 1 452 1 is_stmt 1 view -0
 199              		.cfi_startproc
 200              		@ args = 0, pretend = 0, frame = 0
 201              		@ frame_needed = 0, uses_anonymous_args = 0
 202              		.loc 1 452 1 is_stmt 0 view .LVU46
 203 0000 08B5     		push	{r3, lr}
 204              	.LCFI0:
 205              		.cfi_def_cfa_offset 8
 206              		.cfi_offset 3, -8
 207              		.cfi_offset 14, -4
 453:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_ringbuf_put(rb, value);
 208              		.loc 1 453 3 is_stmt 1 view .LVU47
 209 0002 FFF7FEFF 		bl	mqtt_ringbuf_put
 210              	.LVL11:
 454:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 211              		.loc 1 454 1 is_stmt 0 view .LVU48
 212 0006 08BD     		pop	{r3, pc}
 213              		.cfi_endproc
 214              	.LFE187:
 216              		.section	.text.mqtt_output_append_u16,"ax",%progbits
 217              		.align	1
 218              		.syntax unified
 219              		.thumb
 220              		.thumb_func
 222              	mqtt_output_append_u16:
 223              	.LVL12:
 224              	.LFB188:
 455:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 456:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static
 457:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** void mqtt_output_append_u16(struct mqtt_ringbuf_t *rb, u16_t value)
 458:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 225              		.loc 1 458 1 is_stmt 1 view -0
 226              		.cfi_startproc
 227              		@ args = 0, pretend = 0, frame = 0
 228              		@ frame_needed = 0, uses_anonymous_args = 0
 229              		.loc 1 458 1 is_stmt 0 view .LVU50
 230 0000 38B5     		push	{r3, r4, r5, lr}
 231              	.LCFI1:
 232              		.cfi_def_cfa_offset 16
 233              		.cfi_offset 3, -16
 234              		.cfi_offset 4, -12
 235              		.cfi_offset 5, -8
 236              		.cfi_offset 14, -4
 237 0002 0546     		mov	r5, r0
 238 0004 0C46     		mov	r4, r1
 459:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_ringbuf_put(rb, value >> 8);
ARM GAS  /tmp/ccBURsTh.s 			page 13


 239              		.loc 1 459 3 is_stmt 1 view .LVU51
 240 0006 090A     		lsrs	r1, r1, #8
 241              	.LVL13:
 242              		.loc 1 459 3 is_stmt 0 view .LVU52
 243 0008 FFF7FEFF 		bl	mqtt_ringbuf_put
 244              	.LVL14:
 460:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_ringbuf_put(rb, value & 0xff);
 245              		.loc 1 460 3 is_stmt 1 view .LVU53
 246 000c E1B2     		uxtb	r1, r4
 247 000e 2846     		mov	r0, r5
 248 0010 FFF7FEFF 		bl	mqtt_ringbuf_put
 249              	.LVL15:
 461:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 250              		.loc 1 461 1 is_stmt 0 view .LVU54
 251 0014 38BD     		pop	{r3, r4, r5, pc}
 252              		.loc 1 461 1 view .LVU55
 253              		.cfi_endproc
 254              	.LFE188:
 256              		.section	.text.mqtt_output_append_buf,"ax",%progbits
 257              		.align	1
 258              		.syntax unified
 259              		.thumb
 260              		.thumb_func
 262              	mqtt_output_append_buf:
 263              	.LVL16:
 264              	.LFB189:
 462:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 463:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 464:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_output_append_buf(struct mqtt_ringbuf_t *rb, const void *data, u16_t length)
 465:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 265              		.loc 1 465 1 is_stmt 1 view -0
 266              		.cfi_startproc
 267              		@ args = 0, pretend = 0, frame = 0
 268              		@ frame_needed = 0, uses_anonymous_args = 0
 269              		.loc 1 465 1 is_stmt 0 view .LVU57
 270 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 271              	.LCFI2:
 272              		.cfi_def_cfa_offset 24
 273              		.cfi_offset 3, -24
 274              		.cfi_offset 4, -20
 275              		.cfi_offset 5, -16
 276              		.cfi_offset 6, -12
 277              		.cfi_offset 7, -8
 278              		.cfi_offset 14, -4
 279 0002 0746     		mov	r7, r0
 280 0004 0E46     		mov	r6, r1
 281 0006 1546     		mov	r5, r2
 466:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t n;
 282              		.loc 1 466 3 is_stmt 1 view .LVU58
 467:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 283              		.loc 1 467 3 view .LVU59
 284              	.LVL17:
 285              		.loc 1 467 10 is_stmt 0 view .LVU60
 286 0008 0024     		movs	r4, #0
 287              		.loc 1 467 3 view .LVU61
 288 000a 05E0     		b	.L15
 289              	.LVL18:
ARM GAS  /tmp/ccBURsTh.s 			page 14


 290              	.L16:
 468:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_put(rb, ((const u8_t *)data)[n]);
 291              		.loc 1 468 5 is_stmt 1 view .LVU62
 292 000c 315D     		ldrb	r1, [r6, r4]	@ zero_extendqisi2
 293 000e 3846     		mov	r0, r7
 294 0010 FFF7FEFF 		bl	mqtt_ringbuf_put
 295              	.LVL19:
 467:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 296              		.loc 1 467 28 discriminator 3 view .LVU63
 297 0014 0134     		adds	r4, r4, #1
 298              	.LVL20:
 467:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 299              		.loc 1 467 28 is_stmt 0 discriminator 3 view .LVU64
 300 0016 A4B2     		uxth	r4, r4
 301              	.LVL21:
 302              	.L15:
 467:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 303              		.loc 1 467 17 is_stmt 1 discriminator 1 view .LVU65
 304 0018 AC42     		cmp	r4, r5
 305 001a F7D3     		bcc	.L16
 469:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 470:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 306              		.loc 1 470 1 is_stmt 0 view .LVU66
 307 001c F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 308              		.loc 1 470 1 view .LVU67
 309              		.cfi_endproc
 310              	.LFE189:
 312              		.section	.text.mqtt_output_append_string,"ax",%progbits
 313              		.align	1
 314              		.syntax unified
 315              		.thumb
 316              		.thumb_func
 318              	mqtt_output_append_string:
 319              	.LVL22:
 320              	.LFB190:
 471:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 472:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 473:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_output_append_string(struct mqtt_ringbuf_t *rb, const char *str, u16_t length)
 474:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 321              		.loc 1 474 1 is_stmt 1 view -0
 322              		.cfi_startproc
 323              		@ args = 0, pretend = 0, frame = 0
 324              		@ frame_needed = 0, uses_anonymous_args = 0
 325              		.loc 1 474 1 is_stmt 0 view .LVU69
 326 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 327              	.LCFI3:
 328              		.cfi_def_cfa_offset 24
 329              		.cfi_offset 3, -24
 330              		.cfi_offset 4, -20
 331              		.cfi_offset 5, -16
 332              		.cfi_offset 6, -12
 333              		.cfi_offset 7, -8
 334              		.cfi_offset 14, -4
 335 0002 0646     		mov	r6, r0
 336 0004 0F46     		mov	r7, r1
 337 0006 1546     		mov	r5, r2
 475:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t n;
ARM GAS  /tmp/ccBURsTh.s 			page 15


 338              		.loc 1 475 3 is_stmt 1 view .LVU70
 476:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_ringbuf_put(rb, length >> 8);
 339              		.loc 1 476 3 view .LVU71
 340 0008 110A     		lsrs	r1, r2, #8
 341              	.LVL23:
 342              		.loc 1 476 3 is_stmt 0 view .LVU72
 343 000a FFF7FEFF 		bl	mqtt_ringbuf_put
 344              	.LVL24:
 477:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_ringbuf_put(rb, length & 0xff);
 345              		.loc 1 477 3 is_stmt 1 view .LVU73
 346 000e E9B2     		uxtb	r1, r5
 347 0010 3046     		mov	r0, r6
 348 0012 FFF7FEFF 		bl	mqtt_ringbuf_put
 349              	.LVL25:
 478:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 350              		.loc 1 478 3 view .LVU74
 351              		.loc 1 478 10 is_stmt 0 view .LVU75
 352 0016 0024     		movs	r4, #0
 353              		.loc 1 478 3 view .LVU76
 354 0018 05E0     		b	.L19
 355              	.LVL26:
 356              	.L20:
 479:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_put(rb, str[n]);
 357              		.loc 1 479 5 is_stmt 1 view .LVU77
 358 001a 395D     		ldrb	r1, [r7, r4]	@ zero_extendqisi2
 359 001c 3046     		mov	r0, r6
 360 001e FFF7FEFF 		bl	mqtt_ringbuf_put
 361              	.LVL27:
 478:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 362              		.loc 1 478 28 discriminator 3 view .LVU78
 363 0022 0134     		adds	r4, r4, #1
 364              	.LVL28:
 478:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 365              		.loc 1 478 28 is_stmt 0 discriminator 3 view .LVU79
 366 0024 A4B2     		uxth	r4, r4
 367              	.LVL29:
 368              	.L19:
 478:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < length; n++) {
 369              		.loc 1 478 17 is_stmt 1 discriminator 1 view .LVU80
 370 0026 AC42     		cmp	r4, r5
 371 0028 F7D3     		bcc	.L20
 480:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 481:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 372              		.loc 1 481 1 is_stmt 0 view .LVU81
 373 002a F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 374              		.loc 1 481 1 view .LVU82
 375              		.cfi_endproc
 376              	.LFE190:
 378              		.section	.text.mqtt_output_append_fixed_header,"ax",%progbits
 379              		.align	1
 380              		.syntax unified
 381              		.thumb
 382              		.thumb_func
 384              	mqtt_output_append_fixed_header:
 385              	.LVL30:
 386              	.LFB191:
 482:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 16


 483:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 484:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Append fixed header
 485:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param rb Output ring buffer
 486:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param msg_type see enum mqtt_message_type
 487:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param fdup MQTT DUP flag
 488:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param fqos MQTT QoS field
 489:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param fretain MQTT retain flag
 490:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r_length Remaining length after fixed header
 491:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 492:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 493:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 494:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_output_append_fixed_header(struct mqtt_ringbuf_t *rb, u8_t msg_type, u8_t fdup,
 495:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                                 u8_t fqos, u8_t fretain, u16_t r_length)
 496:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 387              		.loc 1 496 1 is_stmt 1 view -0
 388              		.cfi_startproc
 389              		@ args = 8, pretend = 0, frame = 0
 390              		@ frame_needed = 0, uses_anonymous_args = 0
 391              		.loc 1 496 1 is_stmt 0 view .LVU84
 392 0000 38B5     		push	{r3, r4, r5, lr}
 393              	.LCFI4:
 394              		.cfi_def_cfa_offset 16
 395              		.cfi_offset 3, -16
 396              		.cfi_offset 4, -12
 397              		.cfi_offset 5, -8
 398              		.cfi_offset 14, -4
 399 0002 0546     		mov	r5, r0
 400 0004 BDF81440 		ldrh	r4, [sp, #20]
 497:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Start with control byte */
 498:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_u8(rb, (((msg_type & 0x0f) << 4) | ((fdup & 1) << 3) | ((fqos & 3) << 1) | (fr
 401              		.loc 1 498 3 is_stmt 1 view .LVU85
 402              		.loc 1 498 69 is_stmt 0 view .LVU86
 403 0008 D200     		lsls	r2, r2, #3
 404              	.LVL31:
 405              		.loc 1 498 69 view .LVU87
 406 000a 02F00802 		and	r2, r2, #8
 407              		.loc 1 498 55 view .LVU88
 408 000e 42EA0112 		orr	r2, r2, r1, lsl #4
 409 0012 52B2     		sxtb	r2, r2
 410              		.loc 1 498 89 view .LVU89
 411 0014 5B00     		lsls	r3, r3, #1
 412              	.LVL32:
 413              		.loc 1 498 89 view .LVU90
 414 0016 03F00603 		and	r3, r3, #6
 415              		.loc 1 498 75 view .LVU91
 416 001a 1A43     		orrs	r2, r2, r3
 417              		.loc 1 498 3 view .LVU92
 418 001c 9DF81010 		ldrb	r1, [sp, #16]	@ zero_extendqisi2
 419              	.LVL33:
 420              		.loc 1 498 3 view .LVU93
 421 0020 01F00101 		and	r1, r1, #1
 422 0024 1143     		orrs	r1, r1, r2
 423 0026 C9B2     		uxtb	r1, r1
 424 0028 FFF7FEFF 		bl	mqtt_output_append_u8
 425              	.LVL34:
 426              		.loc 1 498 3 view .LVU94
 427 002c 07E0     		b	.L24
ARM GAS  /tmp/ccBURsTh.s 			page 17


 428              	.LVL35:
 429              	.L25:
 499:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Encode remaining length field */
 500:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   do {
 501:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_u8(rb, (r_length & 0x7f) | (r_length >= 128 ? 0x80 : 0));
 430              		.loc 1 501 49 discriminator 2 view .LVU95
 431 002e 0023     		movs	r3, #0
 432              	.L23:
 433              		.loc 1 501 49 discriminator 4 view .LVU96
 434 0030 1943     		orrs	r1, r1, r3
 435              		.loc 1 501 5 discriminator 4 view .LVU97
 436 0032 C9B2     		uxtb	r1, r1
 437 0034 2846     		mov	r0, r5
 438 0036 FFF7FEFF 		bl	mqtt_output_append_u8
 439              	.LVL36:
 502:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 440              		.loc 1 502 5 is_stmt 1 view .LVU98
 503:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } while (r_length > 0);
 441              		.loc 1 503 21 view .LVU99
 442 003a E409     		lsrs	r4, r4, #7
 443              	.LVL37:
 444              		.loc 1 503 21 is_stmt 0 view .LVU100
 445 003c 06D0     		beq	.L27
 446              	.LVL38:
 447              	.L24:
 500:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_u8(rb, (r_length & 0x7f) | (r_length >= 128 ? 0x80 : 0));
 448              		.loc 1 500 3 is_stmt 1 view .LVU101
 501:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 449              		.loc 1 501 5 view .LVU102
 501:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 450              		.loc 1 501 41 is_stmt 0 view .LVU103
 451 003e 04F07F01 		and	r1, r4, #127
 501:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 452              		.loc 1 501 49 view .LVU104
 453 0042 7F2C     		cmp	r4, #127
 454 0044 F3D9     		bls	.L25
 501:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 455              		.loc 1 501 49 discriminator 1 view .LVU105
 456 0046 6FF07F03 		mvn	r3, #127
 457 004a F1E7     		b	.L23
 458              	.LVL39:
 459              	.L27:
 504:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 460              		.loc 1 504 1 view .LVU106
 461 004c 38BD     		pop	{r3, r4, r5, pc}
 462              		.loc 1 504 1 view .LVU107
 463              		.cfi_endproc
 464              	.LFE191:
 466              		.section	.text.mqtt_incomming_suback,"ax",%progbits
 467              		.align	1
 468              		.syntax unified
 469              		.thumb
 470              		.thumb_func
 472              	mqtt_incomming_suback:
 473              	.LVL40:
 474              	.LFB196:
 505:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 18


 506:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 507:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 508:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Check output buffer space
 509:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param rb Output ring buffer
 510:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r_length Remaining length after fixed header
 511:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return 1 if message will fit, 0 if not enough buffer space
 512:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 513:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static u8_t
 514:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_output_check_space(struct mqtt_ringbuf_t *rb, u16_t r_length)
 515:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 516:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Start with length of type byte + remaining length */
 517:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t total_len = 1 + r_length;
 518:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_output_check_space: rb != NULL", rb != NULL);
 520:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 521:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Calculate number of required bytes to contain the remaining bytes field and add to total*/
 522:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   do {
 523:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     total_len++;
 524:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 525:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } while (r_length > 0);
 526:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 527:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return (total_len <= mqtt_ringbuf_free(rb));
 528:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 529:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 530:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 531:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 532:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Close connection to server
 533:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
 534:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param reason Reason for disconnection
 535:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 536:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 537:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_close(mqtt_client_t *client, mqtt_connection_status_t reason)
 538:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_close: client != NULL", client != NULL);
 540:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 541:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Bring down TCP connection if not already done */
 542:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn != NULL) {
 543:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err_t res;
 544:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_recv(client->conn, NULL);
 545:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_err(client->conn,  NULL);
 546:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_sent(client->conn, NULL);
 547:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     res = altcp_close(client->conn);
 548:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (res != ERR_OK) {
 549:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       altcp_abort(client->conn);
 550:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_close: Close err=%s\n", lwip_strerr(res)));
 551:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 552:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->conn = NULL;
 553:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 554:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 555:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Remove all pending requests */
 556:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_clear_requests(&client->pend_req_queue);
 557:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Stop cyclic timer */
 558:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   sys_untimeout(mqtt_cyclic_timer, client);
 559:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 560:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Notify upper layer of disconnection if changed state */
 561:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state != TCP_DISCONNECTED) {
 562:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 19


 563:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->conn_state = TCP_DISCONNECTED;
 564:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->connect_cb != NULL) {
 565:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->connect_cb(client, client->connect_arg, reason);
 566:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 567:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 568:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 569:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 570:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 571:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 572:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Interval timer, called every MQTT_CYCLIC_TIMER_INTERVAL seconds in MQTT_CONNECTING and MQTT_CONN
 573:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg MQTT client
 574:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 575:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 576:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_cyclic_timer(void *arg)
 577:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 578:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t restart_timer = 1;
 579:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_cyclic_timer: client != NULL", client != NULL);
 581:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 582:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == MQTT_CONNECTING) {
 583:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->cyclic_tick++;
 584:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if ((client->cyclic_tick * MQTT_CYCLIC_TIMER_INTERVAL) >= MQTT_CONNECT_TIMOUT) {
 585:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: CONNECT attempt to server timed out\n"));
 586:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Disconnect TCP */
 587:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       mqtt_close(client, MQTT_CONNECT_TIMEOUT);
 588:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       restart_timer = 0;
 589:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 590:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else if (client->conn_state == MQTT_CONNECTED) {
 591:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Handle timeout for pending requests */
 592:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_request_time_elapsed(&client->pend_req_queue, MQTT_CYCLIC_TIMER_INTERVAL);
 593:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 594:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* keep_alive > 0 means keep alive functionality shall be used */
 595:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->keep_alive > 0) {
 596:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 597:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->server_watchdog++;
 598:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* If reception from server has been idle for 1.5*keep_alive time, server is considered unres
 599:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((client->server_watchdog * MQTT_CYCLIC_TIMER_INTERVAL) > (client->keep_alive + client->ke
 600:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_cyclic_timer: Server incoming keep-alive timeout\n"));
 601:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         mqtt_close(client, MQTT_CONNECT_TIMEOUT);
 602:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         restart_timer = 0;
 603:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 604:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 605:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* If time for a keep alive message to be sent, transmission has been idle for keep_alive tim
 606:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((client->cyclic_tick * MQTT_CYCLIC_TIMER_INTERVAL) >= client->keep_alive) {
 607:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: Sending keep-alive message to server\n")
 608:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (mqtt_output_check_space(&client->output, 0) != 0) {
 609:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           mqtt_output_append_fixed_header(&client->output, MQTT_MSG_TYPE_PINGREQ, 0, 0, 0, 0);
 610:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->cyclic_tick = 0;
 611:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 612:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 613:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->cyclic_tick++;
 614:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 615:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 616:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 617:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_cyclic_timer: Timer should not be running in state %d\n", c
 618:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     restart_timer = 0;
 619:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
ARM GAS  /tmp/ccBURsTh.s 			page 20


 620:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (restart_timer) {
 621:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     sys_timeout(MQTT_CYCLIC_TIMER_INTERVAL * 1000, mqtt_cyclic_timer, arg);
 622:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 623:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 624:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 625:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 626:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 627:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Send PUBACK, PUBREC or PUBREL response message
 628:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
 629:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param msg PUBACK, PUBREC or PUBREL
 630:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param pkt_id Packet identifier
 631:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param qos QoS value
 632:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK if successful, ERR_MEM if out of memory
 633:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 634:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static err_t
 635:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** pub_ack_rec_rel_response(mqtt_client_t *client, u8_t msg, u16_t pkt_id, u8_t qos)
 636:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 637:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err = ERR_OK;
 638:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (mqtt_output_check_space(&client->output, 2)) {
 639:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_fixed_header(&client->output, msg, 0, qos, 0, 2);
 640:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_u16(&client->output, pkt_id);
 641:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_send(&client->output, client->conn);
 642:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 643:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("pub_ack_rec_rel_response: OOM creating response: %s with pkt_id
 644:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                                    mqtt_msg_type_to_str(msg), pkt_id));
 645:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = ERR_MEM;
 646:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 647:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return err;
 648:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 649:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 650:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 651:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Subscribe response from server
 652:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param r Matching request
 653:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param result Result code from server
 654:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 655:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
 656:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_incomming_suback(struct mqtt_request_t *r, u8_t result)
 657:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 475              		.loc 1 657 1 is_stmt 1 view -0
 476              		.cfi_startproc
 477              		@ args = 0, pretend = 0, frame = 0
 478              		@ frame_needed = 0, uses_anonymous_args = 0
 479              		.loc 1 657 1 is_stmt 0 view .LVU109
 480 0000 08B5     		push	{r3, lr}
 481              	.LCFI5:
 482              		.cfi_def_cfa_offset 8
 483              		.cfi_offset 3, -8
 484              		.cfi_offset 14, -4
 658:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (r->cb != NULL) {
 485              		.loc 1 658 3 is_stmt 1 view .LVU110
 486              		.loc 1 658 8 is_stmt 0 view .LVU111
 487 0002 4368     		ldr	r3, [r0, #4]
 488              		.loc 1 658 6 view .LVU112
 489 0004 23B1     		cbz	r3, .L28
 659:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r->cb(r->arg, result < 3 ? ERR_OK : ERR_ABRT);
 490              		.loc 1 659 5 is_stmt 1 view .LVU113
 491 0006 8068     		ldr	r0, [r0, #8]
ARM GAS  /tmp/ccBURsTh.s 			page 21


 492              	.LVL41:
 493              		.loc 1 659 5 is_stmt 0 view .LVU114
 494 0008 0229     		cmp	r1, #2
 495 000a 02D8     		bhi	.L31
 496              		.loc 1 659 5 discriminator 1 view .LVU115
 497 000c 0021     		movs	r1, #0
 498              	.LVL42:
 499              	.L30:
 500              		.loc 1 659 5 discriminator 4 view .LVU116
 501 000e 9847     		blx	r3
 502              	.LVL43:
 503              	.L28:
 660:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 661:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 504              		.loc 1 661 1 view .LVU117
 505 0010 08BD     		pop	{r3, pc}
 506              	.LVL44:
 507              	.L31:
 659:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r->cb(r->arg, result < 3 ? ERR_OK : ERR_ABRT);
 508              		.loc 1 659 5 discriminator 2 view .LVU118
 509 0012 6FF00C01 		mvn	r1, #12
 510              	.LVL45:
 659:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r->cb(r->arg, result < 3 ? ERR_OK : ERR_ABRT);
 511              		.loc 1 659 5 discriminator 2 view .LVU119
 512 0016 FAE7     		b	.L30
 513              		.cfi_endproc
 514              	.LFE196:
 516              		.section	.rodata.mqtt_create_request.str1.4,"aMS",%progbits,1
 517              		.align	2
 518              	.LC0:
 519 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c\000"
 519      6C657761 
 519      7265732F 
 519      54686972 
 519      645F5061 
 520 0032 0000     		.align	2
 521              	.LC1:
 522 0034 6D717474 		.ascii	"mqtt_create_request: r_objs != NULL\000"
 522      5F637265 
 522      6174655F 
 522      72657175 
 522      6573743A 
 523              		.align	2
 524              	.LC2:
 525 0058 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 525      7274696F 
 525      6E202225 
 525      73222066 
 525      61696C65 
 526              		.section	.text.mqtt_create_request,"ax",%progbits
 527              		.align	1
 528              		.syntax unified
 529              		.thumb
 530              		.thumb_func
 532              	mqtt_create_request:
 533              	.LVL46:
 534              	.LFB180:
ARM GAS  /tmp/ccBURsTh.s 			page 22


 288:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r = NULL;
 535              		.loc 1 288 1 is_stmt 1 view -0
 536              		.cfi_startproc
 537              		@ args = 4, pretend = 0, frame = 0
 538              		@ frame_needed = 0, uses_anonymous_args = 0
 288:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r = NULL;
 539              		.loc 1 288 1 is_stmt 0 view .LVU121
 540 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 541              	.LCFI6:
 542              		.cfi_def_cfa_offset 24
 543              		.cfi_offset 3, -24
 544              		.cfi_offset 4, -20
 545              		.cfi_offset 5, -16
 546              		.cfi_offset 6, -12
 547              		.cfi_offset 7, -8
 548              		.cfi_offset 14, -4
 549 0002 0D46     		mov	r5, r1
 550 0004 1646     		mov	r6, r2
 551 0006 1F46     		mov	r7, r3
 289:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 552              		.loc 1 289 3 is_stmt 1 view .LVU122
 553              	.LVL47:
 290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_create_request: r_objs != NULL", r_objs != NULL);
 554              		.loc 1 290 3 view .LVU123
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 555              		.loc 1 291 3 view .LVU124
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 556              		.loc 1 291 3 view .LVU125
 557 0008 0446     		mov	r4, r0
 558 000a 10B1     		cbz	r0, .L40
 559              	.LVL48:
 560              	.L34:
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 561              		.loc 1 291 3 discriminator 3 view .LVU126
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 562              		.loc 1 291 3 discriminator 3 view .LVU127
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 563              		.loc 1 292 3 view .LVU128
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 564              		.loc 1 292 10 is_stmt 0 view .LVU129
 565 000c 4FF0000C 		mov	ip, #0
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 566              		.loc 1 292 3 view .LVU130
 567 0010 0BE0     		b	.L35
 568              	.LVL49:
 569              	.L40:
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 570              		.loc 1 291 3 is_stmt 1 discriminator 1 view .LVU131
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 571              		.loc 1 291 3 discriminator 1 view .LVU132
 572 0012 104B     		ldr	r3, .L42
 573              	.LVL50:
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 574              		.loc 1 291 3 is_stmt 0 discriminator 1 view .LVU133
 575 0014 40F22312 		movw	r2, #291
 576              	.LVL51:
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
ARM GAS  /tmp/ccBURsTh.s 			page 23


 577              		.loc 1 291 3 discriminator 1 view .LVU134
 578 0018 0F49     		ldr	r1, .L42+4
 579              	.LVL52:
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 580              		.loc 1 291 3 discriminator 1 view .LVU135
 581 001a 1048     		ldr	r0, .L42+8
 582              	.LVL53:
 291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 583              		.loc 1 291 3 discriminator 1 view .LVU136
 584 001c FFF7FEFF 		bl	printf
 585              	.LVL54:
 586 0020 F4E7     		b	.L34
 587              	.LVL55:
 588              	.L36:
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 589              		.loc 1 292 32 is_stmt 1 discriminator 2 view .LVU137
 590 0022 0CF1010C 		add	ip, ip, #1
 591              	.LVL56:
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 592              		.loc 1 292 32 is_stmt 0 discriminator 2 view .LVU138
 593 0026 5FFA8CFC 		uxtb	ip, ip
 594              	.LVL57:
 595              	.L35:
 292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item point to itself if not in use */
 596              		.loc 1 292 17 is_stmt 1 discriminator 1 view .LVU139
 597 002a AC45     		cmp	ip, r5
 598 002c 0FD2     		bcs	.L41
 294:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r = &r_objs[n];
 599              		.loc 1 294 5 view .LVU140
 294:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r = &r_objs[n];
 600              		.loc 1 294 15 is_stmt 0 view .LVU141
 601 002e 4FEA0C1E 		lsl	lr, ip, #4
 602 0032 04EB0C10 		add	r0, r4, ip, lsl #4
 294:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r = &r_objs[n];
 603              		.loc 1 294 18 view .LVU142
 604 0036 54F80E30 		ldr	r3, [r4, lr]
 294:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r = &r_objs[n];
 605              		.loc 1 294 8 view .LVU143
 606 003a 9842     		cmp	r0, r3
 607 003c F1D1     		bne	.L36
 295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->next = NULL;
 608              		.loc 1 295 7 is_stmt 1 view .LVU144
 609              	.LVL58:
 296:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->cb = cb;
 610              		.loc 1 296 7 view .LVU145
 296:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->cb = cb;
 611              		.loc 1 296 15 is_stmt 0 view .LVU146
 612 003e 0023     		movs	r3, #0
 613 0040 44F80E30 		str	r3, [r4, lr]
 297:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->arg = arg;
 614              		.loc 1 297 7 is_stmt 1 view .LVU147
 297:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->arg = arg;
 615              		.loc 1 297 13 is_stmt 0 view .LVU148
 616 0044 4760     		str	r7, [r0, #4]
 298:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->pkt_id = pkt_id;
 617              		.loc 1 298 7 is_stmt 1 view .LVU149
 298:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       r->pkt_id = pkt_id;
ARM GAS  /tmp/ccBURsTh.s 			page 24


 618              		.loc 1 298 14 is_stmt 0 view .LVU150
 619 0046 069B     		ldr	r3, [sp, #24]
 620 0048 8360     		str	r3, [r0, #8]
 299:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 621              		.loc 1 299 7 is_stmt 1 view .LVU151
 299:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 622              		.loc 1 299 17 is_stmt 0 view .LVU152
 623 004a 8681     		strh	r6, [r0, #12]	@ movhi
 300:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 624              		.loc 1 300 7 is_stmt 1 view .LVU153
 625 004c 00E0     		b	.L33
 626              	.LVL59:
 627              	.L41:
 289:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 628              		.loc 1 289 26 is_stmt 0 view .LVU154
 629 004e 0020     		movs	r0, #0
 303:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 630              		.loc 1 303 3 is_stmt 1 view .LVU155
 631              	.LVL60:
 632              	.L33:
 304:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 633              		.loc 1 304 1 is_stmt 0 view .LVU156
 634 0050 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 635              	.LVL61:
 636              	.L43:
 304:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 637              		.loc 1 304 1 view .LVU157
 638 0052 00BF     		.align	2
 639              	.L42:
 640 0054 00000000 		.word	.LC0
 641 0058 34000000 		.word	.LC1
 642 005c 58000000 		.word	.LC2
 643              		.cfi_endproc
 644              	.LFE180:
 646              		.section	.rodata.mqtt_output_check_space.str1.4,"aMS",%progbits,1
 647              		.align	2
 648              	.LC3:
 649 0000 6D717474 		.ascii	"mqtt_output_check_space: rb != NULL\000"
 649      5F6F7574 
 649      7075745F 
 649      63686563 
 649      6B5F7370 
 650              		.section	.text.mqtt_output_check_space,"ax",%progbits
 651              		.align	1
 652              		.syntax unified
 653              		.thumb
 654              		.thumb_func
 656              	mqtt_output_check_space:
 657              	.LVL62:
 658              	.LFB192:
 515:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Start with length of type byte + remaining length */
 659              		.loc 1 515 1 is_stmt 1 view -0
 660              		.cfi_startproc
 661              		@ args = 0, pretend = 0, frame = 0
 662              		@ frame_needed = 0, uses_anonymous_args = 0
 515:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Start with length of type byte + remaining length */
 663              		.loc 1 515 1 is_stmt 0 view .LVU159
ARM GAS  /tmp/ccBURsTh.s 			page 25


 664 0000 70B5     		push	{r4, r5, r6, lr}
 665              	.LCFI7:
 666              		.cfi_def_cfa_offset 16
 667              		.cfi_offset 4, -16
 668              		.cfi_offset 5, -12
 669              		.cfi_offset 6, -8
 670              		.cfi_offset 14, -4
 671 0002 0D46     		mov	r5, r1
 517:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 672              		.loc 1 517 3 is_stmt 1 view .LVU160
 517:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 673              		.loc 1 517 9 is_stmt 0 view .LVU161
 674 0004 4C1C     		adds	r4, r1, #1
 675 0006 A4B2     		uxth	r4, r4
 676              	.LVL63:
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 677              		.loc 1 519 3 is_stmt 1 view .LVU162
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 678              		.loc 1 519 3 view .LVU163
 679 0008 0646     		mov	r6, r0
 680 000a 68B1     		cbz	r0, .L48
 681              	.LVL64:
 682              	.L46:
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 683              		.loc 1 519 3 discriminator 3 view .LVU164
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 684              		.loc 1 519 3 discriminator 3 view .LVU165
 522:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     total_len++;
 685              		.loc 1 522 3 view .LVU166
 523:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 686              		.loc 1 523 5 view .LVU167
 523:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 687              		.loc 1 523 14 is_stmt 0 view .LVU168
 688 000c 0134     		adds	r4, r4, #1
 689              	.LVL65:
 523:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     r_length >>= 7;
 690              		.loc 1 523 14 view .LVU169
 691 000e A4B2     		uxth	r4, r4
 692              	.LVL66:
 524:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } while (r_length > 0);
 693              		.loc 1 524 5 is_stmt 1 view .LVU170
 525:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 694              		.loc 1 525 21 discriminator 1 view .LVU171
 695 0010 ED09     		lsrs	r5, r5, #7
 696              	.LVL67:
 525:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 697              		.loc 1 525 21 is_stmt 0 discriminator 1 view .LVU172
 698 0012 FBD1     		bne	.L46
 527:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 699              		.loc 1 527 3 is_stmt 1 view .LVU173
 527:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 700              		.loc 1 527 24 is_stmt 0 view .LVU174
 701 0014 3046     		mov	r0, r6
 702 0016 FFF7FEFF 		bl	mqtt_ringbuf_len
 703              	.LVL68:
 527:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 704              		.loc 1 527 24 discriminator 1 view .LVU175
ARM GAS  /tmp/ccBURsTh.s 			page 26


 705 001a C0F58070 		rsb	r0, r0, #256
 528:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 706              		.loc 1 528 1 view .LVU176
 707 001e 8442     		cmp	r4, r0
 708 0020 CCBF     		ite	gt
 709 0022 0020     		movgt	r0, #0
 710 0024 0120     		movle	r0, #1
 711 0026 70BD     		pop	{r4, r5, r6, pc}
 712              	.LVL69:
 713              	.L48:
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 714              		.loc 1 519 3 is_stmt 1 discriminator 1 view .LVU177
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 715              		.loc 1 519 3 discriminator 1 view .LVU178
 716 0028 034B     		ldr	r3, .L49
 717 002a 40F20722 		movw	r2, #519
 718 002e 0349     		ldr	r1, .L49+4
 719              	.LVL70:
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 720              		.loc 1 519 3 is_stmt 0 discriminator 1 view .LVU179
 721 0030 0348     		ldr	r0, .L49+8
 722              	.LVL71:
 519:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 723              		.loc 1 519 3 discriminator 1 view .LVU180
 724 0032 FFF7FEFF 		bl	printf
 725              	.LVL72:
 726 0036 E9E7     		b	.L46
 727              	.L50:
 728              		.align	2
 729              	.L49:
 730 0038 00000000 		.word	.LC0
 731 003c 00000000 		.word	.LC3
 732 0040 58000000 		.word	.LC2
 733              		.cfi_endproc
 734              	.LFE192:
 736              		.section	.rodata.mqtt_append_request.str1.4,"aMS",%progbits,1
 737              		.align	2
 738              	.LC4:
 739 0000 6D717474 		.ascii	"mqtt_append_request: tail != NULL\000"
 739      5F617070 
 739      656E645F 
 739      72657175 
 739      6573743A 
 740 0022 0000     		.align	2
 741              	.LC5:
 742 0024 6D717474 		.ascii	"mqtt_append_request: time_before <= MQTT_REQ_TIMEOU"
 742      5F617070 
 742      656E645F 
 742      72657175 
 742      6573743A 
 743 0057 5400     		.ascii	"T\000"
 744              		.section	.text.mqtt_append_request,"ax",%progbits
 745              		.align	1
 746              		.syntax unified
 747              		.thumb
 748              		.thumb_func
 750              	mqtt_append_request:
ARM GAS  /tmp/ccBURsTh.s 			page 27


 751              	.LVL73:
 752              	.LFB181:
 314:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *head = NULL;
 753              		.loc 1 314 1 is_stmt 1 view -0
 754              		.cfi_startproc
 755              		@ args = 0, pretend = 0, frame = 0
 756              		@ frame_needed = 0, uses_anonymous_args = 0
 314:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *head = NULL;
 757              		.loc 1 314 1 is_stmt 0 view .LVU182
 758 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 759              	.LCFI8:
 760              		.cfi_def_cfa_offset 24
 761              		.cfi_offset 3, -24
 762              		.cfi_offset 4, -20
 763              		.cfi_offset 5, -16
 764              		.cfi_offset 6, -12
 765              		.cfi_offset 7, -8
 766              		.cfi_offset 14, -4
 767 0002 0E46     		mov	r6, r1
 315:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   s16_t time_before = 0;
 768              		.loc 1 315 3 is_stmt 1 view .LVU183
 769              	.LVL74:
 316:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter;
 770              		.loc 1 316 3 view .LVU184
 317:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 771              		.loc 1 317 3 view .LVU185
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 772              		.loc 1 319 3 view .LVU186
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 773              		.loc 1 319 3 view .LVU187
 774 0004 0746     		mov	r7, r0
 775 0006 18B1     		cbz	r0, .L59
 776              	.LVL75:
 777              	.L52:
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 778              		.loc 1 319 3 discriminator 3 view .LVU188
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 779              		.loc 1 319 3 discriminator 3 view .LVU189
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 780              		.loc 1 322 3 view .LVU190
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 781              		.loc 1 322 13 is_stmt 0 view .LVU191
 782 0008 3B68     		ldr	r3, [r7]
 783              	.LVL76:
 316:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter;
 784              		.loc 1 316 9 view .LVU192
 785 000a 0024     		movs	r4, #0
 315:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   s16_t time_before = 0;
 786              		.loc 1 315 26 view .LVU193
 787 000c 2546     		mov	r5, r4
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 788              		.loc 1 322 3 view .LVU194
 789 000e 0CE0     		b	.L53
 790              	.LVL77:
 791              	.L59:
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 792              		.loc 1 319 3 is_stmt 1 discriminator 1 view .LVU195
ARM GAS  /tmp/ccBURsTh.s 			page 28


 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 793              		.loc 1 319 3 discriminator 1 view .LVU196
 794 0010 104B     		ldr	r3, .L62
 795 0012 40F23F12 		movw	r2, #319
 796 0016 1049     		ldr	r1, .L62+4
 797              	.LVL78:
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 798              		.loc 1 319 3 is_stmt 0 discriminator 1 view .LVU197
 799 0018 1048     		ldr	r0, .L62+8
 800              	.LVL79:
 319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 801              		.loc 1 319 3 discriminator 1 view .LVU198
 802 001a FFF7FEFF 		bl	printf
 803              	.LVL80:
 804 001e F3E7     		b	.L52
 805              	.LVL81:
 806              	.L54:
 323:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     head = iter;
 807              		.loc 1 323 5 is_stmt 1 view .LVU199
 323:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     head = iter;
 808              		.loc 1 323 24 is_stmt 0 view .LVU200
 809 0020 DA89     		ldrh	r2, [r3, #14]
 323:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     head = iter;
 810              		.loc 1 323 17 view .LVU201
 811 0022 1444     		add	r4, r4, r2
 812              	.LVL82:
 323:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     head = iter;
 813              		.loc 1 323 17 view .LVU202
 814 0024 24B2     		sxth	r4, r4
 815              	.LVL83:
 324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 816              		.loc 1 324 5 is_stmt 1 view .LVU203
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 817              		.loc 1 322 41 discriminator 3 view .LVU204
 324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 818              		.loc 1 324 10 is_stmt 0 view .LVU205
 819 0026 1D46     		mov	r5, r3
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 820              		.loc 1 322 41 discriminator 3 view .LVU206
 821 0028 1B68     		ldr	r3, [r3]
 822              	.LVL84:
 823              	.L53:
 322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     time_before += iter->timeout_diff;
 824              		.loc 1 322 27 is_stmt 1 discriminator 1 view .LVU207
 825 002a 002B     		cmp	r3, #0
 826 002c F8D1     		bne	.L54
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 827              		.loc 1 327 3 view .LVU208
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 828              		.loc 1 327 3 view .LVU209
 829 002e 1E2C     		cmp	r4, #30
 830 0030 05DC     		bgt	.L60
 831              	.LVL85:
 832              	.L55:
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 833              		.loc 1 327 3 discriminator 3 view .LVU210
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
ARM GAS  /tmp/ccBURsTh.s 			page 29


 834              		.loc 1 327 3 discriminator 3 view .LVU211
 328:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (head == NULL) {
 835              		.loc 1 328 3 view .LVU212
 328:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (head == NULL) {
 836              		.loc 1 328 38 is_stmt 0 view .LVU213
 837 0032 C4F11E04 		rsb	r4, r4, #30
 838              	.LVL86:
 328:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (head == NULL) {
 839              		.loc 1 328 19 view .LVU214
 840 0036 F481     		strh	r4, [r6, #14]	@ movhi
 329:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     *tail = r;
 841              		.loc 1 329 3 is_stmt 1 view .LVU215
 329:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     *tail = r;
 842              		.loc 1 329 6 is_stmt 0 view .LVU216
 843 0038 4DB1     		cbz	r5, .L61
 332:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 844              		.loc 1 332 5 is_stmt 1 view .LVU217
 332:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 845              		.loc 1 332 16 is_stmt 0 view .LVU218
 846 003a 2E60     		str	r6, [r5]
 847              	.L51:
 334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 848              		.loc 1 334 1 view .LVU219
 849 003c F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 850              	.LVL87:
 851              	.L60:
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 852              		.loc 1 327 3 is_stmt 1 discriminator 1 view .LVU220
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 853              		.loc 1 327 3 discriminator 1 view .LVU221
 854 003e 054B     		ldr	r3, .L62
 855              	.LVL88:
 327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r->timeout_diff = MQTT_REQ_TIMEOUT - time_before;
 856              		.loc 1 327 3 is_stmt 0 discriminator 1 view .LVU222
 857 0040 40F24712 		movw	r2, #327
 858 0044 0649     		ldr	r1, .L62+12
 859 0046 0548     		ldr	r0, .L62+8
 860 0048 FFF7FEFF 		bl	printf
 861              	.LVL89:
 862 004c F1E7     		b	.L55
 863              	.LVL90:
 864              	.L61:
 330:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 865              		.loc 1 330 5 is_stmt 1 view .LVU223
 330:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 866              		.loc 1 330 11 is_stmt 0 view .LVU224
 867 004e 3E60     		str	r6, [r7]
 868 0050 F4E7     		b	.L51
 869              	.L63:
 870 0052 00BF     		.align	2
 871              	.L62:
 872 0054 00000000 		.word	.LC0
 873 0058 00000000 		.word	.LC4
 874 005c 58000000 		.word	.LC2
 875 0060 24000000 		.word	.LC5
 876              		.cfi_endproc
 877              	.LFE181:
ARM GAS  /tmp/ccBURsTh.s 			page 30


 879              		.section	.rodata.mqtt_ringbuf_advance_get_idx.str1.4,"aMS",%progbits,1
 880              		.align	2
 881              	.LC6:
 882 0000 6D717474 		.ascii	"mqtt_ringbuf_advance_get_idx: len < MQTT_OUTPUT_RIN"
 882      5F72696E 
 882      67627566 
 882      5F616476 
 882      616E6365 
 883 0033 47425546 		.ascii	"GBUF_SIZE\000"
 883      5F53495A 
 883      4500
 884              		.section	.text.mqtt_ringbuf_advance_get_idx,"ax",%progbits
 885              		.align	1
 886              		.syntax unified
 887              		.thumb
 888              		.thumb_func
 890              	mqtt_ringbuf_advance_get_idx:
 891              	.LVL91:
 892              	.LFB177:
 202:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_ringbuf_advance_get_idx: len < MQTT_OUTPUT_RINGBUF_SIZE", len < MQTT_OUTPUT_RIN
 893              		.loc 1 202 1 is_stmt 1 view -0
 894              		.cfi_startproc
 895              		@ args = 0, pretend = 0, frame = 0
 896              		@ frame_needed = 0, uses_anonymous_args = 0
 202:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_ringbuf_advance_get_idx: len < MQTT_OUTPUT_RINGBUF_SIZE", len < MQTT_OUTPUT_RIN
 897              		.loc 1 202 1 is_stmt 0 view .LVU226
 898 0000 38B5     		push	{r3, r4, r5, lr}
 899              	.LCFI9:
 900              		.cfi_def_cfa_offset 16
 901              		.cfi_offset 3, -16
 902              		.cfi_offset 4, -12
 903              		.cfi_offset 5, -8
 904              		.cfi_offset 14, -4
 905 0002 0446     		mov	r4, r0
 906 0004 0D46     		mov	r5, r1
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 907              		.loc 1 203 3 is_stmt 1 view .LVU227
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 908              		.loc 1 203 3 view .LVU228
 909 0006 FF29     		cmp	r1, #255
 910 0008 09D8     		bhi	.L68
 911              	.LVL92:
 912              	.L65:
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 913              		.loc 1 203 3 discriminator 3 view .LVU229
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 914              		.loc 1 203 3 discriminator 3 view .LVU230
 205:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (rb->get >= MQTT_OUTPUT_RINGBUF_SIZE) {
 915              		.loc 1 205 3 view .LVU231
 205:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (rb->get >= MQTT_OUTPUT_RINGBUF_SIZE) {
 916              		.loc 1 205 5 is_stmt 0 view .LVU232
 917 000a 6188     		ldrh	r1, [r4, #2]
 205:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (rb->get >= MQTT_OUTPUT_RINGBUF_SIZE) {
 918              		.loc 1 205 11 view .LVU233
 919 000c 2944     		add	r1, r1, r5
 920 000e 89B2     		uxth	r1, r1
 921 0010 6180     		strh	r1, [r4, #2]	@ movhi
ARM GAS  /tmp/ccBURsTh.s 			page 31


 206:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     rb->get = rb->get - MQTT_OUTPUT_RINGBUF_SIZE;
 922              		.loc 1 206 3 is_stmt 1 view .LVU234
 206:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     rb->get = rb->get - MQTT_OUTPUT_RINGBUF_SIZE;
 923              		.loc 1 206 6 is_stmt 0 view .LVU235
 924 0012 FF29     		cmp	r1, #255
 925 0014 02D9     		bls	.L64
 207:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 926              		.loc 1 207 5 is_stmt 1 view .LVU236
 207:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 927              		.loc 1 207 23 is_stmt 0 view .LVU237
 928 0016 A1F58071 		sub	r1, r1, #256
 207:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 929              		.loc 1 207 13 view .LVU238
 930 001a 6180     		strh	r1, [r4, #2]	@ movhi
 931              	.L64:
 209:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 932              		.loc 1 209 1 view .LVU239
 933 001c 38BD     		pop	{r3, r4, r5, pc}
 934              	.LVL93:
 935              	.L68:
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 936              		.loc 1 203 3 is_stmt 1 discriminator 1 view .LVU240
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 937              		.loc 1 203 3 discriminator 1 view .LVU241
 938 001e 034B     		ldr	r3, .L69
 939 0020 CB22     		movs	r2, #203
 940 0022 0349     		ldr	r1, .L69+4
 941              	.LVL94:
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 942              		.loc 1 203 3 is_stmt 0 discriminator 1 view .LVU242
 943 0024 0348     		ldr	r0, .L69+8
 944              	.LVL95:
 203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 945              		.loc 1 203 3 discriminator 1 view .LVU243
 946 0026 FFF7FEFF 		bl	printf
 947              	.LVL96:
 948 002a EEE7     		b	.L65
 949              	.L70:
 950              		.align	2
 951              	.L69:
 952 002c 00000000 		.word	.LC0
 953 0030 00000000 		.word	.LC6
 954 0034 58000000 		.word	.LC2
 955              		.cfi_endproc
 956              	.LFE177:
 958              		.section	.rodata.mqtt_request_time_elapsed.str1.4,"aMS",%progbits,1
 959              		.align	2
 960              	.LC7:
 961 0000 6D717474 		.ascii	"mqtt_request_time_elapsed: tail != NULL\000"
 961      5F726571 
 961      75657374 
 961      5F74696D 
 961      655F656C 
 962              		.section	.text.mqtt_request_time_elapsed,"ax",%progbits
 963              		.align	1
 964              		.syntax unified
 965              		.thumb
ARM GAS  /tmp/ccBURsTh.s 			page 32


 966              		.thumb_func
 968              	mqtt_request_time_elapsed:
 969              	.LVL97:
 970              	.LFB184:
 392:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r;
 971              		.loc 1 392 1 is_stmt 1 view -0
 972              		.cfi_startproc
 973              		@ args = 0, pretend = 0, frame = 0
 974              		@ frame_needed = 0, uses_anonymous_args = 0
 392:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r;
 975              		.loc 1 392 1 is_stmt 0 view .LVU245
 976 0000 70B5     		push	{r4, r5, r6, lr}
 977              	.LCFI10:
 978              		.cfi_def_cfa_offset 16
 979              		.cfi_offset 4, -16
 980              		.cfi_offset 5, -12
 981              		.cfi_offset 6, -8
 982              		.cfi_offset 14, -4
 983 0002 0D46     		mov	r5, r1
 393:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_request_time_elapsed: tail != NULL", tail != NULL);
 984              		.loc 1 393 3 is_stmt 1 view .LVU246
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 985              		.loc 1 394 3 view .LVU247
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 986              		.loc 1 394 3 view .LVU248
 987 0004 0646     		mov	r6, r0
 988 0006 08B1     		cbz	r0, .L80
 989              	.LVL98:
 990              	.L72:
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 991              		.loc 1 394 3 discriminator 3 view .LVU249
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 992              		.loc 1 394 3 discriminator 3 view .LVU250
 395:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   while (t > 0 && r != NULL) {
 993              		.loc 1 395 3 view .LVU251
 395:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   while (t > 0 && r != NULL) {
 994              		.loc 1 395 5 is_stmt 0 view .LVU252
 995 0008 3468     		ldr	r4, [r6]
 996              	.LVL99:
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 997              		.loc 1 396 3 is_stmt 1 view .LVU253
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 998              		.loc 1 396 9 is_stmt 0 view .LVU254
 999 000a 0BE0     		b	.L73
 1000              	.LVL100:
 1001              	.L80:
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 1002              		.loc 1 394 3 is_stmt 1 discriminator 1 view .LVU255
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 1003              		.loc 1 394 3 discriminator 1 view .LVU256
 1004 000c 124B     		ldr	r3, .L81
 1005 000e 4FF4C572 		mov	r2, #394
 1006 0012 1249     		ldr	r1, .L81+4
 1007              	.LVL101:
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 1008              		.loc 1 394 3 is_stmt 0 discriminator 1 view .LVU257
 1009 0014 1248     		ldr	r0, .L81+8
ARM GAS  /tmp/ccBURsTh.s 			page 33


 1010              	.LVL102:
 394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = *tail;
 1011              		.loc 1 394 3 discriminator 1 view .LVU258
 1012 0016 FFF7FEFF 		bl	printf
 1013              	.LVL103:
 1014 001a F5E7     		b	.L72
 1015              	.LVL104:
 1016              	.L75:
 405:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Tail might be be modified in callback, so re-read it in every iteration */
 1017              		.loc 1 405 7 is_stmt 1 view .LVU259
 1018 001c 2046     		mov	r0, r4
 1019 001e FFF7FEFF 		bl	mqtt_delete_request
 1020              	.LVL105:
 407:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 1021              		.loc 1 407 7 view .LVU260
 407:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 1022              		.loc 1 407 9 is_stmt 0 view .LVU261
 1023 0022 3468     		ldr	r4, [r6]
 1024              	.LVL106:
 1025              	.L73:
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 1026              		.loc 1 396 16 is_stmt 1 view .LVU262
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 1027              		.loc 1 396 21 is_stmt 0 view .LVU263
 1028 0024 231E     		subs	r3, r4, #0
 1029 0026 18BF     		it	ne
 1030 0028 0123     		movne	r3, #1
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 1031              		.loc 1 396 16 view .LVU264
 1032 002a A5B1     		cbz	r5, .L71
 396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (t >= r->timeout_diff) {
 1033              		.loc 1 396 16 view .LVU265
 1034 002c 9BB1     		cbz	r3, .L71
 397:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t -= (u8_t)r->timeout_diff;
 1035              		.loc 1 397 5 is_stmt 1 view .LVU266
 397:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t -= (u8_t)r->timeout_diff;
 1036              		.loc 1 397 15 is_stmt 0 view .LVU267
 1037 002e E389     		ldrh	r3, [r4, #14]
 397:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t -= (u8_t)r->timeout_diff;
 1038              		.loc 1 397 8 view .LVU268
 1039 0030 9D42     		cmp	r5, r3
 1040 0032 0CD3     		bcc	.L74
 398:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Unchain */
 1041              		.loc 1 398 7 is_stmt 1 view .LVU269
 398:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Unchain */
 1042              		.loc 1 398 12 is_stmt 0 view .LVU270
 1043 0034 DBB2     		uxtb	r3, r3
 398:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Unchain */
 1044              		.loc 1 398 9 view .LVU271
 1045 0036 EB1A     		subs	r3, r5, r3
 1046 0038 DDB2     		uxtb	r5, r3
 1047              	.LVL107:
 400:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Notify upper layer about timeout */
 1048              		.loc 1 400 7 is_stmt 1 view .LVU272
 400:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Notify upper layer about timeout */
 1049              		.loc 1 400 16 is_stmt 0 view .LVU273
 1050 003a 2368     		ldr	r3, [r4]
ARM GAS  /tmp/ccBURsTh.s 			page 34


 400:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Notify upper layer about timeout */
 1051              		.loc 1 400 13 view .LVU274
 1052 003c 3360     		str	r3, [r6]
 402:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_TIMEOUT);
 1053              		.loc 1 402 7 is_stmt 1 view .LVU275
 402:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_TIMEOUT);
 1054              		.loc 1 402 12 is_stmt 0 view .LVU276
 1055 003e 6368     		ldr	r3, [r4, #4]
 402:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_TIMEOUT);
 1056              		.loc 1 402 10 view .LVU277
 1057 0040 002B     		cmp	r3, #0
 1058 0042 EBD0     		beq	.L75
 403:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 1059              		.loc 1 403 9 is_stmt 1 view .LVU278
 1060 0044 6FF00201 		mvn	r1, #2
 1061 0048 A068     		ldr	r0, [r4, #8]
 1062 004a 9847     		blx	r3
 1063              	.LVL108:
 1064 004c E6E7     		b	.L75
 1065              	.L74:
 409:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t = 0;
 1066              		.loc 1 409 7 view .LVU279
 409:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       t = 0;
 1067              		.loc 1 409 23 is_stmt 0 view .LVU280
 1068 004e 5B1B     		subs	r3, r3, r5
 1069 0050 E381     		strh	r3, [r4, #14]	@ movhi
 410:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1070              		.loc 1 410 7 is_stmt 1 view .LVU281
 1071              	.LVL109:
 410:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1072              		.loc 1 410 9 is_stmt 0 view .LVU282
 1073 0052 0025     		movs	r5, #0
 1074 0054 E6E7     		b	.L73
 1075              	.LVL110:
 1076              	.L71:
 413:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1077              		.loc 1 413 1 view .LVU283
 1078 0056 70BD     		pop	{r4, r5, r6, pc}
 1079              	.LVL111:
 1080              	.L82:
 413:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1081              		.loc 1 413 1 view .LVU284
 1082              		.align	2
 1083              	.L81:
 1084 0058 00000000 		.word	.LC0
 1085 005c 00000000 		.word	.LC7
 1086 0060 58000000 		.word	.LC2
 1087              		.cfi_endproc
 1088              	.LFE184:
 1090              		.section	.rodata.mqtt_clear_requests.str1.4,"aMS",%progbits,1
 1091              		.align	2
 1092              	.LC8:
 1093 0000 6D717474 		.ascii	"mqtt_clear_requests: tail != NULL\000"
 1093      5F636C65 
 1093      61725F72 
 1093      65717565 
 1093      7374733A 
ARM GAS  /tmp/ccBURsTh.s 			page 35


 1094              		.section	.text.mqtt_clear_requests,"ax",%progbits
 1095              		.align	1
 1096              		.syntax unified
 1097              		.thumb
 1098              		.thumb_func
 1100              	mqtt_clear_requests:
 1101              	.LVL112:
 1102              	.LFB185:
 421:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter, *next;
 1103              		.loc 1 421 1 is_stmt 1 view -0
 1104              		.cfi_startproc
 1105              		@ args = 0, pretend = 0, frame = 0
 1106              		@ frame_needed = 0, uses_anonymous_args = 0
 421:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter, *next;
 1107              		.loc 1 421 1 is_stmt 0 view .LVU286
 1108 0000 38B5     		push	{r3, r4, r5, lr}
 1109              	.LCFI11:
 1110              		.cfi_def_cfa_offset 16
 1111              		.cfi_offset 3, -16
 1112              		.cfi_offset 4, -12
 1113              		.cfi_offset 5, -8
 1114              		.cfi_offset 14, -4
 422:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_clear_requests: tail != NULL", tail != NULL);
 1115              		.loc 1 422 3 is_stmt 1 view .LVU287
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 1116              		.loc 1 423 3 view .LVU288
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 1117              		.loc 1 423 3 view .LVU289
 1118 0002 0546     		mov	r5, r0
 1119 0004 08B1     		cbz	r0, .L88
 1120              	.LVL113:
 1121              	.L84:
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 1122              		.loc 1 423 3 discriminator 3 view .LVU290
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 1123              		.loc 1 423 3 discriminator 3 view .LVU291
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 1124              		.loc 1 424 3 view .LVU292
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 1125              		.loc 1 424 13 is_stmt 0 view .LVU293
 1126 0006 2868     		ldr	r0, [r5]
 1127              	.LVL114:
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 1128              		.loc 1 424 3 view .LVU294
 1129 0008 0BE0     		b	.L85
 1130              	.LVL115:
 1131              	.L88:
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 1132              		.loc 1 423 3 is_stmt 1 discriminator 1 view .LVU295
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
 1133              		.loc 1 423 3 discriminator 1 view .LVU296
 1134 000a 084B     		ldr	r3, .L89
 1135 000c 40F2A712 		movw	r2, #423
 1136 0010 0749     		ldr	r1, .L89+4
 1137 0012 0848     		ldr	r0, .L89+8
 1138              	.LVL116:
 423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (iter = *tail; iter != NULL; iter = next) {
ARM GAS  /tmp/ccBURsTh.s 			page 36


 1139              		.loc 1 423 3 is_stmt 0 discriminator 1 view .LVU297
 1140 0014 FFF7FEFF 		bl	printf
 1141              	.LVL117:
 1142 0018 F5E7     		b	.L84
 1143              	.LVL118:
 1144              	.L86:
 425:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_delete_request(iter);
 1145              		.loc 1 425 5 is_stmt 1 view .LVU298
 425:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_delete_request(iter);
 1146              		.loc 1 425 10 is_stmt 0 view .LVU299
 1147 001a 0468     		ldr	r4, [r0]
 1148              	.LVL119:
 426:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1149              		.loc 1 426 5 is_stmt 1 view .LVU300
 1150 001c FFF7FEFF 		bl	mqtt_delete_request
 1151              	.LVL120:
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 1152              		.loc 1 424 41 discriminator 3 view .LVU301
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 1153              		.loc 1 424 41 is_stmt 0 discriminator 3 view .LVU302
 1154 0020 2046     		mov	r0, r4
 1155              	.LVL121:
 1156              	.L85:
 424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     next = iter->next;
 1157              		.loc 1 424 27 is_stmt 1 discriminator 1 view .LVU303
 1158 0022 0028     		cmp	r0, #0
 1159 0024 F9D1     		bne	.L86
 428:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 1160              		.loc 1 428 3 view .LVU304
 428:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 1161              		.loc 1 428 9 is_stmt 0 view .LVU305
 1162 0026 0023     		movs	r3, #0
 1163 0028 2B60     		str	r3, [r5]
 429:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 1164              		.loc 1 429 1 view .LVU306
 1165 002a 38BD     		pop	{r3, r4, r5, pc}
 1166              	.LVL122:
 1167              	.L90:
 429:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 1168              		.loc 1 429 1 view .LVU307
 1169              		.align	2
 1170              	.L89:
 1171 002c 00000000 		.word	.LC0
 1172 0030 00000000 		.word	.LC8
 1173 0034 58000000 		.word	.LC2
 1174              		.cfi_endproc
 1175              	.LFE185:
 1177              		.section	.rodata.mqtt_take_request.str1.4,"aMS",%progbits,1
 1178              		.align	2
 1179              	.LC9:
 1180 0000 6D717474 		.ascii	"mqtt_take_request: tail != NULL\000"
 1180      5F74616B 
 1180      655F7265 
 1180      71756573 
 1180      743A2074 
 1181              		.section	.text.mqtt_take_request,"ax",%progbits
 1182              		.align	1
ARM GAS  /tmp/ccBURsTh.s 			page 37


 1183              		.syntax unified
 1184              		.thumb
 1185              		.thumb_func
 1187              	mqtt_take_request:
 1188              	.LVL123:
 1189              	.LFB183:
 357:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter = NULL, *prev = NULL;
 1190              		.loc 1 357 1 is_stmt 1 view -0
 1191              		.cfi_startproc
 1192              		@ args = 0, pretend = 0, frame = 0
 1193              		@ frame_needed = 0, uses_anonymous_args = 0
 357:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *iter = NULL, *prev = NULL;
 1194              		.loc 1 357 1 is_stmt 0 view .LVU309
 1195 0000 38B5     		push	{r3, r4, r5, lr}
 1196              	.LCFI12:
 1197              		.cfi_def_cfa_offset 16
 1198              		.cfi_offset 3, -16
 1199              		.cfi_offset 4, -12
 1200              		.cfi_offset 5, -8
 1201              		.cfi_offset 14, -4
 1202 0002 0C46     		mov	r4, r1
 358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_take_request: tail != NULL", tail != NULL);
 1203              		.loc 1 358 3 is_stmt 1 view .LVU310
 1204              	.LVL124:
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1205              		.loc 1 359 3 view .LVU311
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1206              		.loc 1 359 3 view .LVU312
 1207 0004 0546     		mov	r5, r0
 1208 0006 10B1     		cbz	r0, .L101
 1209              	.LVL125:
 1210              	.L92:
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1211              		.loc 1 359 3 discriminator 3 view .LVU313
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1212              		.loc 1 359 3 discriminator 3 view .LVU314
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 1213              		.loc 1 361 3 view .LVU315
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 1214              		.loc 1 361 13 is_stmt 0 view .LVU316
 1215 0008 2868     		ldr	r0, [r5]
 1216              	.LVL126:
 358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_take_request: tail != NULL", tail != NULL);
 1217              		.loc 1 358 40 view .LVU317
 1218 000a 0022     		movs	r2, #0
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 1219              		.loc 1 361 3 view .LVU318
 1220 000c 09E0     		b	.L93
 1221              	.LVL127:
 1222              	.L101:
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1223              		.loc 1 359 3 is_stmt 1 discriminator 1 view .LVU319
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1224              		.loc 1 359 3 discriminator 1 view .LVU320
 1225 000e 0F4B     		ldr	r3, .L104
 1226 0010 40F26712 		movw	r2, #359
 1227 0014 0E49     		ldr	r1, .L104+4
ARM GAS  /tmp/ccBURsTh.s 			page 38


 1228              	.LVL128:
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1229              		.loc 1 359 3 is_stmt 0 discriminator 1 view .LVU321
 1230 0016 0F48     		ldr	r0, .L104+8
 1231              	.LVL129:
 359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Search all request for pkt_id */
 1232              		.loc 1 359 3 discriminator 1 view .LVU322
 1233 0018 FFF7FEFF 		bl	printf
 1234              	.LVL130:
 1235 001c F4E7     		b	.L92
 1236              	.LVL131:
 1237              	.L102:
 365:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1238              		.loc 1 365 5 is_stmt 1 view .LVU323
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 1239              		.loc 1 361 41 discriminator 2 view .LVU324
 365:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1240              		.loc 1 365 10 is_stmt 0 view .LVU325
 1241 001e 0246     		mov	r2, r0
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 1242              		.loc 1 361 41 discriminator 2 view .LVU326
 1243 0020 0068     		ldr	r0, [r0]
 1244              	.LVL132:
 1245              	.L93:
 361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (iter->pkt_id == pkt_id) {
 1246              		.loc 1 361 27 is_stmt 1 discriminator 1 view .LVU327
 1247 0022 10B1     		cbz	r0, .L94
 362:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 1248              		.loc 1 362 5 view .LVU328
 362:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 1249              		.loc 1 362 13 is_stmt 0 view .LVU329
 1250 0024 8389     		ldrh	r3, [r0, #12]
 362:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       break;
 1251              		.loc 1 362 8 view .LVU330
 1252 0026 A342     		cmp	r3, r4
 1253 0028 F9D1     		bne	.L102
 1254              	.L94:
 369:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* unchain */
 1255              		.loc 1 369 3 is_stmt 1 view .LVU331
 369:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* unchain */
 1256              		.loc 1 369 6 is_stmt 0 view .LVU332
 1257 002a 50B1     		cbz	r0, .L91
 371:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       *tail = iter->next;
 1258              		.loc 1 371 5 is_stmt 1 view .LVU333
 371:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       *tail = iter->next;
 1259              		.loc 1 371 8 is_stmt 0 view .LVU334
 1260 002c 52B1     		cbz	r2, .L103
 374:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1261              		.loc 1 374 7 is_stmt 1 view .LVU335
 374:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1262              		.loc 1 374 24 is_stmt 0 view .LVU336
 1263 002e 0368     		ldr	r3, [r0]
 374:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1264              		.loc 1 374 18 view .LVU337
 1265 0030 1360     		str	r3, [r2]
 1266              	.L98:
 377:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       iter->next->timeout_diff += iter->timeout_diff;
ARM GAS  /tmp/ccBURsTh.s 			page 39


 1267              		.loc 1 377 5 is_stmt 1 view .LVU338
 377:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       iter->next->timeout_diff += iter->timeout_diff;
 1268              		.loc 1 377 13 is_stmt 0 view .LVU339
 1269 0032 0368     		ldr	r3, [r0]
 377:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       iter->next->timeout_diff += iter->timeout_diff;
 1270              		.loc 1 377 8 view .LVU340
 1271 0034 1BB1     		cbz	r3, .L99
 378:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1272              		.loc 1 378 7 is_stmt 1 view .LVU341
 378:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1273              		.loc 1 378 17 is_stmt 0 view .LVU342
 1274 0036 DA89     		ldrh	r2, [r3, #14]
 1275              	.LVL133:
 378:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1276              		.loc 1 378 39 view .LVU343
 1277 0038 C189     		ldrh	r1, [r0, #14]
 378:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1278              		.loc 1 378 32 view .LVU344
 1279 003a 0A44     		add	r2, r2, r1
 1280 003c DA81     		strh	r2, [r3, #14]	@ movhi
 1281              	.L99:
 380:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1282              		.loc 1 380 5 is_stmt 1 view .LVU345
 380:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1283              		.loc 1 380 16 is_stmt 0 view .LVU346
 1284 003e 0023     		movs	r3, #0
 1285 0040 0360     		str	r3, [r0]
 382:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 1286              		.loc 1 382 3 is_stmt 1 view .LVU347
 1287              	.L91:
 383:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1288              		.loc 1 383 1 is_stmt 0 view .LVU348
 1289 0042 38BD     		pop	{r3, r4, r5, pc}
 1290              	.LVL134:
 1291              	.L103:
 372:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 1292              		.loc 1 372 7 is_stmt 1 view .LVU349
 372:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 1293              		.loc 1 372 19 is_stmt 0 view .LVU350
 1294 0044 0368     		ldr	r3, [r0]
 372:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 1295              		.loc 1 372 13 view .LVU351
 1296 0046 2B60     		str	r3, [r5]
 1297 0048 F3E7     		b	.L98
 1298              	.L105:
 1299 004a 00BF     		.align	2
 1300              	.L104:
 1301 004c 00000000 		.word	.LC0
 1302 0050 00000000 		.word	.LC9
 1303 0054 58000000 		.word	.LC2
 1304              		.cfi_endproc
 1305              	.LFE183:
 1307              		.section	.rodata.mqtt_init_requests.str1.4,"aMS",%progbits,1
 1308              		.align	2
 1309              	.LC10:
 1310 0000 6D717474 		.ascii	"mqtt_init_requests: r_objs != NULL\000"
 1310      5F696E69 
ARM GAS  /tmp/ccBURsTh.s 			page 40


 1310      745F7265 
 1310      71756573 
 1310      74733A20 
 1311              		.section	.text.mqtt_init_requests,"ax",%progbits
 1312              		.align	1
 1313              		.syntax unified
 1314              		.thumb
 1315              		.thumb_func
 1317              	mqtt_init_requests:
 1318              	.LVL135:
 1319              	.LFB186:
 437:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 1320              		.loc 1 437 1 is_stmt 1 view -0
 1321              		.cfi_startproc
 1322              		@ args = 0, pretend = 0, frame = 0
 1323              		@ frame_needed = 0, uses_anonymous_args = 0
 437:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 1324              		.loc 1 437 1 is_stmt 0 view .LVU353
 1325 0000 38B5     		push	{r3, r4, r5, lr}
 1326              	.LCFI13:
 1327              		.cfi_def_cfa_offset 16
 1328              		.cfi_offset 3, -16
 1329              		.cfi_offset 4, -12
 1330              		.cfi_offset 5, -8
 1331              		.cfi_offset 14, -4
 1332 0002 0C46     		mov	r4, r1
 438:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_init_requests: r_objs != NULL", r_objs != NULL);
 1333              		.loc 1 438 3 is_stmt 1 view .LVU354
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 1334              		.loc 1 439 3 view .LVU355
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 1335              		.loc 1 439 3 view .LVU356
 1336 0004 0546     		mov	r5, r0
 1337 0006 08B1     		cbz	r0, .L111
 1338              	.LVL136:
 1339              	.L107:
 437:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t n;
 1340              		.loc 1 437 1 is_stmt 0 view .LVU357
 1341 0008 0023     		movs	r3, #0
 1342 000a 0CE0     		b	.L108
 1343              	.LVL137:
 1344              	.L111:
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 1345              		.loc 1 439 3 is_stmt 1 discriminator 1 view .LVU358
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 1346              		.loc 1 439 3 discriminator 1 view .LVU359
 1347 000c 074B     		ldr	r3, .L112
 1348 000e 40F2B712 		movw	r2, #439
 1349 0012 0749     		ldr	r1, .L112+4
 1350              	.LVL138:
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 1351              		.loc 1 439 3 is_stmt 0 discriminator 1 view .LVU360
 1352 0014 0748     		ldr	r0, .L112+8
 1353              	.LVL139:
 439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   for (n = 0; n < r_objs_len; n++) {
 1354              		.loc 1 439 3 discriminator 1 view .LVU361
 1355 0016 FFF7FEFF 		bl	printf
ARM GAS  /tmp/ccBURsTh.s 			page 41


 1356              	.LVL140:
 1357 001a F5E7     		b	.L107
 1358              	.LVL141:
 1359              	.L109:
 442:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1360              		.loc 1 442 5 is_stmt 1 view .LVU362
 442:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1361              		.loc 1 442 11 is_stmt 0 view .LVU363
 1362 001c 05EB0312 		add	r2, r5, r3, lsl #4
 442:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1363              		.loc 1 442 20 view .LVU364
 1364 0020 1260     		str	r2, [r2]
 440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item pointing to itself indicates unused */
 1365              		.loc 1 440 32 is_stmt 1 discriminator 3 view .LVU365
 1366 0022 0133     		adds	r3, r3, #1
 1367              	.LVL142:
 440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item pointing to itself indicates unused */
 1368              		.loc 1 440 32 is_stmt 0 discriminator 3 view .LVU366
 1369 0024 DBB2     		uxtb	r3, r3
 1370              	.LVL143:
 1371              	.L108:
 440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Item pointing to itself indicates unused */
 1372              		.loc 1 440 17 is_stmt 1 discriminator 1 view .LVU367
 1373 0026 A342     		cmp	r3, r4
 1374 0028 F8D3     		bcc	.L109
 444:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1375              		.loc 1 444 1 is_stmt 0 view .LVU368
 1376 002a 38BD     		pop	{r3, r4, r5, pc}
 1377              	.LVL144:
 1378              	.L113:
 444:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1379              		.loc 1 444 1 view .LVU369
 1380              		.align	2
 1381              	.L112:
 1382 002c 00000000 		.word	.LC0
 1383 0030 00000000 		.word	.LC10
 1384 0034 58000000 		.word	.LC2
 1385              		.cfi_endproc
 1386              	.LFE186:
 1388              		.section	.text.mqtt_output_send,"ax",%progbits
 1389              		.align	1
 1390              		.syntax unified
 1391              		.thumb
 1392              		.thumb_func
 1394              	mqtt_output_send:
 1395              	.LVL145:
 1396              	.LFB179:
 235:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err;
 1397              		.loc 1 235 1 is_stmt 1 view -0
 1398              		.cfi_startproc
 1399              		@ args = 0, pretend = 0, frame = 0
 1400              		@ frame_needed = 0, uses_anonymous_args = 0
 235:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err;
 1401              		.loc 1 235 1 is_stmt 0 view .LVU371
 1402 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 1403              	.LCFI14:
 1404              		.cfi_def_cfa_offset 24
ARM GAS  /tmp/ccBURsTh.s 			page 42


 1405              		.cfi_offset 3, -24
 1406              		.cfi_offset 4, -20
 1407              		.cfi_offset 5, -16
 1408              		.cfi_offset 6, -12
 1409              		.cfi_offset 7, -8
 1410              		.cfi_offset 14, -4
 1411 0002 0546     		mov	r5, r0
 1412 0004 0E46     		mov	r6, r1
 236:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t wrap = 0;
 1413              		.loc 1 236 3 is_stmt 1 view .LVU372
 237:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t ringbuf_lin_len = mqtt_ringbuf_linear_read_length(rb);
 1414              		.loc 1 237 3 view .LVU373
 1415              	.LVL146:
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 1416              		.loc 1 238 3 view .LVU374
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 1417              		.loc 1 238 27 is_stmt 0 view .LVU375
 1418 0006 FFF7FEFF 		bl	mqtt_ringbuf_len
 1419              	.LVL147:
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 1420              		.loc 1 238 27 discriminator 1 view .LVU376
 1421 000a 6B88     		ldrh	r3, [r5, #2]
 1422 000c C3F58072 		rsb	r2, r3, #256
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 1423              		.loc 1 238 9 discriminator 1 view .LVU377
 1424 0010 9042     		cmp	r0, r2
 1425 0012 20DA     		bge	.L115
 1426              	.LVL148:
 239:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_output_send: tpcb != NULL", tpcb != NULL);
 1427              		.loc 1 239 3 is_stmt 1 view .LVU378
 239:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_output_send: tpcb != NULL", tpcb != NULL);
 1428              		.loc 1 239 9 is_stmt 0 view .LVU379
 1429 0014 B6F86470 		ldrh	r7, [r6, #100]
 1430              	.LVL149:
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1431              		.loc 1 240 3 is_stmt 1 view .LVU380
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1432              		.loc 1 240 3 view .LVU381
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 1433              		.loc 1 238 27 is_stmt 0 discriminator 1 view .LVU382
 1434 0018 0346     		mov	r3, r0
 1435              	.LVL150:
 1436              	.L116:
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1437              		.loc 1 240 3 is_stmt 1 discriminator 3 view .LVU383
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1438              		.loc 1 240 3 discriminator 3 view .LVU384
 242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return;
 1439              		.loc 1 242 3 view .LVU385
 242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return;
 1440              		.loc 1 242 6 is_stmt 0 view .LVU386
 1441 001a 002B     		cmp	r3, #0
 1442 001c 18BF     		it	ne
 1443 001e 002F     		cmpne	r7, #0
 1444 0020 18D0     		beq	.L114
 247:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1445              		.loc 1 247 79 is_stmt 1 view .LVU387
ARM GAS  /tmp/ccBURsTh.s 			page 43


 249:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Space in TCP output buffer is larger than available in ring buffer linear portion */
 1446              		.loc 1 249 3 view .LVU388
 249:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Space in TCP output buffer is larger than available in ring buffer linear portion */
 1447              		.loc 1 249 6 is_stmt 0 view .LVU389
 1448 0022 BB42     		cmp	r3, r7
 1449 0024 1DD2     		bcs	.L123
 251:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Wrap around if more data in ring buffer after linear portion */
 1450              		.loc 1 251 5 is_stmt 1 view .LVU390
 1451              	.LVL151:
 253:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1452              		.loc 1 253 5 view .LVU391
 253:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1453              		.loc 1 253 34 is_stmt 0 discriminator 1 view .LVU392
 1454 0026 9842     		cmp	r0, r3
 1455 0028 94BF     		ite	ls
 1456 002a 0024     		movls	r4, #0
 1457 002c 0124     		movhi	r4, #1
 1458              	.LVL152:
 251:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Wrap around if more data in ring buffer after linear portion */
 1459              		.loc 1 251 14 view .LVU393
 1460 002e 1F46     		mov	r7, r3
 1461              	.LVL153:
 1462              	.L118:
 255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((err == ERR_OK) && wrap) {
 1463              		.loc 1 255 3 is_stmt 1 view .LVU394
 255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((err == ERR_OK) && wrap) {
 1464              		.loc 1 255 27 is_stmt 0 view .LVU395
 1465 0030 2846     		mov	r0, r5
 1466 0032 FFF7FEFF 		bl	mqtt_ringbuf_get_ptr
 1467              	.LVL154:
 1468 0036 0146     		mov	r1, r0
 255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((err == ERR_OK) && wrap) {
 1469              		.loc 1 255 9 discriminator 1 view .LVU396
 1470 0038 ACB1     		cbz	r4, .L124
 1471 003a 0323     		movs	r3, #3
 1472              	.L119:
 255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((err == ERR_OK) && wrap) {
 1473              		.loc 1 255 9 discriminator 4 view .LVU397
 1474 003c 3A46     		mov	r2, r7
 1475 003e 3046     		mov	r0, r6
 1476 0040 FFF7FEFF 		bl	tcp_write
 1477              	.LVL155:
 256:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 1478              		.loc 1 256 3 is_stmt 1 view .LVU398
 256:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 1479              		.loc 1 256 12 is_stmt 0 view .LVU399
 1480 0044 0346     		mov	r3, r0
 256:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 1481              		.loc 1 256 23 view .LVU400
 1482 0046 0028     		cmp	r0, #0
 1483 0048 14BF     		ite	ne
 1484 004a 0024     		movne	r4, #0
 1485 004c 04F00104 		andeq	r4, r4, #1
 1486              	.LVL156:
 256:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 1487              		.loc 1 256 6 view .LVU401
 1488 0050 5CB9     		cbnz	r4, .L128
ARM GAS  /tmp/ccBURsTh.s 			page 44


 1489              	.LVL157:
 1490              	.L120:
 263:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 1491              		.loc 1 263 3 is_stmt 1 view .LVU402
 263:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_ringbuf_advance_get_idx(rb, send_len);
 1492              		.loc 1 263 6 is_stmt 0 view .LVU403
 1493 0052 83B3     		cbz	r3, .L129
 1494              	.LVL158:
 1495              	.L114:
 270:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1496              		.loc 1 270 1 view .LVU404
 1497 0054 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 1498              	.LVL159:
 1499              	.L115:
 238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t send_len = altcp_sndbuf(tpcb);
 1500              		.loc 1 238 9 discriminator 2 view .LVU405
 1501 0056 C3F58073 		rsb	r3, r3, #256
 1502 005a 9BB2     		uxth	r3, r3
 1503              	.LVL160:
 239:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_output_send: tpcb != NULL", tpcb != NULL);
 1504              		.loc 1 239 3 is_stmt 1 view .LVU406
 239:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_output_send: tpcb != NULL", tpcb != NULL);
 1505              		.loc 1 239 9 is_stmt 0 view .LVU407
 1506 005c B6F86470 		ldrh	r7, [r6, #100]
 1507              	.LVL161:
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1508              		.loc 1 240 3 is_stmt 1 view .LVU408
 240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1509              		.loc 1 240 3 view .LVU409
 1510 0060 DBE7     		b	.L116
 1511              	.LVL162:
 1512              	.L123:
 237:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t ringbuf_lin_len = mqtt_ringbuf_linear_read_length(rb);
 1513              		.loc 1 237 8 is_stmt 0 view .LVU410
 1514 0062 0024     		movs	r4, #0
 1515 0064 E4E7     		b	.L118
 1516              	.LVL163:
 1517              	.L124:
 255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((err == ERR_OK) && wrap) {
 1518              		.loc 1 255 9 discriminator 2 view .LVU411
 1519 0066 0123     		movs	r3, #1
 1520 0068 E8E7     		b	.L119
 1521              	.LVL164:
 1522              	.L128:
 257:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Use the lesser one of ring buffer linear length and TCP send buffer size */
 1523              		.loc 1 257 5 is_stmt 1 view .LVU412
 1524 006a 3946     		mov	r1, r7
 1525 006c 2846     		mov	r0, r5
 1526              	.LVL165:
 257:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Use the lesser one of ring buffer linear length and TCP send buffer size */
 1527              		.loc 1 257 5 is_stmt 0 view .LVU413
 1528 006e FFF7FEFF 		bl	mqtt_ringbuf_advance_get_idx
 1529              	.LVL166:
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1530              		.loc 1 259 5 is_stmt 1 view .LVU414
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1531              		.loc 1 259 16 is_stmt 0 view .LVU415
ARM GAS  /tmp/ccBURsTh.s 			page 45


 1532 0072 B6F86470 		ldrh	r7, [r6, #100]
 1533              	.LVL167:
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1534              		.loc 1 259 16 view .LVU416
 1535 0076 3C46     		mov	r4, r7
 1536 0078 2846     		mov	r0, r5
 1537 007a FFF7FEFF 		bl	mqtt_ringbuf_len
 1538              	.LVL168:
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1539              		.loc 1 259 16 discriminator 1 view .LVU417
 1540 007e 6A88     		ldrh	r2, [r5, #2]
 1541 0080 C2F58073 		rsb	r3, r2, #256
 1542 0084 9842     		cmp	r0, r3
 1543 0086 12DA     		bge	.L125
 1544 0088 0146     		mov	r1, r0
 1545              	.L121:
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1546              		.loc 1 259 14 discriminator 4 view .LVU418
 1547 008a 8C42     		cmp	r4, r1
 1548 008c 04DB     		blt	.L122
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1549              		.loc 1 259 14 discriminator 1 view .LVU419
 1550 008e 9842     		cmp	r0, r3
 1551 0090 0FDB     		blt	.L126
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1552              		.loc 1 259 14 discriminator 9 view .LVU420
 1553 0092 C2F58072 		rsb	r2, r2, #256
 1554 0096 97B2     		uxth	r7, r2
 1555              	.L122:
 1556              	.LVL169:
 260:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1557              		.loc 1 260 5 is_stmt 1 view .LVU421
 260:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1558              		.loc 1 260 29 is_stmt 0 view .LVU422
 1559 0098 2846     		mov	r0, r5
 1560 009a FFF7FEFF 		bl	mqtt_ringbuf_get_ptr
 1561              	.LVL170:
 1562 009e 0146     		mov	r1, r0
 260:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1563              		.loc 1 260 11 discriminator 1 view .LVU423
 1564 00a0 0123     		movs	r3, #1
 1565 00a2 3A46     		mov	r2, r7
 1566 00a4 3046     		mov	r0, r6
 1567 00a6 FFF7FEFF 		bl	tcp_write
 1568              	.LVL171:
 1569 00aa 0346     		mov	r3, r0
 1570              	.LVL172:
 260:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1571              		.loc 1 260 11 discriminator 1 view .LVU424
 1572 00ac D1E7     		b	.L120
 1573              	.LVL173:
 1574              	.L125:
 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1575              		.loc 1 259 16 discriminator 2 view .LVU425
 1576 00ae 1946     		mov	r1, r3
 1577 00b0 EBE7     		b	.L121
 1578              	.L126:
ARM GAS  /tmp/ccBURsTh.s 			page 46


 259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err = altcp_write(tpcb, mqtt_ringbuf_get_ptr(rb), send_len, TCP_WRITE_FLAG_COPY);
 1579              		.loc 1 259 16 discriminator 8 view .LVU426
 1580 00b2 0746     		mov	r7, r0
 1581 00b4 F0E7     		b	.L122
 1582              	.LVL174:
 1583              	.L129:
 264:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Flush */
 1584              		.loc 1 264 5 is_stmt 1 view .LVU427
 1585 00b6 3946     		mov	r1, r7
 1586 00b8 2846     		mov	r0, r5
 1587 00ba FFF7FEFF 		bl	mqtt_ringbuf_advance_get_idx
 1588              	.LVL175:
 266:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 1589              		.loc 1 266 5 view .LVU428
 1590 00be 3046     		mov	r0, r6
 1591 00c0 FFF7FEFF 		bl	tcp_output
 1592              	.LVL176:
 268:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1593              		.loc 1 268 114 view .LVU429
 1594 00c4 C6E7     		b	.L114
 1595              		.cfi_endproc
 1596              	.LFE179:
 1598              		.section	.text.mqtt_tcp_poll_cb,"ax",%progbits
 1599              		.align	1
 1600              		.syntax unified
 1601              		.thumb
 1602              		.thumb_func
 1604              	mqtt_tcp_poll_cb:
 1605              	.LVL177:
 1606              	.LFB202:
 662:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 663:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 664:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 665:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Complete MQTT message received or buffer full
 666:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
 667:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param fixed_hdr_idx header index
 668:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param length length received part
 669:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param remaining_length Remaining length of complete message
 670:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 671:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static mqtt_connection_status_t
 672:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_message_received(mqtt_client_t *client, u8_t fixed_hdr_idx, u16_t length, u32_t remaining_leng
 673:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_connection_status_t res = MQTT_CONNECT_ACCEPTED;
 675:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 676:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t *var_hdr_payload = client->rx_buffer + fixed_hdr_idx;
 677:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t var_hdr_payload_bufsize = sizeof(client->rx_buffer) - fixed_hdr_idx;
 678:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 679:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Control packet type */
 680:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t pkt_type = MQTT_CTL_PACKET_TYPE(client->rx_buffer[0]);
 681:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t pkt_id = 0;
 682:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("client->msg_idx < MQTT_VAR_HEADER_BUFFER_LEN", client->msg_idx < MQTT_VAR_HEADER_BUF
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 686:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 687:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 688:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (pkt_type == MQTT_MSG_TYPE_CONNACK) {
ARM GAS  /tmp/ccBURsTh.s 			page 47


 689:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->conn_state == MQTT_CONNECTING) {
 690:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2) {
 691:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short CONNACK message\n"));
 692:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         goto out_disconnect;
 693:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 694:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Get result code from CONNACK */
 695:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       res = (mqtt_connection_status_t)var_hdr_payload[1];
 696:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: Connect response code %d\n", res));
 697:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (res == MQTT_CONNECT_ACCEPTED) {
 698:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Reset cyclic_tick when changing to connected state */
 699:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->cyclic_tick = 0;
 700:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->conn_state = MQTT_CONNECTED;
 701:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Notify upper layer */
 702:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (client->connect_cb != 0) {
 703:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->connect_cb(client, client->connect_arg, res);
 704:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 705:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 706:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 707:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Received CONNACK in connected state\n")
 708:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 709:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else if (pkt_type == MQTT_MSG_TYPE_PINGRESP) {
 710:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ( "mqtt_message_received: Received PINGRESP from server\n"));
 711:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 712:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else if (pkt_type == MQTT_MSG_TYPE_PUBLISH) {
 713:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u16_t payload_offset = 0;
 714:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u16_t payload_length = length;
 715:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u8_t qos = MQTT_CTL_PACKET_QOS(client->rx_buffer[0]);
 716:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 717:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->msg_idx <= MQTT_VAR_HEADER_BUFFER_LEN) {
 718:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Should have topic and pkt id*/
 719:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u8_t *topic;
 720:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t after_topic;
 721:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u8_t bkp;
 722:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t topic_len;
 723:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t qos_len = (qos ? 2U : 0U);
 724:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2 + qos_len) {
 725:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet\n"));
 726:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         goto out_disconnect;
 727:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 728:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic_len = var_hdr_payload[0];
 729:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic_len = (topic_len << 8) + (u16_t)(var_hdr_payload[1]);
 730:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((topic_len > length - (2 + qos_len)) ||
 731:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           (topic_len > var_hdr_payload_bufsize - (2 + qos_len))) {
 732:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (topic)
 733:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         goto out_disconnect;
 734:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 735:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 736:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic = var_hdr_payload + 2;
 737:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       after_topic = 2 + topic_len;
 738:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Check buffer length, add one byte even for QoS 0 so that zero termination will fit */
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((after_topic + (qos ? 2U : 1U)) > var_hdr_payload_bufsize) {
 740:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 741:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         goto out_disconnect;
 742:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 743:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 744:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* id for QoS 1 and 2 */
 745:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (qos > 0) {
ARM GAS  /tmp/ccBURsTh.s 			page 48


 746:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (length < after_topic + 2U) {
 747:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (afte
 748:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           goto out_disconnect;
 749:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 750:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->inpub_pkt_id = ((u16_t)var_hdr_payload[after_topic] << 8) + (u16_t)var_hdr_payload[
 751:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         after_topic += 2;
 752:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 753:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->inpub_pkt_id = 0;
 754:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 755:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Take backup of byte after topic */
 756:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       bkp = topic[topic_len];
 757:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Zero terminate string */
 758:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic[topic_len] = 0;
 759:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Payload data remaining in receive buffer */
 760:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       payload_length = length - after_topic;
 761:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       payload_offset = after_topic;
 762:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 763:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Received message with QoS %d at topic
 764:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                                      qos, topic, remaining_length + payload_length));
 765:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (client->pub_cb != NULL) {
 766:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->pub_cb(client->inpub_arg, (const char *)topic, remaining_length + payload_length);
 767:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 768:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Restore byte after topic */
 769:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic[topic_len] = bkp;
 770:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 771:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (payload_length > 0 || remaining_length == 0) {
 772:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < (size_t)(payload_offset + payload_length)) {
 773:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short packet (payload)\n"));
 774:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         goto out_disconnect;
 775:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 776:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->data_cb(client->inpub_arg, var_hdr_payload + payload_offset, payload_length, remainin
 777:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reply if QoS > 0 */
 778:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (remaining_length == 0 && qos > 0) {
 779:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Send PUBACK for QoS 1 or PUBREC for QoS 2 */
 780:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         u8_t resp_msg = (qos == 1) ? MQTT_MSG_TYPE_PUBACK : MQTT_MSG_TYPE_PUBREC;
 781:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Sending publish response: %s with p
 782:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                                        mqtt_msg_type_to_str(resp_msg), client->inpub_pkt_id));
 783:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         pub_ack_rec_rel_response(client, resp_msg, client->inpub_pkt_id, 0);
 784:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 785:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 786:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 787:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Get packet identifier */
 788:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pkt_id = (u16_t)var_hdr_payload[0] << 8;
 789:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pkt_id |= (u16_t)var_hdr_payload[1];
 790:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (pkt_id == 0) {
 791:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Got message with illegal packet identif
 792:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       goto out_disconnect;
 793:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 794:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (pkt_type == MQTT_MSG_TYPE_PUBREC) {
 795:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: PUBREC, sending PUBREL with pkt_id: %d
 796:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pub_ack_rec_rel_response(client, MQTT_MSG_TYPE_PUBREL, pkt_id, 1);
 797:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 798:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else if (pkt_type == MQTT_MSG_TYPE_PUBREL) {
 799:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: PUBREL, sending PUBCOMP response with 
 800:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pub_ack_rec_rel_response(client, MQTT_MSG_TYPE_PUBCOMP, pkt_id, 0);
 801:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 802:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else if (pkt_type == MQTT_MSG_TYPE_SUBACK || pkt_type == MQTT_MSG_TYPE_UNSUBACK ||
ARM GAS  /tmp/ccBURsTh.s 			page 49


 803:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                pkt_type == MQTT_MSG_TYPE_PUBCOMP || pkt_type == MQTT_MSG_TYPE_PUBACK) {
 804:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       struct mqtt_request_t *r = mqtt_take_request(&client->pend_req_queue, pkt_id);
 805:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (r != NULL) {
 806:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: %s response with id %d\n", mqtt_msg_
 807:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (pkt_type == MQTT_MSG_TYPE_SUBACK) {
 808:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           if (length < 3) {
 809:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: To small SUBACK packet\n"));
 810:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             goto out_disconnect;
 811:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           } else {
 812:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             mqtt_incomming_suback(r, var_hdr_payload[2]);
 813:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 814:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         } else if (r->cb != NULL) {
 815:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           r->cb(r->arg, ERR_OK);
 816:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 817:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         mqtt_delete_request(r);
 818:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 819:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ( "mqtt_message_received: Received %s reply, with wrong pkt_id
 820:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 821:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 822:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ( "mqtt_message_received: Received unknown message type: %d\n", 
 823:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       goto out_disconnect;
 824:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 825:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 826:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return res;
 827:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** out_disconnect:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return MQTT_CONNECT_DISCONNECTED;
 829:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 830:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 831:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 832:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 833:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT incoming message parser
 834:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
 835:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param p PBUF chain of received data
 836:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return Connection status
 837:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 838:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static mqtt_connection_status_t
 839:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_parse_incoming(mqtt_client_t *client, struct pbuf *p)
 840:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 841:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t in_offset = 0;
 842:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u32_t msg_rem_len = 0;
 843:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t fixed_hdr_idx = 0;
 844:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t b = 0;
 845:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 846:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   while (p->tot_len > in_offset) {
 847:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* We ALWAYS parse the header here first. Even if the header was not
 848:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****        included in this segment, we re-parse it here by buffering it in
 849:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****        client->rx_buffer. client->msg_idx keeps track of this. */
 850:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if ((fixed_hdr_idx < 2) || ((b & 0x80) != 0)) {
 851:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 852:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (fixed_hdr_idx < client->msg_idx) {
 853:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* parse header from old pbuf (buffered in client->rx_buffer) */
 854:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         b = client->rx_buffer[fixed_hdr_idx];
 855:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 856:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* parse header from this pbuf and save it in client->rx_buffer in case
 857:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****            it comes in segmented */
 858:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         b = pbuf_get_at(p, in_offset++);
 859:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->rx_buffer[client->msg_idx++] = b;
ARM GAS  /tmp/ccBURsTh.s 			page 50


 860:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 861:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       fixed_hdr_idx++;
 862:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 863:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (fixed_hdr_idx >= 2) {
 864:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* fixed header contains at least 2 bytes but can contain more, depending on
 865:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****            'remaining length'. All bytes but the last of this have 0x80 set to
 866:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****            indicate more bytes are coming. */
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         msg_rem_len |= (u32_t)(b & 0x7f) << ((fixed_hdr_idx - 2) * 7);
 868:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 869:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* fixed header is done */
 870:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_parse_incoming: Remaining length after fixed header:
 871:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           if (msg_rem_len == 0) {
 872:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             /* Complete message with no extra headers of payload received */
 873:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             mqtt_message_received(client, fixed_hdr_idx, 0, 0);
 874:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             client->msg_idx = 0;
 875:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             fixed_hdr_idx = 0;
 876:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           } else {
 877:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             /* Bytes remaining in message (changes remaining length if this is
 878:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                not the first segment of this message) */
 879:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             msg_rem_len = (msg_rem_len + fixed_hdr_idx) - client->msg_idx;
 880:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 881:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 882:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 883:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     } else {
 884:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Fixed header has been parsed, parse variable header */
 885:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t cpy_len, cpy_start, buffer_space;
 886:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       cpy_start = (client->msg_idx - fixed_hdr_idx) % (MQTT_VAR_HEADER_BUFFER_LEN - fixed_hdr_idx) 
 888:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 889:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Allow to copy the lesser one of available length in input data or bytes remaining in messa
 890:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       cpy_len = (u16_t)LWIP_MIN((u16_t)(p->tot_len - in_offset), msg_rem_len);
 891:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 892:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Limit to available space in buffer */
 893:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       buffer_space = MQTT_VAR_HEADER_BUFFER_LEN - cpy_start;
 894:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (cpy_len > buffer_space) {
 895:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         cpy_len = buffer_space;
 896:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 897:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pbuf_copy_partial(p, client->rx_buffer + cpy_start, cpy_len, in_offset);
 898:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 899:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Advance get and put indexes  */
 900:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->msg_idx += cpy_len;
 901:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       in_offset += cpy_len;
 902:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       msg_rem_len -= cpy_len;
 903:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 904:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_parse_incoming: msg_idx: %"U32_F", cpy_len: %"U16_F", re
 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((msg_rem_len == 0) || (cpy_len == buffer_space)) {
 906:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 907:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         mqtt_connection_status_t res = mqtt_message_received(client, fixed_hdr_idx, (cpy_start + cp
 908:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (res != MQTT_CONNECT_ACCEPTED) {
 909:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           return res;
 910:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 911:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (msg_rem_len == 0) {
 912:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* Reset parser state */
 913:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->msg_idx = 0;
 914:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* msg_tot_len = 0; */
 915:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           fixed_hdr_idx = 0;
 916:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
ARM GAS  /tmp/ccBURsTh.s 			page 51


 917:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 918:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 919:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 920:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return MQTT_CONNECT_ACCEPTED;
 921:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 922:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 923:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 924:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 925:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * TCP received callback function. @see tcp_recv_fn
 926:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg MQTT client
 927:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param p PBUF chain of received data
 928:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param err Passed as return value if not ERR_OK
 929:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK or err passed into callback
 930:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 931:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static err_t
 932:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_tcp_recv_cb(void *arg, struct altcp_pcb *pcb, struct pbuf *p, err_t err)
 933:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 934:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client != NULL", client != NULL);
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 937:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 938:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (p == NULL) {
 939:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_recv_cb: Recv pbuf=NULL, remote has closed connection\
 940:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_close(client, MQTT_CONNECT_DISCONNECTED);
 941:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 942:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_connection_status_t res;
 943:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (err != ERR_OK) {
 944:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_tcp_recv_cb: Recv err=%d\n", err));
 945:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pbuf_free(p);
 946:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       return err;
 947:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 948:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 949:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Tell remote that data has been received */
 950:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_recved(pcb, p->tot_len);
 951:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     res = mqtt_parse_incoming(client, p);
 952:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pbuf_free(p);
 953:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 954:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (res != MQTT_CONNECT_ACCEPTED) {
 955:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       mqtt_close(client, res);
 956:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 957:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* If keep alive functionality is used */
 958:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->keep_alive != 0) {
 959:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reset server alive watchdog */
 960:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->server_watchdog = 0;
 961:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 962:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 963:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 964:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
 965:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 966:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 967:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 968:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
 969:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * TCP data sent callback function. @see tcp_sent_fn
 970:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg MQTT client
 971:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tpcb TCP connection handle
 972:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param len Number of bytes sent
 973:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK
ARM GAS  /tmp/ccBURsTh.s 			page 52


 974:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
 975:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static err_t
 976:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_tcp_sent_cb(void *arg, struct altcp_pcb *tpcb, u16_t len)
 977:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 978:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 979:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 980:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_UNUSED_ARG(tpcb);
 981:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_UNUSED_ARG(len);
 982:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 983:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == MQTT_CONNECTED) {
 984:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     struct mqtt_request_t *r;
 985:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 986:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Reset keep-alive send timer and server watchdog */
 987:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->cyclic_tick = 0;
 988:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->server_watchdog = 0;
 989:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* QoS 0 publish has no response from server, so call its callbacks here */
 990:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     while ((r = mqtt_take_request(&client->pend_req_queue, 0)) != NULL) {
 991:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_sent_cb: Calling QoS 0 publish complete callback\n")
 992:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (r->cb != NULL) {
 993:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_OK);
 994:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 995:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       mqtt_delete_request(r);
 996:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 997:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Try send any remaining buffers from output queue */
 998:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_send(&client->output, client->conn);
 999:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1000:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
1001:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
1002:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1003:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1004:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * TCP error callback function. @see tcp_err_fn
1005:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg MQTT client
1006:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param err Error encountered
1007:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1008:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static void
1009:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_tcp_err_cb(void *arg, err_t err)
1010:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
1011:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
1012:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_UNUSED_ARG(err); /* only used for debug output */
1013:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_err_cb: TCP error callback: error %d, arg: %p\n", err, a
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_err_cb: client != NULL", client != NULL);
1015:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
1016:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->conn = 0;
1017:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_close(client, MQTT_CONNECT_DISCONNECTED);
1018:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
1019:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1020:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1021:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * TCP poll callback function. @see tcp_poll_fn
1022:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg MQTT client
1023:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param tpcb TCP connection handle
1024:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return err ERR_OK
1025:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1026:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static err_t
1027:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_tcp_poll_cb(void *arg, struct altcp_pcb *tpcb)
1028:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 1607              		.loc 1 1028 1 view -0
 1608              		.cfi_startproc
ARM GAS  /tmp/ccBURsTh.s 			page 53


 1609              		@ args = 0, pretend = 0, frame = 0
 1610              		@ frame_needed = 0, uses_anonymous_args = 0
 1611              		.loc 1 1028 1 is_stmt 0 view .LVU431
 1612 0000 08B5     		push	{r3, lr}
 1613              	.LCFI15:
 1614              		.cfi_def_cfa_offset 8
 1615              		.cfi_offset 3, -8
 1616              		.cfi_offset 14, -4
1029:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 1617              		.loc 1 1029 3 is_stmt 1 view .LVU432
 1618              	.LVL178:
1030:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == MQTT_CONNECTED) {
 1619              		.loc 1 1030 3 view .LVU433
 1620              		.loc 1 1030 13 is_stmt 0 view .LVU434
 1621 0002 837A     		ldrb	r3, [r0, #10]	@ zero_extendqisi2
 1622              		.loc 1 1030 6 view .LVU435
 1623 0004 032B     		cmp	r3, #3
 1624 0006 01D0     		beq	.L133
 1625              	.LVL179:
 1626              	.L131:
1031:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Try send any remaining buffers from output queue */
1032:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_send(&client->output, tpcb);
1033:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1034:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
 1627              		.loc 1 1034 3 is_stmt 1 view .LVU436
1035:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 1628              		.loc 1 1035 1 is_stmt 0 view .LVU437
 1629 0008 0020     		movs	r0, #0
 1630 000a 08BD     		pop	{r3, pc}
 1631              	.LVL180:
 1632              	.L133:
1032:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1633              		.loc 1 1032 5 is_stmt 1 view .LVU438
 1634 000c EC30     		adds	r0, r0, #236
 1635              	.LVL181:
1032:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1636              		.loc 1 1032 5 is_stmt 0 view .LVU439
 1637 000e FFF7FEFF 		bl	mqtt_output_send
 1638              	.LVL182:
1032:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1639              		.loc 1 1032 5 view .LVU440
 1640 0012 F9E7     		b	.L131
 1641              		.cfi_endproc
 1642              	.LFE202:
 1644              		.section	.text.mqtt_tcp_sent_cb,"ax",%progbits
 1645              		.align	1
 1646              		.syntax unified
 1647              		.thumb
 1648              		.thumb_func
 1650              	mqtt_tcp_sent_cb:
 1651              	.LVL183:
 1652              	.LFB200:
 977:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 1653              		.loc 1 977 1 is_stmt 1 view -0
 1654              		.cfi_startproc
 1655              		@ args = 0, pretend = 0, frame = 0
 1656              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  /tmp/ccBURsTh.s 			page 54


 977:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 1657              		.loc 1 977 1 is_stmt 0 view .LVU442
 1658 0000 38B5     		push	{r3, r4, r5, lr}
 1659              	.LCFI16:
 1660              		.cfi_def_cfa_offset 16
 1661              		.cfi_offset 3, -16
 1662              		.cfi_offset 4, -12
 1663              		.cfi_offset 5, -8
 1664              		.cfi_offset 14, -4
 978:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1665              		.loc 1 978 3 is_stmt 1 view .LVU443
 1666              	.LVL184:
 980:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_UNUSED_ARG(len);
 1667              		.loc 1 980 3 view .LVU444
 981:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1668              		.loc 1 981 3 view .LVU445
 983:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     struct mqtt_request_t *r;
 1669              		.loc 1 983 3 view .LVU446
 983:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     struct mqtt_request_t *r;
 1670              		.loc 1 983 13 is_stmt 0 view .LVU447
 1671 0002 837A     		ldrb	r3, [r0, #10]	@ zero_extendqisi2
 983:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     struct mqtt_request_t *r;
 1672              		.loc 1 983 6 view .LVU448
 1673 0004 032B     		cmp	r3, #3
 1674 0006 01D0     		beq	.L140
 1675              	.LVL185:
 1676              	.L135:
1000:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 1677              		.loc 1 1000 3 is_stmt 1 view .LVU449
1001:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1678              		.loc 1 1001 1 is_stmt 0 view .LVU450
 1679 0008 0020     		movs	r0, #0
 1680 000a 38BD     		pop	{r3, r4, r5, pc}
 1681              	.LVL186:
 1682              	.L140:
1001:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1683              		.loc 1 1001 1 view .LVU451
 1684 000c 0546     		mov	r5, r0
 1685              	.LBB2:
 984:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1686              		.loc 1 984 5 is_stmt 1 view .LVU452
 987:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->server_watchdog = 0;
 1687              		.loc 1 987 5 view .LVU453
 987:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->server_watchdog = 0;
 1688              		.loc 1 987 25 is_stmt 0 view .LVU454
 1689 000e 0023     		movs	r3, #0
 1690 0010 0380     		strh	r3, [r0]	@ movhi
 988:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* QoS 0 publish has no response from server, so call its callbacks here */
 1691              		.loc 1 988 5 is_stmt 1 view .LVU455
 988:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* QoS 0 publish has no response from server, so call its callbacks here */
 1692              		.loc 1 988 29 is_stmt 0 view .LVU456
 1693 0012 8380     		strh	r3, [r0, #4]	@ movhi
 990:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_sent_cb: Calling QoS 0 publish complete callback\n")
 1694              		.loc 1 990 5 is_stmt 1 view .LVU457
 990:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_sent_cb: Calling QoS 0 publish complete callback\n")
 1695              		.loc 1 990 11 is_stmt 0 view .LVU458
 1696 0014 02E0     		b	.L136
ARM GAS  /tmp/ccBURsTh.s 			page 55


 1697              	.LVL187:
 1698              	.L137:
 995:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 1699              		.loc 1 995 7 is_stmt 1 view .LVU459
 1700 0016 2046     		mov	r0, r4
 1701 0018 FFF7FEFF 		bl	mqtt_delete_request
 1702              	.LVL188:
 1703              	.L136:
 990:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_sent_cb: Calling QoS 0 publish complete callback\n")
 1704              		.loc 1 990 64 view .LVU460
 990:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_sent_cb: Calling QoS 0 publish complete callback\n")
 1705              		.loc 1 990 17 is_stmt 0 view .LVU461
 1706 001c 0021     		movs	r1, #0
 1707 001e 05F11800 		add	r0, r5, #24
 1708 0022 FFF7FEFF 		bl	mqtt_take_request
 1709              	.LVL189:
 990:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_sent_cb: Calling QoS 0 publish complete callback\n")
 1710              		.loc 1 990 64 discriminator 1 view .LVU462
 1711 0026 0446     		mov	r4, r0
 1712 0028 30B1     		cbz	r0, .L141
 991:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (r->cb != NULL) {
 1713              		.loc 1 991 101 is_stmt 1 view .LVU463
 992:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_OK);
 1714              		.loc 1 992 7 view .LVU464
 992:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_OK);
 1715              		.loc 1 992 12 is_stmt 0 view .LVU465
 1716 002a 6368     		ldr	r3, [r4, #4]
 992:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         r->cb(r->arg, ERR_OK);
 1717              		.loc 1 992 10 view .LVU466
 1718 002c 002B     		cmp	r3, #0
 1719 002e F2D0     		beq	.L137
 993:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 1720              		.loc 1 993 9 is_stmt 1 view .LVU467
 1721 0030 0021     		movs	r1, #0
 1722 0032 A068     		ldr	r0, [r4, #8]
 1723              	.LVL190:
 993:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 1724              		.loc 1 993 9 is_stmt 0 view .LVU468
 1725 0034 9847     		blx	r3
 1726              	.LVL191:
 1727 0036 EEE7     		b	.L137
 1728              	.LVL192:
 1729              	.L141:
 998:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1730              		.loc 1 998 5 is_stmt 1 view .LVU469
 1731 0038 E968     		ldr	r1, [r5, #12]
 1732 003a 05F1EC00 		add	r0, r5, #236
 1733              	.LVL193:
 998:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1734              		.loc 1 998 5 is_stmt 0 view .LVU470
 1735 003e FFF7FEFF 		bl	mqtt_output_send
 1736              	.LVL194:
 1737 0042 E1E7     		b	.L135
 1738              	.LBE2:
 1739              		.cfi_endproc
 1740              	.LFE200:
 1742              		.section	.text.pub_ack_rec_rel_response,"ax",%progbits
ARM GAS  /tmp/ccBURsTh.s 			page 56


 1743              		.align	1
 1744              		.syntax unified
 1745              		.thumb
 1746              		.thumb_func
 1748              	pub_ack_rec_rel_response:
 1749              	.LVL195:
 1750              	.LFB195:
 636:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err = ERR_OK;
 1751              		.loc 1 636 1 is_stmt 1 view -0
 1752              		.cfi_startproc
 1753              		@ args = 0, pretend = 0, frame = 0
 1754              		@ frame_needed = 0, uses_anonymous_args = 0
 636:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err = ERR_OK;
 1755              		.loc 1 636 1 is_stmt 0 view .LVU472
 1756 0000 2DE9F043 		push	{r4, r5, r6, r7, r8, r9, lr}
 1757              	.LCFI17:
 1758              		.cfi_def_cfa_offset 28
 1759              		.cfi_offset 4, -28
 1760              		.cfi_offset 5, -24
 1761              		.cfi_offset 6, -20
 1762              		.cfi_offset 7, -16
 1763              		.cfi_offset 8, -12
 1764              		.cfi_offset 9, -8
 1765              		.cfi_offset 14, -4
 1766 0004 83B0     		sub	sp, sp, #12
 1767              	.LCFI18:
 1768              		.cfi_def_cfa_offset 40
 1769 0006 0546     		mov	r5, r0
 1770 0008 8846     		mov	r8, r1
 1771 000a 9146     		mov	r9, r2
 1772 000c 1F46     		mov	r7, r3
 637:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (mqtt_output_check_space(&client->output, 2)) {
 1773              		.loc 1 637 3 is_stmt 1 view .LVU473
 1774              	.LVL196:
 638:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_fixed_header(&client->output, msg, 0, qos, 0, 2);
 1775              		.loc 1 638 3 view .LVU474
 638:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_fixed_header(&client->output, msg, 0, qos, 0, 2);
 1776              		.loc 1 638 7 is_stmt 0 view .LVU475
 1777 000e 00F1EC06 		add	r6, r0, #236
 1778 0012 0221     		movs	r1, #2
 1779              	.LVL197:
 638:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_fixed_header(&client->output, msg, 0, qos, 0, 2);
 1780              		.loc 1 638 7 view .LVU476
 1781 0014 3046     		mov	r0, r6
 1782              	.LVL198:
 638:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_fixed_header(&client->output, msg, 0, qos, 0, 2);
 1783              		.loc 1 638 7 view .LVU477
 1784 0016 FFF7FEFF 		bl	mqtt_output_check_space
 1785              	.LVL199:
 638:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_fixed_header(&client->output, msg, 0, qos, 0, 2);
 1786              		.loc 1 638 6 discriminator 1 view .LVU478
 1787 001a A8B1     		cbz	r0, .L144
 639:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_u16(&client->output, pkt_id);
 1788              		.loc 1 639 5 is_stmt 1 view .LVU479
 1789 001c 0223     		movs	r3, #2
 1790 001e 0193     		str	r3, [sp, #4]
 1791 0020 0024     		movs	r4, #0
ARM GAS  /tmp/ccBURsTh.s 			page 57


 1792 0022 0094     		str	r4, [sp]
 1793 0024 3B46     		mov	r3, r7
 1794 0026 2246     		mov	r2, r4
 1795 0028 4146     		mov	r1, r8
 1796 002a 3046     		mov	r0, r6
 1797 002c FFF7FEFF 		bl	mqtt_output_append_fixed_header
 1798              	.LVL200:
 640:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_send(&client->output, client->conn);
 1799              		.loc 1 640 5 view .LVU480
 1800 0030 4946     		mov	r1, r9
 1801 0032 3046     		mov	r0, r6
 1802 0034 FFF7FEFF 		bl	mqtt_output_append_u16
 1803              	.LVL201:
 641:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 1804              		.loc 1 641 5 view .LVU481
 1805 0038 E968     		ldr	r1, [r5, #12]
 1806 003a 3046     		mov	r0, r6
 1807 003c FFF7FEFF 		bl	mqtt_output_send
 1808              	.LVL202:
 637:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (mqtt_output_check_space(&client->output, 2)) {
 1809              		.loc 1 637 9 is_stmt 0 view .LVU482
 1810 0040 2046     		mov	r0, r4
 1811              	.L143:
 1812              	.LVL203:
 647:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 1813              		.loc 1 647 3 is_stmt 1 view .LVU483
 648:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1814              		.loc 1 648 1 is_stmt 0 view .LVU484
 1815 0042 03B0     		add	sp, sp, #12
 1816              	.LCFI19:
 1817              		.cfi_remember_state
 1818              		.cfi_def_cfa_offset 28
 1819              		@ sp needed
 1820 0044 BDE8F083 		pop	{r4, r5, r6, r7, r8, r9, pc}
 1821              	.LVL204:
 1822              	.L144:
 1823              	.LCFI20:
 1824              		.cfi_restore_state
 645:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 1825              		.loc 1 645 9 view .LVU485
 1826 0048 4FF0FF30 		mov	r0, #-1
 1827 004c F9E7     		b	.L143
 1828              		.cfi_endproc
 1829              	.LFE195:
 1831              		.section	.rodata.mqtt_message_received.str1.4,"aMS",%progbits,1
 1832              		.align	2
 1833              	.LC11:
 1834 0000 636C6965 		.ascii	"client->msg_idx < MQTT_VAR_HEADER_BUFFER_LEN\000"
 1834      6E742D3E 
 1834      6D73675F 
 1834      69647820 
 1834      3C204D51 
 1835 002d 000000   		.align	2
 1836              	.LC12:
 1837 0030 66697865 		.ascii	"fixed_hdr_idx <= client->msg_idx\000"
 1837      645F6864 
 1837      725F6964 
ARM GAS  /tmp/ccBURsTh.s 			page 58


 1837      78203C3D 
 1837      20636C69 
 1838 0051 000000   		.align	2
 1839              	.LC13:
 1840 0054 62756666 		.ascii	"buffer length mismatch\000"
 1840      6572206C 
 1840      656E6774 
 1840      68206D69 
 1840      736D6174 
 1841              		.section	.text.mqtt_message_received,"ax",%progbits
 1842              		.align	1
 1843              		.syntax unified
 1844              		.thumb
 1845              		.thumb_func
 1847              	mqtt_message_received:
 1848              	.LVL205:
 1849              	.LFB197:
 673:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_connection_status_t res = MQTT_CONNECT_ACCEPTED;
 1850              		.loc 1 673 1 is_stmt 1 view -0
 1851              		.cfi_startproc
 1852              		@ args = 0, pretend = 0, frame = 8
 1853              		@ frame_needed = 0, uses_anonymous_args = 0
 673:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_connection_status_t res = MQTT_CONNECT_ACCEPTED;
 1854              		.loc 1 673 1 is_stmt 0 view .LVU487
 1855 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 1856              	.LCFI21:
 1857              		.cfi_def_cfa_offset 36
 1858              		.cfi_offset 4, -36
 1859              		.cfi_offset 5, -32
 1860              		.cfi_offset 6, -28
 1861              		.cfi_offset 7, -24
 1862              		.cfi_offset 8, -20
 1863              		.cfi_offset 9, -16
 1864              		.cfi_offset 10, -12
 1865              		.cfi_offset 11, -8
 1866              		.cfi_offset 14, -4
 1867 0004 83B0     		sub	sp, sp, #12
 1868              	.LCFI22:
 1869              		.cfi_def_cfa_offset 48
 1870 0006 0446     		mov	r4, r0
 1871 0008 8946     		mov	r9, r1
 1872 000a 1546     		mov	r5, r2
 1873 000c 1E46     		mov	r6, r3
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1874              		.loc 1 674 3 is_stmt 1 view .LVU488
 1875              	.LVL206:
 676:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t var_hdr_payload_bufsize = sizeof(client->rx_buffer) - fixed_hdr_idx;
 1876              		.loc 1 676 3 view .LVU489
 676:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t var_hdr_payload_bufsize = sizeof(client->rx_buffer) - fixed_hdr_idx;
 1877              		.loc 1 676 27 is_stmt 0 view .LVU490
 1878 000e 00F16C0B 		add	fp, r0, #108
 676:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t var_hdr_payload_bufsize = sizeof(client->rx_buffer) - fixed_hdr_idx;
 1879              		.loc 1 676 9 view .LVU491
 1880 0012 0BEB0107 		add	r7, fp, r1
 1881              	.LVL207:
 677:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1882              		.loc 1 677 3 is_stmt 1 view .LVU492
ARM GAS  /tmp/ccBURsTh.s 			page 59


 677:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1883              		.loc 1 677 10 is_stmt 0 view .LVU493
 1884 0016 C1F1800A 		rsb	r10, r1, #128
 1885              	.LVL208:
 680:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t pkt_id = 0;
 1886              		.loc 1 680 3 is_stmt 1 view .LVU494
 680:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t pkt_id = 0;
 1887              		.loc 1 680 19 is_stmt 0 view .LVU495
 1888 001a 90F86C80 		ldrb	r8, [r0, #108]	@ zero_extendqisi2
 680:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t pkt_id = 0;
 1889              		.loc 1 680 8 view .LVU496
 1890 001e 4FEA1818 		lsr	r8, r8, #4
 1891              	.LVL209:
 681:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1892              		.loc 1 681 3 is_stmt 1 view .LVU497
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1893              		.loc 1 683 3 view .LVU498
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1894              		.loc 1 683 3 view .LVU499
 1895 0022 836E     		ldr	r3, [r0, #104]
 1896              	.LVL210:
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1897              		.loc 1 683 3 is_stmt 0 view .LVU500
 1898 0024 7F2B     		cmp	r3, #127
 1899 0026 31D8     		bhi	.L185
 1900              	.LVL211:
 1901              	.L147:
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1902              		.loc 1 683 3 is_stmt 1 discriminator 3 view .LVU501
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1903              		.loc 1 683 3 discriminator 3 view .LVU502
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 1904              		.loc 1 684 3 view .LVU503
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 1905              		.loc 1 684 3 view .LVU504
 1906 0028 A36E     		ldr	r3, [r4, #104]
 1907 002a 9945     		cmp	r9, r3
 1908 002c 36D8     		bhi	.L186
 1909              	.L148:
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 1910              		.loc 1 684 3 discriminator 3 view .LVU505
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 1911              		.loc 1 684 3 discriminator 3 view .LVU506
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 1912              		.loc 1 685 3 view .LVU507
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 1913              		.loc 1 685 3 view .LVU508
 1914 002e 09EB0503 		add	r3, r9, r5
 1915 0032 802B     		cmp	r3, #128
 1916 0034 3ADC     		bgt	.L187
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 1917              		.loc 1 685 3 discriminator 2 view .LVU509
 688:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->conn_state == MQTT_CONNECTING) {
 1918              		.loc 1 688 3 view .LVU510
 688:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->conn_state == MQTT_CONNECTING) {
 1919              		.loc 1 688 6 is_stmt 0 view .LVU511
 1920 0036 B8F1020F 		cmp	r8, #2
ARM GAS  /tmp/ccBURsTh.s 			page 60


 1921 003a 41D0     		beq	.L188
 709:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ( "mqtt_message_received: Received PINGRESP from server\n"));
 1922              		.loc 1 709 10 is_stmt 1 view .LVU512
 709:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ( "mqtt_message_received: Received PINGRESP from server\n"));
 1923              		.loc 1 709 13 is_stmt 0 view .LVU513
 1924 003c B8F10D0F 		cmp	r8, #13
 1925 0040 00F00981 		beq	.L166
 712:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u16_t payload_offset = 0;
 1926              		.loc 1 712 10 is_stmt 1 view .LVU514
 712:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u16_t payload_offset = 0;
 1927              		.loc 1 712 13 is_stmt 0 view .LVU515
 1928 0044 B8F1030F 		cmp	r8, #3
 1929 0048 54D0     		beq	.L189
 788:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pkt_id |= (u16_t)var_hdr_payload[1];
 1930              		.loc 1 788 5 is_stmt 1 view .LVU516
 788:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pkt_id |= (u16_t)var_hdr_payload[1];
 1931              		.loc 1 788 36 is_stmt 0 view .LVU517
 1932 004a 1BF80930 		ldrb	r3, [fp, r9]	@ zero_extendqisi2
 1933              	.LVL212:
 789:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (pkt_id == 0) {
 1934              		.loc 1 789 5 is_stmt 1 view .LVU518
 789:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (pkt_id == 0) {
 1935              		.loc 1 789 37 is_stmt 0 view .LVU519
 1936 004e 7A78     		ldrb	r2, [r7, #1]	@ zero_extendqisi2
 1937              	.LVL213:
 790:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Got message with illegal packet identif
 1938              		.loc 1 790 5 is_stmt 1 view .LVU520
 790:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Got message with illegal packet identif
 1939              		.loc 1 790 8 is_stmt 0 view .LVU521
 1940 0050 52EA0322 		orrs	r2, r2, r3, lsl #8
 1941              	.LVL214:
 790:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Got message with illegal packet identif
 1942              		.loc 1 790 8 view .LVU522
 1943 0054 00F01781 		beq	.L179
 794:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: PUBREC, sending PUBREL with pkt_id: %d
 1944              		.loc 1 794 5 is_stmt 1 view .LVU523
 1945 0058 B8F10B0F 		cmp	r8, #11
 1946 005c 00F21681 		bhi	.L180
 1947 0060 0123     		movs	r3, #1
 1948 0062 03FA08F3 		lsl	r3, r3, r8
 1949 0066 13F4296F 		tst	r3, #2704
 1950 006a 40F0D780 		bne	.L160
 1951 006e 13F0400F 		tst	r3, #64
 1952 0072 40F0CC80 		bne	.L161
 1953 0076 13F0200F 		tst	r3, #32
 1954 007a 00F00A81 		beq	.L181
 795:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pub_ack_rec_rel_response(client, MQTT_MSG_TYPE_PUBREL, pkt_id, 1);
 1955              		.loc 1 795 113 view .LVU524
 796:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1956              		.loc 1 796 7 view .LVU525
 1957 007e 0123     		movs	r3, #1
 1958 0080 0621     		movs	r1, #6
 1959 0082 2046     		mov	r0, r4
 1960 0084 FFF7FEFF 		bl	pub_ack_rec_rel_response
 1961              	.LVL215:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1962              		.loc 1 674 28 is_stmt 0 view .LVU526
ARM GAS  /tmp/ccBURsTh.s 			page 61


 1963 0088 0025     		movs	r5, #0
 1964              	.LVL216:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 1965              		.loc 1 674 28 view .LVU527
 1966 008a 1DE0     		b	.L150
 1967              	.LVL217:
 1968              	.L185:
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1969              		.loc 1 683 3 is_stmt 1 discriminator 1 view .LVU528
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1970              		.loc 1 683 3 discriminator 1 view .LVU529
 1971 008c 854B     		ldr	r3, .L193
 1972 008e 40F2AB22 		movw	r2, #683
 1973              	.LVL218:
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1974              		.loc 1 683 3 is_stmt 0 discriminator 1 view .LVU530
 1975 0092 8549     		ldr	r1, .L193+4
 1976              	.LVL219:
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1977              		.loc 1 683 3 discriminator 1 view .LVU531
 1978 0094 8548     		ldr	r0, .L193+8
 1979              	.LVL220:
 683:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("fixed_hdr_idx <= client->msg_idx", fixed_hdr_idx <= client->msg_idx);
 1980              		.loc 1 683 3 discriminator 1 view .LVU532
 1981 0096 FFF7FEFF 		bl	printf
 1982              	.LVL221:
 1983 009a C5E7     		b	.L147
 1984              	.L186:
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 1985              		.loc 1 684 3 is_stmt 1 discriminator 1 view .LVU533
 684:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("buffer length mismatch", fixed_hdr_idx + length <= MQTT_VAR_HEADER_BUFFER_LEN,
 1986              		.loc 1 684 3 discriminator 1 view .LVU534
 1987 009c 814B     		ldr	r3, .L193
 1988 009e 4FF42B72 		mov	r2, #684
 1989 00a2 8349     		ldr	r1, .L193+12
 1990 00a4 8148     		ldr	r0, .L193+8
 1991 00a6 FFF7FEFF 		bl	printf
 1992              	.LVL222:
 1993 00aa C0E7     		b	.L148
 1994              	.L187:
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 1995              		.loc 1 685 3 discriminator 1 view .LVU535
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 1996              		.loc 1 685 3 discriminator 1 view .LVU536
 1997 00ac 7D4B     		ldr	r3, .L193
 1998 00ae 40F2AD22 		movw	r2, #685
 1999 00b2 8049     		ldr	r1, .L193+16
 2000 00b4 7D48     		ldr	r0, .L193+8
 2001 00b6 FFF7FEFF 		bl	printf
 2002              	.LVL223:
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 2003              		.loc 1 685 3 discriminator 1 view .LVU537
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
 2004              		.loc 1 685 3 discriminator 1 view .LVU538
 2005 00ba 4FF48075 		mov	r5, #256
 2006              	.LVL224:
 685:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              return MQTT_CONNECT_DISCONNECTED);
ARM GAS  /tmp/ccBURsTh.s 			page 62


 2007              		.loc 1 685 3 is_stmt 0 view .LVU539
 2008 00be 03E0     		b	.L150
 2009              	.L188:
 689:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2) {
 2010              		.loc 1 689 5 is_stmt 1 view .LVU540
 689:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2) {
 2011              		.loc 1 689 15 is_stmt 0 view .LVU541
 2012 00c0 A37A     		ldrb	r3, [r4, #10]	@ zero_extendqisi2
 689:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2) {
 2013              		.loc 1 689 8 view .LVU542
 2014 00c2 022B     		cmp	r3, #2
 2015 00c4 04D0     		beq	.L190
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2016              		.loc 1 674 28 view .LVU543
 2017 00c6 0025     		movs	r5, #0
 2018              	.LVL225:
 2019              	.L150:
 829:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2020              		.loc 1 829 1 view .LVU544
 2021 00c8 2846     		mov	r0, r5
 2022 00ca 03B0     		add	sp, sp, #12
 2023              	.LCFI23:
 2024              		.cfi_remember_state
 2025              		.cfi_def_cfa_offset 36
 2026              		@ sp needed
 2027 00cc BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 2028              	.LVL226:
 2029              	.L190:
 2030              	.LCFI24:
 2031              		.cfi_restore_state
 690:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short CONNACK message\n"));
 2032              		.loc 1 690 7 is_stmt 1 view .LVU545
 690:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short CONNACK message\n"));
 2033              		.loc 1 690 10 is_stmt 0 view .LVU546
 2034 00d0 012D     		cmp	r5, #1
 2035 00d2 40F2BD80 		bls	.L165
 695:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: Connect response code %d\n", res));
 2036              		.loc 1 695 7 is_stmt 1 view .LVU547
 695:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: Connect response code %d\n", res));
 2037              		.loc 1 695 54 is_stmt 0 view .LVU548
 2038 00d6 7D78     		ldrb	r5, [r7, #1]	@ zero_extendqisi2
 2039              	.LVL227:
 696:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (res == MQTT_CONNECT_ACCEPTED) {
 2040              		.loc 1 696 96 is_stmt 1 view .LVU549
 697:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Reset cyclic_tick when changing to connected state */
 2041              		.loc 1 697 7 view .LVU550
 697:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Reset cyclic_tick when changing to connected state */
 2042              		.loc 1 697 10 is_stmt 0 view .LVU551
 2043 00d8 002D     		cmp	r5, #0
 2044 00da F5D1     		bne	.L150
 699:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->conn_state = MQTT_CONNECTED;
 2045              		.loc 1 699 9 is_stmt 1 view .LVU552
 699:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->conn_state = MQTT_CONNECTED;
 2046              		.loc 1 699 29 is_stmt 0 view .LVU553
 2047 00dc 0023     		movs	r3, #0
 2048 00de 2380     		strh	r3, [r4]	@ movhi
 700:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Notify upper layer */
ARM GAS  /tmp/ccBURsTh.s 			page 63


 2049              		.loc 1 700 9 is_stmt 1 view .LVU554
 700:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Notify upper layer */
 2050              		.loc 1 700 28 is_stmt 0 view .LVU555
 2051 00e0 0323     		movs	r3, #3
 2052 00e2 A372     		strb	r3, [r4, #10]
 702:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->connect_cb(client, client->connect_arg, res);
 2053              		.loc 1 702 9 is_stmt 1 view .LVU556
 702:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->connect_cb(client, client->connect_arg, res);
 2054              		.loc 1 702 19 is_stmt 0 view .LVU557
 2055 00e4 6369     		ldr	r3, [r4, #20]
 702:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->connect_cb(client, client->connect_arg, res);
 2056              		.loc 1 702 12 view .LVU558
 2057 00e6 002B     		cmp	r3, #0
 2058 00e8 EED0     		beq	.L150
 703:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 2059              		.loc 1 703 11 is_stmt 1 view .LVU559
 2060 00ea 2A46     		mov	r2, r5
 2061 00ec 2169     		ldr	r1, [r4, #16]
 2062 00ee 2046     		mov	r0, r4
 2063 00f0 9847     		blx	r3
 2064              	.LVL228:
 2065 00f2 E9E7     		b	.L150
 2066              	.LVL229:
 2067              	.L189:
 2068              	.LBB3:
 713:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u16_t payload_length = length;
 2069              		.loc 1 713 5 view .LVU560
 714:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u8_t qos = MQTT_CTL_PACKET_QOS(client->rx_buffer[0]);
 2070              		.loc 1 714 5 view .LVU561
 715:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2071              		.loc 1 715 5 view .LVU562
 715:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2072              		.loc 1 715 16 is_stmt 0 view .LVU563
 2073 00f4 94F86C80 		ldrb	r8, [r4, #108]	@ zero_extendqisi2
 2074              	.LVL230:
 715:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2075              		.loc 1 715 10 view .LVU564
 2076 00f8 C8F34108 		ubfx	r8, r8, #1, #2
 2077              	.LVL231:
 717:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Should have topic and pkt id*/
 2078              		.loc 1 717 5 is_stmt 1 view .LVU565
 717:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Should have topic and pkt id*/
 2079              		.loc 1 717 15 is_stmt 0 view .LVU566
 2080 00fc A36E     		ldr	r3, [r4, #104]
 717:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Should have topic and pkt id*/
 2081              		.loc 1 717 8 view .LVU567
 2082 00fe 802B     		cmp	r3, #128
 2083 0100 57D8     		bhi	.L167
 2084              	.LBB4:
 719:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t after_topic;
 2085              		.loc 1 719 7 is_stmt 1 view .LVU568
 720:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u8_t bkp;
 2086              		.loc 1 720 7 view .LVU569
 721:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t topic_len;
 2087              		.loc 1 721 7 view .LVU570
 722:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       u16_t qos_len = (qos ? 2U : 0U);
 2088              		.loc 1 722 7 view .LVU571
ARM GAS  /tmp/ccBURsTh.s 			page 64


 723:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2 + qos_len) {
 2089              		.loc 1 723 7 view .LVU572
 723:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2 + qos_len) {
 2090              		.loc 1 723 13 is_stmt 0 view .LVU573
 2091 0102 B8F1000F 		cmp	r8, #0
 2092 0106 38D0     		beq	.L168
 723:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2 + qos_len) {
 2093              		.loc 1 723 13 discriminator 1 view .LVU574
 2094 0108 0223     		movs	r3, #2
 2095              	.L154:
 2096              	.LVL232:
 724:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet\n"));
 2097              		.loc 1 724 7 is_stmt 1 view .LVU575
 724:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet\n"));
 2098              		.loc 1 724 18 is_stmt 0 view .LVU576
 2099 010a 5A1C     		adds	r2, r3, #1
 724:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet\n"));
 2100              		.loc 1 724 10 view .LVU577
 2101 010c 9542     		cmp	r5, r2
 2102 010e 40F3A480 		ble	.L169
 728:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic_len = (topic_len << 8) + (u16_t)(var_hdr_payload[1]);
 2103              		.loc 1 728 7 is_stmt 1 view .LVU578
 728:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       topic_len = (topic_len << 8) + (u16_t)(var_hdr_payload[1]);
 2104              		.loc 1 728 34 is_stmt 0 view .LVU579
 2105 0112 1BF80920 		ldrb	r2, [fp, r9]	@ zero_extendqisi2
 2106              	.LVL233:
 729:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((topic_len > length - (2 + qos_len)) ||
 2107              		.loc 1 729 7 is_stmt 1 view .LVU580
 729:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((topic_len > length - (2 + qos_len)) ||
 2108              		.loc 1 729 61 is_stmt 0 view .LVU581
 2109 0116 97F801B0 		ldrb	fp, [r7, #1]	@ zero_extendqisi2
 729:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((topic_len > length - (2 + qos_len)) ||
 2110              		.loc 1 729 17 view .LVU582
 2111 011a 0BEB022B 		add	fp, fp, r2, lsl #8
 2112 011e 1FFA8BFB 		uxth	fp, fp
 2113              	.LVL234:
 730:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           (topic_len > var_hdr_payload_bufsize - (2 + qos_len))) {
 2114              		.loc 1 730 7 is_stmt 1 view .LVU583
 730:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           (topic_len > var_hdr_payload_bufsize - (2 + qos_len))) {
 2115              		.loc 1 730 36 is_stmt 0 view .LVU584
 2116 0122 9A1C     		adds	r2, r3, #2
 730:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           (topic_len > var_hdr_payload_bufsize - (2 + qos_len))) {
 2117              		.loc 1 730 31 view .LVU585
 2118 0124 AA1A     		subs	r2, r5, r2
 730:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           (topic_len > var_hdr_payload_bufsize - (2 + qos_len))) {
 2119              		.loc 1 730 10 view .LVU586
 2120 0126 9345     		cmp	fp, r2
 2121 0128 00F39A80 		bgt	.L170
 731:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (topic)
 2122              		.loc 1 731 48 view .LVU587
 2123 012c AAEB0303 		sub	r3, r10, r3
 2124              	.LVL235:
 731:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (topic)
 2125              		.loc 1 731 48 view .LVU588
 2126 0130 023B     		subs	r3, r3, #2
 730:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           (topic_len > var_hdr_payload_bufsize - (2 + qos_len))) {
 2127              		.loc 1 730 48 discriminator 1 view .LVU589
ARM GAS  /tmp/ccBURsTh.s 			page 65


 2128 0132 9B45     		cmp	fp, r3
 2129 0134 00F29780 		bhi	.L171
 736:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       after_topic = 2 + topic_len;
 2130              		.loc 1 736 7 is_stmt 1 view .LVU590
 736:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       after_topic = 2 + topic_len;
 2131              		.loc 1 736 13 is_stmt 0 view .LVU591
 2132 0138 BB1C     		adds	r3, r7, #2
 2133 013a 0093     		str	r3, [sp]
 2134              	.LVL236:
 737:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Check buffer length, add one byte even for QoS 0 so that zero termination will fit */
 2135              		.loc 1 737 7 is_stmt 1 view .LVU592
 737:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Check buffer length, add one byte even for QoS 0 so that zero termination will fit */
 2136              		.loc 1 737 19 is_stmt 0 view .LVU593
 2137 013c 0BF10209 		add	r9, fp, #2
 2138 0140 1FFA89F9 		uxth	r9, r9
 2139              	.LVL237:
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2140              		.loc 1 739 7 is_stmt 1 view .LVU594
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2141              		.loc 1 739 24 is_stmt 0 view .LVU595
 2142 0144 4A46     		mov	r2, r9
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2143              		.loc 1 739 36 view .LVU596
 2144 0146 B8F1000F 		cmp	r8, #0
 2145 014a 18D0     		beq	.L172
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2146              		.loc 1 739 36 discriminator 1 view .LVU597
 2147 014c 0223     		movs	r3, #2
 2148              	.LVL238:
 2149              	.L155:
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2150              		.loc 1 739 24 discriminator 4 view .LVU598
 2151 014e 1344     		add	r3, r3, r2
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2152              		.loc 1 739 10 discriminator 4 view .LVU599
 2153 0150 5345     		cmp	r3, r10
 2154 0152 00F28B80 		bhi	.L173
 745:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (length < after_topic + 2U) {
 2155              		.loc 1 745 7 is_stmt 1 view .LVU600
 745:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (length < after_topic + 2U) {
 2156              		.loc 1 745 10 is_stmt 0 view .LVU601
 2157 0156 B8F1000F 		cmp	r8, #0
 2158 015a 12D0     		beq	.L156
 746:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (afte
 2159              		.loc 1 746 9 is_stmt 1 view .LVU602
 746:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (afte
 2160              		.loc 1 746 34 is_stmt 0 view .LVU603
 2161 015c 931C     		adds	r3, r2, #2
 746:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short PUBLISH packet (afte
 2162              		.loc 1 746 12 view .LVU604
 2163 015e 9D42     		cmp	r5, r3
 2164 0160 C0F08780 		bcc	.L174
 750:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         after_topic += 2;
 2165              		.loc 1 750 9 is_stmt 1 view .LVU605
 750:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         after_topic += 2;
 2166              		.loc 1 750 55 is_stmt 0 view .LVU606
 2167 0164 B95C     		ldrb	r1, [r7, r2]	@ zero_extendqisi2
ARM GAS  /tmp/ccBURsTh.s 			page 66


 750:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         after_topic += 2;
 2168              		.loc 1 750 99 view .LVU607
 2169 0166 3A44     		add	r2, r2, r7
 2170 0168 5378     		ldrb	r3, [r2, #1]	@ zero_extendqisi2
 750:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         after_topic += 2;
 2171              		.loc 1 750 75 view .LVU608
 2172 016a 03EB0123 		add	r3, r3, r1, lsl #8
 750:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         after_topic += 2;
 2173              		.loc 1 750 30 view .LVU609
 2174 016e 2381     		strh	r3, [r4, #8]	@ movhi
 751:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 2175              		.loc 1 751 9 is_stmt 1 view .LVU610
 751:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 2176              		.loc 1 751 21 is_stmt 0 view .LVU611
 2177 0170 0BF10409 		add	r9, fp, #4
 2178              	.LVL239:
 751:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 2179              		.loc 1 751 21 view .LVU612
 2180 0174 1FFA89F9 		uxth	r9, r9
 2181              	.LVL240:
 751:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 2182              		.loc 1 751 21 view .LVU613
 2183 0178 05E0     		b	.L157
 2184              	.LVL241:
 2185              	.L168:
 723:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < 2 + qos_len) {
 2186              		.loc 1 723 13 discriminator 2 view .LVU614
 2187 017a 0023     		movs	r3, #0
 2188 017c C5E7     		b	.L154
 2189              	.LVL242:
 2190              	.L172:
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2191              		.loc 1 739 36 discriminator 2 view .LVU615
 2192 017e 0123     		movs	r3, #1
 2193              	.LVL243:
 739:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Receive buffer can not fit topic + pk
 2194              		.loc 1 739 36 discriminator 2 view .LVU616
 2195 0180 E5E7     		b	.L155
 2196              	.L156:
 753:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2197              		.loc 1 753 9 is_stmt 1 view .LVU617
 753:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2198              		.loc 1 753 30 is_stmt 0 view .LVU618
 2199 0182 0023     		movs	r3, #0
 2200 0184 2381     		strh	r3, [r4, #8]	@ movhi
 2201              	.L157:
 756:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Zero terminate string */
 2202              		.loc 1 756 7 is_stmt 1 view .LVU619
 756:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Zero terminate string */
 2203              		.loc 1 756 11 is_stmt 0 view .LVU620
 2204 0186 0099     		ldr	r1, [sp]
 2205 0188 11F80B30 		ldrb	r3, [r1, fp]	@ zero_extendqisi2
 2206 018c 0193     		str	r3, [sp, #4]
 2207              	.LVL244:
 758:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Payload data remaining in receive buffer */
 2208              		.loc 1 758 7 is_stmt 1 view .LVU621
 758:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Payload data remaining in receive buffer */
ARM GAS  /tmp/ccBURsTh.s 			page 67


 2209              		.loc 1 758 24 is_stmt 0 view .LVU622
 2210 018e 0023     		movs	r3, #0
 2211 0190 01F80B30 		strb	r3, [r1, fp]
 760:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       payload_offset = after_topic;
 2212              		.loc 1 760 7 is_stmt 1 view .LVU623
 760:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       payload_offset = after_topic;
 2213              		.loc 1 760 22 is_stmt 0 view .LVU624
 2214 0194 A5EB090A 		sub	r10, r5, r9
 2215              	.LVL245:
 760:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       payload_offset = after_topic;
 2216              		.loc 1 760 22 view .LVU625
 2217 0198 1FFA8AFA 		uxth	r10, r10
 2218              	.LVL246:
 761:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2219              		.loc 1 761 7 is_stmt 1 view .LVU626
 764:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (client->pub_cb != NULL) {
 2220              		.loc 1 764 85 view .LVU627
 765:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->pub_cb(client->inpub_arg, (const char *)topic, remaining_length + payload_length);
 2221              		.loc 1 765 7 view .LVU628
 765:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->pub_cb(client->inpub_arg, (const char *)topic, remaining_length + payload_length);
 2222              		.loc 1 765 17 is_stmt 0 view .LVU629
 2223 019c 636E     		ldr	r3, [r4, #100]
 765:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->pub_cb(client->inpub_arg, (const char *)topic, remaining_length + payload_length);
 2224              		.loc 1 765 10 view .LVU630
 2225 019e 1BB1     		cbz	r3, .L158
 766:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2226              		.loc 1 766 9 is_stmt 1 view .LVU631
 2227 01a0 0AEB0602 		add	r2, r10, r6
 2228 01a4 E06D     		ldr	r0, [r4, #92]
 2229 01a6 9847     		blx	r3
 2230              	.LVL247:
 2231              	.L158:
 769:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 2232              		.loc 1 769 7 view .LVU632
 769:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 2233              		.loc 1 769 24 is_stmt 0 view .LVU633
 2234 01a8 009B     		ldr	r3, [sp]
 2235 01aa 019A     		ldr	r2, [sp, #4]
 2236 01ac 03F80B20 		strb	r2, [r3, fp]
 2237 01b0 02E0     		b	.L153
 2238              	.LVL248:
 2239              	.L167:
 769:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 2240              		.loc 1 769 24 view .LVU634
 2241              	.LBE4:
 714:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u8_t qos = MQTT_CTL_PACKET_QOS(client->rx_buffer[0]);
 2242              		.loc 1 714 11 view .LVU635
 2243 01b2 AA46     		mov	r10, r5
 2244              	.LVL249:
 713:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     u16_t payload_length = length;
 2245              		.loc 1 713 11 view .LVU636
 2246 01b4 4FF00009 		mov	r9, #0
 2247              	.LVL250:
 2248              	.L153:
 771:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < (size_t)(payload_offset + payload_length)) {
 2249              		.loc 1 771 5 is_stmt 1 view .LVU637
 771:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < (size_t)(payload_offset + payload_length)) {
ARM GAS  /tmp/ccBURsTh.s 			page 68


 2250              		.loc 1 771 48 is_stmt 0 view .LVU638
 2251 01b8 B6FA86F6 		clz	r6, r6
 2252              	.LVL251:
 771:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < (size_t)(payload_offset + payload_length)) {
 2253              		.loc 1 771 48 view .LVU639
 2254 01bc 7609     		lsrs	r6, r6, #5
 771:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < (size_t)(payload_offset + payload_length)) {
 2255              		.loc 1 771 28 view .LVU640
 2256 01be BAF1000F 		cmp	r10, #0
 2257 01c2 0CBF     		ite	eq
 2258 01c4 3346     		moveq	r3, r6
 2259 01c6 46F00103 		orrne	r3, r6, #1
 771:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (length < (size_t)(payload_offset + payload_length)) {
 2260              		.loc 1 771 8 view .LVU641
 2261 01ca 002B     		cmp	r3, #0
 2262 01cc 54D0     		beq	.L175
 772:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short packet (payload)\n"));
 2263              		.loc 1 772 7 is_stmt 1 view .LVU642
 772:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short packet (payload)\n"));
 2264              		.loc 1 772 44 is_stmt 0 view .LVU643
 2265 01ce 09EB0A03 		add	r3, r9, r10
 772:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN,( "mqtt_message_received: Received short packet (payload)\n"));
 2266              		.loc 1 772 10 view .LVU644
 2267 01d2 9D42     		cmp	r5, r3
 2268 01d4 52D3     		bcc	.L176
 776:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reply if QoS > 0 */
 2269              		.loc 1 776 7 is_stmt 1 view .LVU645
 776:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reply if QoS > 0 */
 2270              		.loc 1 776 13 is_stmt 0 view .LVU646
 2271 01d6 256E     		ldr	r5, [r4, #96]
 776:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reply if QoS > 0 */
 2272              		.loc 1 776 7 view .LVU647
 2273 01d8 3346     		mov	r3, r6
 2274 01da 5246     		mov	r2, r10
 2275 01dc 07EB0901 		add	r1, r7, r9
 2276 01e0 E06D     		ldr	r0, [r4, #92]
 2277 01e2 A847     		blx	r5
 2278              	.LVL252:
 778:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Send PUBACK for QoS 1 or PUBREC for QoS 2 */
 2279              		.loc 1 778 7 is_stmt 1 view .LVU648
 778:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Send PUBACK for QoS 1 or PUBREC for QoS 2 */
 2280              		.loc 1 778 33 is_stmt 0 view .LVU649
 2281 01e4 B8F1000F 		cmp	r8, #0
 2282 01e8 0CBF     		ite	eq
 2283 01ea 0026     		moveq	r6, #0
 2284 01ec 06F00106 		andne	r6, r6, #1
 778:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Send PUBACK for QoS 1 or PUBREC for QoS 2 */
 2285              		.loc 1 778 10 view .LVU650
 2286 01f0 002E     		cmp	r6, #0
 2287 01f2 46D0     		beq	.L177
 2288              	.LBB5:
 780:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Sending publish response: %s with p
 2289              		.loc 1 780 9 is_stmt 1 view .LVU651
 780:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Sending publish response: %s with p
 2290              		.loc 1 780 14 is_stmt 0 view .LVU652
 2291 01f4 B8F1010F 		cmp	r8, #1
 2292 01f8 07D0     		beq	.L191
ARM GAS  /tmp/ccBURsTh.s 			page 69


 780:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Sending publish response: %s with p
 2293              		.loc 1 780 14 discriminator 2 view .LVU653
 2294 01fa 0521     		movs	r1, #5
 2295              	.L159:
 2296              	.LVL253:
 782:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         pub_ack_rec_rel_response(client, resp_msg, client->inpub_pkt_id, 0);
 2297              		.loc 1 782 94 is_stmt 1 view .LVU654
 783:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2298              		.loc 1 783 9 view .LVU655
 2299 01fc 0023     		movs	r3, #0
 2300 01fe 2289     		ldrh	r2, [r4, #8]
 2301 0200 2046     		mov	r0, r4
 2302 0202 FFF7FEFF 		bl	pub_ack_rec_rel_response
 2303              	.LVL254:
 783:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2304              		.loc 1 783 9 is_stmt 0 view .LVU656
 2305              	.LBE5:
 2306              	.LBE3:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2307              		.loc 1 674 28 view .LVU657
 2308 0206 0025     		movs	r5, #0
 2309 0208 5EE7     		b	.L150
 2310              	.LVL255:
 2311              	.L191:
 2312              	.LBB7:
 2313              	.LBB6:
 780:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Sending publish response: %s with p
 2314              		.loc 1 780 14 discriminator 1 view .LVU658
 2315 020a 0421     		movs	r1, #4
 2316 020c F6E7     		b	.L159
 2317              	.LVL256:
 2318              	.L161:
 780:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_incomming_publish: Sending publish response: %s with p
 2319              		.loc 1 780 14 discriminator 1 view .LVU659
 2320              	.LBE6:
 2321              	.LBE7:
 799:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pub_ack_rec_rel_response(client, MQTT_MSG_TYPE_PUBCOMP, pkt_id, 0);
 2322              		.loc 1 799 123 is_stmt 1 view .LVU660
 800:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2323              		.loc 1 800 7 view .LVU661
 2324 020e 0023     		movs	r3, #0
 2325 0210 0721     		movs	r1, #7
 2326 0212 2046     		mov	r0, r4
 2327 0214 FFF7FEFF 		bl	pub_ack_rec_rel_response
 2328              	.LVL257:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2329              		.loc 1 674 28 is_stmt 0 view .LVU662
 2330 0218 0025     		movs	r5, #0
 2331 021a 55E7     		b	.L150
 2332              	.LVL258:
 2333              	.L160:
 2334              	.LBB8:
 804:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (r != NULL) {
 2335              		.loc 1 804 7 is_stmt 1 view .LVU663
 804:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (r != NULL) {
 2336              		.loc 1 804 34 is_stmt 0 view .LVU664
 2337 021c 1146     		mov	r1, r2
ARM GAS  /tmp/ccBURsTh.s 			page 70


 2338 021e 04F11800 		add	r0, r4, #24
 2339 0222 FFF7FEFF 		bl	mqtt_take_request
 2340              	.LVL259:
 805:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: %s response with id %d\n", mqtt_msg_
 2341              		.loc 1 805 7 is_stmt 1 view .LVU665
 805:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: %s response with id %d\n", mqtt_msg_
 2342              		.loc 1 805 10 is_stmt 0 view .LVU666
 2343 0226 0446     		mov	r4, r0
 2344              	.LVL260:
 805:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_message_received: %s response with id %d\n", mqtt_msg_
 2345              		.loc 1 805 10 view .LVU667
 2346 0228 B0B3     		cbz	r0, .L182
 806:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (pkt_type == MQTT_MSG_TYPE_SUBACK) {
 2347              		.loc 1 806 131 is_stmt 1 view .LVU668
 807:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           if (length < 3) {
 2348              		.loc 1 807 9 view .LVU669
 807:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           if (length < 3) {
 2349              		.loc 1 807 12 is_stmt 0 view .LVU670
 2350 022a B8F1090F 		cmp	r8, #9
 2351 022e 09D0     		beq	.L192
 814:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           r->cb(r->arg, ERR_OK);
 2352              		.loc 1 814 16 is_stmt 1 view .LVU671
 814:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           r->cb(r->arg, ERR_OK);
 2353              		.loc 1 814 21 is_stmt 0 view .LVU672
 2354 0230 4368     		ldr	r3, [r0, #4]
 814:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           r->cb(r->arg, ERR_OK);
 2355              		.loc 1 814 19 view .LVU673
 2356 0232 13B1     		cbz	r3, .L163
 815:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 2357              		.loc 1 815 11 is_stmt 1 view .LVU674
 2358 0234 0021     		movs	r1, #0
 2359 0236 8068     		ldr	r0, [r0, #8]
 2360              	.LVL261:
 815:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 2361              		.loc 1 815 11 is_stmt 0 view .LVU675
 2362 0238 9847     		blx	r3
 2363              	.LVL262:
 2364              	.L163:
 817:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 2365              		.loc 1 817 9 is_stmt 1 view .LVU676
 2366 023a 2046     		mov	r0, r4
 2367 023c FFF7FEFF 		bl	mqtt_delete_request
 2368              	.LVL263:
 2369              	.LBE8:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2370              		.loc 1 674 28 is_stmt 0 view .LVU677
 2371 0240 0025     		movs	r5, #0
 2372              	.LBB9:
 2373 0242 41E7     		b	.L150
 2374              	.LVL264:
 2375              	.L192:
 808:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: To small SUBACK packet\n"));
 2376              		.loc 1 808 11 is_stmt 1 view .LVU678
 808:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: To small SUBACK packet\n"));
 2377              		.loc 1 808 14 is_stmt 0 view .LVU679
 2378 0244 022D     		cmp	r5, #2
 2379 0246 29D9     		bls	.L183
ARM GAS  /tmp/ccBURsTh.s 			page 71


 812:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 2380              		.loc 1 812 13 is_stmt 1 view .LVU680
 2381 0248 B978     		ldrb	r1, [r7, #2]	@ zero_extendqisi2
 2382 024a FFF7FEFF 		bl	mqtt_incomming_suback
 2383              	.LVL265:
 812:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 2384              		.loc 1 812 13 is_stmt 0 view .LVU681
 2385 024e F4E7     		b	.L163
 2386              	.LVL266:
 2387              	.L165:
 812:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 2388              		.loc 1 812 13 view .LVU682
 2389              	.LBE9:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2390              		.loc 1 828 10 view .LVU683
 2391 0250 4FF48075 		mov	r5, #256
 2392 0254 38E7     		b	.L150
 2393              	.L166:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2394              		.loc 1 674 28 view .LVU684
 2395 0256 0025     		movs	r5, #0
 2396 0258 36E7     		b	.L150
 2397              	.LVL267:
 2398              	.L169:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2399              		.loc 1 828 10 view .LVU685
 2400 025a 4FF48075 		mov	r5, #256
 2401              	.LVL268:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2402              		.loc 1 828 10 view .LVU686
 2403 025e 33E7     		b	.L150
 2404              	.LVL269:
 2405              	.L170:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2406              		.loc 1 828 10 view .LVU687
 2407 0260 4FF48075 		mov	r5, #256
 2408              	.LVL270:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2409              		.loc 1 828 10 view .LVU688
 2410 0264 30E7     		b	.L150
 2411              	.LVL271:
 2412              	.L171:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2413              		.loc 1 828 10 view .LVU689
 2414 0266 4FF48075 		mov	r5, #256
 2415              	.LVL272:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2416              		.loc 1 828 10 view .LVU690
 2417 026a 2DE7     		b	.L150
 2418              	.LVL273:
 2419              	.L173:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2420              		.loc 1 828 10 view .LVU691
 2421 026c 4FF48075 		mov	r5, #256
 2422              	.LVL274:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2423              		.loc 1 828 10 view .LVU692
ARM GAS  /tmp/ccBURsTh.s 			page 72


 2424 0270 2AE7     		b	.L150
 2425              	.LVL275:
 2426              	.L174:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2427              		.loc 1 828 10 view .LVU693
 2428 0272 4FF48075 		mov	r5, #256
 2429              	.LVL276:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2430              		.loc 1 828 10 view .LVU694
 2431 0276 27E7     		b	.L150
 2432              	.LVL277:
 2433              	.L175:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2434              		.loc 1 674 28 view .LVU695
 2435 0278 0025     		movs	r5, #0
 2436 027a 25E7     		b	.L150
 2437              	.L176:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2438              		.loc 1 828 10 view .LVU696
 2439 027c 4FF48075 		mov	r5, #256
 2440 0280 22E7     		b	.L150
 2441              	.L177:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2442              		.loc 1 674 28 view .LVU697
 2443 0282 0025     		movs	r5, #0
 2444 0284 20E7     		b	.L150
 2445              	.LVL278:
 2446              	.L179:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2447              		.loc 1 828 10 view .LVU698
 2448 0286 4FF48075 		mov	r5, #256
 2449 028a 1DE7     		b	.L150
 2450              	.L180:
 790:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_message_received: Got message with illegal packet identif
 2451              		.loc 1 790 8 view .LVU699
 2452 028c 4FF48075 		mov	r5, #256
 2453 0290 1AE7     		b	.L150
 2454              	.L181:
 2455 0292 4FF48075 		mov	r5, #256
 2456 0296 17E7     		b	.L150
 2457              	.LVL279:
 2458              	.L182:
 674:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2459              		.loc 1 674 28 view .LVU700
 2460 0298 0025     		movs	r5, #0
 2461 029a 15E7     		b	.L150
 2462              	.L183:
 828:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2463              		.loc 1 828 10 view .LVU701
 2464 029c 4FF48075 		mov	r5, #256
 2465 02a0 12E7     		b	.L150
 2466              	.L194:
 2467 02a2 00BF     		.align	2
 2468              	.L193:
 2469 02a4 00000000 		.word	.LC0
 2470 02a8 00000000 		.word	.LC11
 2471 02ac 58000000 		.word	.LC2
ARM GAS  /tmp/ccBURsTh.s 			page 73


 2472 02b0 30000000 		.word	.LC12
 2473 02b4 54000000 		.word	.LC13
 2474              		.cfi_endproc
 2475              	.LFE197:
 2477              		.section	.rodata.mqtt_close.str1.4,"aMS",%progbits,1
 2478              		.align	2
 2479              	.LC14:
 2480 0000 6D717474 		.ascii	"mqtt_close: client != NULL\000"
 2480      5F636C6F 
 2480      73653A20 
 2480      636C6965 
 2480      6E742021 
 2481              		.section	.text.mqtt_close,"ax",%progbits
 2482              		.align	1
 2483              		.syntax unified
 2484              		.thumb
 2485              		.thumb_func
 2487              	mqtt_close:
 2488              	.LVL280:
 2489              	.LFB193:
 538:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_close: client != NULL", client != NULL);
 2490              		.loc 1 538 1 is_stmt 1 view -0
 2491              		.cfi_startproc
 2492              		@ args = 0, pretend = 0, frame = 0
 2493              		@ frame_needed = 0, uses_anonymous_args = 0
 538:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_close: client != NULL", client != NULL);
 2494              		.loc 1 538 1 is_stmt 0 view .LVU703
 2495 0000 38B5     		push	{r3, r4, r5, lr}
 2496              	.LCFI25:
 2497              		.cfi_def_cfa_offset 16
 2498              		.cfi_offset 3, -16
 2499              		.cfi_offset 4, -12
 2500              		.cfi_offset 5, -8
 2501              		.cfi_offset 14, -4
 2502 0002 0D46     		mov	r5, r1
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2503              		.loc 1 539 3 is_stmt 1 view .LVU704
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2504              		.loc 1 539 3 view .LVU705
 2505 0004 0446     		mov	r4, r0
 2506 0006 28B3     		cbz	r0, .L201
 2507              	.LVL281:
 2508              	.L196:
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2509              		.loc 1 539 3 discriminator 3 view .LVU706
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2510              		.loc 1 539 3 discriminator 3 view .LVU707
 542:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err_t res;
 2511              		.loc 1 542 3 view .LVU708
 542:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err_t res;
 2512              		.loc 1 542 13 is_stmt 0 view .LVU709
 2513 0008 E068     		ldr	r0, [r4, #12]
 542:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     err_t res;
 2514              		.loc 1 542 6 view .LVU710
 2515 000a 80B1     		cbz	r0, .L197
 2516              	.LBB10:
 543:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_recv(client->conn, NULL);
ARM GAS  /tmp/ccBURsTh.s 			page 74


 2517              		.loc 1 543 5 is_stmt 1 view .LVU711
 544:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_err(client->conn,  NULL);
 2518              		.loc 1 544 5 view .LVU712
 2519 000c 0021     		movs	r1, #0
 2520 000e FFF7FEFF 		bl	tcp_recv
 2521              	.LVL282:
 545:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     altcp_sent(client->conn, NULL);
 2522              		.loc 1 545 5 view .LVU713
 2523 0012 0021     		movs	r1, #0
 2524 0014 E068     		ldr	r0, [r4, #12]
 2525 0016 FFF7FEFF 		bl	tcp_err
 2526              	.LVL283:
 546:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     res = altcp_close(client->conn);
 2527              		.loc 1 546 5 view .LVU714
 2528 001a 0021     		movs	r1, #0
 2529 001c E068     		ldr	r0, [r4, #12]
 2530 001e FFF7FEFF 		bl	tcp_sent
 2531              	.LVL284:
 547:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (res != ERR_OK) {
 2532              		.loc 1 547 5 view .LVU715
 547:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (res != ERR_OK) {
 2533              		.loc 1 547 11 is_stmt 0 view .LVU716
 2534 0022 E068     		ldr	r0, [r4, #12]
 2535 0024 FFF7FEFF 		bl	tcp_close
 2536              	.LVL285:
 548:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       altcp_abort(client->conn);
 2537              		.loc 1 548 5 is_stmt 1 view .LVU717
 548:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       altcp_abort(client->conn);
 2538              		.loc 1 548 8 is_stmt 0 view .LVU718
 2539 0028 E0B9     		cbnz	r0, .L202
 2540              	.LVL286:
 2541              	.L198:
 550:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 2542              		.loc 1 550 86 is_stmt 1 view .LVU719
 552:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 2543              		.loc 1 552 5 view .LVU720
 552:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 2544              		.loc 1 552 18 is_stmt 0 view .LVU721
 2545 002a 0023     		movs	r3, #0
 2546 002c E360     		str	r3, [r4, #12]
 2547              	.LVL287:
 2548              	.L197:
 552:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 2549              		.loc 1 552 18 view .LVU722
 2550              	.LBE10:
 556:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Stop cyclic timer */
 2551              		.loc 1 556 3 is_stmt 1 view .LVU723
 2552 002e 04F11800 		add	r0, r4, #24
 2553 0032 FFF7FEFF 		bl	mqtt_clear_requests
 2554              	.LVL288:
 558:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2555              		.loc 1 558 3 view .LVU724
 2556 0036 2146     		mov	r1, r4
 2557 0038 0C48     		ldr	r0, .L203
 2558 003a FFF7FEFF 		bl	sys_untimeout
 2559              	.LVL289:
 561:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
ARM GAS  /tmp/ccBURsTh.s 			page 75


 2560              		.loc 1 561 3 view .LVU725
 561:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2561              		.loc 1 561 13 is_stmt 0 view .LVU726
 2562 003e A37A     		ldrb	r3, [r4, #10]	@ zero_extendqisi2
 561:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2563              		.loc 1 561 6 view .LVU727
 2564 0040 3BB1     		cbz	r3, .L195
 563:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->connect_cb != NULL) {
 2565              		.loc 1 563 5 is_stmt 1 view .LVU728
 563:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client->connect_cb != NULL) {
 2566              		.loc 1 563 24 is_stmt 0 view .LVU729
 2567 0042 0023     		movs	r3, #0
 2568 0044 A372     		strb	r3, [r4, #10]
 564:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->connect_cb(client, client->connect_arg, reason);
 2569              		.loc 1 564 5 is_stmt 1 view .LVU730
 564:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->connect_cb(client, client->connect_arg, reason);
 2570              		.loc 1 564 15 is_stmt 0 view .LVU731
 2571 0046 6369     		ldr	r3, [r4, #20]
 564:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       client->connect_cb(client, client->connect_arg, reason);
 2572              		.loc 1 564 8 view .LVU732
 2573 0048 1BB1     		cbz	r3, .L195
 565:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 2574              		.loc 1 565 7 is_stmt 1 view .LVU733
 2575 004a 2A46     		mov	r2, r5
 2576 004c 2169     		ldr	r1, [r4, #16]
 2577 004e 2046     		mov	r0, r4
 2578 0050 9847     		blx	r3
 2579              	.LVL290:
 2580              	.L195:
 568:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2581              		.loc 1 568 1 is_stmt 0 view .LVU734
 2582 0052 38BD     		pop	{r3, r4, r5, pc}
 2583              	.LVL291:
 2584              	.L201:
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2585              		.loc 1 539 3 is_stmt 1 discriminator 1 view .LVU735
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2586              		.loc 1 539 3 discriminator 1 view .LVU736
 2587 0054 064B     		ldr	r3, .L203+4
 2588 0056 40F21B22 		movw	r2, #539
 2589 005a 0649     		ldr	r1, .L203+8
 2590              	.LVL292:
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2591              		.loc 1 539 3 is_stmt 0 discriminator 1 view .LVU737
 2592 005c 0648     		ldr	r0, .L203+12
 2593              	.LVL293:
 539:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2594              		.loc 1 539 3 discriminator 1 view .LVU738
 2595 005e FFF7FEFF 		bl	printf
 2596              	.LVL294:
 2597 0062 D1E7     		b	.L196
 2598              	.LVL295:
 2599              	.L202:
 2600              	.LBB11:
 549:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_close: Close err=%s\n", lwip_strerr(res)));
 2601              		.loc 1 549 7 is_stmt 1 view .LVU739
 2602 0064 E068     		ldr	r0, [r4, #12]
ARM GAS  /tmp/ccBURsTh.s 			page 76


 2603              	.LVL296:
 549:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_close: Close err=%s\n", lwip_strerr(res)));
 2604              		.loc 1 549 7 is_stmt 0 view .LVU740
 2605 0066 FFF7FEFF 		bl	tcp_abort
 2606              	.LVL297:
 2607 006a DEE7     		b	.L198
 2608              	.L204:
 2609              		.align	2
 2610              	.L203:
 2611 006c 00000000 		.word	mqtt_cyclic_timer
 2612 0070 00000000 		.word	.LC0
 2613 0074 00000000 		.word	.LC14
 2614 0078 58000000 		.word	.LC2
 2615              	.LBE11:
 2616              		.cfi_endproc
 2617              	.LFE193:
 2619              		.section	.rodata.mqtt_tcp_err_cb.str1.4,"aMS",%progbits,1
 2620              		.align	2
 2621              	.LC15:
 2622 0000 6D717474 		.ascii	"mqtt_tcp_err_cb: client != NULL\000"
 2622      5F746370 
 2622      5F657272 
 2622      5F63623A 
 2622      20636C69 
 2623              		.section	.text.mqtt_tcp_err_cb,"ax",%progbits
 2624              		.align	1
 2625              		.syntax unified
 2626              		.thumb
 2627              		.thumb_func
 2629              	mqtt_tcp_err_cb:
 2630              	.LVL298:
 2631              	.LFB201:
1010:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 2632              		.loc 1 1010 1 is_stmt 1 view -0
 2633              		.cfi_startproc
 2634              		@ args = 0, pretend = 0, frame = 0
 2635              		@ frame_needed = 0, uses_anonymous_args = 0
1010:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 2636              		.loc 1 1010 1 is_stmt 0 view .LVU742
 2637 0000 10B5     		push	{r4, lr}
 2638              	.LCFI26:
 2639              		.cfi_def_cfa_offset 8
 2640              		.cfi_offset 4, -8
 2641              		.cfi_offset 14, -4
1011:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_UNUSED_ARG(err); /* only used for debug output */
 2642              		.loc 1 1011 3 is_stmt 1 view .LVU743
 2643              	.LVL299:
1012:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_err_cb: TCP error callback: error %d, arg: %p\n", err, a
 2644              		.loc 1 1012 3 view .LVU744
1013:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_err_cb: client != NULL", client != NULL);
 2645              		.loc 1 1013 104 view .LVU745
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2646              		.loc 1 1014 3 view .LVU746
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2647              		.loc 1 1014 3 view .LVU747
 2648 0002 0446     		mov	r4, r0
 2649 0004 38B1     		cbz	r0, .L208
ARM GAS  /tmp/ccBURsTh.s 			page 77


 2650              	.LVL300:
 2651              	.L206:
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2652              		.loc 1 1014 3 discriminator 3 view .LVU748
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2653              		.loc 1 1014 3 discriminator 3 view .LVU749
1016:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_close(client, MQTT_CONNECT_DISCONNECTED);
 2654              		.loc 1 1016 3 view .LVU750
1016:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_close(client, MQTT_CONNECT_DISCONNECTED);
 2655              		.loc 1 1016 16 is_stmt 0 view .LVU751
 2656 0006 0023     		movs	r3, #0
 2657 0008 E360     		str	r3, [r4, #12]
1017:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2658              		.loc 1 1017 3 is_stmt 1 view .LVU752
 2659 000a 4FF48071 		mov	r1, #256
 2660 000e 2046     		mov	r0, r4
 2661 0010 FFF7FEFF 		bl	mqtt_close
 2662              	.LVL301:
1018:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2663              		.loc 1 1018 1 is_stmt 0 view .LVU753
 2664 0014 10BD     		pop	{r4, pc}
 2665              	.LVL302:
 2666              	.L208:
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2667              		.loc 1 1014 3 is_stmt 1 discriminator 1 view .LVU754
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2668              		.loc 1 1014 3 discriminator 1 view .LVU755
 2669 0016 044B     		ldr	r3, .L209
 2670 0018 40F2F632 		movw	r2, #1014
 2671 001c 0349     		ldr	r1, .L209+4
 2672              	.LVL303:
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2673              		.loc 1 1014 3 is_stmt 0 discriminator 1 view .LVU756
 2674 001e 0448     		ldr	r0, .L209+8
 2675              	.LVL304:
1014:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set conn to null before calling close as pcb is already deallocated*/
 2676              		.loc 1 1014 3 discriminator 1 view .LVU757
 2677 0020 FFF7FEFF 		bl	printf
 2678              	.LVL305:
 2679 0024 EFE7     		b	.L206
 2680              	.L210:
 2681 0026 00BF     		.align	2
 2682              	.L209:
 2683 0028 00000000 		.word	.LC0
 2684 002c 00000000 		.word	.LC15
 2685 0030 58000000 		.word	.LC2
 2686              		.cfi_endproc
 2687              	.LFE201:
 2689              		.section	.rodata.mqtt_cyclic_timer.str1.4,"aMS",%progbits,1
 2690              		.align	2
 2691              	.LC16:
 2692 0000 6D717474 		.ascii	"mqtt_cyclic_timer: client != NULL\000"
 2692      5F637963 
 2692      6C69635F 
 2692      74696D65 
 2692      723A2063 
 2693              		.section	.text.mqtt_cyclic_timer,"ax",%progbits
ARM GAS  /tmp/ccBURsTh.s 			page 78


 2694              		.align	1
 2695              		.syntax unified
 2696              		.thumb
 2697              		.thumb_func
 2699              	mqtt_cyclic_timer:
 2700              	.LVL306:
 2701              	.LFB194:
 577:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t restart_timer = 1;
 2702              		.loc 1 577 1 is_stmt 1 view -0
 2703              		.cfi_startproc
 2704              		@ args = 0, pretend = 0, frame = 0
 2705              		@ frame_needed = 0, uses_anonymous_args = 0
 577:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t restart_timer = 1;
 2706              		.loc 1 577 1 is_stmt 0 view .LVU759
 2707 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 2708              	.LCFI27:
 2709              		.cfi_def_cfa_offset 20
 2710              		.cfi_offset 4, -20
 2711              		.cfi_offset 5, -16
 2712              		.cfi_offset 6, -12
 2713              		.cfi_offset 7, -8
 2714              		.cfi_offset 14, -4
 2715 0002 83B0     		sub	sp, sp, #12
 2716              	.LCFI28:
 2717              		.cfi_def_cfa_offset 32
 578:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 2718              		.loc 1 578 3 is_stmt 1 view .LVU760
 2719              	.LVL307:
 579:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_cyclic_timer: client != NULL", client != NULL);
 2720              		.loc 1 579 3 view .LVU761
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2721              		.loc 1 580 3 view .LVU762
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2722              		.loc 1 580 3 view .LVU763
 2723 0004 0446     		mov	r4, r0
 2724 0006 30B1     		cbz	r0, .L221
 2725              	.LVL308:
 2726              	.L212:
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2727              		.loc 1 580 3 discriminator 3 view .LVU764
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2728              		.loc 1 580 3 discriminator 3 view .LVU765
 582:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->cyclic_tick++;
 2729              		.loc 1 582 3 view .LVU766
 582:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->cyclic_tick++;
 2730              		.loc 1 582 13 is_stmt 0 view .LVU767
 2731 0008 A37A     		ldrb	r3, [r4, #10]	@ zero_extendqisi2
 582:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->cyclic_tick++;
 2732              		.loc 1 582 6 view .LVU768
 2733 000a 022B     		cmp	r3, #2
 2734 000c 0BD0     		beq	.L222
 590:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Handle timeout for pending requests */
 2735              		.loc 1 590 10 is_stmt 1 view .LVU769
 590:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Handle timeout for pending requests */
 2736              		.loc 1 590 13 is_stmt 0 view .LVU770
 2737 000e 032B     		cmp	r3, #3
 2738 0010 1ED0     		beq	.L223
ARM GAS  /tmp/ccBURsTh.s 			page 79


 2739              	.LVL309:
 2740              	.L211:
 623:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2741              		.loc 1 623 1 view .LVU771
 2742 0012 03B0     		add	sp, sp, #12
 2743              	.LCFI29:
 2744              		.cfi_remember_state
 2745              		.cfi_def_cfa_offset 20
 2746              		@ sp needed
 2747 0014 F0BD     		pop	{r4, r5, r6, r7, pc}
 2748              	.LVL310:
 2749              	.L221:
 2750              	.LCFI30:
 2751              		.cfi_restore_state
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2752              		.loc 1 580 3 is_stmt 1 discriminator 1 view .LVU772
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2753              		.loc 1 580 3 discriminator 1 view .LVU773
 2754 0016 2A4B     		ldr	r3, .L227
 2755 0018 4FF41172 		mov	r2, #580
 2756 001c 2949     		ldr	r1, .L227+4
 2757 001e 2A48     		ldr	r0, .L227+8
 2758              	.LVL311:
 580:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2759              		.loc 1 580 3 is_stmt 0 discriminator 1 view .LVU774
 2760 0020 FFF7FEFF 		bl	printf
 2761              	.LVL312:
 2762 0024 F0E7     		b	.L212
 2763              	.L222:
 583:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if ((client->cyclic_tick * MQTT_CYCLIC_TIMER_INTERVAL) >= MQTT_CONNECT_TIMOUT) {
 2764              		.loc 1 583 5 is_stmt 1 view .LVU775
 583:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if ((client->cyclic_tick * MQTT_CYCLIC_TIMER_INTERVAL) >= MQTT_CONNECT_TIMOUT) {
 2765              		.loc 1 583 11 is_stmt 0 view .LVU776
 2766 0026 2388     		ldrh	r3, [r4]
 583:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if ((client->cyclic_tick * MQTT_CYCLIC_TIMER_INTERVAL) >= MQTT_CONNECT_TIMOUT) {
 2767              		.loc 1 583 24 view .LVU777
 2768 0028 0133     		adds	r3, r3, #1
 2769 002a 9BB2     		uxth	r3, r3
 2770 002c 2380     		strh	r3, [r4]	@ movhi
 584:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: CONNECT attempt to server timed out\n"));
 2771              		.loc 1 584 5 is_stmt 1 view .LVU778
 584:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: CONNECT attempt to server timed out\n"));
 2772              		.loc 1 584 30 is_stmt 0 view .LVU779
 2773 002e 03EB8303 		add	r3, r3, r3, lsl #2
 584:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: CONNECT attempt to server timed out\n"));
 2774              		.loc 1 584 8 view .LVU780
 2775 0032 632B     		cmp	r3, #99
 2776 0034 06DC     		bgt	.L224
 2777              	.LVL313:
 2778              	.L214:
 621:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 2779              		.loc 1 621 5 is_stmt 1 view .LVU781
 2780 0036 2246     		mov	r2, r4
 2781 0038 2449     		ldr	r1, .L227+12
 2782 003a 41F28830 		movw	r0, #5000
 2783 003e FFF7FEFF 		bl	sys_timeout
 2784              	.LVL314:
ARM GAS  /tmp/ccBURsTh.s 			page 80


 623:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2785              		.loc 1 623 1 is_stmt 0 view .LVU782
 2786 0042 E6E7     		b	.L211
 2787              	.LVL315:
 2788              	.L224:
 585:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Disconnect TCP */
 2789              		.loc 1 585 98 is_stmt 1 view .LVU783
 587:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       restart_timer = 0;
 2790              		.loc 1 587 7 view .LVU784
 2791 0044 40F20111 		movw	r1, #257
 2792 0048 2046     		mov	r0, r4
 2793 004a FFF7FEFF 		bl	mqtt_close
 2794              	.LVL316:
 588:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 2795              		.loc 1 588 7 view .LVU785
 620:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     sys_timeout(MQTT_CYCLIC_TIMER_INTERVAL * 1000, mqtt_cyclic_timer, arg);
 2796              		.loc 1 620 3 view .LVU786
 2797 004e E0E7     		b	.L211
 2798              	.LVL317:
 2799              	.L223:
 592:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2800              		.loc 1 592 5 view .LVU787
 2801 0050 0521     		movs	r1, #5
 2802 0052 04F11800 		add	r0, r4, #24
 2803 0056 FFF7FEFF 		bl	mqtt_request_time_elapsed
 2804              	.LVL318:
 595:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2805              		.loc 1 595 5 view .LVU788
 595:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2806              		.loc 1 595 15 is_stmt 0 view .LVU789
 2807 005a 6288     		ldrh	r2, [r4, #2]
 595:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2808              		.loc 1 595 8 view .LVU790
 2809 005c 002A     		cmp	r2, #0
 2810 005e EAD0     		beq	.L214
 597:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* If reception from server has been idle for 1.5*keep_alive time, server is considered unres
 2811              		.loc 1 597 7 is_stmt 1 view .LVU791
 597:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* If reception from server has been idle for 1.5*keep_alive time, server is considered unres
 2812              		.loc 1 597 13 is_stmt 0 view .LVU792
 2813 0060 A388     		ldrh	r3, [r4, #4]
 597:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* If reception from server has been idle for 1.5*keep_alive time, server is considered unres
 2814              		.loc 1 597 30 view .LVU793
 2815 0062 0133     		adds	r3, r3, #1
 2816 0064 9BB2     		uxth	r3, r3
 2817 0066 A380     		strh	r3, [r4, #4]	@ movhi
 599:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_cyclic_timer: Server incoming keep-alive timeout\n"));
 2818              		.loc 1 599 7 is_stmt 1 view .LVU794
 599:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_cyclic_timer: Server incoming keep-alive timeout\n"));
 2819              		.loc 1 599 36 is_stmt 0 view .LVU795
 2820 0068 03EB8303 		add	r3, r3, r3, lsl #2
 599:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_cyclic_timer: Server incoming keep-alive timeout\n"));
 2821              		.loc 1 599 88 view .LVU796
 2822 006c 02EB5202 		add	r2, r2, r2, lsr #1
 599:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_cyclic_timer: Server incoming keep-alive timeout\n"));
 2823              		.loc 1 599 10 view .LVU797
 2824 0070 9342     		cmp	r3, r2
 2825 0072 0BDC     		bgt	.L225
ARM GAS  /tmp/ccBURsTh.s 			page 81


 578:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 2826              		.loc 1 578 8 view .LVU798
 2827 0074 0125     		movs	r5, #1
 2828              	.LVL319:
 2829              	.L216:
 606:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: Sending keep-alive message to server\n")
 2830              		.loc 1 606 7 is_stmt 1 view .LVU799
 606:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: Sending keep-alive message to server\n")
 2831              		.loc 1 606 18 is_stmt 0 view .LVU800
 2832 0076 2388     		ldrh	r3, [r4]
 606:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: Sending keep-alive message to server\n")
 2833              		.loc 1 606 32 view .LVU801
 2834 0078 03EB8301 		add	r1, r3, r3, lsl #2
 606:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: Sending keep-alive message to server\n")
 2835              		.loc 1 606 71 view .LVU802
 2836 007c 6288     		ldrh	r2, [r4, #2]
 606:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_cyclic_timer: Sending keep-alive message to server\n")
 2837              		.loc 1 606 10 view .LVU803
 2838 007e 9142     		cmp	r1, r2
 2839 0080 0BDA     		bge	.L226
 613:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2840              		.loc 1 613 9 is_stmt 1 view .LVU804
 613:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2841              		.loc 1 613 28 is_stmt 0 view .LVU805
 2842 0082 0133     		adds	r3, r3, #1
 2843 0084 2380     		strh	r3, [r4]	@ movhi
 2844              	.L218:
 620:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     sys_timeout(MQTT_CYCLIC_TIMER_INTERVAL * 1000, mqtt_cyclic_timer, arg);
 2845              		.loc 1 620 3 is_stmt 1 view .LVU806
 620:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     sys_timeout(MQTT_CYCLIC_TIMER_INTERVAL * 1000, mqtt_cyclic_timer, arg);
 2846              		.loc 1 620 6 is_stmt 0 view .LVU807
 2847 0086 002D     		cmp	r5, #0
 2848 0088 C3D0     		beq	.L211
 2849 008a D4E7     		b	.L214
 2850              	.LVL320:
 2851              	.L225:
 600:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         mqtt_close(client, MQTT_CONNECT_TIMEOUT);
 2852              		.loc 1 600 98 is_stmt 1 view .LVU808
 601:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         restart_timer = 0;
 2853              		.loc 1 601 9 view .LVU809
 2854 008c 40F20111 		movw	r1, #257
 2855 0090 2046     		mov	r0, r4
 2856 0092 FFF7FEFF 		bl	mqtt_close
 2857              	.LVL321:
 602:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2858              		.loc 1 602 9 view .LVU810
 602:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 2859              		.loc 1 602 23 is_stmt 0 view .LVU811
 2860 0096 0025     		movs	r5, #0
 2861 0098 EDE7     		b	.L216
 2862              	.LVL322:
 2863              	.L226:
 607:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (mqtt_output_check_space(&client->output, 0) != 0) {
 2864              		.loc 1 607 101 is_stmt 1 view .LVU812
 608:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           mqtt_output_append_fixed_header(&client->output, MQTT_MSG_TYPE_PINGREQ, 0, 0, 0, 0);
 2865              		.loc 1 608 9 view .LVU813
 608:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           mqtt_output_append_fixed_header(&client->output, MQTT_MSG_TYPE_PINGREQ, 0, 0, 0, 0);
ARM GAS  /tmp/ccBURsTh.s 			page 82


 2866              		.loc 1 608 13 is_stmt 0 view .LVU814
 2867 009a 04F1EC06 		add	r6, r4, #236
 2868 009e 0021     		movs	r1, #0
 2869 00a0 3046     		mov	r0, r6
 2870 00a2 FFF7FEFF 		bl	mqtt_output_check_space
 2871              	.LVL323:
 608:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           mqtt_output_append_fixed_header(&client->output, MQTT_MSG_TYPE_PINGREQ, 0, 0, 0, 0);
 2872              		.loc 1 608 12 discriminator 1 view .LVU815
 2873 00a6 0028     		cmp	r0, #0
 2874 00a8 EDD0     		beq	.L218
 609:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           client->cyclic_tick = 0;
 2875              		.loc 1 609 11 is_stmt 1 view .LVU816
 2876 00aa 0027     		movs	r7, #0
 2877 00ac 0197     		str	r7, [sp, #4]
 2878 00ae 0097     		str	r7, [sp]
 2879 00b0 3B46     		mov	r3, r7
 2880 00b2 3A46     		mov	r2, r7
 2881 00b4 0C21     		movs	r1, #12
 2882 00b6 3046     		mov	r0, r6
 2883 00b8 FFF7FEFF 		bl	mqtt_output_append_fixed_header
 2884              	.LVL324:
 610:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 2885              		.loc 1 610 11 view .LVU817
 610:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 2886              		.loc 1 610 31 is_stmt 0 view .LVU818
 2887 00bc 2780     		strh	r7, [r4]	@ movhi
 2888 00be E2E7     		b	.L218
 2889              	.L228:
 2890              		.align	2
 2891              	.L227:
 2892 00c0 00000000 		.word	.LC0
 2893 00c4 00000000 		.word	.LC16
 2894 00c8 58000000 		.word	.LC2
 2895 00cc 00000000 		.word	mqtt_cyclic_timer
 2896              		.cfi_endproc
 2897              	.LFE194:
 2899              		.section	.text.mqtt_tcp_connect_cb,"ax",%progbits
 2900              		.align	1
 2901              		.syntax unified
 2902              		.thumb
 2903              		.thumb_func
 2905              	mqtt_tcp_connect_cb:
 2906              	.LVL325:
 2907              	.LFB203:
1036:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1037:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1038:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * TCP connect callback function. @see tcp_connected_fn
1039:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg MQTT client
1040:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param err Always ERR_OK, mqtt_tcp_err_cb is called in case of error
1041:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK
1042:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1043:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** static err_t
1044:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_tcp_connect_cb(void *arg, struct altcp_pcb *tpcb, err_t err)
1045:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 2908              		.loc 1 1045 1 is_stmt 1 view -0
 2909              		.cfi_startproc
 2910              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/ccBURsTh.s 			page 83


 2911              		@ frame_needed = 0, uses_anonymous_args = 0
 2912              		.loc 1 1045 1 is_stmt 0 view .LVU820
 2913 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 2914              	.LCFI31:
 2915              		.cfi_def_cfa_offset 24
 2916              		.cfi_offset 3, -24
 2917              		.cfi_offset 4, -20
 2918              		.cfi_offset 5, -16
 2919              		.cfi_offset 6, -12
 2920              		.cfi_offset 7, -8
 2921              		.cfi_offset 14, -4
1046:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 2922              		.loc 1 1046 3 is_stmt 1 view .LVU821
 2923              	.LVL326:
1047:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1048:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err != ERR_OK) {
 2924              		.loc 1 1048 3 view .LVU822
 2925              		.loc 1 1048 6 is_stmt 0 view .LVU823
 2926 0002 1646     		mov	r6, r2
 2927 0004 0AB1     		cbz	r2, .L232
 2928              	.LVL327:
 2929              	.L230:
1049:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_tcp_connect_cb: TCP connect error %d\n", err));
1050:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return err;
1051:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1052:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1053:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Initiate receiver state */
1054:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->msg_idx = 0;
1055:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1056:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Setup TCP callbacks */
1057:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_recv(tpcb, mqtt_tcp_recv_cb);
1058:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_sent(tpcb, mqtt_tcp_sent_cb);
1059:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_poll(tpcb, mqtt_tcp_poll_cb, 2);
1060:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1061:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_connect_cb: TCP connection established to server\n"));
1062:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Enter MQTT connect state */
1063:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->conn_state = MQTT_CONNECTING;
1064:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1065:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Start cyclic timer */
1066:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   sys_timeout(MQTT_CYCLIC_TIMER_INTERVAL * 1000, mqtt_cyclic_timer, client);
1067:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->cyclic_tick = 0;
1068:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1069:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Start transmission from output queue, connect message is the first one out*/
1070:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_send(&client->output, client->conn);
1071:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1072:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
1073:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2930              		.loc 1 1073 1 view .LVU824
 2931 0006 3046     		mov	r0, r6
 2932 0008 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 2933              	.LVL328:
 2934              	.L232:
 2935              		.loc 1 1073 1 view .LVU825
 2936 000a 0446     		mov	r4, r0
 2937 000c 0D46     		mov	r5, r1
1054:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2938              		.loc 1 1054 3 is_stmt 1 view .LVU826
ARM GAS  /tmp/ccBURsTh.s 			page 84


1054:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2939              		.loc 1 1054 19 is_stmt 0 view .LVU827
 2940 000e 0027     		movs	r7, #0
 2941 0010 8766     		str	r7, [r0, #104]
1057:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_sent(tpcb, mqtt_tcp_sent_cb);
 2942              		.loc 1 1057 3 is_stmt 1 view .LVU828
 2943 0012 0E49     		ldr	r1, .L233
 2944              	.LVL329:
1057:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_sent(tpcb, mqtt_tcp_sent_cb);
 2945              		.loc 1 1057 3 is_stmt 0 view .LVU829
 2946 0014 2846     		mov	r0, r5
 2947              	.LVL330:
1057:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_sent(tpcb, mqtt_tcp_sent_cb);
 2948              		.loc 1 1057 3 view .LVU830
 2949 0016 FFF7FEFF 		bl	tcp_recv
 2950              	.LVL331:
1058:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_poll(tpcb, mqtt_tcp_poll_cb, 2);
 2951              		.loc 1 1058 3 is_stmt 1 view .LVU831
 2952 001a 0D49     		ldr	r1, .L233+4
 2953 001c 2846     		mov	r0, r5
 2954 001e FFF7FEFF 		bl	tcp_sent
 2955              	.LVL332:
1059:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2956              		.loc 1 1059 3 view .LVU832
 2957 0022 0222     		movs	r2, #2
 2958 0024 0B49     		ldr	r1, .L233+8
 2959 0026 2846     		mov	r0, r5
 2960 0028 FFF7FEFF 		bl	tcp_poll
 2961              	.LVL333:
1061:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Enter MQTT connect state */
 2962              		.loc 1 1061 97 view .LVU833
1063:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2963              		.loc 1 1063 3 view .LVU834
1063:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2964              		.loc 1 1063 22 is_stmt 0 view .LVU835
 2965 002c 0223     		movs	r3, #2
 2966 002e A372     		strb	r3, [r4, #10]
1066:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->cyclic_tick = 0;
 2967              		.loc 1 1066 3 is_stmt 1 view .LVU836
 2968 0030 2246     		mov	r2, r4
 2969 0032 0949     		ldr	r1, .L233+12
 2970 0034 41F28830 		movw	r0, #5000
 2971 0038 FFF7FEFF 		bl	sys_timeout
 2972              	.LVL334:
1067:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2973              		.loc 1 1067 3 view .LVU837
1067:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2974              		.loc 1 1067 23 is_stmt 0 view .LVU838
 2975 003c 2780     		strh	r7, [r4]	@ movhi
1070:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 2976              		.loc 1 1070 3 is_stmt 1 view .LVU839
 2977 003e E168     		ldr	r1, [r4, #12]
 2978 0040 04F1EC00 		add	r0, r4, #236
 2979 0044 FFF7FEFF 		bl	mqtt_output_send
 2980              	.LVL335:
1072:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2981              		.loc 1 1072 3 view .LVU840
ARM GAS  /tmp/ccBURsTh.s 			page 85


1072:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 2982              		.loc 1 1072 10 is_stmt 0 view .LVU841
 2983 0048 DDE7     		b	.L230
 2984              	.L234:
 2985 004a 00BF     		.align	2
 2986              	.L233:
 2987 004c 00000000 		.word	mqtt_tcp_recv_cb
 2988 0050 00000000 		.word	mqtt_tcp_sent_cb
 2989 0054 00000000 		.word	mqtt_tcp_poll_cb
 2990 0058 00000000 		.word	mqtt_cyclic_timer
 2991              		.cfi_endproc
 2992              	.LFE203:
 2994              		.section	.text.mqtt_parse_incoming,"ax",%progbits
 2995              		.align	1
 2996              		.syntax unified
 2997              		.thumb
 2998              		.thumb_func
 3000              	mqtt_parse_incoming:
 3001              	.LVL336:
 3002              	.LFB198:
 840:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t in_offset = 0;
 3003              		.loc 1 840 1 is_stmt 1 view -0
 3004              		.cfi_startproc
 3005              		@ args = 0, pretend = 0, frame = 8
 3006              		@ frame_needed = 0, uses_anonymous_args = 0
 840:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t in_offset = 0;
 3007              		.loc 1 840 1 is_stmt 0 view .LVU843
 3008 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 3009              	.LCFI32:
 3010              		.cfi_def_cfa_offset 36
 3011              		.cfi_offset 4, -36
 3012              		.cfi_offset 5, -32
 3013              		.cfi_offset 6, -28
 3014              		.cfi_offset 7, -24
 3015              		.cfi_offset 8, -20
 3016              		.cfi_offset 9, -16
 3017              		.cfi_offset 10, -12
 3018              		.cfi_offset 11, -8
 3019              		.cfi_offset 14, -4
 3020 0004 83B0     		sub	sp, sp, #12
 3021              	.LCFI33:
 3022              		.cfi_def_cfa_offset 48
 3023 0006 8046     		mov	r8, r0
 3024 0008 0091     		str	r1, [sp]
 841:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u32_t msg_rem_len = 0;
 3025              		.loc 1 841 3 is_stmt 1 view .LVU844
 3026              	.LVL337:
 842:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t fixed_hdr_idx = 0;
 3027              		.loc 1 842 3 view .LVU845
 843:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t b = 0;
 3028              		.loc 1 843 3 view .LVU846
 844:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3029              		.loc 1 844 3 view .LVU847
 846:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* We ALWAYS parse the header here first. Even if the header was not
 3030              		.loc 1 846 3 view .LVU848
 844:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3031              		.loc 1 844 8 is_stmt 0 view .LVU849
ARM GAS  /tmp/ccBURsTh.s 			page 86


 3032 000a 4FF0000A 		mov	r10, #0
 843:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t b = 0;
 3033              		.loc 1 843 8 view .LVU850
 3034 000e 5546     		mov	r5, r10
 842:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t fixed_hdr_idx = 0;
 3035              		.loc 1 842 9 view .LVU851
 3036 0010 D146     		mov	r9, r10
 841:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u32_t msg_rem_len = 0;
 3037              		.loc 1 841 9 view .LVU852
 3038 0012 5746     		mov	r7, r10
 846:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* We ALWAYS parse the header here first. Even if the header was not
 3039              		.loc 1 846 9 view .LVU853
 3040 0014 4DE0     		b	.L236
 3041              	.LVL338:
 3042              	.L238:
 858:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->rx_buffer[client->msg_idx++] = b;
 3043              		.loc 1 858 9 is_stmt 1 view .LVU854
 858:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->rx_buffer[client->msg_idx++] = b;
 3044              		.loc 1 858 13 is_stmt 0 view .LVU855
 3045 0016 7C1C     		adds	r4, r7, #1
 3046 0018 A4B2     		uxth	r4, r4
 3047              	.LVL339:
 858:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->rx_buffer[client->msg_idx++] = b;
 3048              		.loc 1 858 13 view .LVU856
 3049 001a 3946     		mov	r1, r7
 3050 001c 0098     		ldr	r0, [sp]
 3051 001e FFF7FEFF 		bl	pbuf_get_at
 3052              	.LVL340:
 3053 0022 8246     		mov	r10, r0
 3054              	.LVL341:
 859:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 3055              		.loc 1 859 9 is_stmt 1 view .LVU857
 859:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 3056              		.loc 1 859 33 is_stmt 0 view .LVU858
 3057 0024 D8F86830 		ldr	r3, [r8, #104]
 859:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 3058              		.loc 1 859 42 view .LVU859
 3059 0028 5A1C     		adds	r2, r3, #1
 3060 002a C8F86820 		str	r2, [r8, #104]
 859:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 3061              		.loc 1 859 46 view .LVU860
 3062 002e 4344     		add	r3, r3, r8
 3063 0030 83F86C00 		strb	r0, [r3, #108]
 858:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         client->rx_buffer[client->msg_idx++] = b;
 3064              		.loc 1 858 13 view .LVU861
 3065 0034 2746     		mov	r7, r4
 3066 0036 50E0     		b	.L239
 3067              	.LVL342:
 3068              	.L251:
 873:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             client->msg_idx = 0;
 3069              		.loc 1 873 13 is_stmt 1 view .LVU862
 3070 0038 0023     		movs	r3, #0
 3071 003a 1A46     		mov	r2, r3
 3072 003c 2946     		mov	r1, r5
 3073 003e 4046     		mov	r0, r8
 3074 0040 FFF7FEFF 		bl	mqtt_message_received
 3075              	.LVL343:
ARM GAS  /tmp/ccBURsTh.s 			page 87


 874:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             fixed_hdr_idx = 0;
 3076              		.loc 1 874 13 view .LVU863
 874:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             fixed_hdr_idx = 0;
 3077              		.loc 1 874 29 is_stmt 0 view .LVU864
 3078 0044 0025     		movs	r5, #0
 3079              	.LVL344:
 874:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             fixed_hdr_idx = 0;
 3080              		.loc 1 874 29 view .LVU865
 3081 0046 C8F86850 		str	r5, [r8, #104]
 875:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           } else {
 3082              		.loc 1 875 13 is_stmt 1 view .LVU866
 3083              	.LVL345:
 875:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           } else {
 3084              		.loc 1 875 13 is_stmt 0 view .LVU867
 3085 004a 32E0     		b	.L236
 3086              	.LVL346:
 3087              	.L237:
 3088              	.LBB12:
 885:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3089              		.loc 1 885 7 is_stmt 1 view .LVU868
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3090              		.loc 1 887 7 view .LVU869
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3091              		.loc 1 887 26 is_stmt 0 view .LVU870
 3092 004c D8F86860 		ldr	r6, [r8, #104]
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3093              		.loc 1 887 36 view .LVU871
 3094 0050 761B     		subs	r6, r6, r5
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3095              		.loc 1 887 83 view .LVU872
 3096 0052 C5F18003 		rsb	r3, r5, #128
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3097              		.loc 1 887 53 view .LVU873
 3098 0056 B6FBF3F2 		udiv	r2, r6, r3
 3099 005a 03FB1266 		mls	r6, r3, r2, r6
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3100              		.loc 1 887 100 view .LVU874
 3101 005e 0195     		str	r5, [sp, #4]
 887:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3102              		.loc 1 887 17 view .LVU875
 3103 0060 15FA86F6 		uxtah	r6, r5, r6
 3104 0064 B6B2     		uxth	r6, r6
 3105              	.LVL347:
 890:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3106              		.loc 1 890 7 is_stmt 1 view .LVU876
 890:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3107              		.loc 1 890 24 is_stmt 0 view .LVU877
 3108 0066 E41B     		subs	r4, r4, r7
 3109 0068 A4B2     		uxth	r4, r4
 890:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3110              		.loc 1 890 17 view .LVU878
 3111 006a 4C45     		cmp	r4, r9
 3112 006c 01D3     		bcc	.L242
 890:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3113              		.loc 1 890 17 discriminator 2 view .LVU879
 3114 006e 1FFA89F4 		uxth	r4, r9
 3115              	.L242:
ARM GAS  /tmp/ccBURsTh.s 			page 88


 3116              	.LVL348:
 893:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (cpy_len > buffer_space) {
 3117              		.loc 1 893 7 is_stmt 1 view .LVU880
 893:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if (cpy_len > buffer_space) {
 3118              		.loc 1 893 20 is_stmt 0 view .LVU881
 3119 0072 C6F1800B 		rsb	fp, r6, #128
 3120 0076 1FFA8BFB 		uxth	fp, fp
 3121              	.LVL349:
 894:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         cpy_len = buffer_space;
 3122              		.loc 1 894 7 is_stmt 1 view .LVU882
 894:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         cpy_len = buffer_space;
 3123              		.loc 1 894 10 is_stmt 0 view .LVU883
 3124 007a 5C45     		cmp	r4, fp
 3125 007c 00D9     		bls	.L243
 895:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       }
 3126              		.loc 1 895 17 view .LVU884
 3127 007e 5C46     		mov	r4, fp
 3128              	.LVL350:
 3129              	.L243:
 897:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3130              		.loc 1 897 7 is_stmt 1 view .LVU885
 897:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3131              		.loc 1 897 28 is_stmt 0 view .LVU886
 3132 0080 08F16C01 		add	r1, r8, #108
 897:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3133              		.loc 1 897 7 view .LVU887
 3134 0084 3B46     		mov	r3, r7
 3135 0086 2246     		mov	r2, r4
 3136 0088 3144     		add	r1, r1, r6
 3137 008a 0098     		ldr	r0, [sp]
 3138 008c FFF7FEFF 		bl	pbuf_copy_partial
 3139              	.LVL351:
 900:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       in_offset += cpy_len;
 3140              		.loc 1 900 7 is_stmt 1 view .LVU888
 900:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       in_offset += cpy_len;
 3141              		.loc 1 900 13 is_stmt 0 view .LVU889
 3142 0090 D8F86830 		ldr	r3, [r8, #104]
 900:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       in_offset += cpy_len;
 3143              		.loc 1 900 23 view .LVU890
 3144 0094 2344     		add	r3, r3, r4
 3145 0096 C8F86830 		str	r3, [r8, #104]
 901:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       msg_rem_len -= cpy_len;
 3146              		.loc 1 901 7 is_stmt 1 view .LVU891
 901:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       msg_rem_len -= cpy_len;
 3147              		.loc 1 901 17 is_stmt 0 view .LVU892
 3148 009a 2744     		add	r7, r7, r4
 3149              	.LVL352:
 901:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       msg_rem_len -= cpy_len;
 3150              		.loc 1 901 17 view .LVU893
 3151 009c BFB2     		uxth	r7, r7
 3152              	.LVL353:
 902:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3153              		.loc 1 902 7 is_stmt 1 view .LVU894
 904:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       if ((msg_rem_len == 0) || (cpy_len == buffer_space)) {
 3154              		.loc 1 904 160 view .LVU895
 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 3155              		.loc 1 905 7 view .LVU896
ARM GAS  /tmp/ccBURsTh.s 			page 89


 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 3156              		.loc 1 905 24 is_stmt 0 view .LVU897
 3157 009e B9EB0409 		subs	r9, r9, r4
 3158              	.LVL354:
 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 3159              		.loc 1 905 24 view .LVU898
 3160 00a2 0CBF     		ite	eq
 3161 00a4 0123     		moveq	r3, #1
 3162 00a6 0023     		movne	r3, #0
 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 3163              		.loc 1 905 30 view .LVU899
 3164 00a8 5C45     		cmp	r4, fp
 3165 00aa 08BF     		it	eq
 3166 00ac 43F00103 		orreq	r3, r3, #1
 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 3167              		.loc 1 905 10 view .LVU900
 3168 00b0 5BBB     		cbnz	r3, .L249
 3169              	.LVL355:
 3170              	.L236:
 905:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* Whole message received or buffer is full */
 3171              		.loc 1 905 10 view .LVU901
 3172              	.LBE12:
 846:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* We ALWAYS parse the header here first. Even if the header was not
 3173              		.loc 1 846 21 is_stmt 1 view .LVU902
 846:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* We ALWAYS parse the header here first. Even if the header was not
 3174              		.loc 1 846 11 is_stmt 0 view .LVU903
 3175 00b2 009B     		ldr	r3, [sp]
 3176 00b4 1C89     		ldrh	r4, [r3, #8]
 846:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* We ALWAYS parse the header here first. Even if the header was not
 3177              		.loc 1 846 21 view .LVU904
 3178 00b6 BC42     		cmp	r4, r7
 3179 00b8 3AD9     		bls	.L250
 850:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3180              		.loc 1 850 5 is_stmt 1 view .LVU905
 850:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3181              		.loc 1 850 44 is_stmt 0 view .LVU906
 3182 00ba CAF3C013 		ubfx	r3, r10, #7, #1
 850:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3183              		.loc 1 850 29 view .LVU907
 3184 00be 012D     		cmp	r5, #1
 3185 00c0 98BF     		it	ls
 3186 00c2 43F00103 		orrls	r3, r3, #1
 850:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3187              		.loc 1 850 8 view .LVU908
 3188 00c6 002B     		cmp	r3, #0
 3189 00c8 C0D0     		beq	.L237
 852:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* parse header from old pbuf (buffered in client->rx_buffer) */
 3190              		.loc 1 852 7 is_stmt 1 view .LVU909
 852:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* parse header from old pbuf (buffered in client->rx_buffer) */
 3191              		.loc 1 852 33 is_stmt 0 view .LVU910
 3192 00ca D8F86830 		ldr	r3, [r8, #104]
 852:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* parse header from old pbuf (buffered in client->rx_buffer) */
 3193              		.loc 1 852 10 view .LVU911
 3194 00ce 9D42     		cmp	r5, r3
 3195 00d0 A1D2     		bcs	.L238
 854:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 3196              		.loc 1 854 9 is_stmt 1 view .LVU912
ARM GAS  /tmp/ccBURsTh.s 			page 90


 854:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       } else {
 3197              		.loc 1 854 11 is_stmt 0 view .LVU913
 3198 00d2 08EB0503 		add	r3, r8, r5
 3199 00d6 93F86CA0 		ldrb	r10, [r3, #108]	@ zero_extendqisi2
 3200              	.LVL356:
 3201              	.L239:
 861:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3202              		.loc 1 861 7 is_stmt 1 view .LVU914
 861:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3203              		.loc 1 861 20 is_stmt 0 view .LVU915
 3204 00da 0135     		adds	r5, r5, #1
 3205              	.LVL357:
 861:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3206              		.loc 1 861 20 view .LVU916
 3207 00dc EDB2     		uxtb	r5, r5
 3208              	.LVL358:
 863:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* fixed header contains at least 2 bytes but can contain more, depending on
 3209              		.loc 1 863 7 is_stmt 1 view .LVU917
 863:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         /* fixed header contains at least 2 bytes but can contain more, depending on
 3210              		.loc 1 863 10 is_stmt 0 view .LVU918
 3211 00de 012D     		cmp	r5, #1
 3212 00e0 E7D9     		bls	.L236
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 3213              		.loc 1 867 9 is_stmt 1 view .LVU919
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 3214              		.loc 1 867 24 is_stmt 0 view .LVU920
 3215 00e2 0AF07F03 		and	r3, r10, #127
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 3216              		.loc 1 867 61 view .LVU921
 3217 00e6 AA1E     		subs	r2, r5, #2
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 3218              		.loc 1 867 66 view .LVU922
 3219 00e8 C2EBC202 		rsb	r2, r2, r2, lsl #3
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 3220              		.loc 1 867 42 view .LVU923
 3221 00ec 9340     		lsls	r3, r3, r2
 867:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if ((b & 0x80) == 0) {
 3222              		.loc 1 867 21 view .LVU924
 3223 00ee 49EA0309 		orr	r9, r9, r3
 3224              	.LVL359:
 868:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* fixed header is done */
 3225              		.loc 1 868 9 is_stmt 1 view .LVU925
 868:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* fixed header is done */
 3226              		.loc 1 868 12 is_stmt 0 view .LVU926
 3227 00f2 1AF0800F 		tst	r10, #128
 3228 00f6 DCD1     		bne	.L236
 870:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           if (msg_rem_len == 0) {
 3229              		.loc 1 870 127 is_stmt 1 view .LVU927
 871:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             /* Complete message with no extra headers of payload received */
 3230              		.loc 1 871 11 view .LVU928
 871:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****             /* Complete message with no extra headers of payload received */
 3231              		.loc 1 871 14 is_stmt 0 view .LVU929
 3232 00f8 B9F1000F 		cmp	r9, #0
 3233 00fc 9CD0     		beq	.L251
 879:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 3234              		.loc 1 879 13 is_stmt 1 view .LVU930
 879:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
ARM GAS  /tmp/ccBURsTh.s 			page 91


 3235              		.loc 1 879 40 is_stmt 0 view .LVU931
 3236 00fe A944     		add	r9, r9, r5
 3237              	.LVL360:
 879:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 3238              		.loc 1 879 65 view .LVU932
 3239 0100 D8F86830 		ldr	r3, [r8, #104]
 879:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 3240              		.loc 1 879 25 view .LVU933
 3241 0104 A9EB0309 		sub	r9, r9, r3
 3242              	.LVL361:
 879:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           }
 3243              		.loc 1 879 25 view .LVU934
 3244 0108 D3E7     		b	.L236
 3245              	.LVL362:
 3246              	.L249:
 3247              	.LBB14:
 3248              	.LBB13:
 907:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (res != MQTT_CONNECT_ACCEPTED) {
 3249              		.loc 1 907 9 is_stmt 1 view .LVU935
 907:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (res != MQTT_CONNECT_ACCEPTED) {
 3250              		.loc 1 907 96 is_stmt 0 view .LVU936
 3251 010a A219     		adds	r2, r4, r6
 3252 010c 92B2     		uxth	r2, r2
 907:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         if (res != MQTT_CONNECT_ACCEPTED) {
 3253              		.loc 1 907 40 view .LVU937
 3254 010e 019B     		ldr	r3, [sp, #4]
 3255 0110 D21A     		subs	r2, r2, r3
 3256 0112 4B46     		mov	r3, r9
 3257 0114 92B2     		uxth	r2, r2
 3258 0116 2946     		mov	r1, r5
 3259 0118 4046     		mov	r0, r8
 3260 011a FFF7FEFF 		bl	mqtt_message_received
 3261              	.LVL363:
 908:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           return res;
 3262              		.loc 1 908 9 is_stmt 1 view .LVU938
 908:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           return res;
 3263              		.loc 1 908 12 is_stmt 0 view .LVU939
 3264 011e 0346     		mov	r3, r0
 3265 0120 38B9     		cbnz	r0, .L244
 911:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* Reset parser state */
 3266              		.loc 1 911 9 is_stmt 1 view .LVU940
 911:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* Reset parser state */
 3267              		.loc 1 911 12 is_stmt 0 view .LVU941
 3268 0122 B9F1000F 		cmp	r9, #0
 3269 0126 C4D1     		bne	.L236
 913:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* msg_tot_len = 0; */
 3270              		.loc 1 913 11 is_stmt 1 view .LVU942
 913:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* msg_tot_len = 0; */
 3271              		.loc 1 913 27 is_stmt 0 view .LVU943
 3272 0128 0025     		movs	r5, #0
 3273              	.LVL364:
 913:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****           /* msg_tot_len = 0; */
 3274              		.loc 1 913 27 view .LVU944
 3275 012a C8F86850 		str	r5, [r8, #104]
 915:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 3276              		.loc 1 915 11 is_stmt 1 view .LVU945
 3277              	.LVL365:
ARM GAS  /tmp/ccBURsTh.s 			page 92


 915:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 3278              		.loc 1 915 11 is_stmt 0 view .LVU946
 3279 012e C0E7     		b	.L236
 3280              	.LVL366:
 3281              	.L250:
 915:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****         }
 3282              		.loc 1 915 11 view .LVU947
 3283              	.LBE13:
 3284              	.LBE14:
 920:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 3285              		.loc 1 920 10 view .LVU948
 3286 0130 0023     		movs	r3, #0
 3287              	.LVL367:
 3288              	.L244:
 921:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3289              		.loc 1 921 1 view .LVU949
 3290 0132 1846     		mov	r0, r3
 3291 0134 03B0     		add	sp, sp, #12
 3292              	.LCFI34:
 3293              		.cfi_def_cfa_offset 36
 3294              	.LVL368:
 921:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3295              		.loc 1 921 1 view .LVU950
 3296              		@ sp needed
 3297 0136 BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 921:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3298              		.loc 1 921 1 view .LVU951
 3299              		.cfi_endproc
 3300              	.LFE198:
 3302              		.section	.rodata.mqtt_tcp_recv_cb.str1.4,"aMS",%progbits,1
 3303              		.align	2
 3304              	.LC17:
 3305 0000 6D717474 		.ascii	"mqtt_tcp_recv_cb: client != NULL\000"
 3305      5F746370 
 3305      5F726563 
 3305      765F6362 
 3305      3A20636C 
 3306 0021 000000   		.align	2
 3307              	.LC18:
 3308 0024 6D717474 		.ascii	"mqtt_tcp_recv_cb: client->conn == pcb\000"
 3308      5F746370 
 3308      5F726563 
 3308      765F6362 
 3308      3A20636C 
 3309              		.section	.text.mqtt_tcp_recv_cb,"ax",%progbits
 3310              		.align	1
 3311              		.syntax unified
 3312              		.thumb
 3313              		.thumb_func
 3315              	mqtt_tcp_recv_cb:
 3316              	.LVL369:
 3317              	.LFB199:
 933:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 3318              		.loc 1 933 1 is_stmt 1 view -0
 3319              		.cfi_startproc
 3320              		@ args = 0, pretend = 0, frame = 0
 3321              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  /tmp/ccBURsTh.s 			page 93


 933:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_client_t *client = (mqtt_client_t *)arg;
 3322              		.loc 1 933 1 is_stmt 0 view .LVU953
 3323 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 3324              	.LCFI35:
 3325              		.cfi_def_cfa_offset 24
 3326              		.cfi_offset 3, -24
 3327              		.cfi_offset 4, -20
 3328              		.cfi_offset 5, -16
 3329              		.cfi_offset 6, -12
 3330              		.cfi_offset 7, -8
 3331              		.cfi_offset 14, -4
 3332 0002 0E46     		mov	r6, r1
 3333 0004 1446     		mov	r4, r2
 3334 0006 1D46     		mov	r5, r3
 934:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client != NULL", client != NULL);
 3335              		.loc 1 934 3 is_stmt 1 view .LVU954
 3336              	.LVL370:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3337              		.loc 1 935 3 view .LVU955
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3338              		.loc 1 935 3 view .LVU956
 3339 0008 0746     		mov	r7, r0
 3340 000a E8B1     		cbz	r0, .L260
 3341              	.LVL371:
 3342              	.L253:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3343              		.loc 1 935 3 discriminator 3 view .LVU957
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3344              		.loc 1 935 3 discriminator 3 view .LVU958
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3345              		.loc 1 936 3 view .LVU959
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3346              		.loc 1 936 3 view .LVU960
 3347 000c FB68     		ldr	r3, [r7, #12]
 3348 000e B342     		cmp	r3, r6
 3349 0010 06D0     		beq	.L254
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3350              		.loc 1 936 3 discriminator 1 view .LVU961
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3351              		.loc 1 936 3 discriminator 1 view .LVU962
 3352 0012 1A4B     		ldr	r3, .L264
 3353 0014 4FF46A72 		mov	r2, #936
 3354 0018 1949     		ldr	r1, .L264+4
 3355 001a 1A48     		ldr	r0, .L264+8
 3356 001c FFF7FEFF 		bl	printf
 3357              	.LVL372:
 3358              	.L254:
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3359              		.loc 1 936 3 discriminator 3 view .LVU963
 936:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3360              		.loc 1 936 3 discriminator 3 view .LVU964
 938:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_recv_cb: Recv pbuf=NULL, remote has closed connection\
 3361              		.loc 1 938 3 view .LVU965
 938:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_tcp_recv_cb: Recv pbuf=NULL, remote has closed connection\
 3362              		.loc 1 938 6 is_stmt 0 view .LVU966
 3363 0020 D4B1     		cbz	r4, .L261
 3364              	.LBB15:
ARM GAS  /tmp/ccBURsTh.s 			page 94


 942:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (err != ERR_OK) {
 3365              		.loc 1 942 5 is_stmt 1 view .LVU967
 943:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_tcp_recv_cb: Recv err=%d\n", err));
 3366              		.loc 1 943 5 view .LVU968
 943:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_tcp_recv_cb: Recv err=%d\n", err));
 3367              		.loc 1 943 8 is_stmt 0 view .LVU969
 3368 0022 05BB     		cbnz	r5, .L262
 950:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     res = mqtt_parse_incoming(client, p);
 3369              		.loc 1 950 5 is_stmt 1 view .LVU970
 3370 0024 2189     		ldrh	r1, [r4, #8]
 3371 0026 3046     		mov	r0, r6
 3372 0028 FFF7FEFF 		bl	tcp_recved
 3373              	.LVL373:
 951:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pbuf_free(p);
 3374              		.loc 1 951 5 view .LVU971
 951:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pbuf_free(p);
 3375              		.loc 1 951 11 is_stmt 0 view .LVU972
 3376 002c 2146     		mov	r1, r4
 3377 002e 3846     		mov	r0, r7
 3378 0030 FFF7FEFF 		bl	mqtt_parse_incoming
 3379              	.LVL374:
 3380 0034 0646     		mov	r6, r0
 3381              	.LVL375:
 952:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3382              		.loc 1 952 5 is_stmt 1 view .LVU973
 3383 0036 2046     		mov	r0, r4
 3384 0038 FFF7FEFF 		bl	pbuf_free
 3385              	.LVL376:
 954:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       mqtt_close(client, res);
 3386              		.loc 1 954 5 view .LVU974
 954:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       mqtt_close(client, res);
 3387              		.loc 1 954 8 is_stmt 0 view .LVU975
 3388 003c C6B9     		cbnz	r6, .L263
 3389              	.L258:
 958:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reset server alive watchdog */
 3390              		.loc 1 958 5 is_stmt 1 view .LVU976
 958:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reset server alive watchdog */
 3391              		.loc 1 958 15 is_stmt 0 view .LVU977
 3392 003e 7B88     		ldrh	r3, [r7, #2]
 958:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       /* Reset server alive watchdog */
 3393              		.loc 1 958 8 view .LVU978
 3394 0040 A3B1     		cbz	r3, .L256
 960:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 3395              		.loc 1 960 7 is_stmt 1 view .LVU979
 960:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 3396              		.loc 1 960 31 is_stmt 0 view .LVU980
 3397 0042 0023     		movs	r3, #0
 3398 0044 BB80     		strh	r3, [r7, #4]	@ movhi
 3399 0046 11E0     		b	.L256
 3400              	.LVL377:
 3401              	.L260:
 960:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 3402              		.loc 1 960 31 view .LVU981
 3403              	.LBE15:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3404              		.loc 1 935 3 is_stmt 1 discriminator 1 view .LVU982
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
ARM GAS  /tmp/ccBURsTh.s 			page 95


 3405              		.loc 1 935 3 discriminator 1 view .LVU983
 3406 0048 0C4B     		ldr	r3, .L264
 3407              	.LVL378:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3408              		.loc 1 935 3 is_stmt 0 discriminator 1 view .LVU984
 3409 004a 40F2A732 		movw	r2, #935
 3410              	.LVL379:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3411              		.loc 1 935 3 discriminator 1 view .LVU985
 3412 004e 0E49     		ldr	r1, .L264+12
 3413              	.LVL380:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3414              		.loc 1 935 3 discriminator 1 view .LVU986
 3415 0050 0C48     		ldr	r0, .L264+8
 3416              	.LVL381:
 935:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_tcp_recv_cb: client->conn == pcb", client->conn == pcb);
 3417              		.loc 1 935 3 discriminator 1 view .LVU987
 3418 0052 FFF7FEFF 		bl	printf
 3419              	.LVL382:
 3420 0056 D9E7     		b	.L253
 3421              	.L261:
 939:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_close(client, MQTT_CONNECT_DISCONNECTED);
 3422              		.loc 1 939 104 is_stmt 1 view .LVU988
 940:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 3423              		.loc 1 940 5 view .LVU989
 3424 0058 4FF48071 		mov	r1, #256
 3425 005c 3846     		mov	r0, r7
 3426 005e FFF7FEFF 		bl	mqtt_close
 3427              	.LVL383:
 964:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 3428              		.loc 1 964 10 is_stmt 0 view .LVU990
 3429 0062 0025     		movs	r5, #0
 3430              	.LVL384:
 964:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 3431              		.loc 1 964 10 view .LVU991
 3432 0064 02E0     		b	.L256
 3433              	.L262:
 3434              	.LBB16:
 944:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       pbuf_free(p);
 3435              		.loc 1 944 77 is_stmt 1 view .LVU992
 945:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       return err;
 3436              		.loc 1 945 7 view .LVU993
 3437 0066 2046     		mov	r0, r4
 3438 0068 FFF7FEFF 		bl	pbuf_free
 3439              	.LVL385:
 946:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 3440              		.loc 1 946 7 view .LVU994
 3441              	.L256:
 946:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 3442              		.loc 1 946 7 is_stmt 0 view .LVU995
 3443              	.LBE16:
 965:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3444              		.loc 1 965 1 view .LVU996
 3445 006c 2846     		mov	r0, r5
 3446 006e F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 3447              	.LVL386:
 3448              	.L263:
ARM GAS  /tmp/ccBURsTh.s 			page 96


 3449              	.LBB17:
 955:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
 3450              		.loc 1 955 7 is_stmt 1 view .LVU997
 3451 0070 3146     		mov	r1, r6
 3452 0072 3846     		mov	r0, r7
 3453 0074 FFF7FEFF 		bl	mqtt_close
 3454              	.LVL387:
 3455 0078 E1E7     		b	.L258
 3456              	.L265:
 3457 007a 00BF     		.align	2
 3458              	.L264:
 3459 007c 00000000 		.word	.LC0
 3460 0080 24000000 		.word	.LC18
 3461 0084 58000000 		.word	.LC2
 3462 0088 00000000 		.word	.LC17
 3463              	.LBE17:
 3464              		.cfi_endproc
 3465              	.LFE199:
 3467              		.section	.rodata.mqtt_publish.str1.4,"aMS",%progbits,1
 3468              		.align	2
 3469              	.LC19:
 3470 0000 6D717474 		.ascii	"mqtt_publish: client != NULL\000"
 3470      5F707562 
 3470      6C697368 
 3470      3A20636C 
 3470      69656E74 
 3471 001d 000000   		.align	2
 3472              	.LC20:
 3473 0020 6D717474 		.ascii	"mqtt_publish: topic != NULL\000"
 3473      5F707562 
 3473      6C697368 
 3473      3A20746F 
 3473      70696320 
 3474              		.align	2
 3475              	.LC21:
 3476 003c 6D717474 		.ascii	"mqtt_publish: TCP disconnected\000"
 3476      5F707562 
 3476      6C697368 
 3476      3A205443 
 3476      50206469 
 3477 005b 00       		.align	2
 3478              	.LC22:
 3479 005c 6D717474 		.ascii	"mqtt_publish: topic length overflow\000"
 3479      5F707562 
 3479      6C697368 
 3479      3A20746F 
 3479      70696320 
 3480              		.align	2
 3481              	.LC23:
 3482 0080 6D717474 		.ascii	"mqtt_publish: total length overflow\000"
 3482      5F707562 
 3482      6C697368 
 3482      3A20746F 
 3482      74616C20 
 3483              		.section	.text.mqtt_publish,"ax",%progbits
 3484              		.align	1
 3485              		.global	mqtt_publish
ARM GAS  /tmp/ccBURsTh.s 			page 97


 3486              		.syntax unified
 3487              		.thumb
 3488              		.thumb_func
 3490              	mqtt_publish:
 3491              	.LVL388:
 3492              	.LFB204:
1074:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1075:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1076:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1077:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /*-------------------------------------------------------------------------------------------------
1078:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /* Public API */
1079:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1080:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1081:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1082:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1083:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT publish function.
1084:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
1085:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param topic Publish topic string
1086:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param payload Data to publish (NULL is allowed)
1087:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param payload_length Length of payload (0 is allowed)
1088:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param qos Quality of service, 0 1 or 2
1089:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param retain MQTT retain flag
1090:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param cb Callback to call when publish is complete or has timed out
1091:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg User supplied argument to publish callback
1092:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK if successful
1093:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *         ERR_CONN if client is disconnected
1094:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  *         ERR_MEM if short on memory
1095:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1096:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** err_t
1097:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_publish(mqtt_client_t *client, const char *topic, const void *payload, u16_t payload_length, u
1098:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****              mqtt_request_cb_t cb, void *arg)
1099:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 3493              		.loc 1 1099 1 view -0
 3494              		.cfi_startproc
 3495              		@ args = 16, pretend = 0, frame = 8
 3496              		@ frame_needed = 0, uses_anonymous_args = 0
 3497              		.loc 1 1099 1 is_stmt 0 view .LVU999
 3498 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 3499              	.LCFI36:
 3500              		.cfi_def_cfa_offset 36
 3501              		.cfi_offset 4, -36
 3502              		.cfi_offset 5, -32
 3503              		.cfi_offset 6, -28
 3504              		.cfi_offset 7, -24
 3505              		.cfi_offset 8, -20
 3506              		.cfi_offset 9, -16
 3507              		.cfi_offset 10, -12
 3508              		.cfi_offset 11, -8
 3509              		.cfi_offset 14, -4
 3510 0004 85B0     		sub	sp, sp, #20
 3511              	.LCFI37:
 3512              		.cfi_def_cfa_offset 56
 3513 0006 0F46     		mov	r7, r1
 3514 0008 0292     		str	r2, [sp, #8]
 3515 000a 9946     		mov	r9, r3
 3516 000c 9DF838A0 		ldrb	r10, [sp, #56]	@ zero_extendqisi2
1100:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r;
ARM GAS  /tmp/ccBURsTh.s 			page 98


 3517              		.loc 1 1100 3 is_stmt 1 view .LVU1000
1101:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t pkt_id;
 3518              		.loc 1 1101 3 view .LVU1001
1102:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t topic_strlen;
 3519              		.loc 1 1102 3 view .LVU1002
1103:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t total_len;
 3520              		.loc 1 1103 3 view .LVU1003
1104:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t topic_len;
 3521              		.loc 1 1104 3 view .LVU1004
1105:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t remaining_length;
 3522              		.loc 1 1105 3 view .LVU1005
1106:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1107:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 3523              		.loc 1 1107 28 view .LVU1006
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: client != NULL", client);
 3524              		.loc 1 1108 3 view .LVU1007
 3525              		.loc 1 1108 3 view .LVU1008
 3526 0010 0646     		mov	r6, r0
 3527 0012 0028     		cmp	r0, #0
 3528 0014 50D0     		beq	.L280
 3529              	.LVL389:
 3530              	.L267:
 3531              		.loc 1 1108 3 discriminator 3 view .LVU1009
 3532              		.loc 1 1108 3 discriminator 3 view .LVU1010
1109:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3533              		.loc 1 1109 3 view .LVU1011
 3534              		.loc 1 1109 3 view .LVU1012
 3535 0016 002F     		cmp	r7, #0
 3536 0018 56D0     		beq	.L281
 3537              	.L268:
 3538              		.loc 1 1109 3 discriminator 3 view .LVU1013
 3539              		.loc 1 1109 3 discriminator 3 view .LVU1014
1110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_publish: TCP disconnected", (client->conn_state != TCP_DISCONNECTED), return ERR
 3540              		.loc 1 1110 3 view .LVU1015
 3541              		.loc 1 1110 3 view .LVU1016
 3542 001a B37A     		ldrb	r3, [r6, #10]	@ zero_extendqisi2
 3543 001c 002B     		cmp	r3, #0
 3544 001e 5BD0     		beq	.L282
 3545              		.loc 1 1110 3 discriminator 2 view .LVU1017
1111:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1112:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_strlen = strlen(topic);
 3546              		.loc 1 1112 3 view .LVU1018
 3547              		.loc 1 1112 18 is_stmt 0 view .LVU1019
 3548 0020 3846     		mov	r0, r7
 3549 0022 FFF7FEFF 		bl	strlen
 3550              	.LVL390:
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_publish: topic length overflow", (topic_strlen <= (0xFFFF - 2)), return ERR_ARG)
 3551              		.loc 1 1113 3 is_stmt 1 view .LVU1020
 3552              		.loc 1 1113 3 view .LVU1021
 3553 0026 4FF6FD73 		movw	r3, #65533
 3554 002a 9842     		cmp	r0, r3
 3555 002c 5ED8     		bhi	.L283
 3556              		.loc 1 1113 3 discriminator 2 view .LVU1022
1114:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3557              		.loc 1 1114 3 view .LVU1023
 3558              		.loc 1 1114 13 is_stmt 0 view .LVU1024
 3559 002e 85B2     		uxth	r5, r0
ARM GAS  /tmp/ccBURsTh.s 			page 99


 3560              	.LVL391:
1115:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   total_len = 2 + topic_len + payload_length;
 3561              		.loc 1 1115 3 is_stmt 1 view .LVU1025
 3562              		.loc 1 1115 17 is_stmt 0 view .LVU1026
 3563 0030 AC1C     		adds	r4, r5, #2
 3564              		.loc 1 1115 29 view .LVU1027
 3565 0032 4C44     		add	r4, r4, r9
 3566              	.LVL392:
1116:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1117:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (qos > 0) {
 3567              		.loc 1 1117 3 is_stmt 1 view .LVU1028
 3568              		.loc 1 1117 6 is_stmt 0 view .LVU1029
 3569 0034 BAF1000F 		cmp	r10, #0
 3570 0038 62D1     		bne	.L284
1118:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     total_len += 2;
1119:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Generate pkt_id id for QoS1 and 2 */
1120:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pkt_id = msg_generate_packet_id(client);
1121:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
1122:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Use reserved value pkt_id 0 for QoS 0 in request handle */
1123:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     pkt_id = 0;
 3571              		.loc 1 1123 12 view .LVU1030
 3572 003a 4FF0000B 		mov	fp, #0
 3573              	.LVL393:
 3574              	.L272:
1124:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_publish: total length overflow", (total_len <= 0xFFFF), return ERR_ARG);
 3575              		.loc 1 1125 3 is_stmt 1 view .LVU1031
 3576              		.loc 1 1125 3 view .LVU1032
 3577 003e B4F5803F 		cmp	r4, #65536
 3578 0042 63D2     		bcs	.L285
 3579              		.loc 1 1125 3 discriminator 2 view .LVU1033
1126:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3580              		.loc 1 1126 3 view .LVU1034
 3581              		.loc 1 1126 20 is_stmt 0 view .LVU1035
 3582 0044 A4B2     		uxth	r4, r4
 3583              	.LVL394:
1127:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1128:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_publish: Publish with payload length %d to topic \"%s\"\n", 
 3584              		.loc 1 1128 123 is_stmt 1 view .LVU1036
1129:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1130:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = mqtt_create_request(client->req_list, LWIP_ARRAYSIZE(client->req_list), pkt_id, cb, arg);
 3585              		.loc 1 1130 3 view .LVU1037
 3586              		.loc 1 1130 7 is_stmt 0 view .LVU1038
 3587 0046 119B     		ldr	r3, [sp, #68]
 3588 0048 0093     		str	r3, [sp]
 3589 004a 109B     		ldr	r3, [sp, #64]
 3590 004c 5A46     		mov	r2, fp
 3591 004e 0421     		movs	r1, #4
 3592 0050 06F11C00 		add	r0, r6, #28
 3593 0054 FFF7FEFF 		bl	mqtt_create_request
 3594              	.LVL395:
1131:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (r == NULL) {
 3595              		.loc 1 1131 3 is_stmt 1 view .LVU1039
 3596              		.loc 1 1131 6 is_stmt 0 view .LVU1040
 3597 0058 0390     		str	r0, [sp, #12]
 3598 005a 0028     		cmp	r0, #0
 3599 005c 71D0     		beq	.L278
ARM GAS  /tmp/ccBURsTh.s 			page 100


1132:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
1133:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1134:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1135:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (mqtt_output_check_space(&client->output, remaining_length) == 0) {
 3600              		.loc 1 1135 3 is_stmt 1 view .LVU1041
 3601              		.loc 1 1135 7 is_stmt 0 view .LVU1042
 3602 005e 06F1EC08 		add	r8, r6, #236
 3603 0062 2146     		mov	r1, r4
 3604 0064 4046     		mov	r0, r8
 3605              	.LVL396:
 3606              		.loc 1 1135 7 view .LVU1043
 3607 0066 FFF7FEFF 		bl	mqtt_output_check_space
 3608              	.LVL397:
 3609              		.loc 1 1135 6 discriminator 1 view .LVU1044
 3610 006a 0028     		cmp	r0, #0
 3611 006c 58D0     		beq	.L286
1136:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_delete_request(r);
1137:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
1138:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1139:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append fixed header */
1140:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_fixed_header(&client->output, MQTT_MSG_TYPE_PUBLISH, 0, qos, retain, remaining
 3612              		.loc 1 1140 3 is_stmt 1 view .LVU1045
 3613 006e 0194     		str	r4, [sp, #4]
 3614 0070 9DF83C30 		ldrb	r3, [sp, #60]	@ zero_extendqisi2
 3615 0074 0093     		str	r3, [sp]
 3616 0076 5346     		mov	r3, r10
 3617 0078 0022     		movs	r2, #0
 3618 007a 0321     		movs	r1, #3
 3619 007c 4046     		mov	r0, r8
 3620 007e FFF7FEFF 		bl	mqtt_output_append_fixed_header
 3621              	.LVL398:
1141:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1142:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append Topic */
1143:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_string(&client->output, topic, topic_len);
 3622              		.loc 1 1143 3 view .LVU1046
 3623 0082 2A46     		mov	r2, r5
 3624 0084 3946     		mov	r1, r7
 3625 0086 4046     		mov	r0, r8
 3626 0088 FFF7FEFF 		bl	mqtt_output_append_string
 3627              	.LVL399:
1144:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1145:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append packet if for QoS 1 and 2*/
1146:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (qos > 0) {
 3628              		.loc 1 1146 3 view .LVU1047
 3629              		.loc 1 1146 6 is_stmt 0 view .LVU1048
 3630 008c BAF1000F 		cmp	r10, #0
 3631 0090 4CD1     		bne	.L287
 3632              	.L275:
1147:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_u16(&client->output, pkt_id);
1148:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1149:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1150:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append optional publish payload */
1151:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((payload != NULL) && (payload_length > 0)) {
 3633              		.loc 1 1151 3 is_stmt 1 view .LVU1049
 3634              		.loc 1 1151 6 is_stmt 0 view .LVU1050
 3635 0092 029B     		ldr	r3, [sp, #8]
 3636 0094 002B     		cmp	r3, #0
ARM GAS  /tmp/ccBURsTh.s 			page 101


 3637 0096 18BF     		it	ne
 3638 0098 B9F1000F 		cmpne	r9, #0
 3639 009c 4BD1     		bne	.L288
 3640              	.L276:
1152:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_buf(&client->output, payload, payload_length);
1153:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1154:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1155:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_append_request(&client->pend_req_queue, r);
 3641              		.loc 1 1155 3 is_stmt 1 view .LVU1051
 3642 009e 0399     		ldr	r1, [sp, #12]
 3643 00a0 06F11800 		add	r0, r6, #24
 3644 00a4 FFF7FEFF 		bl	mqtt_append_request
 3645              	.LVL400:
1156:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_send(&client->output, client->conn);
 3646              		.loc 1 1156 3 view .LVU1052
 3647 00a8 F168     		ldr	r1, [r6, #12]
 3648 00aa 4046     		mov	r0, r8
 3649 00ac FFF7FEFF 		bl	mqtt_output_send
 3650              	.LVL401:
1157:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
 3651              		.loc 1 1157 3 view .LVU1053
 3652              		.loc 1 1157 10 is_stmt 0 view .LVU1054
 3653 00b0 0020     		movs	r0, #0
 3654              	.LVL402:
 3655              	.L270:
1158:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 3656              		.loc 1 1158 1 view .LVU1055
 3657 00b2 05B0     		add	sp, sp, #20
 3658              	.LCFI38:
 3659              		.cfi_remember_state
 3660              		.cfi_def_cfa_offset 36
 3661              		@ sp needed
 3662 00b4 BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 3663              	.LVL403:
 3664              	.L280:
 3665              	.LCFI39:
 3666              		.cfi_restore_state
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3667              		.loc 1 1108 3 is_stmt 1 discriminator 1 view .LVU1056
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3668              		.loc 1 1108 3 discriminator 1 view .LVU1057
 3669 00b8 234B     		ldr	r3, .L289
 3670              	.LVL404:
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3671              		.loc 1 1108 3 is_stmt 0 discriminator 1 view .LVU1058
 3672 00ba 40F25442 		movw	r2, #1108
 3673              	.LVL405:
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3674              		.loc 1 1108 3 discriminator 1 view .LVU1059
 3675 00be 2349     		ldr	r1, .L289+4
 3676              	.LVL406:
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3677              		.loc 1 1108 3 discriminator 1 view .LVU1060
 3678 00c0 2348     		ldr	r0, .L289+8
 3679              	.LVL407:
1108:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_publish: topic != NULL", topic);
 3680              		.loc 1 1108 3 discriminator 1 view .LVU1061
ARM GAS  /tmp/ccBURsTh.s 			page 102


 3681 00c2 FFF7FEFF 		bl	printf
 3682              	.LVL408:
 3683 00c6 A6E7     		b	.L267
 3684              	.L281:
1109:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_publish: TCP disconnected", (client->conn_state != TCP_DISCONNECTED), return ERR
 3685              		.loc 1 1109 3 is_stmt 1 discriminator 1 view .LVU1062
1109:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_publish: TCP disconnected", (client->conn_state != TCP_DISCONNECTED), return ERR
 3686              		.loc 1 1109 3 discriminator 1 view .LVU1063
 3687 00c8 1F4B     		ldr	r3, .L289
 3688 00ca 40F25542 		movw	r2, #1109
 3689 00ce 2149     		ldr	r1, .L289+12
 3690 00d0 1F48     		ldr	r0, .L289+8
 3691 00d2 FFF7FEFF 		bl	printf
 3692              	.LVL409:
 3693 00d6 A0E7     		b	.L268
 3694              	.L282:
1110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3695              		.loc 1 1110 3 discriminator 1 view .LVU1064
1110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3696              		.loc 1 1110 3 discriminator 1 view .LVU1065
 3697 00d8 1B4B     		ldr	r3, .L289
 3698 00da 40F25642 		movw	r2, #1110
 3699 00de 1E49     		ldr	r1, .L289+16
 3700 00e0 1B48     		ldr	r0, .L289+8
 3701 00e2 FFF7FEFF 		bl	printf
 3702              	.LVL410:
1110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3703              		.loc 1 1110 3 discriminator 1 view .LVU1066
1110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3704              		.loc 1 1110 3 discriminator 1 view .LVU1067
 3705 00e6 6FF00A00 		mvn	r0, #10
1110:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 3706              		.loc 1 1110 3 is_stmt 0 view .LVU1068
 3707 00ea E2E7     		b	.L270
 3708              	.LVL411:
 3709              	.L283:
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3710              		.loc 1 1113 3 is_stmt 1 discriminator 1 view .LVU1069
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3711              		.loc 1 1113 3 discriminator 1 view .LVU1070
 3712 00ec 164B     		ldr	r3, .L289
 3713 00ee 40F25942 		movw	r2, #1113
 3714 00f2 1A49     		ldr	r1, .L289+20
 3715 00f4 1648     		ldr	r0, .L289+8
 3716              	.LVL412:
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3717              		.loc 1 1113 3 is_stmt 0 discriminator 1 view .LVU1071
 3718 00f6 FFF7FEFF 		bl	printf
 3719              	.LVL413:
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3720              		.loc 1 1113 3 is_stmt 1 discriminator 1 view .LVU1072
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3721              		.loc 1 1113 3 discriminator 1 view .LVU1073
 3722 00fa 6FF00F00 		mvn	r0, #15
1113:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3723              		.loc 1 1113 3 is_stmt 0 view .LVU1074
 3724 00fe D8E7     		b	.L270
ARM GAS  /tmp/ccBURsTh.s 			page 103


 3725              	.LVL414:
 3726              	.L284:
1118:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Generate pkt_id id for QoS1 and 2 */
 3727              		.loc 1 1118 5 is_stmt 1 view .LVU1075
1118:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Generate pkt_id id for QoS1 and 2 */
 3728              		.loc 1 1118 15 is_stmt 0 view .LVU1076
 3729 0100 0234     		adds	r4, r4, #2
 3730              	.LVL415:
1120:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 3731              		.loc 1 1120 5 is_stmt 1 view .LVU1077
1120:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 3732              		.loc 1 1120 14 is_stmt 0 view .LVU1078
 3733 0102 3046     		mov	r0, r6
 3734              	.LVL416:
1120:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 3735              		.loc 1 1120 14 view .LVU1079
 3736 0104 FFF7FEFF 		bl	msg_generate_packet_id
 3737              	.LVL417:
 3738 0108 8346     		mov	fp, r0
 3739              	.LVL418:
1120:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else {
 3740              		.loc 1 1120 14 view .LVU1080
 3741 010a 98E7     		b	.L272
 3742              	.L285:
1125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3743              		.loc 1 1125 3 is_stmt 1 discriminator 1 view .LVU1081
1125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3744              		.loc 1 1125 3 discriminator 1 view .LVU1082
 3745 010c 0E4B     		ldr	r3, .L289
 3746 010e 40F26542 		movw	r2, #1125
 3747 0112 1349     		ldr	r1, .L289+24
 3748 0114 0E48     		ldr	r0, .L289+8
 3749 0116 FFF7FEFF 		bl	printf
 3750              	.LVL419:
1125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3751              		.loc 1 1125 3 discriminator 1 view .LVU1083
1125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3752              		.loc 1 1125 3 discriminator 1 view .LVU1084
 3753 011a 6FF00F00 		mvn	r0, #15
1125:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3754              		.loc 1 1125 3 is_stmt 0 view .LVU1085
 3755 011e C8E7     		b	.L270
 3756              	.LVL420:
 3757              	.L286:
1136:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
 3758              		.loc 1 1136 5 is_stmt 1 view .LVU1086
 3759 0120 0398     		ldr	r0, [sp, #12]
 3760 0122 FFF7FEFF 		bl	mqtt_delete_request
 3761              	.LVL421:
1137:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 3762              		.loc 1 1137 5 view .LVU1087
1137:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 3763              		.loc 1 1137 12 is_stmt 0 view .LVU1088
 3764 0126 4FF0FF30 		mov	r0, #-1
 3765 012a C2E7     		b	.L270
 3766              	.L287:
1147:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
ARM GAS  /tmp/ccBURsTh.s 			page 104


 3767              		.loc 1 1147 5 is_stmt 1 view .LVU1089
 3768 012c 5946     		mov	r1, fp
 3769 012e 4046     		mov	r0, r8
 3770 0130 FFF7FEFF 		bl	mqtt_output_append_u16
 3771              	.LVL422:
 3772 0134 ADE7     		b	.L275
 3773              	.L288:
1152:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 3774              		.loc 1 1152 5 view .LVU1090
 3775 0136 4A46     		mov	r2, r9
 3776 0138 1946     		mov	r1, r3
 3777 013a 4046     		mov	r0, r8
 3778 013c FFF7FEFF 		bl	mqtt_output_append_buf
 3779              	.LVL423:
 3780 0140 ADE7     		b	.L276
 3781              	.LVL424:
 3782              	.L278:
1132:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 3783              		.loc 1 1132 12 is_stmt 0 view .LVU1091
 3784 0142 4FF0FF30 		mov	r0, #-1
 3785              	.LVL425:
1132:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 3786              		.loc 1 1132 12 view .LVU1092
 3787 0146 B4E7     		b	.L270
 3788              	.L290:
 3789              		.align	2
 3790              	.L289:
 3791 0148 00000000 		.word	.LC0
 3792 014c 00000000 		.word	.LC19
 3793 0150 58000000 		.word	.LC2
 3794 0154 20000000 		.word	.LC20
 3795 0158 3C000000 		.word	.LC21
 3796 015c 5C000000 		.word	.LC22
 3797 0160 80000000 		.word	.LC23
 3798              		.cfi_endproc
 3799              	.LFE204:
 3801              		.section	.rodata.mqtt_sub_unsub.str1.4,"aMS",%progbits,1
 3802              		.align	2
 3803              	.LC24:
 3804 0000 6D717474 		.ascii	"mqtt_sub_unsub: client != NULL\000"
 3804      5F737562 
 3804      5F756E73 
 3804      75623A20 
 3804      636C6965 
 3805 001f 00       		.align	2
 3806              	.LC25:
 3807 0020 6D717474 		.ascii	"mqtt_sub_unsub: topic != NULL\000"
 3807      5F737562 
 3807      5F756E73 
 3807      75623A20 
 3807      746F7069 
 3808 003e 0000     		.align	2
 3809              	.LC26:
 3810 0040 6D717474 		.ascii	"mqtt_sub_unsub: topic length overflow\000"
 3810      5F737562 
 3810      5F756E73 
 3810      75623A20 
ARM GAS  /tmp/ccBURsTh.s 			page 105


 3810      746F7069 
 3811 0066 0000     		.align	2
 3812              	.LC27:
 3813 0068 6D717474 		.ascii	"mqtt_sub_unsub: total length overflow\000"
 3813      5F737562 
 3813      5F756E73 
 3813      75623A20 
 3813      746F7461 
 3814 008e 0000     		.align	2
 3815              	.LC28:
 3816 0090 6D717474 		.ascii	"mqtt_sub_unsub: qos < 3\000"
 3816      5F737562 
 3816      5F756E73 
 3816      75623A20 
 3816      716F7320 
 3817              		.section	.text.mqtt_sub_unsub,"ax",%progbits
 3818              		.align	1
 3819              		.global	mqtt_sub_unsub
 3820              		.syntax unified
 3821              		.thumb
 3822              		.thumb_func
 3824              	mqtt_sub_unsub:
 3825              	.LVL426:
 3826              	.LFB205:
1159:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1160:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1161:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1162:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1163:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * MQTT subscribe/unsubscribe function.
1164:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
1165:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param topic topic to subscribe to
1166:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param qos Quality of service, 0 1 or 2 (only used for subscribe)
1167:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param cb Callback to call when subscribe/unsubscribe reponse is received
1168:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg User supplied argument to publish callback
1169:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param sub 1 for subscribe, 0 for unsubscribe
1170:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK if successful, @see err_t enum for other results
1171:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1172:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** err_t
1173:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_sub_unsub(mqtt_client_t *client, const char *topic, u8_t qos, mqtt_request_cb_t cb, void *arg,
1174:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 3827              		.loc 1 1174 1 is_stmt 1 view -0
 3828              		.cfi_startproc
 3829              		@ args = 8, pretend = 0, frame = 8
 3830              		@ frame_needed = 0, uses_anonymous_args = 0
 3831              		.loc 1 1174 1 is_stmt 0 view .LVU1094
 3832 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 3833              	.LCFI40:
 3834              		.cfi_def_cfa_offset 36
 3835              		.cfi_offset 4, -36
 3836              		.cfi_offset 5, -32
 3837              		.cfi_offset 6, -28
 3838              		.cfi_offset 7, -24
 3839              		.cfi_offset 8, -20
 3840              		.cfi_offset 9, -16
 3841              		.cfi_offset 10, -12
 3842              		.cfi_offset 11, -8
 3843              		.cfi_offset 14, -4
ARM GAS  /tmp/ccBURsTh.s 			page 106


 3844 0004 85B0     		sub	sp, sp, #20
 3845              	.LCFI41:
 3846              		.cfi_def_cfa_offset 56
 3847 0006 8846     		mov	r8, r1
 3848 0008 9246     		mov	r10, r2
 3849 000a 1F46     		mov	r7, r3
 3850 000c 9DF83CB0 		ldrb	fp, [sp, #60]	@ zero_extendqisi2
1175:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t topic_strlen;
 3851              		.loc 1 1175 3 is_stmt 1 view .LVU1095
1176:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t total_len;
 3852              		.loc 1 1176 3 view .LVU1096
1177:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t topic_len;
 3853              		.loc 1 1177 3 view .LVU1097
1178:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t remaining_length;
 3854              		.loc 1 1178 3 view .LVU1098
1179:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t pkt_id;
 3855              		.loc 1 1179 3 view .LVU1099
1180:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   struct mqtt_request_t *r;
 3856              		.loc 1 1180 3 view .LVU1100
1181:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1182:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 3857              		.loc 1 1182 28 view .LVU1101
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: client != NULL", client);
 3858              		.loc 1 1183 3 view .LVU1102
 3859              		.loc 1 1183 3 view .LVU1103
 3860 0010 0646     		mov	r6, r0
 3861 0012 0028     		cmp	r0, #0
 3862 0014 55D0     		beq	.L305
 3863              	.LVL427:
 3864              	.L292:
 3865              		.loc 1 1183 3 discriminator 3 view .LVU1104
 3866              		.loc 1 1183 3 discriminator 3 view .LVU1105
1184:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 3867              		.loc 1 1184 3 view .LVU1106
 3868              		.loc 1 1184 3 view .LVU1107
 3869 0016 B8F1000F 		cmp	r8, #0
 3870 001a 5AD0     		beq	.L306
 3871              	.L293:
 3872              		.loc 1 1184 3 discriminator 3 view .LVU1108
 3873              		.loc 1 1184 3 discriminator 3 view .LVU1109
1185:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1186:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_strlen = strlen(topic);
 3874              		.loc 1 1186 3 view .LVU1110
 3875              		.loc 1 1186 18 is_stmt 0 view .LVU1111
 3876 001c 4046     		mov	r0, r8
 3877 001e FFF7FEFF 		bl	strlen
 3878              	.LVL428:
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_sub_unsub: topic length overflow", (topic_strlen <= (0xFFFF - 2)), return ERR_AR
 3879              		.loc 1 1187 3 is_stmt 1 view .LVU1112
 3880              		.loc 1 1187 3 view .LVU1113
 3881 0022 4FF6FD73 		movw	r3, #65533
 3882 0026 9842     		cmp	r0, r3
 3883 0028 5BD8     		bhi	.L307
 3884              		.loc 1 1187 3 discriminator 2 view .LVU1114
1188:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 3885              		.loc 1 1188 3 view .LVU1115
 3886              		.loc 1 1188 13 is_stmt 0 view .LVU1116
ARM GAS  /tmp/ccBURsTh.s 			page 107


 3887 002a 84B2     		uxth	r4, r0
 3888              	.LVL429:
1189:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Topic string, pkt_id, qos for subscribe */
1190:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   total_len =  topic_len + 2 + 2 + (sub != 0);
 3889              		.loc 1 1190 3 is_stmt 1 view .LVU1117
 3890              		.loc 1 1190 30 is_stmt 0 view .LVU1118
 3891 002c 251D     		adds	r5, r4, #4
 3892              		.loc 1 1190 34 view .LVU1119
 3893 002e BBF1000F 		cmp	fp, #0
 3894 0032 18BF     		it	ne
 3895 0034 0135     		addne	r5, r5, #1
 3896              	.LVL430:
1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_sub_unsub: total length overflow", (total_len <= 0xFFFF), return ERR_ARG);
 3897              		.loc 1 1191 3 is_stmt 1 view .LVU1120
 3898              		.loc 1 1191 3 view .LVU1121
 3899 0036 B5F5803F 		cmp	r5, #65536
 3900 003a 5CD2     		bcs	.L308
 3901              		.loc 1 1191 3 discriminator 2 view .LVU1122
1192:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 3902              		.loc 1 1192 3 view .LVU1123
 3903              		.loc 1 1192 20 is_stmt 0 view .LVU1124
 3904 003c ADB2     		uxth	r5, r5
 3905              	.LVL431:
1193:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1194:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: qos < 3", qos < 3);
 3906              		.loc 1 1194 3 is_stmt 1 view .LVU1125
 3907              		.loc 1 1194 3 view .LVU1126
 3908 003e BAF1020F 		cmp	r10, #2
 3909 0042 62D8     		bhi	.L309
 3910              	.LVL432:
 3911              	.L297:
 3912              		.loc 1 1194 3 discriminator 3 view .LVU1127
 3913              		.loc 1 1194 3 discriminator 3 view .LVU1128
1195:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == TCP_DISCONNECTED) {
 3914              		.loc 1 1195 3 view .LVU1129
 3915              		.loc 1 1195 13 is_stmt 0 view .LVU1130
 3916 0044 B37A     		ldrb	r3, [r6, #10]	@ zero_extendqisi2
 3917              		.loc 1 1195 6 view .LVU1131
 3918 0046 002B     		cmp	r3, #0
 3919 0048 77D0     		beq	.L301
1196:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_sub_unsub: Can not (un)subscribe in disconnected state\n"))
1197:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_CONN;
1198:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1199:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1200:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   pkt_id = msg_generate_packet_id(client);
 3920              		.loc 1 1200 3 is_stmt 1 view .LVU1132
 3921              		.loc 1 1200 12 is_stmt 0 view .LVU1133
 3922 004a 3046     		mov	r0, r6
 3923 004c FFF7FEFF 		bl	msg_generate_packet_id
 3924              	.LVL433:
 3925 0050 8146     		mov	r9, r0
 3926              	.LVL434:
1201:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   r = mqtt_create_request(client->req_list, LWIP_ARRAYSIZE(client->req_list), pkt_id, cb, arg);
 3927              		.loc 1 1201 3 is_stmt 1 view .LVU1134
 3928              		.loc 1 1201 7 is_stmt 0 view .LVU1135
 3929 0052 0E9B     		ldr	r3, [sp, #56]
 3930 0054 0093     		str	r3, [sp]
ARM GAS  /tmp/ccBURsTh.s 			page 108


 3931 0056 3B46     		mov	r3, r7
 3932 0058 0246     		mov	r2, r0
 3933 005a 0421     		movs	r1, #4
 3934 005c 06F11C00 		add	r0, r6, #28
 3935              	.LVL435:
 3936              		.loc 1 1201 7 view .LVU1136
 3937 0060 FFF7FEFF 		bl	mqtt_create_request
 3938              	.LVL436:
1202:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (r == NULL) {
 3939              		.loc 1 1202 3 is_stmt 1 view .LVU1137
 3940              		.loc 1 1202 6 is_stmt 0 view .LVU1138
 3941 0064 0390     		str	r0, [sp, #12]
 3942 0066 0028     		cmp	r0, #0
 3943 0068 6AD0     		beq	.L302
1203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
1204:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1205:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1206:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (mqtt_output_check_space(&client->output, remaining_length) == 0) {
 3944              		.loc 1 1206 3 is_stmt 1 view .LVU1139
 3945              		.loc 1 1206 7 is_stmt 0 view .LVU1140
 3946 006a 06F1EC07 		add	r7, r6, #236
 3947              	.LVL437:
 3948              		.loc 1 1206 7 view .LVU1141
 3949 006e 2946     		mov	r1, r5
 3950 0070 3846     		mov	r0, r7
 3951              	.LVL438:
 3952              		.loc 1 1206 7 view .LVU1142
 3953 0072 FFF7FEFF 		bl	mqtt_output_check_space
 3954              	.LVL439:
 3955              		.loc 1 1206 6 discriminator 1 view .LVU1143
 3956 0076 0028     		cmp	r0, #0
 3957 0078 4FD0     		beq	.L310
1207:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_delete_request(r);
1208:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
1209:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1210:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1211:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_sub_unsub: Client (un)subscribe to topic \"%s\", id: %d\n", 
 3958              		.loc 1 1211 115 is_stmt 1 view .LVU1144
1212:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1213:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_fixed_header(&client->output, sub ? MQTT_MSG_TYPE_SUBSCRIBE : MQTT_MSG_TYPE_UN
 3959              		.loc 1 1213 3 view .LVU1145
 3960 007a BBF1000F 		cmp	fp, #0
 3961 007e 52D0     		beq	.L303
 3962              		.loc 1 1213 3 is_stmt 0 discriminator 1 view .LVU1146
 3963 0080 0821     		movs	r1, #8
 3964              	.L299:
 3965              		.loc 1 1213 3 discriminator 4 view .LVU1147
 3966 0082 0195     		str	r5, [sp, #4]
 3967 0084 0022     		movs	r2, #0
 3968 0086 0092     		str	r2, [sp]
 3969 0088 0123     		movs	r3, #1
 3970 008a 3846     		mov	r0, r7
 3971 008c FFF7FEFF 		bl	mqtt_output_append_fixed_header
 3972              	.LVL440:
1214:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Packet id */
1215:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_u16(&client->output, pkt_id);
 3973              		.loc 1 1215 3 is_stmt 1 view .LVU1148
ARM GAS  /tmp/ccBURsTh.s 			page 109


 3974 0090 4946     		mov	r1, r9
 3975 0092 3846     		mov	r0, r7
 3976 0094 FFF7FEFF 		bl	mqtt_output_append_u16
 3977              	.LVL441:
1216:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Topic */
1217:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_string(&client->output, topic, topic_len);
 3978              		.loc 1 1217 3 view .LVU1149
 3979 0098 2246     		mov	r2, r4
 3980 009a 4146     		mov	r1, r8
 3981 009c 3846     		mov	r0, r7
 3982 009e FFF7FEFF 		bl	mqtt_output_append_string
 3983              	.LVL442:
1218:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* QoS */
1219:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (sub != 0) {
 3984              		.loc 1 1219 3 view .LVU1150
 3985              		.loc 1 1219 6 is_stmt 0 view .LVU1151
 3986 00a2 BBF1000F 		cmp	fp, #0
 3987 00a6 40D1     		bne	.L311
 3988              	.L300:
1220:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_u8(&client->output, LWIP_MIN(qos, 2));
1221:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1222:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1223:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_append_request(&client->pend_req_queue, r);
 3989              		.loc 1 1223 3 is_stmt 1 view .LVU1152
 3990 00a8 0399     		ldr	r1, [sp, #12]
 3991 00aa 06F11800 		add	r0, r6, #24
 3992 00ae FFF7FEFF 		bl	mqtt_append_request
 3993              	.LVL443:
1224:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_send(&client->output, client->conn);
 3994              		.loc 1 1224 3 view .LVU1153
 3995 00b2 F168     		ldr	r1, [r6, #12]
 3996 00b4 3846     		mov	r0, r7
 3997 00b6 FFF7FEFF 		bl	mqtt_output_send
 3998              	.LVL444:
1225:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
 3999              		.loc 1 1225 3 view .LVU1154
 4000              		.loc 1 1225 10 is_stmt 0 view .LVU1155
 4001 00ba 0020     		movs	r0, #0
 4002              	.LVL445:
 4003              	.L295:
1226:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 4004              		.loc 1 1226 1 view .LVU1156
 4005 00bc 05B0     		add	sp, sp, #20
 4006              	.LCFI42:
 4007              		.cfi_remember_state
 4008              		.cfi_def_cfa_offset 36
 4009              		@ sp needed
 4010 00be BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 4011              	.LVL446:
 4012              	.L305:
 4013              	.LCFI43:
 4014              		.cfi_restore_state
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 4015              		.loc 1 1183 3 is_stmt 1 discriminator 1 view .LVU1157
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 4016              		.loc 1 1183 3 discriminator 1 view .LVU1158
 4017 00c2 214B     		ldr	r3, .L312
ARM GAS  /tmp/ccBURsTh.s 			page 110


 4018              	.LVL447:
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 4019              		.loc 1 1183 3 is_stmt 0 discriminator 1 view .LVU1159
 4020 00c4 40F29F42 		movw	r2, #1183
 4021              	.LVL448:
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 4022              		.loc 1 1183 3 discriminator 1 view .LVU1160
 4023 00c8 2049     		ldr	r1, .L312+4
 4024              	.LVL449:
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 4025              		.loc 1 1183 3 discriminator 1 view .LVU1161
 4026 00ca 2148     		ldr	r0, .L312+8
 4027              	.LVL450:
1183:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_sub_unsub: topic != NULL", topic);
 4028              		.loc 1 1183 3 discriminator 1 view .LVU1162
 4029 00cc FFF7FEFF 		bl	printf
 4030              	.LVL451:
 4031 00d0 A1E7     		b	.L292
 4032              	.L306:
1184:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4033              		.loc 1 1184 3 is_stmt 1 discriminator 1 view .LVU1163
1184:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4034              		.loc 1 1184 3 discriminator 1 view .LVU1164
 4035 00d2 1D4B     		ldr	r3, .L312
 4036 00d4 4FF49462 		mov	r2, #1184
 4037 00d8 1E49     		ldr	r1, .L312+12
 4038 00da 1D48     		ldr	r0, .L312+8
 4039 00dc FFF7FEFF 		bl	printf
 4040              	.LVL452:
 4041 00e0 9CE7     		b	.L293
 4042              	.LVL453:
 4043              	.L307:
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 4044              		.loc 1 1187 3 discriminator 1 view .LVU1165
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 4045              		.loc 1 1187 3 discriminator 1 view .LVU1166
 4046 00e2 194B     		ldr	r3, .L312
 4047 00e4 40F2A342 		movw	r2, #1187
 4048 00e8 1B49     		ldr	r1, .L312+16
 4049 00ea 1948     		ldr	r0, .L312+8
 4050              	.LVL454:
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 4051              		.loc 1 1187 3 is_stmt 0 discriminator 1 view .LVU1167
 4052 00ec FFF7FEFF 		bl	printf
 4053              	.LVL455:
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 4054              		.loc 1 1187 3 is_stmt 1 discriminator 1 view .LVU1168
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 4055              		.loc 1 1187 3 discriminator 1 view .LVU1169
 4056 00f0 6FF00F00 		mvn	r0, #15
1187:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   topic_len = (u16_t)topic_strlen;
 4057              		.loc 1 1187 3 is_stmt 0 view .LVU1170
 4058 00f4 E2E7     		b	.L295
 4059              	.LVL456:
 4060              	.L308:
1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 4061              		.loc 1 1191 3 is_stmt 1 discriminator 1 view .LVU1171
ARM GAS  /tmp/ccBURsTh.s 			page 111


1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 4062              		.loc 1 1191 3 discriminator 1 view .LVU1172
 4063 00f6 144B     		ldr	r3, .L312
 4064 00f8 40F2A742 		movw	r2, #1191
 4065 00fc 1749     		ldr	r1, .L312+20
 4066 00fe 1448     		ldr	r0, .L312+8
 4067              	.LVL457:
1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 4068              		.loc 1 1191 3 is_stmt 0 discriminator 1 view .LVU1173
 4069 0100 FFF7FEFF 		bl	printf
 4070              	.LVL458:
1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 4071              		.loc 1 1191 3 is_stmt 1 discriminator 1 view .LVU1174
1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 4072              		.loc 1 1191 3 discriminator 1 view .LVU1175
 4073 0104 6FF00F00 		mvn	r0, #15
1191:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)total_len;
 4074              		.loc 1 1191 3 is_stmt 0 view .LVU1176
 4075 0108 D8E7     		b	.L295
 4076              	.LVL459:
 4077              	.L309:
1194:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == TCP_DISCONNECTED) {
 4078              		.loc 1 1194 3 is_stmt 1 discriminator 1 view .LVU1177
1194:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == TCP_DISCONNECTED) {
 4079              		.loc 1 1194 3 discriminator 1 view .LVU1178
 4080 010a 0F4B     		ldr	r3, .L312
 4081 010c 40F2AA42 		movw	r2, #1194
 4082 0110 1349     		ldr	r1, .L312+24
 4083 0112 0F48     		ldr	r0, .L312+8
 4084              	.LVL460:
1194:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state == TCP_DISCONNECTED) {
 4085              		.loc 1 1194 3 is_stmt 0 discriminator 1 view .LVU1179
 4086 0114 FFF7FEFF 		bl	printf
 4087              	.LVL461:
 4088 0118 94E7     		b	.L297
 4089              	.LVL462:
 4090              	.L310:
1207:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
 4091              		.loc 1 1207 5 is_stmt 1 view .LVU1180
 4092 011a 0398     		ldr	r0, [sp, #12]
 4093 011c FFF7FEFF 		bl	mqtt_delete_request
 4094              	.LVL463:
1208:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 4095              		.loc 1 1208 5 view .LVU1181
1208:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 4096              		.loc 1 1208 12 is_stmt 0 view .LVU1182
 4097 0120 4FF0FF30 		mov	r0, #-1
 4098 0124 CAE7     		b	.L295
 4099              	.L303:
1213:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Packet id */
 4100              		.loc 1 1213 3 discriminator 2 view .LVU1183
 4101 0126 0A21     		movs	r1, #10
 4102 0128 ABE7     		b	.L299
 4103              	.L311:
1220:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 4104              		.loc 1 1220 5 is_stmt 1 view .LVU1184
 4105 012a 5146     		mov	r1, r10
ARM GAS  /tmp/ccBURsTh.s 			page 112


 4106 012c 0229     		cmp	r1, #2
 4107 012e 28BF     		it	cs
 4108 0130 0221     		movcs	r1, #2
 4109 0132 3846     		mov	r0, r7
 4110 0134 FFF7FEFF 		bl	mqtt_output_append_u8
 4111              	.LVL464:
 4112 0138 B6E7     		b	.L300
 4113              	.LVL465:
 4114              	.L301:
1197:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 4115              		.loc 1 1197 12 is_stmt 0 view .LVU1185
 4116 013a 6FF00A00 		mvn	r0, #10
 4117 013e BDE7     		b	.L295
 4118              	.LVL466:
 4119              	.L302:
1203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 4120              		.loc 1 1203 12 view .LVU1186
 4121 0140 4FF0FF30 		mov	r0, #-1
 4122              	.LVL467:
1203:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 4123              		.loc 1 1203 12 view .LVU1187
 4124 0144 BAE7     		b	.L295
 4125              	.L313:
 4126 0146 00BF     		.align	2
 4127              	.L312:
 4128 0148 00000000 		.word	.LC0
 4129 014c 00000000 		.word	.LC24
 4130 0150 58000000 		.word	.LC2
 4131 0154 20000000 		.word	.LC25
 4132 0158 40000000 		.word	.LC26
 4133 015c 68000000 		.word	.LC27
 4134 0160 90000000 		.word	.LC28
 4135              		.cfi_endproc
 4136              	.LFE205:
 4138              		.section	.rodata.mqtt_set_inpub_callback.str1.4,"aMS",%progbits,1
 4139              		.align	2
 4140              	.LC29:
 4141 0000 6D717474 		.ascii	"mqtt_set_inpub_callback: client != NULL\000"
 4141      5F736574 
 4141      5F696E70 
 4141      75625F63 
 4141      616C6C62 
 4142              		.section	.text.mqtt_set_inpub_callback,"ax",%progbits
 4143              		.align	1
 4144              		.global	mqtt_set_inpub_callback
 4145              		.syntax unified
 4146              		.thumb
 4147              		.thumb_func
 4149              	mqtt_set_inpub_callback:
 4150              	.LVL468:
 4151              	.LFB206:
1227:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1228:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1229:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1230:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1231:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Set callback to handle incoming publish requests from server
1232:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
ARM GAS  /tmp/ccBURsTh.s 			page 113


1233:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param pub_cb Callback invoked when publish starts, contain topic and total length of payload
1234:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param data_cb Callback for each fragment of payload that arrives
1235:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg User supplied argument to both callbacks
1236:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1237:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** void
1238:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_set_inpub_callback(mqtt_client_t *client, mqtt_incoming_publish_cb_t pub_cb,
1239:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                         mqtt_incoming_data_cb_t data_cb, void *arg)
1240:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 4152              		.loc 1 1240 1 is_stmt 1 view -0
 4153              		.cfi_startproc
 4154              		@ args = 0, pretend = 0, frame = 0
 4155              		@ frame_needed = 0, uses_anonymous_args = 0
 4156              		.loc 1 1240 1 is_stmt 0 view .LVU1189
 4157 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 4158              	.LCFI44:
 4159              		.cfi_def_cfa_offset 24
 4160              		.cfi_offset 3, -24
 4161              		.cfi_offset 4, -20
 4162              		.cfi_offset 5, -16
 4163              		.cfi_offset 6, -12
 4164              		.cfi_offset 7, -8
 4165              		.cfi_offset 14, -4
 4166 0002 0E46     		mov	r6, r1
 4167 0004 1746     		mov	r7, r2
 4168 0006 1D46     		mov	r5, r3
1241:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 4169              		.loc 1 1241 28 is_stmt 1 view .LVU1190
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_set_inpub_callback: client != NULL", client != NULL);
 4170              		.loc 1 1242 3 view .LVU1191
 4171              		.loc 1 1242 3 view .LVU1192
 4172 0008 0446     		mov	r4, r0
 4173 000a 18B1     		cbz	r0, .L317
 4174              	.LVL469:
 4175              	.L315:
 4176              		.loc 1 1242 3 discriminator 3 view .LVU1193
 4177              		.loc 1 1242 3 discriminator 3 view .LVU1194
1243:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4178              		.loc 1 1243 3 view .LVU1195
 4179              		.loc 1 1243 19 is_stmt 0 view .LVU1196
 4180 000c 2766     		str	r7, [r4, #96]
1244:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->pub_cb = pub_cb;
 4181              		.loc 1 1244 3 is_stmt 1 view .LVU1197
 4182              		.loc 1 1244 18 is_stmt 0 view .LVU1198
 4183 000e 6666     		str	r6, [r4, #100]
1245:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->inpub_arg = arg;
 4184              		.loc 1 1245 3 is_stmt 1 view .LVU1199
 4185              		.loc 1 1245 21 is_stmt 0 view .LVU1200
 4186 0010 E565     		str	r5, [r4, #92]
1246:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 4187              		.loc 1 1246 1 view .LVU1201
 4188 0012 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 4189              	.LVL470:
 4190              	.L317:
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4191              		.loc 1 1242 3 is_stmt 1 discriminator 1 view .LVU1202
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4192              		.loc 1 1242 3 discriminator 1 view .LVU1203
ARM GAS  /tmp/ccBURsTh.s 			page 114


 4193 0014 034B     		ldr	r3, .L318
 4194              	.LVL471:
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4195              		.loc 1 1242 3 is_stmt 0 discriminator 1 view .LVU1204
 4196 0016 40F2DA42 		movw	r2, #1242
 4197              	.LVL472:
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4198              		.loc 1 1242 3 discriminator 1 view .LVU1205
 4199 001a 0349     		ldr	r1, .L318+4
 4200              	.LVL473:
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4201              		.loc 1 1242 3 discriminator 1 view .LVU1206
 4202 001c 0348     		ldr	r0, .L318+8
 4203              	.LVL474:
1242:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->data_cb = data_cb;
 4204              		.loc 1 1242 3 discriminator 1 view .LVU1207
 4205 001e FFF7FEFF 		bl	printf
 4206              	.LVL475:
 4207 0022 F3E7     		b	.L315
 4208              	.L319:
 4209              		.align	2
 4210              	.L318:
 4211 0024 00000000 		.word	.LC0
 4212 0028 00000000 		.word	.LC29
 4213 002c 58000000 		.word	.LC2
 4214              		.cfi_endproc
 4215              	.LFE206:
 4217              		.section	.text.mqtt_client_new,"ax",%progbits
 4218              		.align	1
 4219              		.global	mqtt_client_new
 4220              		.syntax unified
 4221              		.thumb
 4222              		.thumb_func
 4224              	mqtt_client_new:
 4225              	.LFB207:
1247:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1248:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1249:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1250:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Create a new MQTT client instance
1251:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return Pointer to instance on success, NULL otherwise
1252:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1253:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_client_t *
1254:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_client_new(void)
1255:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 4226              		.loc 1 1255 1 is_stmt 1 view -0
 4227              		.cfi_startproc
 4228              		@ args = 0, pretend = 0, frame = 0
 4229              		@ frame_needed = 0, uses_anonymous_args = 0
 4230 0000 08B5     		push	{r3, lr}
 4231              	.LCFI45:
 4232              		.cfi_def_cfa_offset 8
 4233              		.cfi_offset 3, -8
 4234              		.cfi_offset 14, -4
1256:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 4235              		.loc 1 1256 28 view .LVU1209
1257:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return (mqtt_client_t *)mem_calloc(1, sizeof(mqtt_client_t));
 4236              		.loc 1 1257 3 view .LVU1210
ARM GAS  /tmp/ccBURsTh.s 			page 115


 4237              		.loc 1 1257 27 is_stmt 0 view .LVU1211
 4238 0002 4FF4F871 		mov	r1, #496
 4239 0006 0120     		movs	r0, #1
 4240 0008 FFF7FEFF 		bl	mem_calloc
 4241              	.LVL476:
1258:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 4242              		.loc 1 1258 1 view .LVU1212
 4243 000c 08BD     		pop	{r3, pc}
 4244              		.cfi_endproc
 4245              	.LFE207:
 4247              		.section	.text.mqtt_client_free,"ax",%progbits
 4248              		.align	1
 4249              		.global	mqtt_client_free
 4250              		.syntax unified
 4251              		.thumb
 4252              		.thumb_func
 4254              	mqtt_client_free:
 4255              	.LVL477:
 4256              	.LFB208:
1259:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1260:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1261:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1262:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Free MQTT client instance
1263:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client Pointer to instance to be freed
1264:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1265:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** void
1266:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_client_free(mqtt_client_t *client)
1267:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 4257              		.loc 1 1267 1 is_stmt 1 view -0
 4258              		.cfi_startproc
 4259              		@ args = 0, pretend = 0, frame = 0
 4260              		@ frame_needed = 0, uses_anonymous_args = 0
 4261              		.loc 1 1267 1 is_stmt 0 view .LVU1214
 4262 0000 08B5     		push	{r3, lr}
 4263              	.LCFI46:
 4264              		.cfi_def_cfa_offset 8
 4265              		.cfi_offset 3, -8
 4266              		.cfi_offset 14, -4
1268:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mem_free(client);
 4267              		.loc 1 1268 3 is_stmt 1 view .LVU1215
 4268 0002 FFF7FEFF 		bl	mem_free
 4269              	.LVL478:
1269:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 4270              		.loc 1 1269 1 is_stmt 0 view .LVU1216
 4271 0006 08BD     		pop	{r3, pc}
 4272              		.cfi_endproc
 4273              	.LFE208:
 4275              		.section	.rodata.mqtt_client_connect.str1.4,"aMS",%progbits,1
 4276              		.align	2
 4277              	.LC30:
 4278 0000 6D717474 		.ascii	"mqtt_client_connect: client != NULL\000"
 4278      5F636C69 
 4278      656E745F 
 4278      636F6E6E 
 4278      6563743A 
 4279              		.align	2
 4280              	.LC31:
ARM GAS  /tmp/ccBURsTh.s 			page 116


 4281 0024 6D717474 		.ascii	"mqtt_client_connect: ip_addr != NULL\000"
 4281      5F636C69 
 4281      656E745F 
 4281      636F6E6E 
 4281      6563743A 
 4282 0049 000000   		.align	2
 4283              	.LC32:
 4284 004c 6D717474 		.ascii	"mqtt_client_connect: client_info != NULL\000"
 4284      5F636C69 
 4284      656E745F 
 4284      636F6E6E 
 4284      6563743A 
 4285 0075 000000   		.align	2
 4286              	.LC33:
 4287 0078 6D717474 		.ascii	"mqtt_client_connect: client_info->client_id != NULL"
 4287      5F636C69 
 4287      656E745F 
 4287      636F6E6E 
 4287      6563743A 
 4288 00ab 00       		.ascii	"\000"
 4289              		.align	2
 4290              	.LC34:
 4291 00ac 6D717474 		.ascii	"mqtt_client_connect: client_info->will_topic length"
 4291      5F636C69 
 4291      656E745F 
 4291      636F6E6E 
 4291      6563743A 
 4292 00df 206F7665 		.ascii	" overflow\000"
 4292      72666C6F 
 4292      7700
 4293 00e9 000000   		.align	2
 4294              	.LC35:
 4295 00ec 6D717474 		.ascii	"mqtt_client_connect: client_info->will_topic length"
 4295      5F636C69 
 4295      656E745F 
 4295      636F6E6E 
 4295      6563743A 
 4296 011f 206D7573 		.ascii	" must be > 0\000"
 4296      74206265 
 4296      203E2030 
 4296      00
 4297              		.align	2
 4298              	.LC36:
 4299 012c 6D717474 		.ascii	"mqtt_client_connect: client_info->will_msg length o"
 4299      5F636C69 
 4299      656E745F 
 4299      636F6E6E 
 4299      6563743A 
 4300 015f 76657266 		.ascii	"verflow\000"
 4300      6C6F7700 
 4301 0167 00       		.align	2
 4302              	.LC37:
 4303 0168 6D717474 		.ascii	"mqtt_client_connect: remaining_length overflow\000"
 4303      5F636C69 
 4303      656E745F 
 4303      636F6E6E 
 4303      6563743A 
ARM GAS  /tmp/ccBURsTh.s 			page 117


 4304 0197 00       		.align	2
 4305              	.LC38:
 4306 0198 6D717474 		.ascii	"mqtt_client_connect: client_info->client_user lengt"
 4306      5F636C69 
 4306      656E745F 
 4306      636F6E6E 
 4306      6563743A 
 4307 01cb 68206F76 		.ascii	"h overflow\000"
 4307      6572666C 
 4307      6F7700
 4308 01d6 0000     		.align	2
 4309              	.LC39:
 4310 01d8 6D717474 		.ascii	"mqtt_client_connect: client_info->client_user lengt"
 4310      5F636C69 
 4310      656E745F 
 4310      636F6E6E 
 4310      6563743A 
 4311 020b 68206D75 		.ascii	"h must be > 0\000"
 4311      73742062 
 4311      65203E20 
 4311      3000
 4312 0219 000000   		.align	2
 4313              	.LC40:
 4314 021c 6D717474 		.ascii	"mqtt_client_connect: client_info->client_pass lengt"
 4314      5F636C69 
 4314      656E745F 
 4314      636F6E6E 
 4314      6563743A 
 4315 024f 68206F76 		.ascii	"h overflow\000"
 4315      6572666C 
 4315      6F7700
 4316 025a 0000     		.align	2
 4317              	.LC41:
 4318 025c 6D717474 		.ascii	"mqtt_client_connect: client_info->client_pass lengt"
 4318      5F636C69 
 4318      656E745F 
 4318      636F6E6E 
 4318      6563743A 
 4319 028f 68206D75 		.ascii	"h must be > 0\000"
 4319      73742062 
 4319      65203E20 
 4319      3000
 4320 029d 000000   		.align	2
 4321              	.LC42:
 4322 02a0 6D717474 		.ascii	"mqtt_client_connect: client_info->client_id length "
 4322      5F636C69 
 4322      656E745F 
 4322      636F6E6E 
 4322      6563743A 
 4323 02d3 6F766572 		.ascii	"overflow\000"
 4323      666C6F77 
 4323      00
 4324              		.align	2
 4325              	.LC43:
 4326 02dc 4D515454 		.ascii	"MQTT\000"
 4326      00
 4327              		.section	.text.mqtt_client_connect,"ax",%progbits
ARM GAS  /tmp/ccBURsTh.s 			page 118


 4328              		.align	1
 4329              		.global	mqtt_client_connect
 4330              		.syntax unified
 4331              		.thumb
 4332              		.thumb_func
 4334              	mqtt_client_connect:
 4335              	.LVL479:
 4336              	.LFB209:
1270:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1271:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1272:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1273:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Connect to MQTT server
1274:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
1275:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param ip_addr Server IP
1276:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param port Server port
1277:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param cb Connection state change callback
1278:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param arg User supplied argument to connection callback
1279:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client_info Client identification and connection options
1280:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return ERR_OK if successful, @see err_t enum for other results
1281:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1282:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** err_t
1283:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_client_connect(mqtt_client_t *client, const ip_addr_t *ip_addr, u16_t port, mqtt_connection_cb
1284:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****                     const struct mqtt_connect_client_info_t *client_info)
1285:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 4337              		.loc 1 1285 1 is_stmt 1 view -0
 4338              		.cfi_startproc
 4339              		@ args = 8, pretend = 0, frame = 24
 4340              		@ frame_needed = 0, uses_anonymous_args = 0
 4341              		.loc 1 1285 1 is_stmt 0 view .LVU1218
 4342 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 4343              	.LCFI47:
 4344              		.cfi_def_cfa_offset 36
 4345              		.cfi_offset 4, -36
 4346              		.cfi_offset 5, -32
 4347              		.cfi_offset 6, -28
 4348              		.cfi_offset 7, -24
 4349              		.cfi_offset 8, -20
 4350              		.cfi_offset 9, -16
 4351              		.cfi_offset 10, -12
 4352              		.cfi_offset 11, -8
 4353              		.cfi_offset 14, -4
 4354 0004 89B0     		sub	sp, sp, #36
 4355              	.LCFI48:
 4356              		.cfi_def_cfa_offset 72
 4357 0006 0291     		str	r1, [sp, #8]
 4358 0008 0492     		str	r2, [sp, #16]
 4359 000a 1C46     		mov	r4, r3
 4360 000c 139E     		ldr	r6, [sp, #76]
1286:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err_t err;
 4361              		.loc 1 1286 3 is_stmt 1 view .LVU1219
1287:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   size_t len;
 4362              		.loc 1 1287 3 view .LVU1220
1288:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t client_id_length;
 4363              		.loc 1 1288 3 view .LVU1221
1289:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Length is the sum of 2+"MQTT", protocol level, flags and keep alive */
1290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t remaining_length = 2 + 4 + 1 + 1 + 2;
 4364              		.loc 1 1290 3 view .LVU1222
ARM GAS  /tmp/ccBURsTh.s 			page 119


 4365              	.LVL480:
1291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t flags = 0, will_topic_len = 0, will_msg_len = 0;
 4366              		.loc 1 1291 3 view .LVU1223
1292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t client_user_len = 0, client_pass_len = 0;
 4367              		.loc 1 1292 3 view .LVU1224
1293:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1294:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 4368              		.loc 1 1294 28 view .LVU1225
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client != NULL", client != NULL);
 4369              		.loc 1 1295 3 view .LVU1226
 4370              		.loc 1 1295 3 view .LVU1227
 4371 000e 0546     		mov	r5, r0
 4372 0010 0028     		cmp	r0, #0
 4373 0012 00F0B280 		beq	.L357
 4374              	.LVL481:
 4375              	.L325:
 4376              		.loc 1 1295 3 discriminator 3 view .LVU1228
 4377              		.loc 1 1295 3 discriminator 3 view .LVU1229
1296:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4378              		.loc 1 1296 3 view .LVU1230
 4379              		.loc 1 1296 3 view .LVU1231
 4380 0016 029B     		ldr	r3, [sp, #8]
 4381 0018 002B     		cmp	r3, #0
 4382 001a 00F0B680 		beq	.L358
 4383              	.L326:
 4384              		.loc 1 1296 3 discriminator 3 view .LVU1232
 4385              		.loc 1 1296 3 discriminator 3 view .LVU1233
1297:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client_info != NULL", client_info != NULL);
 4386              		.loc 1 1297 3 view .LVU1234
 4387              		.loc 1 1297 3 view .LVU1235
 4388 001e 002E     		cmp	r6, #0
 4389 0020 00F0BB80 		beq	.L359
 4390              	.L327:
 4391              		.loc 1 1297 3 discriminator 3 view .LVU1236
 4392              		.loc 1 1297 3 discriminator 3 view .LVU1237
1298:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client_info->client_id != NULL", client_info->client_id != NULL
 4393              		.loc 1 1298 3 view .LVU1238
 4394              		.loc 1 1298 3 view .LVU1239
 4395 0024 3268     		ldr	r2, [r6]
 4396 0026 002A     		cmp	r2, #0
 4397 0028 00F0BF80 		beq	.L360
 4398              	.L328:
 4399              		.loc 1 1298 3 discriminator 3 view .LVU1240
 4400              		.loc 1 1298 3 discriminator 3 view .LVU1241
1299:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1300:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state != TCP_DISCONNECTED) {
 4401              		.loc 1 1300 3 view .LVU1242
 4402              		.loc 1 1300 13 is_stmt 0 view .LVU1243
 4403 002c AF7A     		ldrb	r7, [r5, #10]	@ zero_extendqisi2
 4404              		.loc 1 1300 6 view .LVU1244
 4405 002e 002F     		cmp	r7, #0
 4406 0030 40F09981 		bne	.L349
1301:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_client_connect: Already connected\n"));
1302:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_ISCONN;
1303:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1304:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1305:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Wipe clean */
ARM GAS  /tmp/ccBURsTh.s 			page 120


1306:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   memset(client, 0, sizeof(mqtt_client_t));
 4407              		.loc 1 1306 3 is_stmt 1 view .LVU1245
 4408 0034 4FF4F872 		mov	r2, #496
 4409 0038 0021     		movs	r1, #0
 4410 003a 2846     		mov	r0, r5
 4411 003c FFF7FEFF 		bl	memset
 4412              	.LVL482:
1307:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->connect_arg = arg;
 4413              		.loc 1 1307 3 view .LVU1246
 4414              		.loc 1 1307 23 is_stmt 0 view .LVU1247
 4415 0040 129B     		ldr	r3, [sp, #72]
 4416 0042 2B61     		str	r3, [r5, #16]
1308:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->connect_cb = cb;
 4417              		.loc 1 1308 3 is_stmt 1 view .LVU1248
 4418              		.loc 1 1308 22 is_stmt 0 view .LVU1249
 4419 0044 6C61     		str	r4, [r5, #20]
1309:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->keep_alive = client_info->keep_alive;
 4420              		.loc 1 1309 3 is_stmt 1 view .LVU1250
 4421              		.loc 1 1309 35 is_stmt 0 view .LVU1251
 4422 0046 B389     		ldrh	r3, [r6, #12]
 4423              		.loc 1 1309 22 view .LVU1252
 4424 0048 6B80     		strh	r3, [r5, #2]	@ movhi
1310:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_init_requests(client->req_list, LWIP_ARRAYSIZE(client->req_list));
 4425              		.loc 1 1310 3 is_stmt 1 view .LVU1253
 4426 004a 0421     		movs	r1, #4
 4427 004c 05F11C00 		add	r0, r5, #28
 4428 0050 FFF7FEFF 		bl	mqtt_init_requests
 4429              	.LVL483:
1311:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1312:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Build connect message */
1313:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client_info->will_topic != NULL && client_info->will_msg != NULL) {
 4430              		.loc 1 1313 3 view .LVU1254
 4431              		.loc 1 1313 18 is_stmt 0 view .LVU1255
 4432 0054 3069     		ldr	r0, [r6, #16]
 4433              		.loc 1 1313 6 view .LVU1256
 4434 0056 0028     		cmp	r0, #0
 4435 0058 00F0D080 		beq	.L350
 4436              		.loc 1 1313 53 discriminator 1 view .LVU1257
 4437 005c D6F81480 		ldr	r8, [r6, #20]
 4438              		.loc 1 1313 39 discriminator 1 view .LVU1258
 4439 0060 B8F1000F 		cmp	r8, #0
 4440 0064 00F0CE80 		beq	.L351
1314:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     flags |= MQTT_CONNECT_FLAG_WILL;
 4441              		.loc 1 1314 5 is_stmt 1 view .LVU1259
 4442              	.LVL484:
1315:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     flags |= (client_info->will_qos & 3) << 3;
 4443              		.loc 1 1315 5 view .LVU1260
 4444              		.loc 1 1315 26 is_stmt 0 view .LVU1261
 4445 0068 337E     		ldrb	r3, [r6, #24]	@ zero_extendqisi2
 4446              		.loc 1 1315 42 view .LVU1262
 4447 006a DB00     		lsls	r3, r3, #3
 4448 006c 03F01803 		and	r3, r3, #24
 4449              		.loc 1 1315 11 view .LVU1263
 4450 0070 43F00407 		orr	r7, r3, #4
 4451              	.LVL485:
1316:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     if (client_info->will_retain) {
 4452              		.loc 1 1316 5 is_stmt 1 view .LVU1264
ARM GAS  /tmp/ccBURsTh.s 			page 121


 4453              		.loc 1 1316 20 is_stmt 0 view .LVU1265
 4454 0074 727E     		ldrb	r2, [r6, #25]	@ zero_extendqisi2
 4455              		.loc 1 1316 8 view .LVU1266
 4456 0076 0AB1     		cbz	r2, .L331
1317:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****       flags |= MQTT_CONNECT_FLAG_WILL_RETAIN;
 4457              		.loc 1 1317 7 is_stmt 1 view .LVU1267
 4458              		.loc 1 1317 13 is_stmt 0 view .LVU1268
 4459 0078 43F02407 		orr	r7, r3, #36
 4460              	.LVL486:
 4461              	.L331:
1318:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     }
1319:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = strlen(client_info->will_topic);
 4462              		.loc 1 1319 5 is_stmt 1 view .LVU1269
 4463              		.loc 1 1319 11 is_stmt 0 view .LVU1270
 4464 007c FFF7FEFF 		bl	strlen
 4465              	.LVL487:
 4466 0080 0446     		mov	r4, r0
 4467              	.LVL488:
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length overflow", len <= 0xFF, return 
 4468              		.loc 1 1320 5 is_stmt 1 view .LVU1271
 4469              		.loc 1 1320 5 view .LVU1272
 4470 0082 FF28     		cmp	r0, #255
 4471 0084 00F29980 		bhi	.L361
 4472              		.loc 1 1320 5 discriminator 2 view .LVU1273
1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4473              		.loc 1 1321 5 view .LVU1274
 4474              		.loc 1 1321 5 view .LVU1275
 4475 0088 0028     		cmp	r0, #0
 4476 008a 00F0A180 		beq	.L362
 4477              		.loc 1 1321 5 discriminator 2 view .LVU1276
1322:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4478              		.loc 1 1322 5 view .LVU1277
 4479              		.loc 1 1322 20 is_stmt 0 view .LVU1278
 4480 008e C3B2     		uxtb	r3, r0
 4481 0090 0693     		str	r3, [sp, #24]
 4482              	.LVL489:
1323:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = strlen(client_info->will_msg);
 4483              		.loc 1 1323 5 is_stmt 1 view .LVU1279
 4484              		.loc 1 1323 11 is_stmt 0 view .LVU1280
 4485 0092 4046     		mov	r0, r8
 4486              	.LVL490:
 4487              		.loc 1 1323 11 view .LVU1281
 4488 0094 FFF7FEFF 		bl	strlen
 4489              	.LVL491:
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_msg length overflow", len <= 0xFF, return ER
 4490              		.loc 1 1324 5 is_stmt 1 view .LVU1282
 4491              		.loc 1 1324 5 view .LVU1283
 4492 0098 FF28     		cmp	r0, #255
 4493 009a 00F2A480 		bhi	.L363
 4494              		.loc 1 1324 5 discriminator 2 view .LVU1284
1325:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4495              		.loc 1 1325 5 view .LVU1285
 4496              		.loc 1 1325 18 is_stmt 0 view .LVU1286
 4497 009e C3B2     		uxtb	r3, r0
 4498 00a0 0793     		str	r3, [sp, #28]
 4499              	.LVL492:
1326:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = remaining_length + 2 + will_topic_len + 2 + will_msg_len;
ARM GAS  /tmp/ccBURsTh.s 			page 122


 4500              		.loc 1 1326 5 is_stmt 1 view .LVU1287
 4501              		.loc 1 1326 32 is_stmt 0 view .LVU1288
 4502 00a2 E4B2     		uxtb	r4, r4
 4503              		.loc 1 1326 49 view .LVU1289
 4504 00a4 0E34     		adds	r4, r4, #14
 4505              		.loc 1 1326 53 view .LVU1290
 4506 00a6 1C44     		add	r4, r4, r3
 4507              	.LVL493:
1327:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: remaining_length overflow", len <= 0xFFFF, return ERR_VAL);
 4508              		.loc 1 1327 5 is_stmt 1 view .LVU1291
 4509              		.loc 1 1327 5 view .LVU1292
 4510              		.loc 1 1327 5 discriminator 2 view .LVU1293
1328:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4511              		.loc 1 1328 5 view .LVU1294
 4512              		.loc 1 1328 22 is_stmt 0 view .LVU1295
 4513 00a8 A4B2     		uxth	r4, r4
 4514              	.LVL494:
 4515              	.L330:
1329:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1330:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client_info->client_user != NULL) {
 4516              		.loc 1 1330 3 is_stmt 1 view .LVU1296
 4517              		.loc 1 1330 18 is_stmt 0 view .LVU1297
 4518 00aa 7068     		ldr	r0, [r6, #4]
 4519              		.loc 1 1330 6 view .LVU1298
 4520 00ac 0028     		cmp	r0, #0
 4521 00ae 00F0CE80 		beq	.L352
1331:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     flags |= MQTT_CONNECT_FLAG_USERNAME;
 4522              		.loc 1 1331 5 is_stmt 1 view .LVU1299
 4523              		.loc 1 1331 11 is_stmt 0 view .LVU1300
 4524 00b2 47F08007 		orr	r7, r7, #128
 4525              	.LVL495:
1332:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = strlen(client_info->client_user);
 4526              		.loc 1 1332 5 is_stmt 1 view .LVU1301
 4527              		.loc 1 1332 11 is_stmt 0 view .LVU1302
 4528 00b6 FFF7FEFF 		bl	strlen
 4529              	.LVL496:
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length overflow", len <= 0xFFFF, retu
 4530              		.loc 1 1333 5 is_stmt 1 view .LVU1303
 4531              		.loc 1 1333 5 view .LVU1304
 4532 00ba B0F5803F 		cmp	r0, #65536
 4533 00be 80F0A580 		bcs	.L364
 4534              		.loc 1 1333 5 discriminator 2 view .LVU1305
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4535              		.loc 1 1334 5 view .LVU1306
 4536              		.loc 1 1334 5 view .LVU1307
 4537 00c2 0028     		cmp	r0, #0
 4538 00c4 00F0AD80 		beq	.L365
 4539              		.loc 1 1334 5 discriminator 2 view .LVU1308
1335:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4540              		.loc 1 1335 5 view .LVU1309
 4541              		.loc 1 1335 21 is_stmt 0 view .LVU1310
 4542 00c8 1FFA80F9 		uxth	r9, r0
 4543              	.LVL497:
1336:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = remaining_length + 2 + client_user_len;
 4544              		.loc 1 1336 5 is_stmt 1 view .LVU1311
 4545              		.loc 1 1336 28 is_stmt 0 view .LVU1312
 4546 00cc 0234     		adds	r4, r4, #2
ARM GAS  /tmp/ccBURsTh.s 			page 123


 4547              	.LVL498:
 4548              		.loc 1 1336 32 view .LVU1313
 4549 00ce 4C44     		add	r4, r4, r9
 4550              	.LVL499:
1337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: remaining_length overflow", len <= 0xFFFF, return ERR_VAL);
 4551              		.loc 1 1337 5 is_stmt 1 view .LVU1314
 4552              		.loc 1 1337 5 view .LVU1315
 4553 00d0 B4F5803F 		cmp	r4, #65536
 4554 00d4 80F0B080 		bcs	.L366
 4555              		.loc 1 1337 5 discriminator 2 view .LVU1316
1338:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4556              		.loc 1 1338 5 view .LVU1317
 4557              		.loc 1 1338 22 is_stmt 0 view .LVU1318
 4558 00d8 A4B2     		uxth	r4, r4
 4559              	.LVL500:
 4560              	.L336:
1339:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1340:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client_info->client_pass != NULL) {
 4561              		.loc 1 1340 3 is_stmt 1 view .LVU1319
 4562              		.loc 1 1340 18 is_stmt 0 view .LVU1320
 4563 00da B068     		ldr	r0, [r6, #8]
 4564              		.loc 1 1340 6 view .LVU1321
 4565 00dc 0028     		cmp	r0, #0
 4566 00de 00F0DA80 		beq	.L353
1341:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     flags |= MQTT_CONNECT_FLAG_PASSWORD;
 4567              		.loc 1 1341 5 is_stmt 1 view .LVU1322
 4568              		.loc 1 1341 11 is_stmt 0 view .LVU1323
 4569 00e2 47F04007 		orr	r7, r7, #64
 4570              	.LVL501:
1342:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = strlen(client_info->client_pass);
 4571              		.loc 1 1342 5 is_stmt 1 view .LVU1324
 4572              		.loc 1 1342 11 is_stmt 0 view .LVU1325
 4573 00e6 FFF7FEFF 		bl	strlen
 4574              	.LVL502:
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length overflow", len <= 0xFFFF, retu
 4575              		.loc 1 1343 5 is_stmt 1 view .LVU1326
 4576              		.loc 1 1343 5 view .LVU1327
 4577 00ea B0F5803F 		cmp	r0, #65536
 4578 00ee 80F0B180 		bcs	.L367
 4579              		.loc 1 1343 5 discriminator 2 view .LVU1328
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4580              		.loc 1 1344 5 view .LVU1329
 4581              		.loc 1 1344 5 view .LVU1330
 4582 00f2 0028     		cmp	r0, #0
 4583 00f4 00F0B980 		beq	.L368
 4584              		.loc 1 1344 5 discriminator 2 view .LVU1331
1345:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
 4585              		.loc 1 1345 5 view .LVU1332
 4586              		.loc 1 1345 21 is_stmt 0 view .LVU1333
 4587 00f8 1FFA80FB 		uxth	fp, r0
 4588              	.LVL503:
1346:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     len = remaining_length + 2 + client_pass_len;
 4589              		.loc 1 1346 5 is_stmt 1 view .LVU1334
 4590              		.loc 1 1346 28 is_stmt 0 view .LVU1335
 4591 00fc 0234     		adds	r4, r4, #2
 4592              	.LVL504:
 4593              		.loc 1 1346 32 view .LVU1336
ARM GAS  /tmp/ccBURsTh.s 			page 124


 4594 00fe 5C44     		add	r4, r4, fp
 4595              	.LVL505:
1347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: remaining_length overflow", len <= 0xFFFF, return ERR_VAL);
 4596              		.loc 1 1347 5 is_stmt 1 view .LVU1337
 4597              		.loc 1 1347 5 view .LVU1338
 4598 0100 B4F5803F 		cmp	r4, #65536
 4599 0104 80F0BC80 		bcs	.L369
 4600              		.loc 1 1347 5 discriminator 2 view .LVU1339
1348:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4601              		.loc 1 1348 5 view .LVU1340
 4602              		.loc 1 1348 22 is_stmt 0 view .LVU1341
 4603 0108 A4B2     		uxth	r4, r4
 4604              	.LVL506:
 4605              	.L340:
1349:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1350:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1351:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Don't complicate things, always connect using clean session */
1352:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   flags |= MQTT_CONNECT_FLAG_CLEAN_SESSION;
 4606              		.loc 1 1352 3 is_stmt 1 view .LVU1342
 4607              		.loc 1 1352 9 is_stmt 0 view .LVU1343
 4608 010a 47F00203 		orr	r3, r7, #2
 4609 010e 0593     		str	r3, [sp, #20]
 4610              	.LVL507:
1353:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1354:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   len = strlen(client_info->client_id);
 4611              		.loc 1 1354 3 is_stmt 1 view .LVU1344
 4612              		.loc 1 1354 9 is_stmt 0 view .LVU1345
 4613 0110 3068     		ldr	r0, [r6]
 4614 0112 FFF7FEFF 		bl	strlen
 4615              	.LVL508:
1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_client_connect: client_info->client_id length overflow", len <= 0xFFFF, return E
 4616              		.loc 1 1355 3 is_stmt 1 view .LVU1346
 4617              		.loc 1 1355 3 view .LVU1347
 4618 0116 B0F5803F 		cmp	r0, #65536
 4619 011a 80F0BF80 		bcs	.L370
 4620              		.loc 1 1355 3 discriminator 2 view .LVU1348
1356:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4621              		.loc 1 1356 3 view .LVU1349
 4622              		.loc 1 1356 20 is_stmt 0 view .LVU1350
 4623 011e 1FFA80F8 		uxth	r8, r0
 4624              	.LVL509:
1357:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   len = remaining_length + 2 + client_id_length;
 4625              		.loc 1 1357 3 is_stmt 1 view .LVU1351
 4626              		.loc 1 1357 26 is_stmt 0 view .LVU1352
 4627 0122 0234     		adds	r4, r4, #2
 4628              	.LVL510:
 4629              		.loc 1 1357 30 view .LVU1353
 4630 0124 4444     		add	r4, r4, r8
 4631              	.LVL511:
1358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ERROR("mqtt_client_connect: remaining_length overflow", len <= 0xFFFF, return ERR_VAL);
 4632              		.loc 1 1358 3 is_stmt 1 view .LVU1354
 4633              		.loc 1 1358 3 view .LVU1355
 4634 0126 B4F5803F 		cmp	r4, #65536
 4635 012a 80F0C280 		bcs	.L371
 4636              		.loc 1 1358 3 discriminator 2 view .LVU1356
1359:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)len;
 4637              		.loc 1 1359 3 view .LVU1357
ARM GAS  /tmp/ccBURsTh.s 			page 125


 4638              		.loc 1 1359 20 is_stmt 0 view .LVU1358
 4639 012e A4B2     		uxth	r4, r4
 4640              	.LVL512:
1360:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1361:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (mqtt_output_check_space(&client->output, remaining_length) == 0) {
 4641              		.loc 1 1361 3 is_stmt 1 view .LVU1359
 4642              		.loc 1 1361 7 is_stmt 0 view .LVU1360
 4643 0130 05F1EC0A 		add	r10, r5, #236
 4644 0134 2146     		mov	r1, r4
 4645 0136 5046     		mov	r0, r10
 4646 0138 FFF7FEFF 		bl	mqtt_output_check_space
 4647              	.LVL513:
 4648              		.loc 1 1361 6 discriminator 1 view .LVU1361
 4649 013c 0028     		cmp	r0, #0
 4650 013e 00F01681 		beq	.L354
1362:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
1363:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1364:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1365:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #if LWIP_ALTCP && LWIP_ALTCP_TLS
1366:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client_info->tls_config) {
1367:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->conn = altcp_tls_new(client_info->tls_config, IP_GET_TYPE(ip_addr));
1368:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   } else
1369:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** #endif
1370:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   {
1371:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->conn = altcp_tcp_new_ip_type(IP_GET_TYPE(ip_addr));
 4651              		.loc 1 1371 5 is_stmt 1 view .LVU1362
 4652              		.loc 1 1371 20 is_stmt 0 view .LVU1363
 4653 0142 0020     		movs	r0, #0
 4654 0144 FFF7FEFF 		bl	tcp_new_ip_type
 4655              	.LVL514:
 4656              		.loc 1 1371 18 discriminator 1 view .LVU1364
 4657 0148 E860     		str	r0, [r5, #12]
1372:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1373:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn == NULL) {
 4658              		.loc 1 1373 3 is_stmt 1 view .LVU1365
 4659              		.loc 1 1373 6 is_stmt 0 view .LVU1366
 4660 014a 0028     		cmp	r0, #0
 4661 014c 00F01381 		beq	.L355
1374:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     return ERR_MEM;
1375:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1376:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1377:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set arg pointer for callbacks */
1378:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_arg(client->conn, client);
 4662              		.loc 1 1378 3 is_stmt 1 view .LVU1367
 4663 0150 2946     		mov	r1, r5
 4664 0152 FFF7FEFF 		bl	tcp_arg
 4665              	.LVL515:
1379:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Any local address, pick random local port number */
1380:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err = altcp_bind(client->conn, IP_ADDR_ANY, 0);
 4666              		.loc 1 1380 3 view .LVU1368
 4667              		.loc 1 1380 9 is_stmt 0 view .LVU1369
 4668 0156 0022     		movs	r2, #0
 4669 0158 8949     		ldr	r1, .L375
 4670 015a E868     		ldr	r0, [r5, #12]
 4671 015c FFF7FEFF 		bl	tcp_bind
 4672              	.LVL516:
1381:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err != ERR_OK) {
ARM GAS  /tmp/ccBURsTh.s 			page 126


 4673              		.loc 1 1381 3 is_stmt 1 view .LVU1370
 4674              		.loc 1 1381 6 is_stmt 0 view .LVU1371
 4675 0160 0390     		str	r0, [sp, #12]
 4676 0162 0028     		cmp	r0, #0
 4677 0164 00F0B080 		beq	.L372
 4678              	.L346:
 4679              	.LVL517:
1382:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_WARN, ("mqtt_client_connect: Error binding to local ip/port, %d\n", err)
1383:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     goto tcp_fail;
1384:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1385:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_client_connect: Connecting to host: %s at port:%"U16_F"\n", 
1386:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1387:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Connect to server */
1388:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   err = altcp_connect(client->conn, ip_addr, port, mqtt_tcp_connect_cb);
1389:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err != ERR_OK) {
1390:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_client_connect: Error connecting to remote ip/port, %d\n",
1391:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     goto tcp_fail;
1392:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1393:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Set error callback */
1394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_err(client->conn, mqtt_tcp_err_cb);
1395:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->conn_state = TCP_CONNECTING;
1396:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1397:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append fixed header */
1398:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_fixed_header(&client->output, MQTT_MSG_TYPE_CONNECT, 0, 0, 0, remaining_length
1399:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append Protocol string */
1400:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_string(&client->output, "MQTT", 4);
1401:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append Protocol level */
1402:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_u8(&client->output, 4);
1403:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append connect flags */
1404:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_u8(&client->output, flags);
1405:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append keep-alive */
1406:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_u16(&client->output, client_info->keep_alive);
1407:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append client id */
1408:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   mqtt_output_append_string(&client->output, client_info->client_id, client_id_length);
1409:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append will message if used */
1410:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((flags & MQTT_CONNECT_FLAG_WILL) != 0) {
1411:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->will_topic, will_topic_len);
1412:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->will_msg, will_msg_len);
1413:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1414:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append user name if given */
1415:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((flags & MQTT_CONNECT_FLAG_USERNAME) != 0) {
1416:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->client_user, client_user_len);
1417:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1418:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append password if given */
1419:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if ((flags & MQTT_CONNECT_FLAG_PASSWORD) != 0) {
1420:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->client_pass, client_pass_len);
1421:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1422:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return ERR_OK;
1423:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1424:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** tcp_fail:
1425:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   altcp_abort(client->conn);
 4680              		.loc 1 1425 3 is_stmt 1 view .LVU1372
 4681 0168 E868     		ldr	r0, [r5, #12]
 4682 016a FFF7FEFF 		bl	tcp_abort
 4683              	.LVL518:
1426:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->conn = NULL;
 4684              		.loc 1 1426 3 view .LVU1373
ARM GAS  /tmp/ccBURsTh.s 			page 127


 4685              		.loc 1 1426 16 is_stmt 0 view .LVU1374
 4686 016e 0023     		movs	r3, #0
 4687 0170 EB60     		str	r3, [r5, #12]
1427:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return err;
 4688              		.loc 1 1427 3 is_stmt 1 view .LVU1375
 4689              	.LVL519:
 4690              	.L329:
1428:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 4691              		.loc 1 1428 1 is_stmt 0 view .LVU1376
 4692 0172 0398     		ldr	r0, [sp, #12]
 4693 0174 09B0     		add	sp, sp, #36
 4694              	.LCFI49:
 4695              		.cfi_remember_state
 4696              		.cfi_def_cfa_offset 36
 4697              		@ sp needed
 4698 0176 BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 4699              	.LVL520:
 4700              	.L357:
 4701              	.LCFI50:
 4702              		.cfi_restore_state
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4703              		.loc 1 1295 3 is_stmt 1 discriminator 1 view .LVU1377
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4704              		.loc 1 1295 3 discriminator 1 view .LVU1378
 4705 017a 824B     		ldr	r3, .L375+4
 4706              	.LVL521:
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4707              		.loc 1 1295 3 is_stmt 0 discriminator 1 view .LVU1379
 4708 017c 40F20F52 		movw	r2, #1295
 4709              	.LVL522:
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4710              		.loc 1 1295 3 discriminator 1 view .LVU1380
 4711 0180 8149     		ldr	r1, .L375+8
 4712              	.LVL523:
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4713              		.loc 1 1295 3 discriminator 1 view .LVU1381
 4714 0182 8248     		ldr	r0, .L375+12
 4715              	.LVL524:
1295:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: ip_addr != NULL", ip_addr != NULL);
 4716              		.loc 1 1295 3 discriminator 1 view .LVU1382
 4717 0184 FFF7FEFF 		bl	printf
 4718              	.LVL525:
 4719 0188 45E7     		b	.L325
 4720              	.L358:
1296:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client_info != NULL", client_info != NULL);
 4721              		.loc 1 1296 3 is_stmt 1 discriminator 1 view .LVU1383
1296:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client_info != NULL", client_info != NULL);
 4722              		.loc 1 1296 3 discriminator 1 view .LVU1384
 4723 018a 7E4B     		ldr	r3, .L375+4
 4724 018c 4FF4A262 		mov	r2, #1296
 4725 0190 7F49     		ldr	r1, .L375+16
 4726 0192 7E48     		ldr	r0, .L375+12
 4727 0194 FFF7FEFF 		bl	printf
 4728              	.LVL526:
 4729 0198 41E7     		b	.L326
 4730              	.L359:
1297:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client_info->client_id != NULL", client_info->client_id != NULL
ARM GAS  /tmp/ccBURsTh.s 			page 128


 4731              		.loc 1 1297 3 discriminator 1 view .LVU1385
1297:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_connect: client_info->client_id != NULL", client_info->client_id != NULL
 4732              		.loc 1 1297 3 discriminator 1 view .LVU1386
 4733 019a 7A4B     		ldr	r3, .L375+4
 4734 019c 40F21152 		movw	r2, #1297
 4735 01a0 7C49     		ldr	r1, .L375+20
 4736 01a2 7A48     		ldr	r0, .L375+12
 4737 01a4 FFF7FEFF 		bl	printf
 4738              	.LVL527:
 4739 01a8 3CE7     		b	.L327
 4740              	.L360:
1298:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4741              		.loc 1 1298 3 discriminator 1 view .LVU1387
1298:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4742              		.loc 1 1298 3 discriminator 1 view .LVU1388
 4743 01aa 764B     		ldr	r3, .L375+4
 4744 01ac 40F21252 		movw	r2, #1298
 4745 01b0 7949     		ldr	r1, .L375+24
 4746 01b2 7648     		ldr	r0, .L375+12
 4747 01b4 FFF7FEFF 		bl	printf
 4748              	.LVL528:
 4749 01b8 38E7     		b	.L328
 4750              	.LVL529:
 4751              	.L361:
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4752              		.loc 1 1320 5 discriminator 1 view .LVU1389
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4753              		.loc 1 1320 5 discriminator 1 view .LVU1390
 4754 01ba 724B     		ldr	r3, .L375+4
 4755 01bc 4FF4A562 		mov	r2, #1320
 4756 01c0 7649     		ldr	r1, .L375+28
 4757 01c2 7248     		ldr	r0, .L375+12
 4758              	.LVL530:
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4759              		.loc 1 1320 5 is_stmt 0 discriminator 1 view .LVU1391
 4760 01c4 FFF7FEFF 		bl	printf
 4761              	.LVL531:
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4762              		.loc 1 1320 5 is_stmt 1 discriminator 1 view .LVU1392
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4763              		.loc 1 1320 5 discriminator 1 view .LVU1393
 4764 01c8 6FF00503 		mvn	r3, #5
 4765 01cc 0393     		str	r3, [sp, #12]
1320:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->will_topic length must be > 0", len > 0, return E
 4766              		.loc 1 1320 5 is_stmt 0 view .LVU1394
 4767 01ce D0E7     		b	.L329
 4768              	.LVL532:
 4769              	.L362:
1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4770              		.loc 1 1321 5 is_stmt 1 discriminator 1 view .LVU1395
1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4771              		.loc 1 1321 5 discriminator 1 view .LVU1396
 4772 01d0 6C4B     		ldr	r3, .L375+4
 4773 01d2 40F22952 		movw	r2, #1321
 4774 01d6 7249     		ldr	r1, .L375+32
 4775 01d8 6C48     		ldr	r0, .L375+12
 4776              	.LVL533:
ARM GAS  /tmp/ccBURsTh.s 			page 129


1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4777              		.loc 1 1321 5 is_stmt 0 discriminator 1 view .LVU1397
 4778 01da FFF7FEFF 		bl	printf
 4779              	.LVL534:
1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4780              		.loc 1 1321 5 is_stmt 1 discriminator 1 view .LVU1398
1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4781              		.loc 1 1321 5 discriminator 1 view .LVU1399
 4782 01de 6FF00503 		mvn	r3, #5
 4783 01e2 0393     		str	r3, [sp, #12]
1321:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_topic_len = (u8_t)len;
 4784              		.loc 1 1321 5 is_stmt 0 view .LVU1400
 4785 01e4 C5E7     		b	.L329
 4786              	.LVL535:
 4787              	.L363:
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4788              		.loc 1 1324 5 is_stmt 1 discriminator 1 view .LVU1401
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4789              		.loc 1 1324 5 discriminator 1 view .LVU1402
 4790 01e6 674B     		ldr	r3, .L375+4
 4791 01e8 40F22C52 		movw	r2, #1324
 4792 01ec 6D49     		ldr	r1, .L375+36
 4793 01ee 6748     		ldr	r0, .L375+12
 4794              	.LVL536:
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4795              		.loc 1 1324 5 is_stmt 0 discriminator 1 view .LVU1403
 4796 01f0 FFF7FEFF 		bl	printf
 4797              	.LVL537:
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4798              		.loc 1 1324 5 is_stmt 1 discriminator 1 view .LVU1404
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4799              		.loc 1 1324 5 discriminator 1 view .LVU1405
 4800 01f4 6FF00503 		mvn	r3, #5
 4801 01f8 0393     		str	r3, [sp, #12]
1324:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     will_msg_len = (u8_t)len;
 4802              		.loc 1 1324 5 is_stmt 0 view .LVU1406
 4803 01fa BAE7     		b	.L329
 4804              	.LVL538:
 4805              	.L350:
1291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t client_user_len = 0, client_pass_len = 0;
 4806              		.loc 1 1291 39 view .LVU1407
 4807 01fc 0797     		str	r7, [sp, #28]
1291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t client_user_len = 0, client_pass_len = 0;
 4808              		.loc 1 1291 19 view .LVU1408
 4809 01fe 0697     		str	r7, [sp, #24]
1290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t flags = 0, will_topic_len = 0, will_msg_len = 0;
 4810              		.loc 1 1290 9 view .LVU1409
 4811 0200 0A24     		movs	r4, #10
 4812              	.LVL539:
1290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t flags = 0, will_topic_len = 0, will_msg_len = 0;
 4813              		.loc 1 1290 9 view .LVU1410
 4814 0202 52E7     		b	.L330
 4815              	.LVL540:
 4816              	.L351:
1291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t client_user_len = 0, client_pass_len = 0;
 4817              		.loc 1 1291 39 view .LVU1411
 4818 0204 0797     		str	r7, [sp, #28]
ARM GAS  /tmp/ccBURsTh.s 			page 130


1291:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u16_t client_user_len = 0, client_pass_len = 0;
 4819              		.loc 1 1291 19 view .LVU1412
 4820 0206 0697     		str	r7, [sp, #24]
1290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t flags = 0, will_topic_len = 0, will_msg_len = 0;
 4821              		.loc 1 1290 9 view .LVU1413
 4822 0208 0A24     		movs	r4, #10
 4823              	.LVL541:
1290:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   u8_t flags = 0, will_topic_len = 0, will_msg_len = 0;
 4824              		.loc 1 1290 9 view .LVU1414
 4825 020a 4EE7     		b	.L330
 4826              	.LVL542:
 4827              	.L364:
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4828              		.loc 1 1333 5 is_stmt 1 discriminator 1 view .LVU1415
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4829              		.loc 1 1333 5 discriminator 1 view .LVU1416
 4830 020c 5D4B     		ldr	r3, .L375+4
 4831 020e 40F23552 		movw	r2, #1333
 4832 0212 6549     		ldr	r1, .L375+40
 4833 0214 5D48     		ldr	r0, .L375+12
 4834              	.LVL543:
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4835              		.loc 1 1333 5 is_stmt 0 discriminator 1 view .LVU1417
 4836 0216 FFF7FEFF 		bl	printf
 4837              	.LVL544:
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4838              		.loc 1 1333 5 is_stmt 1 discriminator 1 view .LVU1418
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4839              		.loc 1 1333 5 discriminator 1 view .LVU1419
 4840 021a 6FF00503 		mvn	r3, #5
 4841 021e 0393     		str	r3, [sp, #12]
1333:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_user length must be > 0", len > 0, return 
 4842              		.loc 1 1333 5 is_stmt 0 view .LVU1420
 4843 0220 A7E7     		b	.L329
 4844              	.LVL545:
 4845              	.L365:
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4846              		.loc 1 1334 5 is_stmt 1 discriminator 1 view .LVU1421
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4847              		.loc 1 1334 5 discriminator 1 view .LVU1422
 4848 0222 584B     		ldr	r3, .L375+4
 4849 0224 40F23652 		movw	r2, #1334
 4850 0228 6049     		ldr	r1, .L375+44
 4851 022a 5848     		ldr	r0, .L375+12
 4852              	.LVL546:
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4853              		.loc 1 1334 5 is_stmt 0 discriminator 1 view .LVU1423
 4854 022c FFF7FEFF 		bl	printf
 4855              	.LVL547:
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4856              		.loc 1 1334 5 is_stmt 1 discriminator 1 view .LVU1424
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4857              		.loc 1 1334 5 discriminator 1 view .LVU1425
 4858 0230 6FF00503 		mvn	r3, #5
 4859 0234 0393     		str	r3, [sp, #12]
1334:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_user_len = (u16_t)len;
 4860              		.loc 1 1334 5 is_stmt 0 view .LVU1426
ARM GAS  /tmp/ccBURsTh.s 			page 131


 4861 0236 9CE7     		b	.L329
 4862              	.LVL548:
 4863              	.L366:
1337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4864              		.loc 1 1337 5 is_stmt 1 discriminator 1 view .LVU1427
1337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4865              		.loc 1 1337 5 discriminator 1 view .LVU1428
 4866 0238 524B     		ldr	r3, .L375+4
 4867 023a 40F23952 		movw	r2, #1337
 4868 023e 5C49     		ldr	r1, .L375+48
 4869 0240 5248     		ldr	r0, .L375+12
 4870 0242 FFF7FEFF 		bl	printf
 4871              	.LVL549:
1337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4872              		.loc 1 1337 5 discriminator 1 view .LVU1429
1337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4873              		.loc 1 1337 5 discriminator 1 view .LVU1430
 4874 0246 6FF00503 		mvn	r3, #5
 4875 024a 0393     		str	r3, [sp, #12]
1337:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4876              		.loc 1 1337 5 is_stmt 0 view .LVU1431
 4877 024c 91E7     		b	.L329
 4878              	.LVL550:
 4879              	.L352:
1292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4880              		.loc 1 1292 9 view .LVU1432
 4881 024e 4FF00009 		mov	r9, #0
 4882 0252 42E7     		b	.L336
 4883              	.LVL551:
 4884              	.L367:
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4885              		.loc 1 1343 5 is_stmt 1 discriminator 1 view .LVU1433
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4886              		.loc 1 1343 5 discriminator 1 view .LVU1434
 4887 0254 4B4B     		ldr	r3, .L375+4
 4888 0256 40F23F52 		movw	r2, #1343
 4889 025a 5649     		ldr	r1, .L375+52
 4890 025c 4B48     		ldr	r0, .L375+12
 4891              	.LVL552:
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4892              		.loc 1 1343 5 is_stmt 0 discriminator 1 view .LVU1435
 4893 025e FFF7FEFF 		bl	printf
 4894              	.LVL553:
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4895              		.loc 1 1343 5 is_stmt 1 discriminator 1 view .LVU1436
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4896              		.loc 1 1343 5 discriminator 1 view .LVU1437
 4897 0262 6FF00503 		mvn	r3, #5
 4898 0266 0393     		str	r3, [sp, #12]
1343:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_ERROR("mqtt_client_connect: client_info->client_pass length must be > 0", len > 0, return 
 4899              		.loc 1 1343 5 is_stmt 0 view .LVU1438
 4900 0268 83E7     		b	.L329
 4901              	.LVL554:
 4902              	.L368:
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
 4903              		.loc 1 1344 5 is_stmt 1 discriminator 1 view .LVU1439
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
ARM GAS  /tmp/ccBURsTh.s 			page 132


 4904              		.loc 1 1344 5 discriminator 1 view .LVU1440
 4905 026a 464B     		ldr	r3, .L375+4
 4906 026c 4FF4A862 		mov	r2, #1344
 4907 0270 5149     		ldr	r1, .L375+56
 4908 0272 4648     		ldr	r0, .L375+12
 4909              	.LVL555:
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
 4910              		.loc 1 1344 5 is_stmt 0 discriminator 1 view .LVU1441
 4911 0274 FFF7FEFF 		bl	printf
 4912              	.LVL556:
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
 4913              		.loc 1 1344 5 is_stmt 1 discriminator 1 view .LVU1442
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
 4914              		.loc 1 1344 5 discriminator 1 view .LVU1443
 4915 0278 6FF00503 		mvn	r3, #5
 4916 027c 0393     		str	r3, [sp, #12]
1344:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client_pass_len = (u16_t)len;
 4917              		.loc 1 1344 5 is_stmt 0 view .LVU1444
 4918 027e 78E7     		b	.L329
 4919              	.LVL557:
 4920              	.L369:
1347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4921              		.loc 1 1347 5 is_stmt 1 discriminator 1 view .LVU1445
1347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4922              		.loc 1 1347 5 discriminator 1 view .LVU1446
 4923 0280 404B     		ldr	r3, .L375+4
 4924 0282 40F24352 		movw	r2, #1347
 4925 0286 4A49     		ldr	r1, .L375+48
 4926 0288 4048     		ldr	r0, .L375+12
 4927 028a FFF7FEFF 		bl	printf
 4928              	.LVL558:
1347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4929              		.loc 1 1347 5 discriminator 1 view .LVU1447
1347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4930              		.loc 1 1347 5 discriminator 1 view .LVU1448
 4931 028e 6FF00503 		mvn	r3, #5
 4932 0292 0393     		str	r3, [sp, #12]
1347:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     remaining_length = (u16_t)len;
 4933              		.loc 1 1347 5 is_stmt 0 view .LVU1449
 4934 0294 6DE7     		b	.L329
 4935              	.LVL559:
 4936              	.L353:
1292:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4937              		.loc 1 1292 30 view .LVU1450
 4938 0296 4FF0000B 		mov	fp, #0
 4939 029a 36E7     		b	.L340
 4940              	.LVL560:
 4941              	.L370:
1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4942              		.loc 1 1355 3 is_stmt 1 discriminator 1 view .LVU1451
1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4943              		.loc 1 1355 3 discriminator 1 view .LVU1452
 4944 029c 394B     		ldr	r3, .L375+4
 4945 029e 40F24B52 		movw	r2, #1355
 4946 02a2 4649     		ldr	r1, .L375+60
 4947 02a4 3948     		ldr	r0, .L375+12
 4948              	.LVL561:
ARM GAS  /tmp/ccBURsTh.s 			page 133


1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4949              		.loc 1 1355 3 is_stmt 0 discriminator 1 view .LVU1453
 4950 02a6 FFF7FEFF 		bl	printf
 4951              	.LVL562:
1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4952              		.loc 1 1355 3 is_stmt 1 discriminator 1 view .LVU1454
1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4953              		.loc 1 1355 3 discriminator 1 view .LVU1455
 4954 02aa 6FF00503 		mvn	r3, #5
 4955 02ae 0393     		str	r3, [sp, #12]
1355:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client_id_length = (u16_t)len;
 4956              		.loc 1 1355 3 is_stmt 0 view .LVU1456
 4957 02b0 5FE7     		b	.L329
 4958              	.LVL563:
 4959              	.L371:
1358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)len;
 4960              		.loc 1 1358 3 is_stmt 1 discriminator 1 view .LVU1457
1358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)len;
 4961              		.loc 1 1358 3 discriminator 1 view .LVU1458
 4962 02b2 344B     		ldr	r3, .L375+4
 4963 02b4 40F24E52 		movw	r2, #1358
 4964 02b8 3D49     		ldr	r1, .L375+48
 4965 02ba 3448     		ldr	r0, .L375+12
 4966 02bc FFF7FEFF 		bl	printf
 4967              	.LVL564:
1358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)len;
 4968              		.loc 1 1358 3 discriminator 1 view .LVU1459
1358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)len;
 4969              		.loc 1 1358 3 discriminator 1 view .LVU1460
 4970 02c0 6FF00503 		mvn	r3, #5
 4971 02c4 0393     		str	r3, [sp, #12]
1358:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   remaining_length = (u16_t)len;
 4972              		.loc 1 1358 3 is_stmt 0 view .LVU1461
 4973 02c6 54E7     		b	.L329
 4974              	.LVL565:
 4975              	.L372:
1385:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4976              		.loc 1 1385 128 is_stmt 1 view .LVU1462
1388:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err != ERR_OK) {
 4977              		.loc 1 1388 3 view .LVU1463
1388:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err != ERR_OK) {
 4978              		.loc 1 1388 9 is_stmt 0 view .LVU1464
 4979 02c8 3D4B     		ldr	r3, .L375+64
 4980 02ca 049A     		ldr	r2, [sp, #16]
 4981 02cc 0299     		ldr	r1, [sp, #8]
 4982 02ce E868     		ldr	r0, [r5, #12]
 4983              	.LVL566:
1388:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (err != ERR_OK) {
 4984              		.loc 1 1388 9 view .LVU1465
 4985 02d0 FFF7FEFF 		bl	tcp_connect
 4986              	.LVL567:
1389:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_client_connect: Error connecting to remote ip/port, %d\n",
 4987              		.loc 1 1389 3 is_stmt 1 view .LVU1466
1389:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     LWIP_DEBUGF(MQTT_DEBUG_TRACE, ("mqtt_client_connect: Error connecting to remote ip/port, %d\n",
 4988              		.loc 1 1389 6 is_stmt 0 view .LVU1467
 4989 02d4 0390     		str	r0, [sp, #12]
 4990 02d6 0028     		cmp	r0, #0
ARM GAS  /tmp/ccBURsTh.s 			page 134


 4991 02d8 7FF446AF 		bne	.L346
1394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->conn_state = TCP_CONNECTING;
 4992              		.loc 1 1394 3 is_stmt 1 view .LVU1468
 4993 02dc 3949     		ldr	r1, .L375+68
 4994 02de E868     		ldr	r0, [r5, #12]
 4995              	.LVL568:
1394:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   client->conn_state = TCP_CONNECTING;
 4996              		.loc 1 1394 3 is_stmt 0 view .LVU1469
 4997 02e0 FFF7FEFF 		bl	tcp_err
 4998              	.LVL569:
1395:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 4999              		.loc 1 1395 3 is_stmt 1 view .LVU1470
1395:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
 5000              		.loc 1 1395 22 is_stmt 0 view .LVU1471
 5001 02e4 0121     		movs	r1, #1
 5002 02e6 A972     		strb	r1, [r5, #10]
1398:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append Protocol string */
 5003              		.loc 1 1398 3 is_stmt 1 view .LVU1472
 5004 02e8 0194     		str	r4, [sp, #4]
 5005 02ea 0022     		movs	r2, #0
 5006 02ec 0092     		str	r2, [sp]
 5007 02ee 1346     		mov	r3, r2
 5008 02f0 5046     		mov	r0, r10
 5009 02f2 FFF7FEFF 		bl	mqtt_output_append_fixed_header
 5010              	.LVL570:
1400:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append Protocol level */
 5011              		.loc 1 1400 3 view .LVU1473
 5012 02f6 0422     		movs	r2, #4
 5013 02f8 3349     		ldr	r1, .L375+72
 5014 02fa 5046     		mov	r0, r10
 5015 02fc FFF7FEFF 		bl	mqtt_output_append_string
 5016              	.LVL571:
1402:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append connect flags */
 5017              		.loc 1 1402 3 view .LVU1474
 5018 0300 0421     		movs	r1, #4
 5019 0302 5046     		mov	r0, r10
 5020 0304 FFF7FEFF 		bl	mqtt_output_append_u8
 5021              	.LVL572:
1404:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append keep-alive */
 5022              		.loc 1 1404 3 view .LVU1475
 5023 0308 0599     		ldr	r1, [sp, #20]
 5024 030a 5046     		mov	r0, r10
 5025 030c FFF7FEFF 		bl	mqtt_output_append_u8
 5026              	.LVL573:
1406:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append client id */
 5027              		.loc 1 1406 3 view .LVU1476
 5028 0310 B189     		ldrh	r1, [r6, #12]
 5029 0312 5046     		mov	r0, r10
 5030 0314 FFF7FEFF 		bl	mqtt_output_append_u16
 5031              	.LVL574:
1408:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* Append will message if used */
 5032              		.loc 1 1408 3 view .LVU1477
 5033 0318 4246     		mov	r2, r8
 5034 031a 3168     		ldr	r1, [r6]
 5035 031c 5046     		mov	r0, r10
 5036 031e FFF7FEFF 		bl	mqtt_output_append_string
 5037              	.LVL575:
ARM GAS  /tmp/ccBURsTh.s 			page 135


1410:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->will_topic, will_topic_len);
 5038              		.loc 1 1410 3 view .LVU1478
1410:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->will_topic, will_topic_len);
 5039              		.loc 1 1410 6 is_stmt 0 view .LVU1479
 5040 0322 17F0040F 		tst	r7, #4
 5041 0326 0DD1     		bne	.L373
 5042              	.L347:
1415:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->client_user, client_user_len);
 5043              		.loc 1 1415 3 is_stmt 1 view .LVU1480
1415:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->client_user, client_user_len);
 5044              		.loc 1 1415 6 is_stmt 0 view .LVU1481
 5045 0328 059B     		ldr	r3, [sp, #20]
 5046 032a 13F0800F 		tst	r3, #128
 5047 032e 14D1     		bne	.L374
 5048              	.L348:
1419:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->client_pass, client_pass_len);
 5049              		.loc 1 1419 3 is_stmt 1 view .LVU1482
1419:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->client_pass, client_pass_len);
 5050              		.loc 1 1419 6 is_stmt 0 view .LVU1483
 5051 0330 17F0400F 		tst	r7, #64
 5052 0334 3FF41DAF 		beq	.L329
1420:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 5053              		.loc 1 1420 5 is_stmt 1 view .LVU1484
 5054 0338 5A46     		mov	r2, fp
 5055 033a B168     		ldr	r1, [r6, #8]
 5056 033c 5046     		mov	r0, r10
 5057 033e FFF7FEFF 		bl	mqtt_output_append_string
 5058              	.LVL576:
 5059 0342 16E7     		b	.L329
 5060              	.L373:
1411:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_output_append_string(&client->output, client_info->will_msg, will_msg_len);
 5061              		.loc 1 1411 5 view .LVU1485
 5062 0344 069A     		ldr	r2, [sp, #24]
 5063 0346 3169     		ldr	r1, [r6, #16]
 5064 0348 5046     		mov	r0, r10
 5065 034a FFF7FEFF 		bl	mqtt_output_append_string
 5066              	.LVL577:
1412:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 5067              		.loc 1 1412 5 view .LVU1486
 5068 034e 079A     		ldr	r2, [sp, #28]
 5069 0350 7169     		ldr	r1, [r6, #20]
 5070 0352 5046     		mov	r0, r10
 5071 0354 FFF7FEFF 		bl	mqtt_output_append_string
 5072              	.LVL578:
 5073 0358 E6E7     		b	.L347
 5074              	.L374:
1416:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 5075              		.loc 1 1416 5 view .LVU1487
 5076 035a 4A46     		mov	r2, r9
 5077 035c 7168     		ldr	r1, [r6, #4]
 5078 035e 5046     		mov	r0, r10
 5079 0360 FFF7FEFF 		bl	mqtt_output_append_string
 5080              	.LVL579:
 5081 0364 E4E7     		b	.L348
 5082              	.LVL580:
 5083              	.L349:
1302:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
ARM GAS  /tmp/ccBURsTh.s 			page 136


 5084              		.loc 1 1302 12 is_stmt 0 view .LVU1488
 5085 0366 6FF00903 		mvn	r3, #9
 5086 036a 0393     		str	r3, [sp, #12]
 5087 036c 01E7     		b	.L329
 5088              	.LVL581:
 5089              	.L354:
1362:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 5090              		.loc 1 1362 12 view .LVU1489
 5091 036e 4FF0FF33 		mov	r3, #-1
 5092 0372 0393     		str	r3, [sp, #12]
 5093 0374 FDE6     		b	.L329
 5094              	.L355:
1374:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 5095              		.loc 1 1374 12 view .LVU1490
 5096 0376 4FF0FF33 		mov	r3, #-1
 5097 037a 0393     		str	r3, [sp, #12]
 5098 037c F9E6     		b	.L329
 5099              	.L376:
 5100 037e 00BF     		.align	2
 5101              	.L375:
 5102 0380 00000000 		.word	ip_addr_any
 5103 0384 00000000 		.word	.LC0
 5104 0388 00000000 		.word	.LC30
 5105 038c 58000000 		.word	.LC2
 5106 0390 24000000 		.word	.LC31
 5107 0394 4C000000 		.word	.LC32
 5108 0398 78000000 		.word	.LC33
 5109 039c AC000000 		.word	.LC34
 5110 03a0 EC000000 		.word	.LC35
 5111 03a4 2C010000 		.word	.LC36
 5112 03a8 98010000 		.word	.LC38
 5113 03ac ******** 		.word	.LC39
 5114 03b0 68010000 		.word	.LC37
 5115 03b4 1C020000 		.word	.LC40
 5116 03b8 5C020000 		.word	.LC41
 5117 03bc ******** 		.word	.LC42
 5118 03c0 00000000 		.word	mqtt_tcp_connect_cb
 5119 03c4 00000000 		.word	mqtt_tcp_err_cb
 5120 03c8 DC020000 		.word	.LC43
 5121              		.cfi_endproc
 5122              	.LFE209:
 5124              		.section	.rodata.mqtt_disconnect.str1.4,"aMS",%progbits,1
 5125              		.align	2
 5126              	.LC44:
 5127 0000 6D717474 		.ascii	"mqtt_disconnect: client != NULL\000"
 5127      5F646973 
 5127      636F6E6E 
 5127      6563743A 
 5127      20636C69 
 5128              		.section	.text.mqtt_disconnect,"ax",%progbits
 5129              		.align	1
 5130              		.global	mqtt_disconnect
 5131              		.syntax unified
 5132              		.thumb
 5133              		.thumb_func
 5135              	mqtt_disconnect:
 5136              	.LVL582:
ARM GAS  /tmp/ccBURsTh.s 			page 137


 5137              	.LFB210:
1429:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1430:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1431:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1432:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1433:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Disconnect from MQTT server
1434:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
1435:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1436:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** void
1437:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_disconnect(mqtt_client_t *client)
1438:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 5138              		.loc 1 1438 1 is_stmt 1 view -0
 5139              		.cfi_startproc
 5140              		@ args = 0, pretend = 0, frame = 0
 5141              		@ frame_needed = 0, uses_anonymous_args = 0
 5142              		.loc 1 1438 1 is_stmt 0 view .LVU1492
 5143 0000 10B5     		push	{r4, lr}
 5144              	.LCFI51:
 5145              		.cfi_def_cfa_offset 8
 5146              		.cfi_offset 4, -8
 5147              		.cfi_offset 14, -4
1439:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 5148              		.loc 1 1439 28 is_stmt 1 view .LVU1493
1440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_disconnect: client != NULL", client);
 5149              		.loc 1 1440 3 view .LVU1494
 5150              		.loc 1 1440 3 view .LVU1495
 5151 0002 0446     		mov	r4, r0
 5152 0004 10B1     		cbz	r0, .L381
 5153              	.LVL583:
 5154              	.L378:
 5155              		.loc 1 1440 3 discriminator 3 view .LVU1496
 5156              		.loc 1 1440 3 discriminator 3 view .LVU1497
1441:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* If connection in not already closed */
1442:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   if (client->conn_state != TCP_DISCONNECTED) {
 5157              		.loc 1 1442 3 view .LVU1498
 5158              		.loc 1 1442 13 is_stmt 0 view .LVU1499
 5159 0006 A37A     		ldrb	r3, [r4, #10]	@ zero_extendqisi2
 5160              		.loc 1 1442 6 view .LVU1500
 5161 0008 43B9     		cbnz	r3, .L382
 5162              	.L377:
1443:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     /* Set conn_state before calling mqtt_close to prevent callback from being called */
1444:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     client->conn_state = TCP_DISCONNECTED;
1445:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_close(client, (mqtt_connection_status_t)0);
1446:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
1447:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 5163              		.loc 1 1447 1 view .LVU1501
 5164 000a 10BD     		pop	{r4, pc}
 5165              	.LVL584:
 5166              	.L381:
1440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* If connection in not already closed */
 5167              		.loc 1 1440 3 is_stmt 1 discriminator 1 view .LVU1502
1440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* If connection in not already closed */
 5168              		.loc 1 1440 3 discriminator 1 view .LVU1503
 5169 000c 064B     		ldr	r3, .L383
 5170 000e 4FF4B462 		mov	r2, #1440
 5171 0012 0649     		ldr	r1, .L383+4
 5172 0014 0648     		ldr	r0, .L383+8
ARM GAS  /tmp/ccBURsTh.s 			page 138


 5173              	.LVL585:
1440:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   /* If connection in not already closed */
 5174              		.loc 1 1440 3 is_stmt 0 discriminator 1 view .LVU1504
 5175 0016 FFF7FEFF 		bl	printf
 5176              	.LVL586:
 5177 001a F4E7     		b	.L378
 5178              	.L382:
1444:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_close(client, (mqtt_connection_status_t)0);
 5179              		.loc 1 1444 5 is_stmt 1 view .LVU1505
1444:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****     mqtt_close(client, (mqtt_connection_status_t)0);
 5180              		.loc 1 1444 24 is_stmt 0 view .LVU1506
 5181 001c 0021     		movs	r1, #0
 5182 001e A172     		strb	r1, [r4, #10]
1445:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   }
 5183              		.loc 1 1445 5 is_stmt 1 view .LVU1507
 5184 0020 2046     		mov	r0, r4
 5185 0022 FFF7FEFF 		bl	mqtt_close
 5186              	.LVL587:
 5187              		.loc 1 1447 1 is_stmt 0 view .LVU1508
 5188 0026 F0E7     		b	.L377
 5189              	.L384:
 5190              		.align	2
 5191              	.L383:
 5192 0028 00000000 		.word	.LC0
 5193 002c 00000000 		.word	.LC44
 5194 0030 58000000 		.word	.LC2
 5195              		.cfi_endproc
 5196              	.LFE210:
 5198              		.section	.rodata.mqtt_client_is_connected.str1.4,"aMS",%progbits,1
 5199              		.align	2
 5200              	.LC45:
 5201 0000 6D717474 		.ascii	"mqtt_client_is_connected: client != NULL\000"
 5201      5F636C69 
 5201      656E745F 
 5201      69735F63 
 5201      6F6E6E65 
 5202              		.section	.text.mqtt_client_is_connected,"ax",%progbits
 5203              		.align	1
 5204              		.global	mqtt_client_is_connected
 5205              		.syntax unified
 5206              		.thumb
 5207              		.thumb_func
 5209              	mqtt_client_is_connected:
 5210              	.LVL588:
 5211              	.LFB211:
1448:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** 
1449:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** /**
1450:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @ingroup mqtt
1451:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * Check connection with server
1452:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @param client MQTT client
1453:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  * @return 1 if connected to server, 0 otherwise
1454:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****  */
1455:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** u8_t
1456:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** mqtt_client_is_connected(mqtt_client_t *client)
1457:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** {
 5212              		.loc 1 1457 1 is_stmt 1 view -0
 5213              		.cfi_startproc
ARM GAS  /tmp/ccBURsTh.s 			page 139


 5214              		@ args = 0, pretend = 0, frame = 0
 5215              		@ frame_needed = 0, uses_anonymous_args = 0
 5216              		.loc 1 1457 1 is_stmt 0 view .LVU1510
 5217 0000 10B5     		push	{r4, lr}
 5218              	.LCFI52:
 5219              		.cfi_def_cfa_offset 8
 5220              		.cfi_offset 4, -8
 5221              		.cfi_offset 14, -4
1458:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT_CORE_LOCKED();
 5222              		.loc 1 1458 28 is_stmt 1 view .LVU1511
1459:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_is_connected: client != NULL", client);
 5223              		.loc 1 1459 3 view .LVU1512
 5224              		.loc 1 1459 3 view .LVU1513
 5225 0002 0446     		mov	r4, r0
 5226 0004 28B1     		cbz	r0, .L388
 5227              	.LVL589:
 5228              	.L386:
 5229              		.loc 1 1459 3 discriminator 3 view .LVU1514
 5230              		.loc 1 1459 3 discriminator 3 view .LVU1515
1460:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   return client->conn_state == MQTT_CONNECTED;
 5231              		.loc 1 1460 3 view .LVU1516
 5232              		.loc 1 1460 16 is_stmt 0 view .LVU1517
 5233 0006 A07A     		ldrb	r0, [r4, #10]	@ zero_extendqisi2
1461:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c **** }
 5234              		.loc 1 1461 1 view .LVU1518
 5235 0008 0328     		cmp	r0, #3
 5236 000a 14BF     		ite	ne
 5237 000c 0020     		movne	r0, #0
 5238 000e 0120     		moveq	r0, #1
 5239 0010 10BD     		pop	{r4, pc}
 5240              	.LVL590:
 5241              	.L388:
1459:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_is_connected: client != NULL", client);
 5242              		.loc 1 1459 3 is_stmt 1 discriminator 1 view .LVU1519
1459:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_is_connected: client != NULL", client);
 5243              		.loc 1 1459 3 discriminator 1 view .LVU1520
 5244 0012 044B     		ldr	r3, .L389
 5245 0014 40F2B352 		movw	r2, #1459
 5246 0018 0349     		ldr	r1, .L389+4
 5247 001a 0448     		ldr	r0, .L389+8
 5248              	.LVL591:
1459:Middlewares/Third_Party/LwIP/src/apps/mqtt/mqtt.c ****   LWIP_ASSERT("mqtt_client_is_connected: client != NULL", client);
 5249              		.loc 1 1459 3 is_stmt 0 discriminator 1 view .LVU1521
 5250 001c FFF7FEFF 		bl	printf
 5251              	.LVL592:
 5252 0020 F1E7     		b	.L386
 5253              	.L390:
 5254 0022 00BF     		.align	2
 5255              	.L389:
 5256 0024 00000000 		.word	.LC0
 5257 0028 00000000 		.word	.LC45
 5258 002c 58000000 		.word	.LC2
 5259              		.cfi_endproc
 5260              	.LFE211:
 5262              		.text
 5263              	.Letext0:
 5264              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
ARM GAS  /tmp/ccBURsTh.s 			page 140


 5265              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 5266              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 5267              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 5268              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 5269              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 5270              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 5271              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/apps/mqtt.h"
 5272              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/apps/mqtt_priv.h"
 5273              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpbase.h"
 5274              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/mem.h"
 5275              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 5276              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/tcp.h"
 5277              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/timeouts.h"
 5278              		.file 16 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 5279              		.file 17 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 5280              		.file 18 "<built-in>"
ARM GAS  /tmp/ccBURsTh.s 			page 141


DEFINED SYMBOLS
                            *ABS*:00000000 mqtt.c
     /tmp/ccBURsTh.s:20     .text.msg_generate_packet_id:00000000 $t
     /tmp/ccBURsTh.s:25     .text.msg_generate_packet_id:00000000 msg_generate_packet_id
     /tmp/ccBURsTh.s:58     .text.mqtt_ringbuf_put:00000000 $t
     /tmp/ccBURsTh.s:63     .text.mqtt_ringbuf_put:00000000 mqtt_ringbuf_put
     /tmp/ccBURsTh.s:97     .text.mqtt_ringbuf_get_ptr:00000000 $t
     /tmp/ccBURsTh.s:102    .text.mqtt_ringbuf_get_ptr:00000000 mqtt_ringbuf_get_ptr
     /tmp/ccBURsTh.s:123    .text.mqtt_ringbuf_len:00000000 $t
     /tmp/ccBURsTh.s:128    .text.mqtt_ringbuf_len:00000000 mqtt_ringbuf_len
     /tmp/ccBURsTh.s:163    .text.mqtt_delete_request:00000000 $t
     /tmp/ccBURsTh.s:168    .text.mqtt_delete_request:00000000 mqtt_delete_request
     /tmp/ccBURsTh.s:190    .text.mqtt_output_append_u8:00000000 $t
     /tmp/ccBURsTh.s:195    .text.mqtt_output_append_u8:00000000 mqtt_output_append_u8
     /tmp/ccBURsTh.s:217    .text.mqtt_output_append_u16:00000000 $t
     /tmp/ccBURsTh.s:222    .text.mqtt_output_append_u16:00000000 mqtt_output_append_u16
     /tmp/ccBURsTh.s:257    .text.mqtt_output_append_buf:00000000 $t
     /tmp/ccBURsTh.s:262    .text.mqtt_output_append_buf:00000000 mqtt_output_append_buf
     /tmp/ccBURsTh.s:313    .text.mqtt_output_append_string:00000000 $t
     /tmp/ccBURsTh.s:318    .text.mqtt_output_append_string:00000000 mqtt_output_append_string
     /tmp/ccBURsTh.s:379    .text.mqtt_output_append_fixed_header:00000000 $t
     /tmp/ccBURsTh.s:384    .text.mqtt_output_append_fixed_header:00000000 mqtt_output_append_fixed_header
     /tmp/ccBURsTh.s:467    .text.mqtt_incomming_suback:00000000 $t
     /tmp/ccBURsTh.s:472    .text.mqtt_incomming_suback:00000000 mqtt_incomming_suback
     /tmp/ccBURsTh.s:517    .rodata.mqtt_create_request.str1.4:00000000 $d
     /tmp/ccBURsTh.s:527    .text.mqtt_create_request:00000000 $t
     /tmp/ccBURsTh.s:532    .text.mqtt_create_request:00000000 mqtt_create_request
     /tmp/ccBURsTh.s:640    .text.mqtt_create_request:00000054 $d
     /tmp/ccBURsTh.s:647    .rodata.mqtt_output_check_space.str1.4:00000000 $d
     /tmp/ccBURsTh.s:651    .text.mqtt_output_check_space:00000000 $t
     /tmp/ccBURsTh.s:656    .text.mqtt_output_check_space:00000000 mqtt_output_check_space
     /tmp/ccBURsTh.s:730    .text.mqtt_output_check_space:00000038 $d
     /tmp/ccBURsTh.s:737    .rodata.mqtt_append_request.str1.4:00000000 $d
     /tmp/ccBURsTh.s:745    .text.mqtt_append_request:00000000 $t
     /tmp/ccBURsTh.s:750    .text.mqtt_append_request:00000000 mqtt_append_request
     /tmp/ccBURsTh.s:872    .text.mqtt_append_request:00000054 $d
     /tmp/ccBURsTh.s:880    .rodata.mqtt_ringbuf_advance_get_idx.str1.4:00000000 $d
     /tmp/ccBURsTh.s:885    .text.mqtt_ringbuf_advance_get_idx:00000000 $t
     /tmp/ccBURsTh.s:890    .text.mqtt_ringbuf_advance_get_idx:00000000 mqtt_ringbuf_advance_get_idx
     /tmp/ccBURsTh.s:952    .text.mqtt_ringbuf_advance_get_idx:0000002c $d
     /tmp/ccBURsTh.s:959    .rodata.mqtt_request_time_elapsed.str1.4:00000000 $d
     /tmp/ccBURsTh.s:963    .text.mqtt_request_time_elapsed:00000000 $t
     /tmp/ccBURsTh.s:968    .text.mqtt_request_time_elapsed:00000000 mqtt_request_time_elapsed
     /tmp/ccBURsTh.s:1084   .text.mqtt_request_time_elapsed:00000058 $d
     /tmp/ccBURsTh.s:1091   .rodata.mqtt_clear_requests.str1.4:00000000 $d
     /tmp/ccBURsTh.s:1095   .text.mqtt_clear_requests:00000000 $t
     /tmp/ccBURsTh.s:1100   .text.mqtt_clear_requests:00000000 mqtt_clear_requests
     /tmp/ccBURsTh.s:1171   .text.mqtt_clear_requests:0000002c $d
     /tmp/ccBURsTh.s:1178   .rodata.mqtt_take_request.str1.4:00000000 $d
     /tmp/ccBURsTh.s:1182   .text.mqtt_take_request:00000000 $t
     /tmp/ccBURsTh.s:1187   .text.mqtt_take_request:00000000 mqtt_take_request
     /tmp/ccBURsTh.s:1301   .text.mqtt_take_request:0000004c $d
     /tmp/ccBURsTh.s:1308   .rodata.mqtt_init_requests.str1.4:00000000 $d
     /tmp/ccBURsTh.s:1312   .text.mqtt_init_requests:00000000 $t
     /tmp/ccBURsTh.s:1317   .text.mqtt_init_requests:00000000 mqtt_init_requests
     /tmp/ccBURsTh.s:1382   .text.mqtt_init_requests:0000002c $d
     /tmp/ccBURsTh.s:1389   .text.mqtt_output_send:00000000 $t
ARM GAS  /tmp/ccBURsTh.s 			page 142


     /tmp/ccBURsTh.s:1394   .text.mqtt_output_send:00000000 mqtt_output_send
     /tmp/ccBURsTh.s:1599   .text.mqtt_tcp_poll_cb:00000000 $t
     /tmp/ccBURsTh.s:1604   .text.mqtt_tcp_poll_cb:00000000 mqtt_tcp_poll_cb
     /tmp/ccBURsTh.s:1645   .text.mqtt_tcp_sent_cb:00000000 $t
     /tmp/ccBURsTh.s:1650   .text.mqtt_tcp_sent_cb:00000000 mqtt_tcp_sent_cb
     /tmp/ccBURsTh.s:1743   .text.pub_ack_rec_rel_response:00000000 $t
     /tmp/ccBURsTh.s:1748   .text.pub_ack_rec_rel_response:00000000 pub_ack_rec_rel_response
     /tmp/ccBURsTh.s:1832   .rodata.mqtt_message_received.str1.4:00000000 $d
     /tmp/ccBURsTh.s:1842   .text.mqtt_message_received:00000000 $t
     /tmp/ccBURsTh.s:1847   .text.mqtt_message_received:00000000 mqtt_message_received
     /tmp/ccBURsTh.s:2469   .text.mqtt_message_received:000002a4 $d
     /tmp/ccBURsTh.s:2478   .rodata.mqtt_close.str1.4:00000000 $d
     /tmp/ccBURsTh.s:2482   .text.mqtt_close:00000000 $t
     /tmp/ccBURsTh.s:2487   .text.mqtt_close:00000000 mqtt_close
     /tmp/ccBURsTh.s:2611   .text.mqtt_close:0000006c $d
     /tmp/ccBURsTh.s:2699   .text.mqtt_cyclic_timer:00000000 mqtt_cyclic_timer
     /tmp/ccBURsTh.s:2620   .rodata.mqtt_tcp_err_cb.str1.4:00000000 $d
     /tmp/ccBURsTh.s:2624   .text.mqtt_tcp_err_cb:00000000 $t
     /tmp/ccBURsTh.s:2629   .text.mqtt_tcp_err_cb:00000000 mqtt_tcp_err_cb
     /tmp/ccBURsTh.s:2683   .text.mqtt_tcp_err_cb:00000028 $d
     /tmp/ccBURsTh.s:2690   .rodata.mqtt_cyclic_timer.str1.4:00000000 $d
     /tmp/ccBURsTh.s:2694   .text.mqtt_cyclic_timer:00000000 $t
     /tmp/ccBURsTh.s:2892   .text.mqtt_cyclic_timer:000000c0 $d
     /tmp/ccBURsTh.s:2900   .text.mqtt_tcp_connect_cb:00000000 $t
     /tmp/ccBURsTh.s:2905   .text.mqtt_tcp_connect_cb:00000000 mqtt_tcp_connect_cb
     /tmp/ccBURsTh.s:2987   .text.mqtt_tcp_connect_cb:0000004c $d
     /tmp/ccBURsTh.s:3315   .text.mqtt_tcp_recv_cb:00000000 mqtt_tcp_recv_cb
     /tmp/ccBURsTh.s:2995   .text.mqtt_parse_incoming:00000000 $t
     /tmp/ccBURsTh.s:3000   .text.mqtt_parse_incoming:00000000 mqtt_parse_incoming
     /tmp/ccBURsTh.s:3303   .rodata.mqtt_tcp_recv_cb.str1.4:00000000 $d
     /tmp/ccBURsTh.s:3310   .text.mqtt_tcp_recv_cb:00000000 $t
     /tmp/ccBURsTh.s:3459   .text.mqtt_tcp_recv_cb:0000007c $d
     /tmp/ccBURsTh.s:3468   .rodata.mqtt_publish.str1.4:00000000 $d
     /tmp/ccBURsTh.s:3484   .text.mqtt_publish:00000000 $t
     /tmp/ccBURsTh.s:3490   .text.mqtt_publish:00000000 mqtt_publish
     /tmp/ccBURsTh.s:3791   .text.mqtt_publish:00000148 $d
     /tmp/ccBURsTh.s:3802   .rodata.mqtt_sub_unsub.str1.4:00000000 $d
     /tmp/ccBURsTh.s:3818   .text.mqtt_sub_unsub:00000000 $t
     /tmp/ccBURsTh.s:3824   .text.mqtt_sub_unsub:00000000 mqtt_sub_unsub
     /tmp/ccBURsTh.s:4128   .text.mqtt_sub_unsub:00000148 $d
     /tmp/ccBURsTh.s:4139   .rodata.mqtt_set_inpub_callback.str1.4:00000000 $d
     /tmp/ccBURsTh.s:4143   .text.mqtt_set_inpub_callback:00000000 $t
     /tmp/ccBURsTh.s:4149   .text.mqtt_set_inpub_callback:00000000 mqtt_set_inpub_callback
     /tmp/ccBURsTh.s:4211   .text.mqtt_set_inpub_callback:00000024 $d
     /tmp/ccBURsTh.s:4218   .text.mqtt_client_new:00000000 $t
     /tmp/ccBURsTh.s:4224   .text.mqtt_client_new:00000000 mqtt_client_new
     /tmp/ccBURsTh.s:4248   .text.mqtt_client_free:00000000 $t
     /tmp/ccBURsTh.s:4254   .text.mqtt_client_free:00000000 mqtt_client_free
     /tmp/ccBURsTh.s:4276   .rodata.mqtt_client_connect.str1.4:00000000 $d
     /tmp/ccBURsTh.s:4328   .text.mqtt_client_connect:00000000 $t
     /tmp/ccBURsTh.s:4334   .text.mqtt_client_connect:00000000 mqtt_client_connect
     /tmp/ccBURsTh.s:5102   .text.mqtt_client_connect:00000380 $d
     /tmp/ccBURsTh.s:5125   .rodata.mqtt_disconnect.str1.4:00000000 $d
     /tmp/ccBURsTh.s:5129   .text.mqtt_disconnect:00000000 $t
     /tmp/ccBURsTh.s:5135   .text.mqtt_disconnect:00000000 mqtt_disconnect
     /tmp/ccBURsTh.s:5192   .text.mqtt_disconnect:00000028 $d
     /tmp/ccBURsTh.s:5199   .rodata.mqtt_client_is_connected.str1.4:00000000 $d
ARM GAS  /tmp/ccBURsTh.s 			page 143


     /tmp/ccBURsTh.s:5203   .text.mqtt_client_is_connected:00000000 $t
     /tmp/ccBURsTh.s:5209   .text.mqtt_client_is_connected:00000000 mqtt_client_is_connected
     /tmp/ccBURsTh.s:5256   .text.mqtt_client_is_connected:00000024 $d

UNDEFINED SYMBOLS
printf
tcp_write
tcp_output
tcp_recv
tcp_err
tcp_sent
tcp_close
sys_untimeout
tcp_abort
sys_timeout
tcp_poll
pbuf_get_at
pbuf_copy_partial
tcp_recved
pbuf_free
strlen
mem_calloc
mem_free
memset
tcp_new_ip_type
tcp_arg
tcp_bind
tcp_connect
ip_addr_any
