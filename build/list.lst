ARM GAS  /tmp/ccUpFPQp.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"list.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/FreeRTOS/Source/list.c"
  19              		.section	.text.vListInitialise,"ax",%progbits
  20              		.align	1
  21              		.global	vListInitialise
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	vListInitialise:
  27              	.LVL0:
  28              	.LFB4:
   1:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  29:Middlewares/Third_Party/FreeRTOS/Source/list.c **** #include <stdlib.h>
  30:Middlewares/Third_Party/FreeRTOS/Source/list.c **** #include "FreeRTOS.h"
ARM GAS  /tmp/ccUpFPQp.s 			page 2


  31:Middlewares/Third_Party/FreeRTOS/Source/list.c **** #include "list.h"
  32:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  33:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /*-----------------------------------------------------------
  34:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  * PUBLIC LIST API documented in list.h
  35:Middlewares/Third_Party/FreeRTOS/Source/list.c ****  *----------------------------------------------------------*/
  36:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  37:Middlewares/Third_Party/FreeRTOS/Source/list.c **** void vListInitialise( List_t * const pxList )
  38:Middlewares/Third_Party/FreeRTOS/Source/list.c **** {
  29              		.loc 1 38 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  39:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* The list structure contains a list item which is used to mark the
  40:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	end of the list.  To initialise the list the list end is inserted
  41:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	as the only list entry. */
  42:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxList->pxIndex = ( ListItem_t * ) &( pxList->xListEnd );			/*lint !e826 !e740 !e9087 The mini lis
  34              		.loc 1 42 2 view .LVU1
  35              		.loc 1 42 37 is_stmt 0 view .LVU2
  36 0000 00F10803 		add	r3, r0, #8
  37              		.loc 1 42 18 view .LVU3
  38 0004 4360     		str	r3, [r0, #4]
  43:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  44:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* The list end value is the highest possible value in the list to
  45:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	ensure it remains at the end of the list. */
  46:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxList->xListEnd.xItemValue = portMAX_DELAY;
  39              		.loc 1 46 2 is_stmt 1 view .LVU4
  40              		.loc 1 46 30 is_stmt 0 view .LVU5
  41 0006 4FF0FF32 		mov	r2, #-1
  42 000a 8260     		str	r2, [r0, #8]
  47:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  48:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* The list end next and previous pointers point to itself so we know
  49:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	when the list is empty. */
  50:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxList->xListEnd.pxNext = ( ListItem_t * ) &( pxList->xListEnd );	/*lint !e826 !e740 !e9087 The mi
  43              		.loc 1 50 2 is_stmt 1 view .LVU6
  44              		.loc 1 50 26 is_stmt 0 view .LVU7
  45 000c C360     		str	r3, [r0, #12]
  51:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxList->xListEnd.pxPrevious = ( ListItem_t * ) &( pxList->xListEnd );/*lint !e826 !e740 !e9087 The
  46              		.loc 1 51 2 is_stmt 1 view .LVU8
  47              		.loc 1 51 30 is_stmt 0 view .LVU9
  48 000e 0361     		str	r3, [r0, #16]
  52:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  53:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxList->uxNumberOfItems = ( UBaseType_t ) 0U;
  49              		.loc 1 53 2 is_stmt 1 view .LVU10
  50              		.loc 1 53 26 is_stmt 0 view .LVU11
  51 0010 0023     		movs	r3, #0
  52 0012 0360     		str	r3, [r0]
  54:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Write known values into the list if
  56:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	configUSE_LIST_DATA_INTEGRITY_CHECK_BYTES is set to 1. */
  57:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listSET_LIST_INTEGRITY_CHECK_1_VALUE( pxList );
  53              		.loc 1 57 48 is_stmt 1 view .LVU12
  58:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listSET_LIST_INTEGRITY_CHECK_2_VALUE( pxList );
  54              		.loc 1 58 48 view .LVU13
  59:Middlewares/Third_Party/FreeRTOS/Source/list.c **** }
  55              		.loc 1 59 1 is_stmt 0 view .LVU14
  56 0014 7047     		bx	lr
ARM GAS  /tmp/ccUpFPQp.s 			page 3


  57              		.cfi_endproc
  58              	.LFE4:
  60              		.section	.text.vListInitialiseItem,"ax",%progbits
  61              		.align	1
  62              		.global	vListInitialiseItem
  63              		.syntax unified
  64              		.thumb
  65              		.thumb_func
  67              	vListInitialiseItem:
  68              	.LVL1:
  69              	.LFB5:
  60:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /*-----------------------------------------------------------*/
  61:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  62:Middlewares/Third_Party/FreeRTOS/Source/list.c **** void vListInitialiseItem( ListItem_t * const pxItem )
  63:Middlewares/Third_Party/FreeRTOS/Source/list.c **** {
  70              		.loc 1 63 1 is_stmt 1 view -0
  71              		.cfi_startproc
  72              		@ args = 0, pretend = 0, frame = 0
  73              		@ frame_needed = 0, uses_anonymous_args = 0
  74              		@ link register save eliminated.
  64:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Make sure the list item is not recorded as being on a list. */
  65:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxItem->pxContainer = NULL;
  75              		.loc 1 65 2 view .LVU16
  76              		.loc 1 65 22 is_stmt 0 view .LVU17
  77 0000 0023     		movs	r3, #0
  78 0002 0361     		str	r3, [r0, #16]
  66:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  67:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Write known values into the list item if
  68:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	configUSE_LIST_DATA_INTEGRITY_CHECK_BYTES is set to 1. */
  69:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listSET_FIRST_LIST_ITEM_INTEGRITY_CHECK_VALUE( pxItem );
  79              		.loc 1 69 57 is_stmt 1 view .LVU18
  70:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listSET_SECOND_LIST_ITEM_INTEGRITY_CHECK_VALUE( pxItem );
  80              		.loc 1 70 58 view .LVU19
  71:Middlewares/Third_Party/FreeRTOS/Source/list.c **** }
  81              		.loc 1 71 1 is_stmt 0 view .LVU20
  82 0004 7047     		bx	lr
  83              		.cfi_endproc
  84              	.LFE5:
  86              		.section	.text.vListInsertEnd,"ax",%progbits
  87              		.align	1
  88              		.global	vListInsertEnd
  89              		.syntax unified
  90              		.thumb
  91              		.thumb_func
  93              	vListInsertEnd:
  94              	.LVL2:
  95              	.LFB6:
  72:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /*-----------------------------------------------------------*/
  73:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  74:Middlewares/Third_Party/FreeRTOS/Source/list.c **** void vListInsertEnd( List_t * const pxList, ListItem_t * const pxNewListItem )
  75:Middlewares/Third_Party/FreeRTOS/Source/list.c **** {
  96              		.loc 1 75 1 is_stmt 1 view -0
  97              		.cfi_startproc
  98              		@ args = 0, pretend = 0, frame = 0
  99              		@ frame_needed = 0, uses_anonymous_args = 0
 100              		@ link register save eliminated.
  76:Middlewares/Third_Party/FreeRTOS/Source/list.c **** ListItem_t * const pxIndex = pxList->pxIndex;
ARM GAS  /tmp/ccUpFPQp.s 			page 4


 101              		.loc 1 76 1 view .LVU22
 102              		.loc 1 76 20 is_stmt 0 view .LVU23
 103 0000 4368     		ldr	r3, [r0, #4]
 104              	.LVL3:
  77:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  78:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Only effective when configASSERT() is also defined, these tests may catch
  79:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	the list data structures being overwritten in memory.  They will not catch
  80:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	data errors caused by incorrect configuration or use of FreeRTOS. */
  81:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listTEST_LIST_INTEGRITY( pxList );
 105              		.loc 1 81 35 is_stmt 1 view .LVU24
  82:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listTEST_LIST_ITEM_INTEGRITY( pxNewListItem );
 106              		.loc 1 82 47 view .LVU25
  83:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  84:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Insert a new list item into pxList, but rather than sort the list,
  85:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	makes the new list item the last item to be removed by a call to
  86:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listGET_OWNER_OF_NEXT_ENTRY(). */
  87:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxNext = pxIndex;
 107              		.loc 1 87 2 view .LVU26
 108              		.loc 1 87 24 is_stmt 0 view .LVU27
 109 0002 4B60     		str	r3, [r1, #4]
  88:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxPrevious = pxIndex->pxPrevious;
 110              		.loc 1 88 2 is_stmt 1 view .LVU28
 111              		.loc 1 88 37 is_stmt 0 view .LVU29
 112 0004 9A68     		ldr	r2, [r3, #8]
 113              		.loc 1 88 28 view .LVU30
 114 0006 8A60     		str	r2, [r1, #8]
  89:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Only used during decision coverage testing. */
  91:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	mtCOVERAGE_TEST_DELAY();
 115              		.loc 1 91 25 is_stmt 1 view .LVU31
  92:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  93:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxIndex->pxPrevious->pxNext = pxNewListItem;
 116              		.loc 1 93 2 view .LVU32
 117              		.loc 1 93 30 is_stmt 0 view .LVU33
 118 0008 5160     		str	r1, [r2, #4]
  94:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxIndex->pxPrevious = pxNewListItem;
 119              		.loc 1 94 2 is_stmt 1 view .LVU34
 120              		.loc 1 94 22 is_stmt 0 view .LVU35
 121 000a 9960     		str	r1, [r3, #8]
  95:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  96:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Remember which list the item is in. */
  97:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxContainer = pxList;
 122              		.loc 1 97 2 is_stmt 1 view .LVU36
 123              		.loc 1 97 29 is_stmt 0 view .LVU37
 124 000c 0861     		str	r0, [r1, #16]
  98:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
  99:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	( pxList->uxNumberOfItems )++;
 125              		.loc 1 99 2 is_stmt 1 view .LVU38
 126              		.loc 1 99 10 is_stmt 0 view .LVU39
 127 000e 0368     		ldr	r3, [r0]
 128              	.LVL4:
 129              		.loc 1 99 29 view .LVU40
 130 0010 0133     		adds	r3, r3, #1
 131 0012 0360     		str	r3, [r0]
 100:Middlewares/Third_Party/FreeRTOS/Source/list.c **** }
 132              		.loc 1 100 1 view .LVU41
 133 0014 7047     		bx	lr
ARM GAS  /tmp/ccUpFPQp.s 			page 5


 134              		.cfi_endproc
 135              	.LFE6:
 137              		.section	.text.vListInsert,"ax",%progbits
 138              		.align	1
 139              		.global	vListInsert
 140              		.syntax unified
 141              		.thumb
 142              		.thumb_func
 144              	vListInsert:
 145              	.LVL5:
 146              	.LFB7:
 101:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /*-----------------------------------------------------------*/
 102:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 103:Middlewares/Third_Party/FreeRTOS/Source/list.c **** void vListInsert( List_t * const pxList, ListItem_t * const pxNewListItem )
 104:Middlewares/Third_Party/FreeRTOS/Source/list.c **** {
 147              		.loc 1 104 1 is_stmt 1 view -0
 148              		.cfi_startproc
 149              		@ args = 0, pretend = 0, frame = 0
 150              		@ frame_needed = 0, uses_anonymous_args = 0
 151              		@ link register save eliminated.
 152              		.loc 1 104 1 is_stmt 0 view .LVU43
 153 0000 30B4     		push	{r4, r5}
 154              	.LCFI0:
 155              		.cfi_def_cfa_offset 8
 156              		.cfi_offset 4, -8
 157              		.cfi_offset 5, -4
 105:Middlewares/Third_Party/FreeRTOS/Source/list.c **** ListItem_t *pxIterator;
 158              		.loc 1 105 1 is_stmt 1 view .LVU44
 106:Middlewares/Third_Party/FreeRTOS/Source/list.c **** const TickType_t xValueOfInsertion = pxNewListItem->xItemValue;
 159              		.loc 1 106 1 view .LVU45
 160              		.loc 1 106 18 is_stmt 0 view .LVU46
 161 0002 0D68     		ldr	r5, [r1]
 162              	.LVL6:
 107:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 108:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Only effective when configASSERT() is also defined, these tests may catch
 109:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	the list data structures being overwritten in memory.  They will not catch
 110:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	data errors caused by incorrect configuration or use of FreeRTOS. */
 111:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listTEST_LIST_INTEGRITY( pxList );
 163              		.loc 1 111 35 is_stmt 1 view .LVU47
 112:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	listTEST_LIST_ITEM_INTEGRITY( pxNewListItem );
 164              		.loc 1 112 47 view .LVU48
 113:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 114:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Insert the new list item into the list, sorted in xItemValue order.
 115:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 116:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	If the list already contains a list item with the same item value then the
 117:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	new list item should be placed after it.  This ensures that TCBs which are
 118:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	stored in ready lists (all of which have the same xItemValue value) get a
 119:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	share of the CPU.  However, if the xItemValue is the same as the back marker
 120:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	the iteration loop below will not end.  Therefore the value is checked
 121:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	first, and the algorithm slightly modified if necessary. */
 122:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	if( xValueOfInsertion == portMAX_DELAY )
 165              		.loc 1 122 2 view .LVU49
 166              		.loc 1 122 4 is_stmt 0 view .LVU50
 167 0004 B5F1FF3F 		cmp	r5, #-1
 168 0008 11D0     		beq	.L9
 123:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	{
 124:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		pxIterator = pxList->xListEnd.pxPrevious;
ARM GAS  /tmp/ccUpFPQp.s 			page 6


 125:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 126:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	else
 127:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	{
 128:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		/* *** NOTE ***********************************************************
 129:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		If you find your application is crashing here then likely causes are
 130:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		listed below.  In addition see https://www.freertos.org/FAQHelp.html for
 131:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		more tips, and ensure configASSERT() is defined!
 132:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		https://www.freertos.org/a00110.html#configASSERT
 133:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			1) Stack overflow -
 135:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   see https://www.freertos.org/Stacks-and-stack-overflow-checking.html
 136:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			2) Incorrect interrupt priority assignment, especially on Cortex-M
 137:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   parts where numerically high priority values denote low actual
 138:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   interrupt priorities, which can seem counter intuitive.  See
 139:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   https://www.freertos.org/RTOS-Cortex-M3-M4.html and the definition
 140:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   of configMAX_SYSCALL_INTERRUPT_PRIORITY on
 141:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   https://www.freertos.org/a00110.html
 142:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			3) Calling an API function from within a critical section or when
 143:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   the scheduler is suspended, or calling an API function that does
 144:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   not end in "FromISR" from an interrupt.
 145:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			4) Using a queue or semaphore before it has been initialised or
 146:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   before the scheduler has been started (are interrupts firing
 147:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			   before vTaskStartScheduler() has been called?).
 148:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		**********************************************************************/
 149:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 150:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		for( pxIterator = ( ListItem_t * ) &( pxList->xListEnd ); pxIterator->pxNext->xItemValue <= xValu
 169              		.loc 1 150 3 is_stmt 1 view .LVU51
 170              		.loc 1 150 19 is_stmt 0 view .LVU52
 171 000a 00F10803 		add	r3, r0, #8
 172              	.LVL7:
 173              	.L7:
 174              		.loc 1 150 92 is_stmt 1 discriminator 1 view .LVU53
 175 000e 1C46     		mov	r4, r3
 176              		.loc 1 150 71 is_stmt 0 discriminator 1 view .LVU54
 177 0010 5B68     		ldr	r3, [r3, #4]
 178              	.LVL8:
 179              		.loc 1 150 79 discriminator 1 view .LVU55
 180 0012 1A68     		ldr	r2, [r3]
 181              		.loc 1 150 92 discriminator 1 view .LVU56
 182 0014 AA42     		cmp	r2, r5
 183 0016 FAD9     		bls	.L7
 184              	.L6:
 151:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		{
 152:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			/* There is nothing to do here, just iterating to the wanted
 153:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 			insertion position. */
 154:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		}
 155:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 156:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 157:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxNext = pxIterator->pxNext;
 185              		.loc 1 157 2 is_stmt 1 view .LVU57
 186              		.loc 1 157 36 is_stmt 0 view .LVU58
 187 0018 6368     		ldr	r3, [r4, #4]
 188              		.loc 1 157 24 view .LVU59
 189 001a 4B60     		str	r3, [r1, #4]
 158:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxNext->pxPrevious = pxNewListItem;
 190              		.loc 1 158 2 is_stmt 1 view .LVU60
 191              		.loc 1 158 36 is_stmt 0 view .LVU61
ARM GAS  /tmp/ccUpFPQp.s 			page 7


 192 001c 9960     		str	r1, [r3, #8]
 159:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxPrevious = pxIterator;
 193              		.loc 1 159 2 is_stmt 1 view .LVU62
 194              		.loc 1 159 28 is_stmt 0 view .LVU63
 195 001e 8C60     		str	r4, [r1, #8]
 160:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxIterator->pxNext = pxNewListItem;
 196              		.loc 1 160 2 is_stmt 1 view .LVU64
 197              		.loc 1 160 21 is_stmt 0 view .LVU65
 198 0020 6160     		str	r1, [r4, #4]
 161:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 162:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Remember which list the item is in.  This allows fast removal of the
 163:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	item later. */
 164:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxNewListItem->pxContainer = pxList;
 199              		.loc 1 164 2 is_stmt 1 view .LVU66
 200              		.loc 1 164 29 is_stmt 0 view .LVU67
 201 0022 0861     		str	r0, [r1, #16]
 165:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 166:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	( pxList->uxNumberOfItems )++;
 202              		.loc 1 166 2 is_stmt 1 view .LVU68
 203              		.loc 1 166 10 is_stmt 0 view .LVU69
 204 0024 0368     		ldr	r3, [r0]
 205              		.loc 1 166 29 view .LVU70
 206 0026 0133     		adds	r3, r3, #1
 207 0028 0360     		str	r3, [r0]
 167:Middlewares/Third_Party/FreeRTOS/Source/list.c **** }
 208              		.loc 1 167 1 view .LVU71
 209 002a 30BC     		pop	{r4, r5}
 210              	.LCFI1:
 211              		.cfi_remember_state
 212              		.cfi_restore 5
 213              		.cfi_restore 4
 214              		.cfi_def_cfa_offset 0
 215              	.LVL9:
 216              		.loc 1 167 1 view .LVU72
 217 002c 7047     		bx	lr
 218              	.LVL10:
 219              	.L9:
 220              	.LCFI2:
 221              		.cfi_restore_state
 124:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 222              		.loc 1 124 3 is_stmt 1 view .LVU73
 124:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 223              		.loc 1 124 14 is_stmt 0 view .LVU74
 224 002e 0469     		ldr	r4, [r0, #16]
 225              	.LVL11:
 124:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 226              		.loc 1 124 14 view .LVU75
 227 0030 F2E7     		b	.L6
 228              		.cfi_endproc
 229              	.LFE7:
 231              		.section	.text.uxListRemove,"ax",%progbits
 232              		.align	1
 233              		.global	uxListRemove
 234              		.syntax unified
 235              		.thumb
 236              		.thumb_func
 238              	uxListRemove:
ARM GAS  /tmp/ccUpFPQp.s 			page 8


 239              	.LVL12:
 240              	.LFB8:
 168:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /*-----------------------------------------------------------*/
 169:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 170:Middlewares/Third_Party/FreeRTOS/Source/list.c **** UBaseType_t uxListRemove( ListItem_t * const pxItemToRemove )
 171:Middlewares/Third_Party/FreeRTOS/Source/list.c **** {
 241              		.loc 1 171 1 is_stmt 1 view -0
 242              		.cfi_startproc
 243              		@ args = 0, pretend = 0, frame = 0
 244              		@ frame_needed = 0, uses_anonymous_args = 0
 245              		@ link register save eliminated.
 172:Middlewares/Third_Party/FreeRTOS/Source/list.c **** /* The list item knows which list it is in.  Obtain the list from the list
 173:Middlewares/Third_Party/FreeRTOS/Source/list.c **** item. */
 174:Middlewares/Third_Party/FreeRTOS/Source/list.c **** List_t * const pxList = pxItemToRemove->pxContainer;
 246              		.loc 1 174 1 view .LVU77
 247              		.loc 1 174 16 is_stmt 0 view .LVU78
 248 0000 0369     		ldr	r3, [r0, #16]
 249              	.LVL13:
 175:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 176:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxItemToRemove->pxNext->pxPrevious = pxItemToRemove->pxPrevious;
 250              		.loc 1 176 2 is_stmt 1 view .LVU79
 251              		.loc 1 176 16 is_stmt 0 view .LVU80
 252 0002 4168     		ldr	r1, [r0, #4]
 253              		.loc 1 176 53 view .LVU81
 254 0004 8268     		ldr	r2, [r0, #8]
 255              		.loc 1 176 37 view .LVU82
 256 0006 8A60     		str	r2, [r1, #8]
 177:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxItemToRemove->pxPrevious->pxNext = pxItemToRemove->pxNext;
 257              		.loc 1 177 2 is_stmt 1 view .LVU83
 258              		.loc 1 177 53 is_stmt 0 view .LVU84
 259 0008 4168     		ldr	r1, [r0, #4]
 260              		.loc 1 177 37 view .LVU85
 261 000a 5160     		str	r1, [r2, #4]
 178:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 179:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Only used during decision coverage testing. */
 180:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	mtCOVERAGE_TEST_DELAY();
 262              		.loc 1 180 25 is_stmt 1 view .LVU86
 181:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 182:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	/* Make sure the index is left pointing to a valid item. */
 183:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	if( pxList->pxIndex == pxItemToRemove )
 263              		.loc 1 183 2 view .LVU87
 264              		.loc 1 183 12 is_stmt 0 view .LVU88
 265 000c 5A68     		ldr	r2, [r3, #4]
 266              		.loc 1 183 4 view .LVU89
 267 000e 8242     		cmp	r2, r0
 268 0010 06D0     		beq	.L12
 269              	.L11:
 184:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	{
 185:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		pxList->pxIndex = pxItemToRemove->pxPrevious;
 186:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 187:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	else
 188:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	{
 189:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 		mtCOVERAGE_TEST_MARKER();
 270              		.loc 1 189 27 is_stmt 1 view .LVU90
 190:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 191:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 192:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	pxItemToRemove->pxContainer = NULL;
ARM GAS  /tmp/ccUpFPQp.s 			page 9


 271              		.loc 1 192 2 view .LVU91
 272              		.loc 1 192 30 is_stmt 0 view .LVU92
 273 0012 0022     		movs	r2, #0
 274 0014 0261     		str	r2, [r0, #16]
 193:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	( pxList->uxNumberOfItems )--;
 275              		.loc 1 193 2 is_stmt 1 view .LVU93
 276              		.loc 1 193 10 is_stmt 0 view .LVU94
 277 0016 1A68     		ldr	r2, [r3]
 278              		.loc 1 193 29 view .LVU95
 279 0018 013A     		subs	r2, r2, #1
 280 001a 1A60     		str	r2, [r3]
 194:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	return pxList->uxNumberOfItems;
 281              		.loc 1 195 2 is_stmt 1 view .LVU96
 282              		.loc 1 195 15 is_stmt 0 view .LVU97
 283 001c 1868     		ldr	r0, [r3]
 284              	.LVL14:
 196:Middlewares/Third_Party/FreeRTOS/Source/list.c **** }
 285              		.loc 1 196 1 view .LVU98
 286 001e 7047     		bx	lr
 287              	.LVL15:
 288              	.L12:
 185:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 289              		.loc 1 185 3 is_stmt 1 view .LVU99
 185:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 290              		.loc 1 185 35 is_stmt 0 view .LVU100
 291 0020 8268     		ldr	r2, [r0, #8]
 185:Middlewares/Third_Party/FreeRTOS/Source/list.c **** 	}
 292              		.loc 1 185 19 view .LVU101
 293 0022 5A60     		str	r2, [r3, #4]
 294 0024 F5E7     		b	.L11
 295              		.cfi_endproc
 296              	.LFE8:
 298              		.text
 299              	.Letext0:
 300              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 301              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 302              		.file 4 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
 303              		.file 5 "Middlewares/Third_Party/FreeRTOS/Source/include/list.h"
ARM GAS  /tmp/ccUpFPQp.s 			page 10


DEFINED SYMBOLS
                            *ABS*:00000000 list.c
     /tmp/ccUpFPQp.s:20     .text.vListInitialise:00000000 $t
     /tmp/ccUpFPQp.s:26     .text.vListInitialise:00000000 vListInitialise
     /tmp/ccUpFPQp.s:61     .text.vListInitialiseItem:00000000 $t
     /tmp/ccUpFPQp.s:67     .text.vListInitialiseItem:00000000 vListInitialiseItem
     /tmp/ccUpFPQp.s:87     .text.vListInsertEnd:00000000 $t
     /tmp/ccUpFPQp.s:93     .text.vListInsertEnd:00000000 vListInsertEnd
     /tmp/ccUpFPQp.s:138    .text.vListInsert:00000000 $t
     /tmp/ccUpFPQp.s:144    .text.vListInsert:00000000 vListInsert
     /tmp/ccUpFPQp.s:232    .text.uxListRemove:00000000 $t
     /tmp/ccUpFPQp.s:238    .text.uxListRemove:00000000 uxListRemove

NO UNDEFINED SYMBOLS
