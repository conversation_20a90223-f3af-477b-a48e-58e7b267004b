ARM GAS  /tmp/ccaXFjiW.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_it.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/stm32h7xx_it.c"
  19              		.section	.text.NMI_Handler,"ax",%progbits
  20              		.align	1
  21              		.global	NMI_Handler
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	NMI_Handler:
  27              	.LFB144:
   1:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN Header */
   2:Core/Src/stm32h7xx_it.c **** /**
   3:Core/Src/stm32h7xx_it.c ****   ******************************************************************************
   4:Core/Src/stm32h7xx_it.c ****   * @file    stm32h7xx_it.c
   5:Core/Src/stm32h7xx_it.c ****   * @brief   Interrupt Service Routines.
   6:Core/Src/stm32h7xx_it.c ****   ******************************************************************************
   7:Core/Src/stm32h7xx_it.c ****   * @attention
   8:Core/Src/stm32h7xx_it.c ****   *
   9:Core/Src/stm32h7xx_it.c ****   * Copyright (c) 2025 STMicroelectronics.
  10:Core/Src/stm32h7xx_it.c ****   * All rights reserved.
  11:Core/Src/stm32h7xx_it.c ****   *
  12:Core/Src/stm32h7xx_it.c ****   * This software is licensed under terms that can be found in the LICENSE file
  13:Core/Src/stm32h7xx_it.c ****   * in the root directory of this software component.
  14:Core/Src/stm32h7xx_it.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  15:Core/Src/stm32h7xx_it.c ****   *
  16:Core/Src/stm32h7xx_it.c ****   ******************************************************************************
  17:Core/Src/stm32h7xx_it.c ****   */
  18:Core/Src/stm32h7xx_it.c **** /* USER CODE END Header */
  19:Core/Src/stm32h7xx_it.c **** 
  20:Core/Src/stm32h7xx_it.c **** /* Includes ------------------------------------------------------------------*/
  21:Core/Src/stm32h7xx_it.c **** #include "main.h"
  22:Core/Src/stm32h7xx_it.c **** #include "stm32h7xx_it.h"
  23:Core/Src/stm32h7xx_it.c **** /* Private includes ----------------------------------------------------------*/
  24:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN Includes */
  25:Core/Src/stm32h7xx_it.c **** /* USER CODE END Includes */
  26:Core/Src/stm32h7xx_it.c **** 
  27:Core/Src/stm32h7xx_it.c **** /* Private typedef -----------------------------------------------------------*/
  28:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN TD */
  29:Core/Src/stm32h7xx_it.c **** 
  30:Core/Src/stm32h7xx_it.c **** /* USER CODE END TD */
  31:Core/Src/stm32h7xx_it.c **** 
ARM GAS  /tmp/ccaXFjiW.s 			page 2


  32:Core/Src/stm32h7xx_it.c **** /* Private define ------------------------------------------------------------*/
  33:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PD */
  34:Core/Src/stm32h7xx_it.c **** 
  35:Core/Src/stm32h7xx_it.c **** /* USER CODE END PD */
  36:Core/Src/stm32h7xx_it.c **** 
  37:Core/Src/stm32h7xx_it.c **** /* Private macro -------------------------------------------------------------*/
  38:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PM */
  39:Core/Src/stm32h7xx_it.c **** 
  40:Core/Src/stm32h7xx_it.c **** /* USER CODE END PM */
  41:Core/Src/stm32h7xx_it.c **** 
  42:Core/Src/stm32h7xx_it.c **** /* Private variables ---------------------------------------------------------*/
  43:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PV */
  44:Core/Src/stm32h7xx_it.c **** 
  45:Core/Src/stm32h7xx_it.c **** /* USER CODE END PV */
  46:Core/Src/stm32h7xx_it.c **** 
  47:Core/Src/stm32h7xx_it.c **** /* Private function prototypes -----------------------------------------------*/
  48:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN PFP */
  49:Core/Src/stm32h7xx_it.c **** 
  50:Core/Src/stm32h7xx_it.c **** /* USER CODE END PFP */
  51:Core/Src/stm32h7xx_it.c **** 
  52:Core/Src/stm32h7xx_it.c **** /* Private user code ---------------------------------------------------------*/
  53:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN 0 */
  54:Core/Src/stm32h7xx_it.c **** 
  55:Core/Src/stm32h7xx_it.c **** /* USER CODE END 0 */
  56:Core/Src/stm32h7xx_it.c **** 
  57:Core/Src/stm32h7xx_it.c **** /* External variables --------------------------------------------------------*/
  58:Core/Src/stm32h7xx_it.c **** extern TIM_HandleTypeDef htim1;
  59:Core/Src/stm32h7xx_it.c **** 
  60:Core/Src/stm32h7xx_it.c **** /* USER CODE BEGIN EV */
  61:Core/Src/stm32h7xx_it.c **** 
  62:Core/Src/stm32h7xx_it.c **** /* USER CODE END EV */
  63:Core/Src/stm32h7xx_it.c **** 
  64:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
  65:Core/Src/stm32h7xx_it.c **** /*           Cortex Processor Interruption and Exception Handlers          */
  66:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
  67:Core/Src/stm32h7xx_it.c **** /**
  68:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Non maskable interrupt.
  69:Core/Src/stm32h7xx_it.c ****   */
  70:Core/Src/stm32h7xx_it.c **** void NMI_Handler(void)
  71:Core/Src/stm32h7xx_it.c **** {
  28              		.loc 1 71 1 view -0
  29              		.cfi_startproc
  30              		@ Volatile: function does not return.
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  34              	.L2:
  72:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN NonMaskableInt_IRQn 0 */
  73:Core/Src/stm32h7xx_it.c **** 
  74:Core/Src/stm32h7xx_it.c ****   /* USER CODE END NonMaskableInt_IRQn 0 */
  75:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
  76:Core/Src/stm32h7xx_it.c ****    while (1)
  35              		.loc 1 76 4 view .LVU1
  77:Core/Src/stm32h7xx_it.c ****   {
  78:Core/Src/stm32h7xx_it.c ****   }
  36              		.loc 1 78 3 view .LVU2
  76:Core/Src/stm32h7xx_it.c ****   {
ARM GAS  /tmp/ccaXFjiW.s 			page 3


  37              		.loc 1 76 10 view .LVU3
  38 0000 FEE7     		b	.L2
  39              		.cfi_endproc
  40              	.LFE144:
  42              		.section	.text.HardFault_Handler,"ax",%progbits
  43              		.align	1
  44              		.global	HardFault_Handler
  45              		.syntax unified
  46              		.thumb
  47              		.thumb_func
  49              	HardFault_Handler:
  50              	.LFB145:
  79:Core/Src/stm32h7xx_it.c ****   /* USER CODE END NonMaskableInt_IRQn 1 */
  80:Core/Src/stm32h7xx_it.c **** }
  81:Core/Src/stm32h7xx_it.c **** 
  82:Core/Src/stm32h7xx_it.c **** /**
  83:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Hard fault interrupt.
  84:Core/Src/stm32h7xx_it.c ****   */
  85:Core/Src/stm32h7xx_it.c **** void HardFault_Handler(void)
  86:Core/Src/stm32h7xx_it.c **** {
  51              		.loc 1 86 1 view -0
  52              		.cfi_startproc
  53              		@ Volatile: function does not return.
  54              		@ args = 0, pretend = 0, frame = 0
  55              		@ frame_needed = 0, uses_anonymous_args = 0
  56              		@ link register save eliminated.
  57              	.L4:
  87:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN HardFault_IRQn 0 */
  88:Core/Src/stm32h7xx_it.c **** 
  89:Core/Src/stm32h7xx_it.c ****   /* USER CODE END HardFault_IRQn 0 */
  90:Core/Src/stm32h7xx_it.c ****   while (1)
  58              		.loc 1 90 3 view .LVU5
  91:Core/Src/stm32h7xx_it.c ****   {
  92:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_HardFault_IRQn 0 */
  93:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_HardFault_IRQn 0 */
  94:Core/Src/stm32h7xx_it.c ****   }
  59              		.loc 1 94 3 view .LVU6
  90:Core/Src/stm32h7xx_it.c ****   {
  60              		.loc 1 90 9 view .LVU7
  61 0000 FEE7     		b	.L4
  62              		.cfi_endproc
  63              	.LFE145:
  65              		.section	.text.MemManage_Handler,"ax",%progbits
  66              		.align	1
  67              		.global	MemManage_Handler
  68              		.syntax unified
  69              		.thumb
  70              		.thumb_func
  72              	MemManage_Handler:
  73              	.LFB146:
  95:Core/Src/stm32h7xx_it.c **** }
  96:Core/Src/stm32h7xx_it.c **** 
  97:Core/Src/stm32h7xx_it.c **** /**
  98:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Memory management fault.
  99:Core/Src/stm32h7xx_it.c ****   */
 100:Core/Src/stm32h7xx_it.c **** void MemManage_Handler(void)
 101:Core/Src/stm32h7xx_it.c **** {
ARM GAS  /tmp/ccaXFjiW.s 			page 4


  74              		.loc 1 101 1 view -0
  75              		.cfi_startproc
  76              		@ Volatile: function does not return.
  77              		@ args = 0, pretend = 0, frame = 0
  78              		@ frame_needed = 0, uses_anonymous_args = 0
  79              		@ link register save eliminated.
  80              	.L6:
 102:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN MemoryManagement_IRQn 0 */
 103:Core/Src/stm32h7xx_it.c **** 
 104:Core/Src/stm32h7xx_it.c ****   /* USER CODE END MemoryManagement_IRQn 0 */
 105:Core/Src/stm32h7xx_it.c ****   while (1)
  81              		.loc 1 105 3 view .LVU9
 106:Core/Src/stm32h7xx_it.c ****   {
 107:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_MemoryManagement_IRQn 0 */
 108:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_MemoryManagement_IRQn 0 */
 109:Core/Src/stm32h7xx_it.c ****   }
  82              		.loc 1 109 3 view .LVU10
 105:Core/Src/stm32h7xx_it.c ****   {
  83              		.loc 1 105 9 view .LVU11
  84 0000 FEE7     		b	.L6
  85              		.cfi_endproc
  86              	.LFE146:
  88              		.section	.text.BusFault_Handler,"ax",%progbits
  89              		.align	1
  90              		.global	BusFault_Handler
  91              		.syntax unified
  92              		.thumb
  93              		.thumb_func
  95              	BusFault_Handler:
  96              	.LFB147:
 110:Core/Src/stm32h7xx_it.c **** }
 111:Core/Src/stm32h7xx_it.c **** 
 112:Core/Src/stm32h7xx_it.c **** /**
 113:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Pre-fetch fault, memory access fault.
 114:Core/Src/stm32h7xx_it.c ****   */
 115:Core/Src/stm32h7xx_it.c **** void BusFault_Handler(void)
 116:Core/Src/stm32h7xx_it.c **** {
  97              		.loc 1 116 1 view -0
  98              		.cfi_startproc
  99              		@ Volatile: function does not return.
 100              		@ args = 0, pretend = 0, frame = 0
 101              		@ frame_needed = 0, uses_anonymous_args = 0
 102              		@ link register save eliminated.
 103              	.L8:
 117:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN BusFault_IRQn 0 */
 118:Core/Src/stm32h7xx_it.c **** 
 119:Core/Src/stm32h7xx_it.c ****   /* USER CODE END BusFault_IRQn 0 */
 120:Core/Src/stm32h7xx_it.c ****   while (1)
 104              		.loc 1 120 3 view .LVU13
 121:Core/Src/stm32h7xx_it.c ****   {
 122:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_BusFault_IRQn 0 */
 123:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_BusFault_IRQn 0 */
 124:Core/Src/stm32h7xx_it.c ****   }
 105              		.loc 1 124 3 view .LVU14
 120:Core/Src/stm32h7xx_it.c ****   {
 106              		.loc 1 120 9 view .LVU15
 107 0000 FEE7     		b	.L8
ARM GAS  /tmp/ccaXFjiW.s 			page 5


 108              		.cfi_endproc
 109              	.LFE147:
 111              		.section	.text.UsageFault_Handler,"ax",%progbits
 112              		.align	1
 113              		.global	UsageFault_Handler
 114              		.syntax unified
 115              		.thumb
 116              		.thumb_func
 118              	UsageFault_Handler:
 119              	.LFB148:
 125:Core/Src/stm32h7xx_it.c **** }
 126:Core/Src/stm32h7xx_it.c **** 
 127:Core/Src/stm32h7xx_it.c **** /**
 128:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Undefined instruction or illegal state.
 129:Core/Src/stm32h7xx_it.c ****   */
 130:Core/Src/stm32h7xx_it.c **** void UsageFault_Handler(void)
 131:Core/Src/stm32h7xx_it.c **** {
 120              		.loc 1 131 1 view -0
 121              		.cfi_startproc
 122              		@ Volatile: function does not return.
 123              		@ args = 0, pretend = 0, frame = 0
 124              		@ frame_needed = 0, uses_anonymous_args = 0
 125              		@ link register save eliminated.
 126              	.L10:
 132:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN UsageFault_IRQn 0 */
 133:Core/Src/stm32h7xx_it.c **** 
 134:Core/Src/stm32h7xx_it.c ****   /* USER CODE END UsageFault_IRQn 0 */
 135:Core/Src/stm32h7xx_it.c ****   while (1)
 127              		.loc 1 135 3 view .LVU17
 136:Core/Src/stm32h7xx_it.c ****   {
 137:Core/Src/stm32h7xx_it.c ****     /* USER CODE BEGIN W1_UsageFault_IRQn 0 */
 138:Core/Src/stm32h7xx_it.c ****     /* USER CODE END W1_UsageFault_IRQn 0 */
 139:Core/Src/stm32h7xx_it.c ****   }
 128              		.loc 1 139 3 view .LVU18
 135:Core/Src/stm32h7xx_it.c ****   {
 129              		.loc 1 135 9 view .LVU19
 130 0000 FEE7     		b	.L10
 131              		.cfi_endproc
 132              	.LFE148:
 134              		.section	.text.DebugMon_Handler,"ax",%progbits
 135              		.align	1
 136              		.global	DebugMon_Handler
 137              		.syntax unified
 138              		.thumb
 139              		.thumb_func
 141              	DebugMon_Handler:
 142              	.LFB149:
 140:Core/Src/stm32h7xx_it.c **** }
 141:Core/Src/stm32h7xx_it.c **** 
 142:Core/Src/stm32h7xx_it.c **** /**
 143:Core/Src/stm32h7xx_it.c ****   * @brief This function handles Debug monitor.
 144:Core/Src/stm32h7xx_it.c ****   */
 145:Core/Src/stm32h7xx_it.c **** void DebugMon_Handler(void)
 146:Core/Src/stm32h7xx_it.c **** {
 143              		.loc 1 146 1 view -0
 144              		.cfi_startproc
 145              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/ccaXFjiW.s 			page 6


 146              		@ frame_needed = 0, uses_anonymous_args = 0
 147              		@ link register save eliminated.
 147:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN DebugMonitor_IRQn 0 */
 148:Core/Src/stm32h7xx_it.c **** 
 149:Core/Src/stm32h7xx_it.c ****   /* USER CODE END DebugMonitor_IRQn 0 */
 150:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN DebugMonitor_IRQn 1 */
 151:Core/Src/stm32h7xx_it.c **** 
 152:Core/Src/stm32h7xx_it.c ****   /* USER CODE END DebugMonitor_IRQn 1 */
 153:Core/Src/stm32h7xx_it.c **** }
 148              		.loc 1 153 1 view .LVU21
 149 0000 7047     		bx	lr
 150              		.cfi_endproc
 151              	.LFE149:
 153              		.section	.text.TIM1_UP_IRQHandler,"ax",%progbits
 154              		.align	1
 155              		.global	TIM1_UP_IRQHandler
 156              		.syntax unified
 157              		.thumb
 158              		.thumb_func
 160              	TIM1_UP_IRQHandler:
 161              	.LFB150:
 154:Core/Src/stm32h7xx_it.c **** 
 155:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
 156:Core/Src/stm32h7xx_it.c **** /* STM32H7xx Peripheral Interrupt Handlers                                    */
 157:Core/Src/stm32h7xx_it.c **** /* Add here the Interrupt Handlers for the used peripherals.                  */
 158:Core/Src/stm32h7xx_it.c **** /* For the available peripheral interrupt handler names,                      */
 159:Core/Src/stm32h7xx_it.c **** /* please refer to the startup file (startup_stm32h7xx.s).                    */
 160:Core/Src/stm32h7xx_it.c **** /******************************************************************************/
 161:Core/Src/stm32h7xx_it.c **** 
 162:Core/Src/stm32h7xx_it.c **** /**
 163:Core/Src/stm32h7xx_it.c ****   * @brief This function handles TIM1 update interrupt.
 164:Core/Src/stm32h7xx_it.c ****   */
 165:Core/Src/stm32h7xx_it.c **** void TIM1_UP_IRQHandler(void)
 166:Core/Src/stm32h7xx_it.c **** {
 162              		.loc 1 166 1 view -0
 163              		.cfi_startproc
 164              		@ args = 0, pretend = 0, frame = 0
 165              		@ frame_needed = 0, uses_anonymous_args = 0
 166 0000 08B5     		push	{r3, lr}
 167              	.LCFI0:
 168              		.cfi_def_cfa_offset 8
 169              		.cfi_offset 3, -8
 170              		.cfi_offset 14, -4
 167:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN TIM1_UP_IRQn 0 */
 168:Core/Src/stm32h7xx_it.c **** 
 169:Core/Src/stm32h7xx_it.c ****   /* USER CODE END TIM1_UP_IRQn 0 */
 170:Core/Src/stm32h7xx_it.c ****   HAL_TIM_IRQHandler(&htim1);
 171              		.loc 1 170 3 view .LVU23
 172 0002 0248     		ldr	r0, .L14
 173 0004 FFF7FEFF 		bl	HAL_TIM_IRQHandler
 174              	.LVL0:
 171:Core/Src/stm32h7xx_it.c ****   /* USER CODE BEGIN TIM1_UP_IRQn 1 */
 172:Core/Src/stm32h7xx_it.c **** 
 173:Core/Src/stm32h7xx_it.c ****   /* USER CODE END TIM1_UP_IRQn 1 */
 174:Core/Src/stm32h7xx_it.c **** }
 175              		.loc 1 174 1 is_stmt 0 view .LVU24
 176 0008 08BD     		pop	{r3, pc}
ARM GAS  /tmp/ccaXFjiW.s 			page 7


 177              	.L15:
 178 000a 00BF     		.align	2
 179              	.L14:
 180 000c 00000000 		.word	htim1
 181              		.cfi_endproc
 182              	.LFE150:
 184              		.text
 185              	.Letext0:
 186              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 187              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 188              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 189              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 190              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h"
 191              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim.h"
ARM GAS  /tmp/ccaXFjiW.s 			page 8


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_it.c
     /tmp/ccaXFjiW.s:20     .text.NMI_Handler:00000000 $t
     /tmp/ccaXFjiW.s:26     .text.NMI_Handler:00000000 NMI_Handler
     /tmp/ccaXFjiW.s:43     .text.HardFault_Handler:00000000 $t
     /tmp/ccaXFjiW.s:49     .text.HardFault_Handler:00000000 HardFault_Handler
     /tmp/ccaXFjiW.s:66     .text.MemManage_Handler:00000000 $t
     /tmp/ccaXFjiW.s:72     .text.MemManage_Handler:00000000 MemManage_Handler
     /tmp/ccaXFjiW.s:89     .text.BusFault_Handler:00000000 $t
     /tmp/ccaXFjiW.s:95     .text.BusFault_Handler:00000000 BusFault_Handler
     /tmp/ccaXFjiW.s:112    .text.UsageFault_Handler:00000000 $t
     /tmp/ccaXFjiW.s:118    .text.UsageFault_Handler:00000000 UsageFault_Handler
     /tmp/ccaXFjiW.s:135    .text.DebugMon_Handler:00000000 $t
     /tmp/ccaXFjiW.s:141    .text.DebugMon_Handler:00000000 DebugMon_Handler
     /tmp/ccaXFjiW.s:154    .text.TIM1_UP_IRQHandler:00000000 $t
     /tmp/ccaXFjiW.s:160    .text.TIM1_UP_IRQHandler:00000000 TIM1_UP_IRQHandler
     /tmp/ccaXFjiW.s:180    .text.TIM1_UP_IRQHandler:0000000c $d

UNDEFINED SYMBOLS
HAL_TIM_IRQHandler
htim1
