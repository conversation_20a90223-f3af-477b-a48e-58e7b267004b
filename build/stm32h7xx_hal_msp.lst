ARM GAS  /tmp/cco3NW26.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_msp.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/stm32h7xx_hal_msp.c"
  19              		.section	.text.HAL_MspInit,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_MspInit
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_MspInit:
  27              	.LFB144:
   1:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN Header */
   2:Core/Src/stm32h7xx_hal_msp.c **** /**
   3:Core/Src/stm32h7xx_hal_msp.c ****   ******************************************************************************
   4:Core/Src/stm32h7xx_hal_msp.c ****   * @file         stm32h7xx_hal_msp.c
   5:Core/Src/stm32h7xx_hal_msp.c ****   * @brief        This file provides code for the MSP Initialization
   6:Core/Src/stm32h7xx_hal_msp.c ****   *               and de-Initialization codes.
   7:Core/Src/stm32h7xx_hal_msp.c ****   ******************************************************************************
   8:Core/Src/stm32h7xx_hal_msp.c ****   * @attention
   9:Core/Src/stm32h7xx_hal_msp.c ****   *
  10:Core/Src/stm32h7xx_hal_msp.c ****   * Copyright (c) 2025 STMicroelectronics.
  11:Core/Src/stm32h7xx_hal_msp.c ****   * All rights reserved.
  12:Core/Src/stm32h7xx_hal_msp.c ****   *
  13:Core/Src/stm32h7xx_hal_msp.c ****   * This software is licensed under terms that can be found in the LICENSE file
  14:Core/Src/stm32h7xx_hal_msp.c ****   * in the root directory of this software component.
  15:Core/Src/stm32h7xx_hal_msp.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  16:Core/Src/stm32h7xx_hal_msp.c ****   *
  17:Core/Src/stm32h7xx_hal_msp.c ****   ******************************************************************************
  18:Core/Src/stm32h7xx_hal_msp.c ****   */
  19:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END Header */
  20:Core/Src/stm32h7xx_hal_msp.c **** 
  21:Core/Src/stm32h7xx_hal_msp.c **** /* Includes ------------------------------------------------------------------*/
  22:Core/Src/stm32h7xx_hal_msp.c **** #include "main.h"
  23:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN Includes */
  24:Core/Src/stm32h7xx_hal_msp.c **** 
  25:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END Includes */
  26:Core/Src/stm32h7xx_hal_msp.c **** 
  27:Core/Src/stm32h7xx_hal_msp.c **** /* Private typedef -----------------------------------------------------------*/
  28:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN TD */
  29:Core/Src/stm32h7xx_hal_msp.c **** 
  30:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END TD */
  31:Core/Src/stm32h7xx_hal_msp.c **** 
ARM GAS  /tmp/cco3NW26.s 			page 2


  32:Core/Src/stm32h7xx_hal_msp.c **** /* Private define ------------------------------------------------------------*/
  33:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN Define */
  34:Core/Src/stm32h7xx_hal_msp.c **** 
  35:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END Define */
  36:Core/Src/stm32h7xx_hal_msp.c **** 
  37:Core/Src/stm32h7xx_hal_msp.c **** /* Private macro -------------------------------------------------------------*/
  38:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN Macro */
  39:Core/Src/stm32h7xx_hal_msp.c **** 
  40:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END Macro */
  41:Core/Src/stm32h7xx_hal_msp.c **** 
  42:Core/Src/stm32h7xx_hal_msp.c **** /* Private variables ---------------------------------------------------------*/
  43:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN PV */
  44:Core/Src/stm32h7xx_hal_msp.c **** 
  45:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END PV */
  46:Core/Src/stm32h7xx_hal_msp.c **** 
  47:Core/Src/stm32h7xx_hal_msp.c **** /* Private function prototypes -----------------------------------------------*/
  48:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN PFP */
  49:Core/Src/stm32h7xx_hal_msp.c **** 
  50:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END PFP */
  51:Core/Src/stm32h7xx_hal_msp.c **** 
  52:Core/Src/stm32h7xx_hal_msp.c **** /* External functions --------------------------------------------------------*/
  53:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN ExternalFunctions */
  54:Core/Src/stm32h7xx_hal_msp.c **** 
  55:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END ExternalFunctions */
  56:Core/Src/stm32h7xx_hal_msp.c **** 
  57:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE BEGIN 0 */
  58:Core/Src/stm32h7xx_hal_msp.c **** 
  59:Core/Src/stm32h7xx_hal_msp.c **** /* USER CODE END 0 */
  60:Core/Src/stm32h7xx_hal_msp.c **** /**
  61:Core/Src/stm32h7xx_hal_msp.c ****   * Initializes the Global MSP.
  62:Core/Src/stm32h7xx_hal_msp.c ****   */
  63:Core/Src/stm32h7xx_hal_msp.c **** void HAL_MspInit(void)
  64:Core/Src/stm32h7xx_hal_msp.c **** {
  28              		.loc 1 64 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 8
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32 0000 00B5     		push	{lr}
  33              	.LCFI0:
  34              		.cfi_def_cfa_offset 4
  35              		.cfi_offset 14, -4
  36 0002 83B0     		sub	sp, sp, #12
  37              	.LCFI1:
  38              		.cfi_def_cfa_offset 16
  65:Core/Src/stm32h7xx_hal_msp.c **** 
  66:Core/Src/stm32h7xx_hal_msp.c ****   /* USER CODE BEGIN MspInit 0 */
  67:Core/Src/stm32h7xx_hal_msp.c **** 
  68:Core/Src/stm32h7xx_hal_msp.c ****   /* USER CODE END MspInit 0 */
  69:Core/Src/stm32h7xx_hal_msp.c **** 
  70:Core/Src/stm32h7xx_hal_msp.c ****   __HAL_RCC_SYSCFG_CLK_ENABLE();
  39              		.loc 1 70 3 view .LVU1
  40              	.LBB2:
  41              		.loc 1 70 3 view .LVU2
  42              		.loc 1 70 3 view .LVU3
  43 0004 0A4B     		ldr	r3, .L3
  44 0006 D3F8F420 		ldr	r2, [r3, #244]
  45 000a 42F00202 		orr	r2, r2, #2
ARM GAS  /tmp/cco3NW26.s 			page 3


  46 000e C3F8F420 		str	r2, [r3, #244]
  47              		.loc 1 70 3 view .LVU4
  48 0012 D3F8F430 		ldr	r3, [r3, #244]
  49 0016 03F00203 		and	r3, r3, #2
  50 001a 0193     		str	r3, [sp, #4]
  51              		.loc 1 70 3 view .LVU5
  52 001c 019B     		ldr	r3, [sp, #4]
  53              	.LBE2:
  54              		.loc 1 70 3 view .LVU6
  71:Core/Src/stm32h7xx_hal_msp.c **** 
  72:Core/Src/stm32h7xx_hal_msp.c ****   /* System interrupt init*/
  73:Core/Src/stm32h7xx_hal_msp.c ****   /* PendSV_IRQn interrupt configuration */
  74:Core/Src/stm32h7xx_hal_msp.c ****   HAL_NVIC_SetPriority(PendSV_IRQn, 15, 0);
  55              		.loc 1 74 3 view .LVU7
  56 001e 0022     		movs	r2, #0
  57 0020 0F21     		movs	r1, #15
  58 0022 6FF00100 		mvn	r0, #1
  59 0026 FFF7FEFF 		bl	HAL_NVIC_SetPriority
  60              	.LVL0:
  75:Core/Src/stm32h7xx_hal_msp.c **** 
  76:Core/Src/stm32h7xx_hal_msp.c ****   /* USER CODE BEGIN MspInit 1 */
  77:Core/Src/stm32h7xx_hal_msp.c **** 
  78:Core/Src/stm32h7xx_hal_msp.c ****   /* USER CODE END MspInit 1 */
  79:Core/Src/stm32h7xx_hal_msp.c **** }
  61              		.loc 1 79 1 is_stmt 0 view .LVU8
  62 002a 03B0     		add	sp, sp, #12
  63              	.LCFI2:
  64              		.cfi_def_cfa_offset 4
  65              		@ sp needed
  66 002c 5DF804FB 		ldr	pc, [sp], #4
  67              	.L4:
  68              		.align	2
  69              	.L3:
  70 0030 00440258 		.word	1476543488
  71              		.cfi_endproc
  72              	.LFE144:
  74              		.text
  75              	.Letext0:
  76              		.file 2 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
  77              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
  78              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
  79              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h"
ARM GAS  /tmp/cco3NW26.s 			page 4


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_msp.c
     /tmp/cco3NW26.s:20     .text.HAL_MspInit:00000000 $t
     /tmp/cco3NW26.s:26     .text.HAL_MspInit:00000000 HAL_MspInit
     /tmp/cco3NW26.s:70     .text.HAL_MspInit:00000030 $d

UNDEFINED SYMBOLS
HAL_NVIC_SetPriority
