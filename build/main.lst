ARM GAS  /tmp/cc9Pn31k.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"main.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/main.c"
  19              		.section	.text.HAL_TIM_PeriodElapsedCallback,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_TIM_PeriodElapsedCallback
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_TIM_PeriodElapsedCallback:
  27              	.LVL0:
  28              	.LFB176:
   1:Core/Src/main.c **** /* USER CODE BEGIN Header */
   2:Core/Src/main.c **** /**
   3:Core/Src/main.c ****   ******************************************************************************
   4:Core/Src/main.c ****   * @file           : main.c
   5:Core/Src/main.c ****   * @brief          : Main program body
   6:Core/Src/main.c ****   ******************************************************************************
   7:Core/Src/main.c ****   * @attention
   8:Core/Src/main.c ****   *
   9:Core/Src/main.c ****   * Copyright (c) 2025 STMicroelectronics.
  10:Core/Src/main.c ****   * All rights reserved.
  11:Core/Src/main.c ****   *
  12:Core/Src/main.c ****   * This software is licensed under terms that can be found in the LICENSE file
  13:Core/Src/main.c ****   * in the root directory of this software component.
  14:Core/Src/main.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  15:Core/Src/main.c ****   *
  16:Core/Src/main.c ****   ******************************************************************************
  17:Core/Src/main.c ****   */
  18:Core/Src/main.c **** /* USER CODE END Header */
  19:Core/Src/main.c **** /* Includes ------------------------------------------------------------------*/
  20:Core/Src/main.c **** #include "main.h"
  21:Core/Src/main.c **** #include "cmsis_os.h"
  22:Core/Src/main.c **** #include "lwip.h"
  23:Core/Src/main.c **** #include "gpio.h"
  24:Core/Src/main.c **** 
  25:Core/Src/main.c **** /* Private includes ----------------------------------------------------------*/
  26:Core/Src/main.c **** /* USER CODE BEGIN Includes */
  27:Core/Src/main.c **** /* USER CODE END Includes */
  28:Core/Src/main.c **** 
  29:Core/Src/main.c **** /* Private typedef -----------------------------------------------------------*/
  30:Core/Src/main.c **** /* USER CODE BEGIN PTD */
ARM GAS  /tmp/cc9Pn31k.s 			page 2


  31:Core/Src/main.c **** 
  32:Core/Src/main.c **** /* USER CODE END PTD */
  33:Core/Src/main.c **** 
  34:Core/Src/main.c **** /* Private define ------------------------------------------------------------*/
  35:Core/Src/main.c **** /* USER CODE BEGIN PD */
  36:Core/Src/main.c **** 
  37:Core/Src/main.c **** /* USER CODE END PD */
  38:Core/Src/main.c **** 
  39:Core/Src/main.c **** /* Private macro -------------------------------------------------------------*/
  40:Core/Src/main.c **** /* USER CODE BEGIN PM */
  41:Core/Src/main.c **** 
  42:Core/Src/main.c **** /* USER CODE END PM */
  43:Core/Src/main.c **** 
  44:Core/Src/main.c **** /* Private variables ---------------------------------------------------------*/
  45:Core/Src/main.c **** 
  46:Core/Src/main.c **** /* USER CODE BEGIN PV */
  47:Core/Src/main.c **** 
  48:Core/Src/main.c **** /* USER CODE END PV */
  49:Core/Src/main.c **** 
  50:Core/Src/main.c **** /* Private function prototypes -----------------------------------------------*/
  51:Core/Src/main.c **** void SystemClock_Config(void);
  52:Core/Src/main.c **** //static void MPU_Config(void);
  53:Core/Src/main.c **** void MX_FREERTOS_Init(void);
  54:Core/Src/main.c **** /* USER CODE BEGIN PFP */
  55:Core/Src/main.c **** 
  56:Core/Src/main.c **** /* USER CODE END PFP */
  57:Core/Src/main.c **** 
  58:Core/Src/main.c **** /* Private user code ---------------------------------------------------------*/
  59:Core/Src/main.c **** /* USER CODE BEGIN 0 */
  60:Core/Src/main.c **** 
  61:Core/Src/main.c **** /* USER CODE END 0 */
  62:Core/Src/main.c **** 
  63:Core/Src/main.c **** /**
  64:Core/Src/main.c ****   * @brief  The application entry point.
  65:Core/Src/main.c ****   * @retval int
  66:Core/Src/main.c ****   */
  67:Core/Src/main.c **** int main(void)
  68:Core/Src/main.c **** {
  69:Core/Src/main.c **** 
  70:Core/Src/main.c ****   /* USER CODE BEGIN 1 */
  71:Core/Src/main.c **** 
  72:Core/Src/main.c ****   /* USER CODE END 1 */
  73:Core/Src/main.c **** 
  74:Core/Src/main.c ****   /* MPU Configuration--------------------------------------------------------*/
  75:Core/Src/main.c ****   //MPU_Config();
  76:Core/Src/main.c **** 
  77:Core/Src/main.c ****   /* MCU Configuration--------------------------------------------------------*/
  78:Core/Src/main.c **** 
  79:Core/Src/main.c ****   /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  80:Core/Src/main.c ****   HAL_Init();
  81:Core/Src/main.c **** 
  82:Core/Src/main.c ****   /* USER CODE BEGIN Init */
  83:Core/Src/main.c **** 
  84:Core/Src/main.c ****   /* USER CODE END Init */
  85:Core/Src/main.c **** 
  86:Core/Src/main.c ****   /* Configure the system clock */
  87:Core/Src/main.c ****   SystemClock_Config();
ARM GAS  /tmp/cc9Pn31k.s 			page 3


  88:Core/Src/main.c **** 
  89:Core/Src/main.c ****   /* USER CODE BEGIN SysInit */
  90:Core/Src/main.c **** 
  91:Core/Src/main.c ****   /* USER CODE END SysInit */
  92:Core/Src/main.c **** 
  93:Core/Src/main.c ****   /* Initialize all configured peripherals */
  94:Core/Src/main.c ****   MX_GPIO_Init();
  95:Core/Src/main.c ****   /* USER CODE BEGIN 2 */
  96:Core/Src/main.c **** 
  97:Core/Src/main.c ****   /* USER CODE END 2 */
  98:Core/Src/main.c **** 
  99:Core/Src/main.c ****   /* Init scheduler */
 100:Core/Src/main.c ****   osKernelInitialize();  /* Call init function for freertos objects (in cmsis_os2.c) */
 101:Core/Src/main.c ****   MX_FREERTOS_Init();
 102:Core/Src/main.c **** 
 103:Core/Src/main.c ****   /* Start scheduler */
 104:Core/Src/main.c ****   osKernelStart();
 105:Core/Src/main.c **** 
 106:Core/Src/main.c ****   /* We should never get here as control is now taken by the scheduler */
 107:Core/Src/main.c **** 
 108:Core/Src/main.c ****   /* Infinite loop */
 109:Core/Src/main.c ****   /* USER CODE BEGIN WHILE */
 110:Core/Src/main.c ****   while (1)
 111:Core/Src/main.c ****   {
 112:Core/Src/main.c ****     /* USER CODE END WHILE */
 113:Core/Src/main.c **** 
 114:Core/Src/main.c ****     /* USER CODE BEGIN 3 */
 115:Core/Src/main.c ****   }
 116:Core/Src/main.c ****   /* USER CODE END 3 */
 117:Core/Src/main.c **** }
 118:Core/Src/main.c **** 
 119:Core/Src/main.c **** /**
 120:Core/Src/main.c ****   * @brief System Clock Configuration
 121:Core/Src/main.c ****   * @retval None
 122:Core/Src/main.c ****   */
 123:Core/Src/main.c **** void SystemClock_Config(void)
 124:Core/Src/main.c **** {
 125:Core/Src/main.c ****   RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 126:Core/Src/main.c ****   RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 127:Core/Src/main.c **** 
 128:Core/Src/main.c ****   /** Supply configuration update enable
 129:Core/Src/main.c ****   */
 130:Core/Src/main.c ****   HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);
 131:Core/Src/main.c **** 
 132:Core/Src/main.c ****   /** Configure the main internal regulator output voltage
 133:Core/Src/main.c ****   */
 134:Core/Src/main.c ****   __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE0);
 135:Core/Src/main.c **** 
 136:Core/Src/main.c ****   while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}
 137:Core/Src/main.c **** 
 138:Core/Src/main.c ****   /** Initializes the RCC Oscillators according to the specified parameters
 139:Core/Src/main.c ****   * in the RCC_OscInitTypeDef structure.
 140:Core/Src/main.c ****   */
 141:Core/Src/main.c ****   RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
 142:Core/Src/main.c ****   RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 143:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 144:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
ARM GAS  /tmp/cc9Pn31k.s 			page 4


 145:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLM = 8;
 146:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLN = 420;
 147:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLP = 1;
 148:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLQ = 4;
 149:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLR = 2;
 150:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_0;
 151:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOMEDIUM;
 152:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLFRACN = 0;
 153:Core/Src/main.c ****   if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 154:Core/Src/main.c ****   {
 155:Core/Src/main.c ****     Error_Handler();
 156:Core/Src/main.c ****   }
 157:Core/Src/main.c **** 
 158:Core/Src/main.c ****   /** Initializes the CPU, AHB and APB buses clocks
 159:Core/Src/main.c ****   */
 160:Core/Src/main.c ****   RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 161:Core/Src/main.c ****                               |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
 162:Core/Src/main.c ****                               |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
 163:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
 164:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
 165:Core/Src/main.c ****   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
 166:Core/Src/main.c ****   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
 167:Core/Src/main.c ****   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
 168:Core/Src/main.c ****   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
 169:Core/Src/main.c ****   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
 170:Core/Src/main.c **** 
 171:Core/Src/main.c ****   if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
 172:Core/Src/main.c ****   {
 173:Core/Src/main.c ****     Error_Handler();
 174:Core/Src/main.c ****   }
 175:Core/Src/main.c **** }
 176:Core/Src/main.c **** 
 177:Core/Src/main.c **** /* USER CODE BEGIN 4 */
 178:Core/Src/main.c **** /* USER CODE END 4 */
 179:Core/Src/main.c **** 
 180:Core/Src/main.c **** 
 181:Core/Src/main.c **** /**
 182:Core/Src/main.c ****   * @brief  Period elapsed callback in non blocking mode
 183:Core/Src/main.c ****   * @note   This function is called  when TIM1 interrupt took place, inside
 184:Core/Src/main.c ****   * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
 185:Core/Src/main.c ****   * a global variable "uwTick" used as application time base.
 186:Core/Src/main.c ****   * @param  htim : TIM handle
 187:Core/Src/main.c ****   * @retval None
 188:Core/Src/main.c ****   */
 189:Core/Src/main.c **** void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
 190:Core/Src/main.c **** {
  29              		.loc 1 190 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		.loc 1 190 1 is_stmt 0 view .LVU1
  34 0000 08B5     		push	{r3, lr}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 8
  37              		.cfi_offset 3, -8
  38              		.cfi_offset 14, -4
 191:Core/Src/main.c ****   /* USER CODE BEGIN Callback 0 */
ARM GAS  /tmp/cc9Pn31k.s 			page 5


 192:Core/Src/main.c **** 
 193:Core/Src/main.c ****   /* USER CODE END Callback 0 */
 194:Core/Src/main.c ****   if (htim->Instance == TIM1)
  39              		.loc 1 194 3 is_stmt 1 view .LVU2
  40              		.loc 1 194 11 is_stmt 0 view .LVU3
  41 0002 0268     		ldr	r2, [r0]
  42              		.loc 1 194 6 view .LVU4
  43 0004 034B     		ldr	r3, .L5
  44 0006 9A42     		cmp	r2, r3
  45 0008 00D0     		beq	.L4
  46              	.LVL1:
  47              	.L1:
 195:Core/Src/main.c ****   {
 196:Core/Src/main.c ****     HAL_IncTick();
 197:Core/Src/main.c ****   }
 198:Core/Src/main.c ****   /* USER CODE BEGIN Callback 1 */
 199:Core/Src/main.c **** 
 200:Core/Src/main.c ****   /* USER CODE END Callback 1 */
 201:Core/Src/main.c **** }
  48              		.loc 1 201 1 view .LVU5
  49 000a 08BD     		pop	{r3, pc}
  50              	.LVL2:
  51              	.L4:
 196:Core/Src/main.c ****   }
  52              		.loc 1 196 5 is_stmt 1 view .LVU6
  53 000c FFF7FEFF 		bl	HAL_IncTick
  54              	.LVL3:
  55              		.loc 1 201 1 is_stmt 0 view .LVU7
  56 0010 FBE7     		b	.L1
  57              	.L6:
  58 0012 00BF     		.align	2
  59              	.L5:
  60 0014 00000140 		.word	1073807360
  61              		.cfi_endproc
  62              	.LFE176:
  64              		.section	.text.Error_Handler,"ax",%progbits
  65              		.align	1
  66              		.global	Error_Handler
  67              		.syntax unified
  68              		.thumb
  69              		.thumb_func
  71              	Error_Handler:
  72              	.LFB177:
 202:Core/Src/main.c **** 
 203:Core/Src/main.c **** /**
 204:Core/Src/main.c ****   * @brief  This function is executed in case of error occurrence.
 205:Core/Src/main.c ****   * @retval None
 206:Core/Src/main.c ****   */
 207:Core/Src/main.c **** void Error_Handler(void)
 208:Core/Src/main.c **** {
  73              		.loc 1 208 1 is_stmt 1 view -0
  74              		.cfi_startproc
  75              		@ Volatile: function does not return.
  76              		@ args = 0, pretend = 0, frame = 0
  77              		@ frame_needed = 0, uses_anonymous_args = 0
  78              		@ link register save eliminated.
 209:Core/Src/main.c ****   /* USER CODE BEGIN Error_Handler_Debug */
ARM GAS  /tmp/cc9Pn31k.s 			page 6


 210:Core/Src/main.c ****   /* User can add his own implementation to report the HAL error return state */
 211:Core/Src/main.c ****   __disable_irq();
  79              		.loc 1 211 3 view .LVU9
  80              	.LBB4:
  81              	.LBI4:
  82              		.file 2 "Drivers/CMSIS/Include/cmsis_gcc.h"
   1:Drivers/CMSIS/Include/cmsis_gcc.h **** /**************************************************************************//**
   2:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @file     cmsis_gcc.h
   3:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @brief    CMSIS compiler GCC header file
   4:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @version  V5.2.0
   5:Drivers/CMSIS/Include/cmsis_gcc.h ****  * @date     08. May 2019
   6:Drivers/CMSIS/Include/cmsis_gcc.h ****  ******************************************************************************/
   7:Drivers/CMSIS/Include/cmsis_gcc.h **** /*
   8:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Copyright (c) 2009-2019 Arm Limited. All rights reserved.
   9:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  10:Drivers/CMSIS/Include/cmsis_gcc.h ****  * SPDX-License-Identifier: Apache-2.0
  11:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  12:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Licensed under the Apache License, Version 2.0 (the License); you may
  13:Drivers/CMSIS/Include/cmsis_gcc.h ****  * not use this file except in compliance with the License.
  14:Drivers/CMSIS/Include/cmsis_gcc.h ****  * You may obtain a copy of the License at
  15:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  16:Drivers/CMSIS/Include/cmsis_gcc.h ****  * www.apache.org/licenses/LICENSE-2.0
  17:Drivers/CMSIS/Include/cmsis_gcc.h ****  *
  18:Drivers/CMSIS/Include/cmsis_gcc.h ****  * Unless required by applicable law or agreed to in writing, software
  19:Drivers/CMSIS/Include/cmsis_gcc.h ****  * distributed under the License is distributed on an AS IS BASIS, WITHOUT
  20:Drivers/CMSIS/Include/cmsis_gcc.h ****  * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  21:Drivers/CMSIS/Include/cmsis_gcc.h ****  * See the License for the specific language governing permissions and
  22:Drivers/CMSIS/Include/cmsis_gcc.h ****  * limitations under the License.
  23:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
  24:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  25:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __CMSIS_GCC_H
  26:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __CMSIS_GCC_H
  27:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  28:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ignore some GCC warnings */
  29:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic push
  30:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wsign-conversion"
  31:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wconversion"
  32:Drivers/CMSIS/Include/cmsis_gcc.h **** #pragma GCC diagnostic ignored "-Wunused-parameter"
  33:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  34:Drivers/CMSIS/Include/cmsis_gcc.h **** /* Fallback for __has_builtin */
  35:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __has_builtin
  36:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __has_builtin(x) (0)
  37:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  38:Drivers/CMSIS/Include/cmsis_gcc.h **** 
  39:Drivers/CMSIS/Include/cmsis_gcc.h **** /* CMSIS compiler specific defines */
  40:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ASM
  41:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ASM                                  __asm
  42:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  43:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __INLINE
  44:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __INLINE                               inline
  45:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  46:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_INLINE
  47:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_INLINE                        static inline
  48:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  49:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __STATIC_FORCEINLINE                 
  50:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __STATIC_FORCEINLINE                   __attribute__((always_inline)) static inline
  51:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif                                           
ARM GAS  /tmp/cc9Pn31k.s 			page 7


  52:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __NO_RETURN
  53:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __NO_RETURN                            __attribute__((__noreturn__))
  54:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  55:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __USED
  56:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __USED                                 __attribute__((used))
  57:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  58:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __WEAK
  59:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __WEAK                                 __attribute__((weak))
  60:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  61:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED
  62:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED                               __attribute__((packed, aligned(1)))
  63:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  64:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_STRUCT
  65:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_STRUCT                        struct __attribute__((packed, aligned(1)))
  66:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  67:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __PACKED_UNION
  68:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __PACKED_UNION                         union __attribute__((packed, aligned(1)))
  69:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  70:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32        /* deprecated */
  71:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  72:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  73:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  74:Drivers/CMSIS/Include/cmsis_gcc.h ****   struct __attribute__((packed)) T_UINT32 { uint32_t v; };
  75:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  76:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32(x)                  (((struct T_UINT32 *)(x))->v)
  77:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  78:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_WRITE
  79:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  80:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  81:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  82:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_WRITE { uint16_t v; };
  83:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  84:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_WRITE(addr, val)    (void)((((struct T_UINT16_WRITE *)(void *)(addr))-
  85:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  86:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT16_READ
  87:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  88:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  89:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  90:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT16_READ { uint16_t v; };
  91:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
  92:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT16_READ(addr)          (((const struct T_UINT16_READ *)(const void *)(add
  93:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
  94:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_WRITE
  95:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
  96:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
  97:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
  98:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_WRITE { uint32_t v; };
  99:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 100:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_WRITE(addr, val)    (void)((((struct T_UINT32_WRITE *)(void *)(addr))-
 101:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 102:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __UNALIGNED_UINT32_READ
 103:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic push
 104:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wpacked"
 105:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic ignored "-Wattributes"
 106:Drivers/CMSIS/Include/cmsis_gcc.h ****   __PACKED_STRUCT T_UINT32_READ { uint32_t v; };
 107:Drivers/CMSIS/Include/cmsis_gcc.h ****   #pragma GCC diagnostic pop
 108:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __UNALIGNED_UINT32_READ(addr)          (((const struct T_UINT32_READ *)(const void *)(add
ARM GAS  /tmp/cc9Pn31k.s 			page 8


 109:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 110:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __ALIGNED
 111:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __ALIGNED(x)                           __attribute__((aligned(x)))
 112:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 113:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __RESTRICT
 114:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __RESTRICT                             __restrict
 115:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 116:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef   __COMPILER_BARRIER
 117:Drivers/CMSIS/Include/cmsis_gcc.h ****   #define __COMPILER_BARRIER()                   __ASM volatile("":::"memory")
 118:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 119:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 120:Drivers/CMSIS/Include/cmsis_gcc.h **** /* #########################  Startup and Lowlevel Init  ######################## */
 121:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 122:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __PROGRAM_START
 123:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 124:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 125:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Initializes data and bss sections
 126:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details This default implementations initialized all data and additional bss
 127:Drivers/CMSIS/Include/cmsis_gcc.h ****            sections relying on .copy.table and .zero.table specified properly
 128:Drivers/CMSIS/Include/cmsis_gcc.h ****            in the used linker script.
 129:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 130:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 131:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE __NO_RETURN void __cmsis_start(void)
 132:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 133:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern void _start(void) __NO_RETURN;
 134:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 135:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 136:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t const* src;
 137:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 138:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 139:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __copy_table_t;
 140:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 141:Drivers/CMSIS/Include/cmsis_gcc.h ****   typedef struct {
 142:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t* dest;
 143:Drivers/CMSIS/Include/cmsis_gcc.h ****     uint32_t  wlen;
 144:Drivers/CMSIS/Include/cmsis_gcc.h ****   } __zero_table_t;
 145:Drivers/CMSIS/Include/cmsis_gcc.h ****   
 146:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_start__;
 147:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __copy_table_t __copy_table_end__;
 148:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_start__;
 149:Drivers/CMSIS/Include/cmsis_gcc.h ****   extern const __zero_table_t __zero_table_end__;
 150:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 151:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__copy_table_t const* pTable = &__copy_table_start__; pTable < &__copy_table_end__; ++pTable
 152:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 153:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = pTable->src[i];
 154:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 155:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 156:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 157:Drivers/CMSIS/Include/cmsis_gcc.h ****   for (__zero_table_t const* pTable = &__zero_table_start__; pTable < &__zero_table_end__; ++pTable
 158:Drivers/CMSIS/Include/cmsis_gcc.h ****     for(uint32_t i=0u; i<pTable->wlen; ++i) {
 159:Drivers/CMSIS/Include/cmsis_gcc.h ****       pTable->dest[i] = 0u;
 160:Drivers/CMSIS/Include/cmsis_gcc.h ****     }
 161:Drivers/CMSIS/Include/cmsis_gcc.h ****   }
 162:Drivers/CMSIS/Include/cmsis_gcc.h ****  
 163:Drivers/CMSIS/Include/cmsis_gcc.h ****   _start();
 164:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 165:Drivers/CMSIS/Include/cmsis_gcc.h ****   
ARM GAS  /tmp/cc9Pn31k.s 			page 9


 166:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __PROGRAM_START           __cmsis_start
 167:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 168:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 169:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __INITIAL_SP
 170:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __INITIAL_SP              __StackTop
 171:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 172:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 173:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __STACK_LIMIT
 174:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __STACK_LIMIT             __StackLimit
 175:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 176:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 177:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE
 178:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE            __Vectors
 179:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 180:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 181:Drivers/CMSIS/Include/cmsis_gcc.h **** #ifndef __VECTOR_TABLE_ATTRIBUTE
 182:Drivers/CMSIS/Include/cmsis_gcc.h **** #define __VECTOR_TABLE_ATTRIBUTE  __attribute((used, section(".vectors")))
 183:Drivers/CMSIS/Include/cmsis_gcc.h **** #endif
 184:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 185:Drivers/CMSIS/Include/cmsis_gcc.h **** /* ###########################  Core Function Access  ########################### */
 186:Drivers/CMSIS/Include/cmsis_gcc.h **** /** \ingroup  CMSIS_Core_FunctionInterface
 187:Drivers/CMSIS/Include/cmsis_gcc.h ****     \defgroup CMSIS_Core_RegAccFunctions CMSIS Core Register Access Functions
 188:Drivers/CMSIS/Include/cmsis_gcc.h ****   @{
 189:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 190:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 191:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 192:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Enable IRQ Interrupts
 193:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Enables IRQ interrupts by clearing the I-bit in the CPSR.
 194:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 195:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 196:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __enable_irq(void)
 197:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 198:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsie i" : : : "memory");
 199:Drivers/CMSIS/Include/cmsis_gcc.h **** }
 200:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 201:Drivers/CMSIS/Include/cmsis_gcc.h **** 
 202:Drivers/CMSIS/Include/cmsis_gcc.h **** /**
 203:Drivers/CMSIS/Include/cmsis_gcc.h ****   \brief   Disable IRQ Interrupts
 204:Drivers/CMSIS/Include/cmsis_gcc.h ****   \details Disables IRQ interrupts by setting the I-bit in the CPSR.
 205:Drivers/CMSIS/Include/cmsis_gcc.h ****            Can only be executed in Privileged modes.
 206:Drivers/CMSIS/Include/cmsis_gcc.h ****  */
 207:Drivers/CMSIS/Include/cmsis_gcc.h **** __STATIC_FORCEINLINE void __disable_irq(void)
  83              		.loc 2 207 27 view .LVU10
  84              	.LBB5:
 208:Drivers/CMSIS/Include/cmsis_gcc.h **** {
 209:Drivers/CMSIS/Include/cmsis_gcc.h ****   __ASM volatile ("cpsid i" : : : "memory");
  85              		.loc 2 209 3 view .LVU11
  86              		.syntax unified
  87              	@ 209 "Drivers/CMSIS/Include/cmsis_gcc.h" 1
  88 0000 72B6     		cpsid i
  89              	@ 0 "" 2
  90              		.thumb
  91              		.syntax unified
  92              	.L8:
  93              	.LBE5:
  94              	.LBE4:
 212:Core/Src/main.c ****   while (1)
ARM GAS  /tmp/cc9Pn31k.s 			page 10


  95              		.loc 1 212 3 view .LVU12
 213:Core/Src/main.c ****   {
 214:Core/Src/main.c ****   }
  96              		.loc 1 214 3 view .LVU13
 212:Core/Src/main.c ****   while (1)
  97              		.loc 1 212 9 view .LVU14
  98 0002 FEE7     		b	.L8
  99              		.cfi_endproc
 100              	.LFE177:
 102              		.section	.text.SystemClock_Config,"ax",%progbits
 103              		.align	1
 104              		.global	SystemClock_Config
 105              		.syntax unified
 106              		.thumb
 107              		.thumb_func
 109              	SystemClock_Config:
 110              	.LFB175:
 124:Core/Src/main.c ****   RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 111              		.loc 1 124 1 view -0
 112              		.cfi_startproc
 113              		@ args = 0, pretend = 0, frame = 112
 114              		@ frame_needed = 0, uses_anonymous_args = 0
 115 0000 00B5     		push	{lr}
 116              	.LCFI1:
 117              		.cfi_def_cfa_offset 4
 118              		.cfi_offset 14, -4
 119 0002 9DB0     		sub	sp, sp, #116
 120              	.LCFI2:
 121              		.cfi_def_cfa_offset 120
 125:Core/Src/main.c ****   RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 122              		.loc 1 125 3 view .LVU16
 125:Core/Src/main.c ****   RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 123              		.loc 1 125 22 is_stmt 0 view .LVU17
 124 0004 4C22     		movs	r2, #76
 125 0006 0021     		movs	r1, #0
 126 0008 09A8     		add	r0, sp, #36
 127 000a FFF7FEFF 		bl	memset
 128              	.LVL4:
 126:Core/Src/main.c **** 
 129              		.loc 1 126 3 is_stmt 1 view .LVU18
 126:Core/Src/main.c **** 
 130              		.loc 1 126 22 is_stmt 0 view .LVU19
 131 000e 2022     		movs	r2, #32
 132 0010 0021     		movs	r1, #0
 133 0012 01A8     		add	r0, sp, #4
 134 0014 FFF7FEFF 		bl	memset
 135              	.LVL5:
 130:Core/Src/main.c **** 
 136              		.loc 1 130 3 is_stmt 1 view .LVU20
 137 0018 0220     		movs	r0, #2
 138 001a FFF7FEFF 		bl	HAL_PWREx_ConfigSupply
 139              	.LVL6:
 134:Core/Src/main.c **** 
 140              		.loc 1 134 3 view .LVU21
 141              	.LBB6:
 134:Core/Src/main.c **** 
 142              		.loc 1 134 3 view .LVU22
ARM GAS  /tmp/cc9Pn31k.s 			page 11


 143 001e 0023     		movs	r3, #0
 144 0020 0093     		str	r3, [sp]
 134:Core/Src/main.c **** 
 145              		.loc 1 134 3 view .LVU23
 146 0022 214B     		ldr	r3, .L16
 147 0024 9A69     		ldr	r2, [r3, #24]
 148 0026 22F44042 		bic	r2, r2, #49152
 149 002a 9A61     		str	r2, [r3, #24]
 134:Core/Src/main.c **** 
 150              		.loc 1 134 3 view .LVU24
 151 002c 9B69     		ldr	r3, [r3, #24]
 152 002e 03F44043 		and	r3, r3, #49152
 153 0032 0093     		str	r3, [sp]
 134:Core/Src/main.c **** 
 154              		.loc 1 134 3 view .LVU25
 155 0034 009B     		ldr	r3, [sp]
 156              	.LBE6:
 134:Core/Src/main.c **** 
 157              		.loc 1 134 3 view .LVU26
 136:Core/Src/main.c **** 
 158              		.loc 1 136 3 view .LVU27
 159              	.L10:
 136:Core/Src/main.c **** 
 160              		.loc 1 136 48 discriminator 1 view .LVU28
 136:Core/Src/main.c **** 
 161              		.loc 1 136 9 discriminator 1 view .LVU29
 136:Core/Src/main.c **** 
 162              		.loc 1 136 10 is_stmt 0 discriminator 1 view .LVU30
 163 0036 1C4B     		ldr	r3, .L16
 164 0038 9B69     		ldr	r3, [r3, #24]
 136:Core/Src/main.c **** 
 165              		.loc 1 136 9 discriminator 1 view .LVU31
 166 003a 13F4005F 		tst	r3, #8192
 167 003e FAD0     		beq	.L10
 141:Core/Src/main.c ****   RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 168              		.loc 1 141 3 is_stmt 1 view .LVU32
 141:Core/Src/main.c ****   RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 169              		.loc 1 141 36 is_stmt 0 view .LVU33
 170 0040 0122     		movs	r2, #1
 171 0042 0992     		str	r2, [sp, #36]
 142:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 172              		.loc 1 142 3 is_stmt 1 view .LVU34
 142:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 173              		.loc 1 142 30 is_stmt 0 view .LVU35
 174 0044 4FF48033 		mov	r3, #65536
 175 0048 0A93     		str	r3, [sp, #40]
 143:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 176              		.loc 1 143 3 is_stmt 1 view .LVU36
 143:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 177              		.loc 1 143 34 is_stmt 0 view .LVU37
 178 004a 0223     		movs	r3, #2
 179 004c 1293     		str	r3, [sp, #72]
 144:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLM = 8;
 180              		.loc 1 144 3 is_stmt 1 view .LVU38
 144:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLM = 8;
 181              		.loc 1 144 35 is_stmt 0 view .LVU39
 182 004e 1393     		str	r3, [sp, #76]
ARM GAS  /tmp/cc9Pn31k.s 			page 12


 145:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLN = 420;
 183              		.loc 1 145 3 is_stmt 1 view .LVU40
 145:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLN = 420;
 184              		.loc 1 145 30 is_stmt 0 view .LVU41
 185 0050 0821     		movs	r1, #8
 186 0052 1491     		str	r1, [sp, #80]
 146:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLP = 1;
 187              		.loc 1 146 3 is_stmt 1 view .LVU42
 146:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLP = 1;
 188              		.loc 1 146 30 is_stmt 0 view .LVU43
 189 0054 4FF4D271 		mov	r1, #420
 190 0058 1591     		str	r1, [sp, #84]
 147:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLQ = 4;
 191              		.loc 1 147 3 is_stmt 1 view .LVU44
 147:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLQ = 4;
 192              		.loc 1 147 30 is_stmt 0 view .LVU45
 193 005a 1692     		str	r2, [sp, #88]
 148:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLR = 2;
 194              		.loc 1 148 3 is_stmt 1 view .LVU46
 148:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLR = 2;
 195              		.loc 1 148 30 is_stmt 0 view .LVU47
 196 005c 0422     		movs	r2, #4
 197 005e 1792     		str	r2, [sp, #92]
 149:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_0;
 198              		.loc 1 149 3 is_stmt 1 view .LVU48
 149:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_0;
 199              		.loc 1 149 30 is_stmt 0 view .LVU49
 200 0060 1893     		str	r3, [sp, #96]
 150:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOMEDIUM;
 201              		.loc 1 150 3 is_stmt 1 view .LVU50
 150:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOMEDIUM;
 202              		.loc 1 150 32 is_stmt 0 view .LVU51
 203 0062 0022     		movs	r2, #0
 204 0064 1992     		str	r2, [sp, #100]
 151:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLFRACN = 0;
 205              		.loc 1 151 3 is_stmt 1 view .LVU52
 151:Core/Src/main.c ****   RCC_OscInitStruct.PLL.PLLFRACN = 0;
 206              		.loc 1 151 35 is_stmt 0 view .LVU53
 207 0066 1A93     		str	r3, [sp, #104]
 152:Core/Src/main.c ****   if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 208              		.loc 1 152 3 is_stmt 1 view .LVU54
 152:Core/Src/main.c ****   if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 209              		.loc 1 152 34 is_stmt 0 view .LVU55
 210 0068 1B92     		str	r2, [sp, #108]
 153:Core/Src/main.c ****   {
 211              		.loc 1 153 3 is_stmt 1 view .LVU56
 153:Core/Src/main.c ****   {
 212              		.loc 1 153 7 is_stmt 0 view .LVU57
 213 006a 09A8     		add	r0, sp, #36
 214 006c FFF7FEFF 		bl	HAL_RCC_OscConfig
 215              	.LVL7:
 153:Core/Src/main.c ****   {
 216              		.loc 1 153 6 discriminator 1 view .LVU58
 217 0070 B0B9     		cbnz	r0, .L14
 160:Core/Src/main.c ****                               |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
 218              		.loc 1 160 3 is_stmt 1 view .LVU59
 160:Core/Src/main.c ****                               |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
ARM GAS  /tmp/cc9Pn31k.s 			page 13


 219              		.loc 1 160 31 is_stmt 0 view .LVU60
 220 0072 3F23     		movs	r3, #63
 221 0074 0193     		str	r3, [sp, #4]
 163:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
 222              		.loc 1 163 3 is_stmt 1 view .LVU61
 163:Core/Src/main.c ****   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
 223              		.loc 1 163 34 is_stmt 0 view .LVU62
 224 0076 0323     		movs	r3, #3
 225 0078 0293     		str	r3, [sp, #8]
 164:Core/Src/main.c ****   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
 226              		.loc 1 164 3 is_stmt 1 view .LVU63
 164:Core/Src/main.c ****   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
 227              		.loc 1 164 35 is_stmt 0 view .LVU64
 228 007a 0023     		movs	r3, #0
 229 007c 0393     		str	r3, [sp, #12]
 165:Core/Src/main.c ****   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
 230              		.loc 1 165 3 is_stmt 1 view .LVU65
 165:Core/Src/main.c ****   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
 231              		.loc 1 165 35 is_stmt 0 view .LVU66
 232 007e 0823     		movs	r3, #8
 233 0080 0493     		str	r3, [sp, #16]
 166:Core/Src/main.c ****   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
 234              		.loc 1 166 3 is_stmt 1 view .LVU67
 166:Core/Src/main.c ****   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
 235              		.loc 1 166 36 is_stmt 0 view .LVU68
 236 0082 4023     		movs	r3, #64
 237 0084 0593     		str	r3, [sp, #20]
 167:Core/Src/main.c ****   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
 238              		.loc 1 167 3 is_stmt 1 view .LVU69
 167:Core/Src/main.c ****   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
 239              		.loc 1 167 36 is_stmt 0 view .LVU70
 240 0086 0693     		str	r3, [sp, #24]
 168:Core/Src/main.c ****   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
 241              		.loc 1 168 3 is_stmt 1 view .LVU71
 168:Core/Src/main.c ****   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;
 242              		.loc 1 168 36 is_stmt 0 view .LVU72
 243 0088 4FF48062 		mov	r2, #1024
 244 008c 0792     		str	r2, [sp, #28]
 169:Core/Src/main.c **** 
 245              		.loc 1 169 3 is_stmt 1 view .LVU73
 169:Core/Src/main.c **** 
 246              		.loc 1 169 36 is_stmt 0 view .LVU74
 247 008e 0893     		str	r3, [sp, #32]
 171:Core/Src/main.c ****   {
 248              		.loc 1 171 3 is_stmt 1 view .LVU75
 171:Core/Src/main.c ****   {
 249              		.loc 1 171 7 is_stmt 0 view .LVU76
 250 0090 0221     		movs	r1, #2
 251 0092 01A8     		add	r0, sp, #4
 252 0094 FFF7FEFF 		bl	HAL_RCC_ClockConfig
 253              	.LVL8:
 171:Core/Src/main.c ****   {
 254              		.loc 1 171 6 discriminator 1 view .LVU77
 255 0098 20B9     		cbnz	r0, .L15
 175:Core/Src/main.c **** 
 256              		.loc 1 175 1 view .LVU78
 257 009a 1DB0     		add	sp, sp, #116
ARM GAS  /tmp/cc9Pn31k.s 			page 14


 258              	.LCFI3:
 259              		.cfi_remember_state
 260              		.cfi_def_cfa_offset 4
 261              		@ sp needed
 262 009c 5DF804FB 		ldr	pc, [sp], #4
 263              	.L14:
 264              	.LCFI4:
 265              		.cfi_restore_state
 155:Core/Src/main.c ****   }
 266              		.loc 1 155 5 is_stmt 1 view .LVU79
 267 00a0 FFF7FEFF 		bl	Error_Handler
 268              	.LVL9:
 269              	.L15:
 173:Core/Src/main.c ****   }
 270              		.loc 1 173 5 view .LVU80
 271 00a4 FFF7FEFF 		bl	Error_Handler
 272              	.LVL10:
 273              	.L17:
 274              		.align	2
 275              	.L16:
 276 00a8 00480258 		.word	1476544512
 277              		.cfi_endproc
 278              	.LFE175:
 280              		.section	.text.main,"ax",%progbits
 281              		.align	1
 282              		.global	main
 283              		.syntax unified
 284              		.thumb
 285              		.thumb_func
 287              	main:
 288              	.LFB174:
  68:Core/Src/main.c **** 
 289              		.loc 1 68 1 view -0
 290              		.cfi_startproc
 291              		@ Volatile: function does not return.
 292              		@ args = 0, pretend = 0, frame = 0
 293              		@ frame_needed = 0, uses_anonymous_args = 0
 294 0000 08B5     		push	{r3, lr}
 295              	.LCFI5:
 296              		.cfi_def_cfa_offset 8
 297              		.cfi_offset 3, -8
 298              		.cfi_offset 14, -4
  80:Core/Src/main.c **** 
 299              		.loc 1 80 3 view .LVU82
 300 0002 FFF7FEFF 		bl	HAL_Init
 301              	.LVL11:
  87:Core/Src/main.c **** 
 302              		.loc 1 87 3 view .LVU83
 303 0006 FFF7FEFF 		bl	SystemClock_Config
 304              	.LVL12:
  94:Core/Src/main.c ****   /* USER CODE BEGIN 2 */
 305              		.loc 1 94 3 view .LVU84
 306 000a FFF7FEFF 		bl	MX_GPIO_Init
 307              	.LVL13:
 100:Core/Src/main.c ****   MX_FREERTOS_Init();
 308              		.loc 1 100 3 view .LVU85
 309 000e FFF7FEFF 		bl	osKernelInitialize
ARM GAS  /tmp/cc9Pn31k.s 			page 15


 310              	.LVL14:
 101:Core/Src/main.c **** 
 311              		.loc 1 101 3 view .LVU86
 312 0012 FFF7FEFF 		bl	MX_FREERTOS_Init
 313              	.LVL15:
 104:Core/Src/main.c **** 
 314              		.loc 1 104 3 view .LVU87
 315 0016 FFF7FEFF 		bl	osKernelStart
 316              	.LVL16:
 317              	.L19:
 110:Core/Src/main.c ****   {
 318              		.loc 1 110 3 view .LVU88
 115:Core/Src/main.c ****   /* USER CODE END 3 */
 319              		.loc 1 115 3 view .LVU89
 110:Core/Src/main.c ****   {
 320              		.loc 1 110 9 view .LVU90
 321 001a FEE7     		b	.L19
 322              		.cfi_endproc
 323              	.LFE174:
 325              		.text
 326              	.Letext0:
 327              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 328              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 329              		.file 5 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 330              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 331              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc.h"
 332              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h"
 333              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim.h"
 334              		.file 10 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 335              		.file 11 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_pwr_ex.h"
 336              		.file 12 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
 337              		.file 13 "Core/Inc/gpio.h"
 338              		.file 14 "<built-in>"
ARM GAS  /tmp/cc9Pn31k.s 			page 16


DEFINED SYMBOLS
                            *ABS*:00000000 main.c
     /tmp/cc9Pn31k.s:20     .text.HAL_TIM_PeriodElapsedCallback:00000000 $t
     /tmp/cc9Pn31k.s:26     .text.HAL_TIM_PeriodElapsedCallback:00000000 HAL_TIM_PeriodElapsedCallback
     /tmp/cc9Pn31k.s:60     .text.HAL_TIM_PeriodElapsedCallback:00000014 $d
     /tmp/cc9Pn31k.s:65     .text.Error_Handler:00000000 $t
     /tmp/cc9Pn31k.s:71     .text.Error_Handler:00000000 Error_Handler
     /tmp/cc9Pn31k.s:103    .text.SystemClock_Config:00000000 $t
     /tmp/cc9Pn31k.s:109    .text.SystemClock_Config:00000000 SystemClock_Config
     /tmp/cc9Pn31k.s:276    .text.SystemClock_Config:000000a8 $d
     /tmp/cc9Pn31k.s:281    .text.main:00000000 $t
     /tmp/cc9Pn31k.s:287    .text.main:00000000 main

UNDEFINED SYMBOLS
HAL_IncTick
memset
HAL_PWREx_ConfigSupply
HAL_RCC_OscConfig
HAL_RCC_ClockConfig
HAL_Init
MX_GPIO_Init
osKernelInitialize
MX_FREERTOS_Init
osKernelStart
