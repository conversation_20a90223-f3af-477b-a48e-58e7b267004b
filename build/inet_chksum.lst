ARM GAS  /tmp/cckEb2gS.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"inet_chksum.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/inet_chksum.c"
  19              		.section	.text.lwip_standard_chksum,"ax",%progbits
  20              		.align	1
  21              		.global	lwip_standard_chksum
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	lwip_standard_chksum:
  27              	.LVL0:
  28              	.LFB170:
   1:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Internet checksum functions.\n
   4:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * These are some reference implementations of the checksum algorithm, with the
   6:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * aim of being simple, correct and fully portable. Checksumming is the
   7:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * first thing you would want to optimize for your platform. If you create
   8:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * your own version, link it in and in your cc.h put:
   9:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  10:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * \#define LWIP_CHKSUM your_checksum_routine
  11:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  12:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Or you can select from the implementations below by defining
  13:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * LWIP_CHKSUM_ALGORITHM to 1, 2 or 3.
  14:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
  15:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  16:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /*
  17:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  18:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * All rights reserved.
  19:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  20:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Redistribution and use in source and binary forms, with or without modification,
  21:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * are permitted provided that the following conditions are met:
  22:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  23:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  24:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *    this list of conditions and the following disclaimer.
  25:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  26:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *    this list of conditions and the following disclaimer in the documentation
  27:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *    and/or other materials provided with the distribution.
  28:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * 3. The name of the author may not be used to endorse or promote products
  29:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *    derived from this software without specific prior written permission.
  30:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
ARM GAS  /tmp/cckEb2gS.s 			page 2


  31:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  32:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  33:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  34:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  35:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  36:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  37:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  38:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  39:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  40:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * OF SUCH DAMAGE.
  41:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  42:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * This file is part of the lwIP TCP/IP stack.
  43:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  44:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Author: Adam Dunkels <<EMAIL>>
  45:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  46:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
  47:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  48:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #include "lwip/opt.h"
  49:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  50:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #include "lwip/inet_chksum.h"
  51:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #include "lwip/def.h"
  52:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #include "lwip/ip_addr.h"
  53:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  54:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #include <string.h>
  55:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  56:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #ifndef LWIP_CHKSUM
  57:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** # define LWIP_CHKSUM lwip_standard_chksum
  58:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** # ifndef LWIP_CHKSUM_ALGORITHM
  59:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #  define LWIP_CHKSUM_ALGORITHM 2
  60:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** # endif
  61:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t lwip_standard_chksum(const void *dataptr, int len);
  62:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif
  63:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /* If none set: */
  64:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #ifndef LWIP_CHKSUM_ALGORITHM
  65:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** # define LWIP_CHKSUM_ALGORITHM 0
  66:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif
  67:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  68:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if (LWIP_CHKSUM_ALGORITHM == 1) /* Version #1 */
  69:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /**
  70:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * lwip checksum
  71:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  72:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dataptr points to start of data to be summed at any boundary
  73:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param len length of data to be summed
  74:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return host order (!) lwip checksum (non-inverted Internet sum)
  75:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
  76:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @note accumulator size limits summable length to 64k
  77:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @note host endianess is irrelevant (p3 RFC1071)
  78:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
  79:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
  80:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** lwip_standard_chksum(const void *dataptr, int len)
  81:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
  82:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc;
  83:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u16_t src;
  84:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   const u8_t *octetptr;
  85:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
  86:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = 0;
  87:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* dataptr may be at odd or even addresses */
ARM GAS  /tmp/cckEb2gS.s 			page 3


  88:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   octetptr = (const u8_t *)dataptr;
  89:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   while (len > 1) {
  90:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /* declare first octet as most significant
  91:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****        thus assume network order, ignoring host order */
  92:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     src = (*octetptr) << 8;
  93:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     octetptr++;
  94:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /* declare second octet as least significant */
  95:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     src |= (*octetptr);
  96:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     octetptr++;
  97:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc += src;
  98:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len -= 2;
  99:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 100:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (len > 0) {
 101:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /* accumulate remaining octet */
 102:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     src = (*octetptr) << 8;
 103:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc += src;
 104:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 105:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* add deferred carry bits */
 106:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (acc >> 16) + (acc & 0x0000ffffUL);
 107:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if ((acc & 0xffff0000UL) != 0) {
 108:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (acc >> 16) + (acc & 0x0000ffffUL);
 109:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 110:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* This maybe a little confusing: reorder sum using lwip_htons()
 111:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****      instead of lwip_ntohs() since it has a little less call overhead.
 112:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****      The caller must invert bits for Internet sum ! */
 113:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return lwip_htons((u16_t)acc);
 114:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 115:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif
 116:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 117:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if (LWIP_CHKSUM_ALGORITHM == 2) /* Alternative version #2 */
 118:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /*
 119:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Curt McDowell
 120:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Broadcom Corp.
 121:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * <EMAIL>
 122:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 123:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IP checksum two bytes at a time with support for
 124:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * unaligned buffer.
 125:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Works for len up to and including 0x20000.
 126:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * by Curt McDowell, Broadcom Corp. 12/08/2005
 127:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 128:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dataptr points to start of data to be summed at any boundary
 129:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param len length of data to be summed
 130:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return host order (!) lwip checksum (non-inverted Internet sum)
 131:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 132:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 133:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** lwip_standard_chksum(const void *dataptr, int len)
 134:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
  29              		.loc 1 134 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		@ link register save eliminated.
  34              		.loc 1 134 1 is_stmt 0 view .LVU1
  35 0000 10B4     		push	{r4}
  36              	.LCFI0:
  37              		.cfi_def_cfa_offset 4
  38              		.cfi_offset 4, -4
ARM GAS  /tmp/cckEb2gS.s 			page 4


 135:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   const u8_t *pb = (const u8_t *)dataptr;
  39              		.loc 1 135 3 is_stmt 1 view .LVU2
  40              	.LVL1:
 136:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   const u16_t *ps;
  41              		.loc 1 136 3 view .LVU3
 137:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u16_t t = 0;
  42              		.loc 1 137 3 view .LVU4
  43              		.loc 1 137 9 is_stmt 0 view .LVU5
  44 0002 4FF0000C 		mov	ip, #0
 138:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t sum = 0;
  45              		.loc 1 138 3 is_stmt 1 view .LVU6
  46              	.LVL2:
 139:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int odd = ((mem_ptr_t)pb & 1);
  47              		.loc 1 139 3 view .LVU7
  48              		.loc 1 139 7 is_stmt 0 view .LVU8
  49 0006 00F00104 		and	r4, r0, #1
  50              	.LVL3:
 140:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 141:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Get aligned to u16_t */
 142:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (odd && len > 0) {
  51              		.loc 1 142 3 is_stmt 1 view .LVU9
  52              		.loc 1 142 18 is_stmt 0 view .LVU10
  53 000a 0B46     		mov	r3, r1
  54              		.loc 1 142 11 view .LVU11
  55 000c 6145     		cmp	r1, ip
  56 000e D4BF     		ite	le
  57 0010 0022     		movle	r2, #0
  58 0012 00F00102 		andgt	r2, r0, #1
  59              		.loc 1 142 6 view .LVU12
  60 0016 22B1     		cbz	r2, .L2
 143:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     ((u8_t *)&t)[1] = *pb++;
  61              		.loc 1 143 5 is_stmt 1 view .LVU13
  62              	.LVL4:
  63              		.loc 1 143 23 is_stmt 0 view .LVU14
  64 0018 10F8012B 		ldrb	r2, [r0], #1	@ zero_extendqisi2
  65              	.LVL5:
  66              		.loc 1 143 21 view .LVU15
  67 001c 62F31F2C 		bfi	ip, r2, #8, #24
 144:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len--;
  68              		.loc 1 144 5 is_stmt 1 view .LVU16
  69              		.loc 1 144 8 is_stmt 0 view .LVU17
  70 0020 4B1E     		subs	r3, r1, #1
  71              	.LVL6:
  72              	.L2:
 145:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 146:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 147:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Add the bulk of the data */
 148:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   ps = (const u16_t *)(const void *)pb;
  73              		.loc 1 148 3 is_stmt 1 view .LVU18
 149:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   while (len > 1) {
  74              		.loc 1 149 3 view .LVU19
 138:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int odd = ((mem_ptr_t)pb & 1);
  75              		.loc 1 138 9 is_stmt 0 view .LVU20
  76 0022 0022     		movs	r2, #0
  77              		.loc 1 149 9 view .LVU21
  78 0024 03E0     		b	.L3
  79              	.LVL7:
ARM GAS  /tmp/cckEb2gS.s 			page 5


  80              	.L4:
 150:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum += *ps++;
  81              		.loc 1 150 5 is_stmt 1 view .LVU22
  82              		.loc 1 150 12 is_stmt 0 view .LVU23
  83 0026 30F8021B 		ldrh	r1, [r0], #2
  84              	.LVL8:
  85              		.loc 1 150 9 view .LVU24
  86 002a 0A44     		add	r2, r2, r1
  87              	.LVL9:
 151:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len -= 2;
  88              		.loc 1 151 5 is_stmt 1 view .LVU25
  89              		.loc 1 151 9 is_stmt 0 view .LVU26
  90 002c 023B     		subs	r3, r3, #2
  91              	.LVL10:
  92              	.L3:
 149:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum += *ps++;
  93              		.loc 1 149 14 is_stmt 1 view .LVU27
  94 002e 012B     		cmp	r3, #1
  95 0030 F9DC     		bgt	.L4
 152:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 153:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 154:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Consume left-over byte, if any */
 155:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (len > 0) {
  96              		.loc 1 155 3 view .LVU28
  97              		.loc 1 155 6 is_stmt 0 view .LVU29
  98 0032 002B     		cmp	r3, #0
  99 0034 04DD     		ble	.L5
 156:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     ((u8_t *)&t)[0] = *(const u8_t *)ps;
 100              		.loc 1 156 5 is_stmt 1 view .LVU30
 101              		.loc 1 156 23 is_stmt 0 view .LVU31
 102 0036 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 103              	.LVL11:
 104              		.loc 1 156 21 view .LVU32
 105 0038 63F3070C 		bfi	ip, r3, #0, #8
 106 003c 1FFA8CFC 		uxth	ip, ip
 107              	.L5:
 157:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 158:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 159:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Add end bytes */
 160:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum += t;
 108              		.loc 1 160 3 is_stmt 1 view .LVU33
 109              		.loc 1 160 7 is_stmt 0 view .LVU34
 110 0040 9444     		add	ip, ip, r2
 111              	.LVL12:
 161:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 162:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Fold 32-bit sum to 16 bits
 163:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****      calling this twice is probably faster than if statements... */
 164:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum = FOLD_U32T(sum);
 112              		.loc 1 164 3 is_stmt 1 view .LVU35
 113              		.loc 1 164 9 is_stmt 0 view .LVU36
 114 0042 1FFA8CF3 		uxth	r3, ip
 115              		.loc 1 164 7 view .LVU37
 116 0046 03EB1C43 		add	r3, r3, ip, lsr #16
 117              	.LVL13:
 165:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum = FOLD_U32T(sum);
 118              		.loc 1 165 3 is_stmt 1 view .LVU38
 119              		.loc 1 165 9 is_stmt 0 view .LVU39
ARM GAS  /tmp/cckEb2gS.s 			page 6


 120 004a 98B2     		uxth	r0, r3
 121              	.LVL14:
 122              		.loc 1 165 7 view .LVU40
 123 004c 00EB1340 		add	r0, r0, r3, lsr #16
 124              	.LVL15:
 166:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 167:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Swap if alignment was odd */
 168:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (odd) {
 125              		.loc 1 168 3 is_stmt 1 view .LVU41
 126              		.loc 1 168 6 is_stmt 0 view .LVU42
 127 0050 24B1     		cbz	r4, .L6
 169:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum = SWAP_BYTES_IN_WORD(sum);
 128              		.loc 1 169 5 is_stmt 1 view .LVU43
 129              		.loc 1 169 11 is_stmt 0 view .LVU44
 130 0052 0302     		lsls	r3, r0, #8
 131 0054 9BB2     		uxth	r3, r3
 132 0056 C0F30720 		ubfx	r0, r0, #8, #8
 133              	.LVL16:
 134              		.loc 1 169 9 view .LVU45
 135 005a 1843     		orrs	r0, r0, r3
 136              	.LVL17:
 137              	.L6:
 170:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 171:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 172:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return (u16_t)sum;
 138              		.loc 1 172 3 is_stmt 1 view .LVU46
 173:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 139              		.loc 1 173 1 is_stmt 0 view .LVU47
 140 005c 80B2     		uxth	r0, r0
 141              	.LVL18:
 142              		.loc 1 173 1 view .LVU48
 143 005e 5DF8044B 		ldr	r4, [sp], #4
 144              	.LCFI1:
 145              		.cfi_restore 4
 146              		.cfi_def_cfa_offset 0
 147              	.LVL19:
 148              		.loc 1 173 1 view .LVU49
 149 0062 7047     		bx	lr
 150              		.cfi_endproc
 151              	.LFE170:
 153              		.section	.text.inet_cksum_pseudo_base,"ax",%progbits
 154              		.align	1
 155              		.syntax unified
 156              		.thumb
 157              		.thumb_func
 159              	inet_cksum_pseudo_base:
 160              	.LVL20:
 161              	.LFB171:
 174:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif
 175:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 176:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if (LWIP_CHKSUM_ALGORITHM == 3) /* Alternative version #3 */
 177:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /**
 178:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * An optimized checksum routine. Basically, it uses loop-unrolling on
 179:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * the checksum loop, treating the head and tail bytes specially, whereas
 180:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * the inner loop acts on 8 bytes at a time.
 181:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 182:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @arg start of buffer to be checksummed. May be an odd byte address.
ARM GAS  /tmp/cckEb2gS.s 			page 7


 183:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @len number of bytes in the buffer to be checksummed.
 184:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return host order (!) lwip checksum (non-inverted Internet sum)
 185:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 186:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * by Curt McDowell, Broadcom Corp. December 8th, 2005
 187:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 188:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 189:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** lwip_standard_chksum(const void *dataptr, int len)
 190:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 191:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   const u8_t *pb = (const u8_t *)dataptr;
 192:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   const u16_t *ps;
 193:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u16_t t = 0;
 194:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   const u32_t *pl;
 195:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t sum = 0, tmp;
 196:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* starts at odd byte address? */
 197:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int odd = ((mem_ptr_t)pb & 1);
 198:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 199:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (odd && len > 0) {
 200:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     ((u8_t *)&t)[1] = *pb++;
 201:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len--;
 202:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 203:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 204:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   ps = (const u16_t *)(const void *)pb;
 205:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 206:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (((mem_ptr_t)ps & 3) && len > 1) {
 207:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum += *ps++;
 208:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len -= 2;
 209:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 210:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 211:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   pl = (const u32_t *)(const void *)ps;
 212:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 213:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   while (len > 7)  {
 214:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     tmp = sum + *pl++;          /* ping */
 215:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (tmp < sum) {
 216:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       tmp++;                    /* add back carry */
 217:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 218:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 219:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum = tmp + *pl++;          /* pong */
 220:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (sum < tmp) {
 221:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       sum++;                    /* add back carry */
 222:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 223:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 224:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len -= 8;
 225:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 226:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 227:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* make room in upper bits */
 228:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum = FOLD_U32T(sum);
 229:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 230:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   ps = (const u16_t *)pl;
 231:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 232:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* 16-bit aligned word remaining? */
 233:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   while (len > 1) {
 234:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum += *ps++;
 235:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     len -= 2;
 236:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 237:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 238:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* dangling tail byte remaining? */
 239:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (len > 0) {                /* include odd byte */
ARM GAS  /tmp/cckEb2gS.s 			page 8


 240:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     ((u8_t *)&t)[0] = *(const u8_t *)ps;
 241:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 242:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum += t;                     /* add end bytes */
 244:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 245:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Fold 32-bit sum to 16 bits
 246:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****      calling this twice is probably faster than if statements... */
 247:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum = FOLD_U32T(sum);
 248:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   sum = FOLD_U32T(sum);
 249:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 250:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (odd) {
 251:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     sum = SWAP_BYTES_IN_WORD(sum);
 252:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 253:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 254:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return (u16_t)sum;
 255:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 256:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif
 257:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 258:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /** Parts of the pseudo checksum which are common to IPv4 and IPv6 */
 259:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** static u16_t
 260:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** inet_cksum_pseudo_base(struct pbuf *p, u8_t proto, u16_t proto_len, u32_t acc)
 261:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 162              		.loc 1 261 1 is_stmt 1 view -0
 163              		.cfi_startproc
 164              		@ args = 0, pretend = 0, frame = 0
 165              		@ frame_needed = 0, uses_anonymous_args = 0
 166              		.loc 1 261 1 is_stmt 0 view .LVU51
 167 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 168              	.LCFI2:
 169              		.cfi_def_cfa_offset 32
 170              		.cfi_offset 3, -32
 171              		.cfi_offset 4, -28
 172              		.cfi_offset 5, -24
 173              		.cfi_offset 6, -20
 174              		.cfi_offset 7, -16
 175              		.cfi_offset 8, -12
 176              		.cfi_offset 9, -8
 177              		.cfi_offset 14, -4
 178 0004 0546     		mov	r5, r0
 179 0006 8946     		mov	r9, r1
 180 0008 9046     		mov	r8, r2
 181 000a 1C46     		mov	r4, r3
 262:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   struct pbuf *q;
 182              		.loc 1 262 3 is_stmt 1 view .LVU52
 263:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int swapped = 0;
 183              		.loc 1 263 3 view .LVU53
 184              	.LVL21:
 264:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 265:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* iterate through all pbuf in chain */
 266:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   for (q = p; q != NULL; q = q->next) {
 185              		.loc 1 266 3 view .LVU54
 263:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int swapped = 0;
 186              		.loc 1 263 7 is_stmt 0 view .LVU55
 187 000c 0027     		movs	r7, #0
 188              		.loc 1 266 3 view .LVU56
 189 000e 00E0     		b	.L9
 190              	.LVL22:
ARM GAS  /tmp/cckEb2gS.s 			page 9


 191              	.L10:
 192              		.loc 1 266 28 is_stmt 1 discriminator 2 view .LVU57
 193 0010 2D68     		ldr	r5, [r5]
 194              	.LVL23:
 195              	.L9:
 196              		.loc 1 266 17 discriminator 1 view .LVU58
 197 0012 9DB1     		cbz	r5, .L14
 267:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 268:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                              (void *)q, (void *)q->next));
 198              		.loc 1 268 58 view .LVU59
 269:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc += LWIP_CHKSUM(q->payload, q->len);
 199              		.loc 1 269 5 view .LVU60
 200              		.loc 1 269 37 is_stmt 0 view .LVU61
 201 0014 6E89     		ldrh	r6, [r5, #10]
 202              		.loc 1 269 12 view .LVU62
 203 0016 3146     		mov	r1, r6
 204 0018 6868     		ldr	r0, [r5, #4]
 205 001a FFF7FEFF 		bl	lwip_standard_chksum
 206              	.LVL24:
 207              		.loc 1 269 9 discriminator 1 view .LVU63
 208 001e 2044     		add	r0, r0, r4
 209              	.LVL25:
 270:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /*LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): unwrapped lwip_chksum()=%"X32_F" \n", acc));*
 271:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /* just executing this next line is probably faster that the if statement needed
 272:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****        to check whether we really need to execute it, and does no harm */
 273:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = FOLD_U32T(acc);
 210              		.loc 1 273 5 is_stmt 1 view .LVU64
 211              		.loc 1 273 11 is_stmt 0 view .LVU65
 212 0020 84B2     		uxth	r4, r0
 213              		.loc 1 273 9 view .LVU66
 214 0022 04EB1044 		add	r4, r4, r0, lsr #16
 215              	.LVL26:
 274:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (q->len % 2 != 0) {
 216              		.loc 1 274 5 is_stmt 1 view .LVU67
 217              		.loc 1 274 8 is_stmt 0 view .LVU68
 218 0026 16F0010F 		tst	r6, #1
 219 002a F1D0     		beq	.L10
 275:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       swapped = !swapped;
 220              		.loc 1 275 7 is_stmt 1 view .LVU69
 221              		.loc 1 275 17 is_stmt 0 view .LVU70
 222 002c 87F00107 		eor	r7, r7, #1
 223              	.LVL27:
 276:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       acc = SWAP_BYTES_IN_WORD(acc);
 224              		.loc 1 276 7 is_stmt 1 view .LVU71
 225              		.loc 1 276 13 is_stmt 0 view .LVU72
 226 0030 2302     		lsls	r3, r4, #8
 227 0032 9BB2     		uxth	r3, r3
 228 0034 C4F30724 		ubfx	r4, r4, #8, #8
 229              	.LVL28:
 230              		.loc 1 276 11 view .LVU73
 231 0038 1C43     		orrs	r4, r4, r3
 232              	.LVL29:
 233              		.loc 1 276 11 view .LVU74
 234 003a E9E7     		b	.L10
 235              	.L14:
 277:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 278:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /*LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): wrapped lwip_chksum()=%"X32_F" \n", acc));*/
ARM GAS  /tmp/cckEb2gS.s 			page 10


 279:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 280:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 281:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (swapped) {
 236              		.loc 1 281 3 is_stmt 1 view .LVU75
 237              		.loc 1 281 6 is_stmt 0 view .LVU76
 238 003c 27B1     		cbz	r7, .L12
 282:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = SWAP_BYTES_IN_WORD(acc);
 239              		.loc 1 282 5 is_stmt 1 view .LVU77
 240              		.loc 1 282 11 is_stmt 0 view .LVU78
 241 003e 2302     		lsls	r3, r4, #8
 242 0040 9BB2     		uxth	r3, r3
 243 0042 C4F30724 		ubfx	r4, r4, #8, #8
 244              	.LVL30:
 245              		.loc 1 282 9 view .LVU79
 246 0046 1C43     		orrs	r4, r4, r3
 247              	.LVL31:
 248              	.L12:
 283:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 284:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 285:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc += (u32_t)lwip_htons((u16_t)proto);
 249              		.loc 1 285 3 is_stmt 1 view .LVU80
 250              		.loc 1 285 17 is_stmt 0 view .LVU81
 251 0048 4846     		mov	r0, r9
 252 004a FFF7FEFF 		bl	lwip_htons
 253              	.LVL32:
 254              		.loc 1 285 7 discriminator 1 view .LVU82
 255 004e 0444     		add	r4, r4, r0
 256              	.LVL33:
 286:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc += (u32_t)lwip_htons(proto_len);
 257              		.loc 1 286 3 is_stmt 1 view .LVU83
 258              		.loc 1 286 17 is_stmt 0 view .LVU84
 259 0050 4046     		mov	r0, r8
 260 0052 FFF7FEFF 		bl	lwip_htons
 261              	.LVL34:
 262              		.loc 1 286 7 discriminator 1 view .LVU85
 263 0056 2044     		add	r0, r0, r4
 264              	.LVL35:
 287:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 288:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Fold 32-bit sum to 16 bits
 289:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****      calling this twice is probably faster than if statements... */
 290:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 265              		.loc 1 290 3 is_stmt 1 view .LVU86
 266              		.loc 1 290 9 is_stmt 0 view .LVU87
 267 0058 83B2     		uxth	r3, r0
 268              		.loc 1 290 7 view .LVU88
 269 005a 03EB1043 		add	r3, r3, r0, lsr #16
 270              	.LVL36:
 291:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 271              		.loc 1 291 3 is_stmt 1 view .LVU89
 272              		.loc 1 291 9 is_stmt 0 view .LVU90
 273 005e 98B2     		uxth	r0, r3
 274              		.loc 1 291 7 view .LVU91
 275 0060 00EB1340 		add	r0, r0, r3, lsr #16
 276              	.LVL37:
 292:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): pbuf chain lwip_chksum()=%"X32_F"\n", acc));
 277              		.loc 1 292 94 is_stmt 1 view .LVU92
 293:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return (u16_t)~(acc & 0xffffUL);
ARM GAS  /tmp/cckEb2gS.s 			page 11


 278              		.loc 1 293 3 view .LVU93
 279              		.loc 1 293 10 is_stmt 0 view .LVU94
 280 0064 C043     		mvns	r0, r0
 281              	.LVL38:
 294:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 282              		.loc 1 294 1 view .LVU95
 283 0066 80B2     		uxth	r0, r0
 284              	.LVL39:
 285              		.loc 1 294 1 view .LVU96
 286 0068 BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 287              		.loc 1 294 1 view .LVU97
 288              		.cfi_endproc
 289              	.LFE171:
 291              		.section	.rodata.inet_cksum_pseudo_partial_base.str1.4,"aMS",%progbits,1
 292              		.align	2
 293              	.LC0:
 294 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/inet_chksum.c"
 294      6C657761 
 294      7265732F 
 294      54686972 
 294      645F5061 
 295 0033 00       		.ascii	"\000"
 296              		.align	2
 297              	.LC1:
 298 0034 64656C65 		.ascii	"delete me\000"
 298      7465206D 
 298      6500
 299 003e 0000     		.align	2
 300              	.LC2:
 301 0040 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 301      7274696F 
 301      6E202225 
 301      73222066 
 301      61696C65 
 302              		.section	.text.inet_cksum_pseudo_partial_base,"ax",%progbits
 303              		.align	1
 304              		.syntax unified
 305              		.thumb
 306              		.thumb_func
 308              	inet_cksum_pseudo_partial_base:
 309              	.LVL40:
 310              	.LFB174:
 295:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 296:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV4
 297:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /* inet_chksum_pseudo:
 298:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 299:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the IPv4 pseudo Internet checksum used by TCP and UDP for a pbuf chain.
 300:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IP addresses are expected to be in network byte order.
 301:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 302:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p chain of pbufs over that a checksum should be calculated (ip data part)
 303:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param src source ip address (used for checksum of pseudo header)
 304:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dst destination ip address (used for checksum of pseudo header)
 305:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto ip protocol (used for checksum of pseudo header)
 306:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto_len length of the ip data part (used for checksum of pseudo header)
 307:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 308:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 309:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
ARM GAS  /tmp/cckEb2gS.s 			page 12


 310:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** inet_chksum_pseudo(struct pbuf *p, u8_t proto, u16_t proto_len,
 311:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                    const ip4_addr_t *src, const ip4_addr_t *dest)
 312:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 313:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc;
 314:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t addr;
 315:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 316:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(src);
 317:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (addr & 0xffffUL);
 318:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 319:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(dest);
 320:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + (addr & 0xffffUL));
 321:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 322:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* fold down to 16 bits */
 323:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 324:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 325:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 326:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return inet_cksum_pseudo_base(p, proto, proto_len, acc);
 327:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 328:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 */
 329:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 330:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV6
 331:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /**
 332:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the checksum with IPv6 pseudo header used by TCP and UDP for a pbuf chain.
 333:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IPv6 addresses are expected to be in network byte order.
 334:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 335:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p chain of pbufs over that a checksum should be calculated (ip data part)
 336:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto ipv6 protocol/next header (used for checksum of pseudo header)
 337:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto_len length of the ipv6 payload (used for checksum of pseudo header)
 338:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param src source ipv6 address (used for checksum of pseudo header)
 339:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dest destination ipv6 address (used for checksum of pseudo header)
 340:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 341:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 342:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 343:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** ip6_chksum_pseudo(struct pbuf *p, u8_t proto, u16_t proto_len,
 344:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                   const ip6_addr_t *src, const ip6_addr_t *dest)
 345:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 346:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc = 0;
 347:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t addr;
 348:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u8_t addr_part;
 349:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 350:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   for (addr_part = 0; addr_part < 4; addr_part++) {
 351:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     addr = src->addr[addr_part];
 352:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + (addr & 0xffffUL));
 353:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 354:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     addr = dest->addr[addr_part];
 355:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + (addr & 0xffffUL));
 356:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 357:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 358:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* fold down to 16 bits */
 359:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 360:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 361:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 362:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return inet_cksum_pseudo_base(p, proto, proto_len, acc);
 363:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 364:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV6 */
 365:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 366:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /* ip_chksum_pseudo:
ARM GAS  /tmp/cckEb2gS.s 			page 13


 367:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 368:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the IPv4 or IPv6 pseudo Internet checksum used by TCP and UDP for a pbuf chain.
 369:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IP addresses are expected to be in network byte order.
 370:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 371:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p chain of pbufs over that a checksum should be calculated (ip data part)
 372:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param src source ip address (used for checksum of pseudo header)
 373:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dst destination ip address (used for checksum of pseudo header)
 374:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto ip protocol (used for checksum of pseudo header)
 375:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto_len length of the ip data part (used for checksum of pseudo header)
 376:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 377:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 378:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 379:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** ip_chksum_pseudo(struct pbuf *p, u8_t proto, u16_t proto_len,
 380:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                  const ip_addr_t *src, const ip_addr_t *dest)
 381:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 382:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV6
 383:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (IP_IS_V6(dest)) {
 384:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     return ip6_chksum_pseudo(p, proto, proto_len, ip_2_ip6(src), ip_2_ip6(dest));
 385:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 386:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV6 */
 387:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV4 && LWIP_IPV6
 388:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   else
 389:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 && LWIP_IPV6 */
 390:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV4
 391:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   {
 392:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     return inet_chksum_pseudo(p, proto, proto_len, ip_2_ip4(src), ip_2_ip4(dest));
 393:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 394:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 */
 395:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 396:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 397:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /** Parts of the pseudo checksum which are common to IPv4 and IPv6 */
 398:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** static u16_t
 399:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** inet_cksum_pseudo_partial_base(struct pbuf *p, u8_t proto, u16_t proto_len,
 400:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                                u16_t chksum_len, u32_t acc)
 401:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 311              		.loc 1 401 1 is_stmt 1 view -0
 312              		.cfi_startproc
 313              		@ args = 4, pretend = 0, frame = 0
 314              		@ frame_needed = 0, uses_anonymous_args = 0
 315              		.loc 1 401 1 is_stmt 0 view .LVU99
 316 0000 2DE9F84F 		push	{r3, r4, r5, r6, r7, r8, r9, r10, fp, lr}
 317              	.LCFI3:
 318              		.cfi_def_cfa_offset 40
 319              		.cfi_offset 3, -40
 320              		.cfi_offset 4, -36
 321              		.cfi_offset 5, -32
 322              		.cfi_offset 6, -28
 323              		.cfi_offset 7, -24
 324              		.cfi_offset 8, -20
 325              		.cfi_offset 9, -16
 326              		.cfi_offset 10, -12
 327              		.cfi_offset 11, -8
 328              		.cfi_offset 14, -4
 329 0004 0746     		mov	r7, r0
 330 0006 8A46     		mov	r10, r1
 331 0008 9146     		mov	r9, r2
 332 000a 9B46     		mov	fp, r3
ARM GAS  /tmp/cckEb2gS.s 			page 14


 333 000c 0A9C     		ldr	r4, [sp, #40]
 402:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   struct pbuf *q;
 334              		.loc 1 402 3 is_stmt 1 view .LVU100
 403:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int swapped = 0;
 335              		.loc 1 403 3 view .LVU101
 336              	.LVL41:
 404:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u16_t chklen;
 337              		.loc 1 404 3 view .LVU102
 405:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 406:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* iterate through all pbuf in chain */
 407:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   for (q = p; (q != NULL) && (chksum_len > 0); q = q->next) {
 338              		.loc 1 407 3 view .LVU103
 403:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u16_t chklen;
 339              		.loc 1 403 7 is_stmt 0 view .LVU104
 340 000e 4FF00008 		mov	r8, #0
 341              		.loc 1 407 3 view .LVU105
 342 0012 08E0     		b	.L16
 343              	.LVL42:
 344              	.L26:
 408:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 409:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                              (void *)q, (void *)q->next));
 410:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     chklen = q->len;
 411:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (chklen > chksum_len) {
 412:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       chklen = chksum_len;
 413:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 414:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc += LWIP_CHKSUM(q->payload, chklen);
 415:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     chksum_len = (u16_t)(chksum_len - chklen);
 416:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_ASSERT("delete me", chksum_len < 0x7fff);
 345              		.loc 1 416 5 is_stmt 1 discriminator 1 view .LVU106
 346              		.loc 1 416 5 discriminator 1 view .LVU107
 347 0014 244B     		ldr	r3, .L27
 348 0016 4FF4D072 		mov	r2, #416
 349 001a 2449     		ldr	r1, .L27+4
 350 001c 2448     		ldr	r0, .L27+8
 351 001e FFF7FEFF 		bl	printf
 352              	.LVL43:
 353 0022 17E0     		b	.L18
 354              	.LVL44:
 355              	.L19:
 407:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 356              		.loc 1 407 50 discriminator 2 view .LVU108
 357 0024 3F68     		ldr	r7, [r7]
 358              	.LVL45:
 359              	.L16:
 407:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 360              		.loc 1 407 27 discriminator 1 view .LVU109
 407:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 361              		.loc 1 407 42 is_stmt 0 discriminator 1 view .LVU110
 362 0026 BBF10003 		subs	r3, fp, #0
 363 002a 18BF     		it	ne
 364 002c 0123     		movne	r3, #1
 407:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 365              		.loc 1 407 27 discriminator 1 view .LVU111
 366 002e 07B3     		cbz	r7, .L24
 407:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): checksumming pbuf %p (has next %p) \n",
 367              		.loc 1 407 27 discriminator 1 view .LVU112
 368 0030 FBB1     		cbz	r3, .L24
ARM GAS  /tmp/cckEb2gS.s 			page 15


 409:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     chklen = q->len;
 369              		.loc 1 409 58 is_stmt 1 view .LVU113
 410:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (chklen > chksum_len) {
 370              		.loc 1 410 5 view .LVU114
 410:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (chklen > chksum_len) {
 371              		.loc 1 410 12 is_stmt 0 view .LVU115
 372 0032 7D89     		ldrh	r5, [r7, #10]
 373              	.LVL46:
 411:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       chklen = chksum_len;
 374              		.loc 1 411 5 is_stmt 1 view .LVU116
 411:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       chklen = chksum_len;
 375              		.loc 1 411 8 is_stmt 0 view .LVU117
 376 0034 AB45     		cmp	fp, r5
 377 0036 00D2     		bcs	.L17
 412:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 378              		.loc 1 412 14 view .LVU118
 379 0038 5D46     		mov	r5, fp
 380              	.LVL47:
 381              	.L17:
 414:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     chksum_len = (u16_t)(chksum_len - chklen);
 382              		.loc 1 414 5 is_stmt 1 view .LVU119
 414:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     chksum_len = (u16_t)(chksum_len - chklen);
 383              		.loc 1 414 12 is_stmt 0 view .LVU120
 384 003a 2946     		mov	r1, r5
 385 003c 7868     		ldr	r0, [r7, #4]
 386 003e FFF7FEFF 		bl	lwip_standard_chksum
 387              	.LVL48:
 414:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     chksum_len = (u16_t)(chksum_len - chklen);
 388              		.loc 1 414 9 discriminator 1 view .LVU121
 389 0042 0619     		adds	r6, r0, r4
 390              	.LVL49:
 415:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_ASSERT("delete me", chksum_len < 0x7fff);
 391              		.loc 1 415 5 is_stmt 1 view .LVU122
 415:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_ASSERT("delete me", chksum_len < 0x7fff);
 392              		.loc 1 415 16 is_stmt 0 view .LVU123
 393 0044 ABEB0505 		sub	r5, fp, r5
 394              	.LVL50:
 415:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     LWIP_ASSERT("delete me", chksum_len < 0x7fff);
 395              		.loc 1 415 16 view .LVU124
 396 0048 1FFA85FB 		uxth	fp, r5
 397              	.LVL51:
 398              		.loc 1 416 5 is_stmt 1 view .LVU125
 399              		.loc 1 416 5 view .LVU126
 400 004c 47F6FE73 		movw	r3, #32766
 401 0050 9B45     		cmp	fp, r3
 402 0052 DFD8     		bhi	.L26
 403              	.L18:
 404              		.loc 1 416 5 discriminator 3 view .LVU127
 405              		.loc 1 416 5 discriminator 3 view .LVU128
 417:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /*LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): unwrapped lwip_chksum()=%"X32_F" \n", acc));*
 418:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /* fold the upper bit down */
 419:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = FOLD_U32T(acc);
 406              		.loc 1 419 5 view .LVU129
 407              		.loc 1 419 11 is_stmt 0 view .LVU130
 408 0054 B4B2     		uxth	r4, r6
 409              		.loc 1 419 9 view .LVU131
 410 0056 04EB1644 		add	r4, r4, r6, lsr #16
ARM GAS  /tmp/cckEb2gS.s 			page 16


 411              	.LVL52:
 420:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (q->len % 2 != 0) {
 412              		.loc 1 420 5 is_stmt 1 view .LVU132
 413              		.loc 1 420 10 is_stmt 0 view .LVU133
 414 005a 7B89     		ldrh	r3, [r7, #10]
 415              		.loc 1 420 8 view .LVU134
 416 005c 13F0010F 		tst	r3, #1
 417 0060 E0D0     		beq	.L19
 421:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       swapped = !swapped;
 418              		.loc 1 421 7 is_stmt 1 view .LVU135
 419              		.loc 1 421 17 is_stmt 0 view .LVU136
 420 0062 88F00108 		eor	r8, r8, #1
 421              	.LVL53:
 422:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       acc = SWAP_BYTES_IN_WORD(acc);
 422              		.loc 1 422 7 is_stmt 1 view .LVU137
 423              		.loc 1 422 13 is_stmt 0 view .LVU138
 424 0066 2302     		lsls	r3, r4, #8
 425 0068 9BB2     		uxth	r3, r3
 426 006a C4F30724 		ubfx	r4, r4, #8, #8
 427              	.LVL54:
 428              		.loc 1 422 11 view .LVU139
 429 006e 1C43     		orrs	r4, r4, r3
 430              	.LVL55:
 431              		.loc 1 422 11 view .LVU140
 432 0070 D8E7     		b	.L19
 433              	.LVL56:
 434              	.L24:
 423:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 424:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     /*LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): wrapped lwip_chksum()=%"X32_F" \n", acc));*/
 425:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 426:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 427:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (swapped) {
 435              		.loc 1 427 3 is_stmt 1 view .LVU141
 436              		.loc 1 427 6 is_stmt 0 view .LVU142
 437 0072 B8F1000F 		cmp	r8, #0
 438 0076 04D0     		beq	.L22
 428:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = SWAP_BYTES_IN_WORD(acc);
 439              		.loc 1 428 5 is_stmt 1 view .LVU143
 440              		.loc 1 428 11 is_stmt 0 view .LVU144
 441 0078 2302     		lsls	r3, r4, #8
 442 007a 9BB2     		uxth	r3, r3
 443 007c C4F30724 		ubfx	r4, r4, #8, #8
 444              	.LVL57:
 445              		.loc 1 428 9 view .LVU145
 446 0080 1C43     		orrs	r4, r4, r3
 447              	.LVL58:
 448              	.L22:
 429:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 430:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 431:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc += (u32_t)lwip_htons((u16_t)proto);
 449              		.loc 1 431 3 is_stmt 1 view .LVU146
 450              		.loc 1 431 17 is_stmt 0 view .LVU147
 451 0082 5046     		mov	r0, r10
 452 0084 FFF7FEFF 		bl	lwip_htons
 453              	.LVL59:
 454              		.loc 1 431 7 discriminator 1 view .LVU148
 455 0088 0444     		add	r4, r4, r0
ARM GAS  /tmp/cckEb2gS.s 			page 17


 456              	.LVL60:
 432:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc += (u32_t)lwip_htons(proto_len);
 457              		.loc 1 432 3 is_stmt 1 view .LVU149
 458              		.loc 1 432 17 is_stmt 0 view .LVU150
 459 008a 4846     		mov	r0, r9
 460 008c FFF7FEFF 		bl	lwip_htons
 461              	.LVL61:
 462              		.loc 1 432 7 discriminator 1 view .LVU151
 463 0090 2044     		add	r0, r0, r4
 464              	.LVL62:
 433:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 434:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* Fold 32-bit sum to 16 bits
 435:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****      calling this twice is probably faster than if statements... */
 436:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 465              		.loc 1 436 3 is_stmt 1 view .LVU152
 466              		.loc 1 436 9 is_stmt 0 view .LVU153
 467 0092 83B2     		uxth	r3, r0
 468              		.loc 1 436 7 view .LVU154
 469 0094 03EB1043 		add	r3, r3, r0, lsr #16
 470              	.LVL63:
 437:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 471              		.loc 1 437 3 is_stmt 1 view .LVU155
 472              		.loc 1 437 9 is_stmt 0 view .LVU156
 473 0098 98B2     		uxth	r0, r3
 474              		.loc 1 437 7 view .LVU157
 475 009a 00EB1340 		add	r0, r0, r3, lsr #16
 476              	.LVL64:
 438:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   LWIP_DEBUGF(INET_DEBUG, ("inet_chksum_pseudo(): pbuf chain lwip_chksum()=%"X32_F"\n", acc));
 477              		.loc 1 438 94 is_stmt 1 view .LVU158
 439:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return (u16_t)~(acc & 0xffffUL);
 478              		.loc 1 439 3 view .LVU159
 479              		.loc 1 439 10 is_stmt 0 view .LVU160
 480 009e C043     		mvns	r0, r0
 481              	.LVL65:
 440:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 482              		.loc 1 440 1 view .LVU161
 483 00a0 80B2     		uxth	r0, r0
 484              	.LVL66:
 485              		.loc 1 440 1 view .LVU162
 486 00a2 BDE8F88F 		pop	{r3, r4, r5, r6, r7, r8, r9, r10, fp, pc}
 487              	.LVL67:
 488              	.L28:
 489              		.loc 1 440 1 view .LVU163
 490 00a6 00BF     		.align	2
 491              	.L27:
 492 00a8 00000000 		.word	.LC0
 493 00ac 34000000 		.word	.LC1
 494 00b0 40000000 		.word	.LC2
 495              		.cfi_endproc
 496              	.LFE174:
 498              		.section	.text.inet_chksum_pseudo,"ax",%progbits
 499              		.align	1
 500              		.global	inet_chksum_pseudo
 501              		.syntax unified
 502              		.thumb
 503              		.thumb_func
 505              	inet_chksum_pseudo:
ARM GAS  /tmp/cckEb2gS.s 			page 18


 506              	.LVL68:
 507              	.LFB172:
 312:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc;
 508              		.loc 1 312 1 is_stmt 1 view -0
 509              		.cfi_startproc
 510              		@ args = 4, pretend = 0, frame = 0
 511              		@ frame_needed = 0, uses_anonymous_args = 0
 312:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc;
 512              		.loc 1 312 1 is_stmt 0 view .LVU165
 513 0000 08B5     		push	{r3, lr}
 514              	.LCFI4:
 515              		.cfi_def_cfa_offset 8
 516              		.cfi_offset 3, -8
 517              		.cfi_offset 14, -4
 313:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t addr;
 518              		.loc 1 313 3 is_stmt 1 view .LVU166
 314:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 519              		.loc 1 314 3 view .LVU167
 316:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (addr & 0xffffUL);
 520              		.loc 1 316 3 view .LVU168
 316:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (addr & 0xffffUL);
 521              		.loc 1 316 8 is_stmt 0 view .LVU169
 522 0002 1B68     		ldr	r3, [r3]
 523              	.LVL69:
 317:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 524              		.loc 1 317 3 is_stmt 1 view .LVU170
 318:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(dest);
 525              		.loc 1 318 3 view .LVU171
 318:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(dest);
 526              		.loc 1 318 30 is_stmt 0 view .LVU172
 527 0004 4FEA134C 		lsr	ip, r3, #16
 318:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(dest);
 528              		.loc 1 318 7 view .LVU173
 529 0008 1CFA83FC 		uxtah	ip, ip, r3
 530              	.LVL70:
 319:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + (addr & 0xffffUL));
 531              		.loc 1 319 3 is_stmt 1 view .LVU174
 319:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + (addr & 0xffffUL));
 532              		.loc 1 319 8 is_stmt 0 view .LVU175
 533 000c 029B     		ldr	r3, [sp, #8]
 534              	.LVL71:
 319:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + (addr & 0xffffUL));
 535              		.loc 1 319 8 view .LVU176
 536 000e 1B68     		ldr	r3, [r3]
 537              	.LVL72:
 320:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 538              		.loc 1 320 3 is_stmt 1 view .LVU177
 320:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 539              		.loc 1 320 7 is_stmt 0 view .LVU178
 540 0010 1CFA83FC 		uxtah	ip, ip, r3
 541              	.LVL73:
 321:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* fold down to 16 bits */
 542              		.loc 1 321 3 is_stmt 1 view .LVU179
 321:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* fold down to 16 bits */
 543              		.loc 1 321 7 is_stmt 0 view .LVU180
 544 0014 0CEB134C 		add	ip, ip, r3, lsr #16
 545              	.LVL74:
ARM GAS  /tmp/cckEb2gS.s 			page 19


 323:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 546              		.loc 1 323 3 is_stmt 1 view .LVU181
 323:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 547              		.loc 1 323 9 is_stmt 0 view .LVU182
 548 0018 1FFA8CF3 		uxth	r3, ip
 549              	.LVL75:
 323:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 550              		.loc 1 323 7 view .LVU183
 551 001c 03EB1C43 		add	r3, r3, ip, lsr #16
 552              	.LVL76:
 324:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 553              		.loc 1 324 3 is_stmt 1 view .LVU184
 324:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 554              		.loc 1 324 9 is_stmt 0 view .LVU185
 555 0020 1FFA83FC 		uxth	ip, r3
 556              	.LVL77:
 326:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 557              		.loc 1 326 3 is_stmt 1 view .LVU186
 326:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 558              		.loc 1 326 10 is_stmt 0 view .LVU187
 559 0024 0CEB1343 		add	r3, ip, r3, lsr #16
 560              	.LVL78:
 326:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 561              		.loc 1 326 10 view .LVU188
 562 0028 FFF7FEFF 		bl	inet_cksum_pseudo_base
 563              	.LVL79:
 327:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 */
 564              		.loc 1 327 1 view .LVU189
 565 002c 08BD     		pop	{r3, pc}
 327:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 */
 566              		.loc 1 327 1 view .LVU190
 567              		.cfi_endproc
 568              	.LFE172:
 570              		.section	.text.ip_chksum_pseudo,"ax",%progbits
 571              		.align	1
 572              		.global	ip_chksum_pseudo
 573              		.syntax unified
 574              		.thumb
 575              		.thumb_func
 577              	ip_chksum_pseudo:
 578              	.LVL80:
 579              	.LFB173:
 381:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV6
 580              		.loc 1 381 1 is_stmt 1 view -0
 581              		.cfi_startproc
 582              		@ args = 4, pretend = 0, frame = 0
 583              		@ frame_needed = 0, uses_anonymous_args = 0
 381:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV6
 584              		.loc 1 381 1 is_stmt 0 view .LVU192
 585 0000 10B5     		push	{r4, lr}
 586              	.LCFI5:
 587              		.cfi_def_cfa_offset 8
 588              		.cfi_offset 4, -8
 589              		.cfi_offset 14, -4
 590 0002 82B0     		sub	sp, sp, #8
 591              	.LCFI6:
 592              		.cfi_def_cfa_offset 16
ARM GAS  /tmp/cckEb2gS.s 			page 20


 392:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 593              		.loc 1 392 5 is_stmt 1 view .LVU193
 392:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 594              		.loc 1 392 12 is_stmt 0 view .LVU194
 595 0004 049C     		ldr	r4, [sp, #16]
 596 0006 0094     		str	r4, [sp]
 597 0008 FFF7FEFF 		bl	inet_chksum_pseudo
 598              	.LVL81:
 395:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 599              		.loc 1 395 1 view .LVU195
 600 000c 02B0     		add	sp, sp, #8
 601              	.LCFI7:
 602              		.cfi_def_cfa_offset 8
 603              		@ sp needed
 604 000e 10BD     		pop	{r4, pc}
 395:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 605              		.loc 1 395 1 view .LVU196
 606              		.cfi_endproc
 607              	.LFE173:
 609              		.section	.text.inet_chksum_pseudo_partial,"ax",%progbits
 610              		.align	1
 611              		.global	inet_chksum_pseudo_partial
 612              		.syntax unified
 613              		.thumb
 614              		.thumb_func
 616              	inet_chksum_pseudo_partial:
 617              	.LVL82:
 618              	.LFB175:
 441:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 442:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV4
 443:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /* inet_chksum_pseudo_partial:
 444:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 445:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the IPv4 pseudo Internet checksum used by TCP and UDP for a pbuf chain.
 446:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IP addresses are expected to be in network byte order.
 447:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 448:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p chain of pbufs over that a checksum should be calculated (ip data part)
 449:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param src source ip address (used for checksum of pseudo header)
 450:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dst destination ip address (used for checksum of pseudo header)
 451:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto ip protocol (used for checksum of pseudo header)
 452:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto_len length of the ip data part (used for checksum of pseudo header)
 453:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 454:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 455:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 456:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** inet_chksum_pseudo_partial(struct pbuf *p, u8_t proto, u16_t proto_len,
 457:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                            u16_t chksum_len, const ip4_addr_t *src, const ip4_addr_t *dest)
 458:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 619              		.loc 1 458 1 is_stmt 1 view -0
 620              		.cfi_startproc
 621              		@ args = 8, pretend = 0, frame = 0
 622              		@ frame_needed = 0, uses_anonymous_args = 0
 623              		.loc 1 458 1 is_stmt 0 view .LVU198
 624 0000 10B5     		push	{r4, lr}
 625              	.LCFI8:
 626              		.cfi_def_cfa_offset 8
 627              		.cfi_offset 4, -8
 628              		.cfi_offset 14, -4
 629 0002 82B0     		sub	sp, sp, #8
ARM GAS  /tmp/cckEb2gS.s 			page 21


 630              	.LCFI9:
 631              		.cfi_def_cfa_offset 16
 459:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc;
 632              		.loc 1 459 3 is_stmt 1 view .LVU199
 460:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t addr;
 633              		.loc 1 460 3 view .LVU200
 461:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 462:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(src);
 634              		.loc 1 462 3 view .LVU201
 635              		.loc 1 462 8 is_stmt 0 view .LVU202
 636 0004 049C     		ldr	r4, [sp, #16]
 637 0006 2468     		ldr	r4, [r4]
 638              	.LVL83:
 463:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (addr & 0xffffUL);
 639              		.loc 1 463 3 is_stmt 1 view .LVU203
 464:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 640              		.loc 1 464 3 view .LVU204
 641              		.loc 1 464 30 is_stmt 0 view .LVU205
 642 0008 4FEA144C 		lsr	ip, r4, #16
 643              		.loc 1 464 7 view .LVU206
 644 000c 1CFA84FC 		uxtah	ip, ip, r4
 645              	.LVL84:
 465:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   addr = ip4_addr_get_u32(dest);
 646              		.loc 1 465 3 is_stmt 1 view .LVU207
 647              		.loc 1 465 8 is_stmt 0 view .LVU208
 648 0010 059C     		ldr	r4, [sp, #20]
 649              	.LVL85:
 650              		.loc 1 465 8 view .LVU209
 651 0012 2468     		ldr	r4, [r4]
 652              	.LVL86:
 466:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + (addr & 0xffffUL));
 653              		.loc 1 466 3 is_stmt 1 view .LVU210
 654              		.loc 1 466 7 is_stmt 0 view .LVU211
 655 0014 1CFA84FC 		uxtah	ip, ip, r4
 656              	.LVL87:
 467:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 657              		.loc 1 467 3 is_stmt 1 view .LVU212
 658              		.loc 1 467 7 is_stmt 0 view .LVU213
 659 0018 0CEB144C 		add	ip, ip, r4, lsr #16
 660              	.LVL88:
 468:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* fold down to 16 bits */
 469:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 661              		.loc 1 469 3 is_stmt 1 view .LVU214
 662              		.loc 1 469 9 is_stmt 0 view .LVU215
 663 001c 1FFA8CF4 		uxth	r4, ip
 664              	.LVL89:
 665              		.loc 1 469 7 view .LVU216
 666 0020 04EB1C44 		add	r4, r4, ip, lsr #16
 667              	.LVL90:
 470:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 668              		.loc 1 470 3 is_stmt 1 view .LVU217
 669              		.loc 1 470 9 is_stmt 0 view .LVU218
 670 0024 1FFA84FC 		uxth	ip, r4
 671              		.loc 1 470 7 view .LVU219
 672 0028 0CEB1444 		add	r4, ip, r4, lsr #16
 673              	.LVL91:
 471:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
ARM GAS  /tmp/cckEb2gS.s 			page 22


 472:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return inet_cksum_pseudo_partial_base(p, proto, proto_len, chksum_len, acc);
 674              		.loc 1 472 3 is_stmt 1 view .LVU220
 675              		.loc 1 472 10 is_stmt 0 view .LVU221
 676 002c 0094     		str	r4, [sp]
 677              	.LVL92:
 678              		.loc 1 472 10 view .LVU222
 679 002e FFF7FEFF 		bl	inet_cksum_pseudo_partial_base
 680              	.LVL93:
 473:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 681              		.loc 1 473 1 view .LVU223
 682 0032 02B0     		add	sp, sp, #8
 683              	.LCFI10:
 684              		.cfi_def_cfa_offset 8
 685              		@ sp needed
 686 0034 10BD     		pop	{r4, pc}
 687              		.loc 1 473 1 view .LVU224
 688              		.cfi_endproc
 689              	.LFE175:
 691              		.section	.text.ip_chksum_pseudo_partial,"ax",%progbits
 692              		.align	1
 693              		.global	ip_chksum_pseudo_partial
 694              		.syntax unified
 695              		.thumb
 696              		.thumb_func
 698              	ip_chksum_pseudo_partial:
 699              	.LVL94:
 700              	.LFB176:
 474:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 */
 475:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 476:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV6
 477:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /**
 478:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the checksum with IPv6 pseudo header used by TCP and UDP for a pbuf chain.
 479:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * IPv6 addresses are expected to be in network byte order. Will only compute for a
 480:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * portion of the payload.
 481:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 482:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p chain of pbufs over that a checksum should be calculated (ip data part)
 483:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto ipv6 protocol/next header (used for checksum of pseudo header)
 484:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto_len length of the ipv6 payload (used for checksum of pseudo header)
 485:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param chksum_len number of payload bytes used to compute chksum
 486:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param src source ipv6 address (used for checksum of pseudo header)
 487:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dest destination ipv6 address (used for checksum of pseudo header)
 488:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 489:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 490:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 491:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** ip6_chksum_pseudo_partial(struct pbuf *p, u8_t proto, u16_t proto_len,
 492:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                           u16_t chksum_len, const ip6_addr_t *src, const ip6_addr_t *dest)
 493:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 494:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc = 0;
 495:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t addr;
 496:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u8_t addr_part;
 497:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 498:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   for (addr_part = 0; addr_part < 4; addr_part++) {
 499:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     addr = src->addr[addr_part];
 500:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + (addr & 0xffffUL));
 501:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 502:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     addr = dest->addr[addr_part];
 503:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + (addr & 0xffffUL));
ARM GAS  /tmp/cckEb2gS.s 			page 23


 504:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = (u32_t)(acc + ((addr >> 16) & 0xffffUL));
 505:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 506:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   /* fold down to 16 bits */
 507:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 508:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = FOLD_U32T(acc);
 509:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 510:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return inet_cksum_pseudo_partial_base(p, proto, proto_len, chksum_len, acc);
 511:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 512:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV6 */
 513:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 514:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /* ip_chksum_pseudo_partial:
 515:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 516:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the IPv4 or IPv6 pseudo Internet checksum used by TCP and UDP for a pbuf chain.
 517:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 518:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p chain of pbufs over that a checksum should be calculated (ip data part)
 519:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param src source ip address (used for checksum of pseudo header)
 520:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dst destination ip address (used for checksum of pseudo header)
 521:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto ip protocol (used for checksum of pseudo header)
 522:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param proto_len length of the ip data part (used for checksum of pseudo header)
 523:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 524:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 525:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 526:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** ip_chksum_pseudo_partial(struct pbuf *p, u8_t proto, u16_t proto_len,
 527:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****                          u16_t chksum_len, const ip_addr_t *src, const ip_addr_t *dest)
 528:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 701              		.loc 1 528 1 is_stmt 1 view -0
 702              		.cfi_startproc
 703              		@ args = 8, pretend = 0, frame = 0
 704              		@ frame_needed = 0, uses_anonymous_args = 0
 705              		.loc 1 528 1 is_stmt 0 view .LVU226
 706 0000 10B5     		push	{r4, lr}
 707              	.LCFI11:
 708              		.cfi_def_cfa_offset 8
 709              		.cfi_offset 4, -8
 710              		.cfi_offset 14, -4
 711 0002 82B0     		sub	sp, sp, #8
 712              	.LCFI12:
 713              		.cfi_def_cfa_offset 16
 529:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV6
 530:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (IP_IS_V6(dest)) {
 531:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     return ip6_chksum_pseudo_partial(p, proto, proto_len, chksum_len, ip_2_ip6(src), ip_2_ip6(dest)
 532:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 533:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV6 */
 534:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV4 && LWIP_IPV6
 535:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   else
 536:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 && LWIP_IPV6 */
 537:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #if LWIP_IPV4
 538:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   {
 539:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     return inet_chksum_pseudo_partial(p, proto, proto_len, chksum_len, ip_2_ip4(src), ip_2_ip4(dest
 714              		.loc 1 539 5 is_stmt 1 view .LVU227
 715              		.loc 1 539 12 is_stmt 0 view .LVU228
 716 0004 059C     		ldr	r4, [sp, #20]
 717 0006 0194     		str	r4, [sp, #4]
 718 0008 049C     		ldr	r4, [sp, #16]
 719 000a 0094     		str	r4, [sp]
 720 000c FFF7FEFF 		bl	inet_chksum_pseudo_partial
 721              	.LVL95:
ARM GAS  /tmp/cckEb2gS.s 			page 24


 540:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 541:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** #endif /* LWIP_IPV4 */
 542:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 722              		.loc 1 542 1 view .LVU229
 723 0010 02B0     		add	sp, sp, #8
 724              	.LCFI13:
 725              		.cfi_def_cfa_offset 8
 726              		@ sp needed
 727 0012 10BD     		pop	{r4, pc}
 728              		.loc 1 542 1 view .LVU230
 729              		.cfi_endproc
 730              	.LFE176:
 732              		.section	.text.inet_chksum,"ax",%progbits
 733              		.align	1
 734              		.global	inet_chksum
 735              		.syntax unified
 736              		.thumb
 737              		.thumb_func
 739              	inet_chksum:
 740              	.LVL96:
 741              	.LFB177:
 543:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 544:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /* inet_chksum:
 545:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 546:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculates the Internet checksum over a portion of memory. Used primarily for IP
 547:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * and ICMP.
 548:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 549:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param dataptr start of the buffer to calculate the checksum (no alignment needed)
 550:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param len length of the buffer to calculate the checksum
 551:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 552:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 553:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 554:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 555:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** inet_chksum(const void *dataptr, u16_t len)
 556:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 742              		.loc 1 556 1 is_stmt 1 view -0
 743              		.cfi_startproc
 744              		@ args = 0, pretend = 0, frame = 0
 745              		@ frame_needed = 0, uses_anonymous_args = 0
 746              		.loc 1 556 1 is_stmt 0 view .LVU232
 747 0000 08B5     		push	{r3, lr}
 748              	.LCFI14:
 749              		.cfi_def_cfa_offset 8
 750              		.cfi_offset 3, -8
 751              		.cfi_offset 14, -4
 557:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return (u16_t)~(unsigned int)LWIP_CHKSUM(dataptr, len);
 752              		.loc 1 557 3 is_stmt 1 view .LVU233
 753              		.loc 1 557 32 is_stmt 0 view .LVU234
 754 0002 FFF7FEFF 		bl	lwip_standard_chksum
 755              	.LVL97:
 756              		.loc 1 557 10 discriminator 1 view .LVU235
 757 0006 C043     		mvns	r0, r0
 558:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 758              		.loc 1 558 1 view .LVU236
 759 0008 80B2     		uxth	r0, r0
 760 000a 08BD     		pop	{r3, pc}
 761              		.cfi_endproc
ARM GAS  /tmp/cckEb2gS.s 			page 25


 762              	.LFE177:
 764              		.section	.text.inet_chksum_pbuf,"ax",%progbits
 765              		.align	1
 766              		.global	inet_chksum_pbuf
 767              		.syntax unified
 768              		.thumb
 769              		.thumb_func
 771              	inet_chksum_pbuf:
 772              	.LVL98:
 773              	.LFB178:
 559:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 560:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** /**
 561:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * Calculate a checksum over a chain of pbufs (without pseudo-header, much like
 562:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * inet_chksum only pbufs are used).
 563:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  *
 564:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @param p pbuf chain over that the checksum should be calculated
 565:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  * @return checksum (as u16_t) to be saved directly in the protocol header
 566:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****  */
 567:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** u16_t
 568:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** inet_chksum_pbuf(struct pbuf *p)
 569:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** {
 774              		.loc 1 569 1 is_stmt 1 view -0
 775              		.cfi_startproc
 776              		@ args = 0, pretend = 0, frame = 0
 777              		@ frame_needed = 0, uses_anonymous_args = 0
 778              		.loc 1 569 1 is_stmt 0 view .LVU238
 779 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 780              	.LCFI15:
 781              		.cfi_def_cfa_offset 24
 782              		.cfi_offset 3, -24
 783              		.cfi_offset 4, -20
 784              		.cfi_offset 5, -16
 785              		.cfi_offset 6, -12
 786              		.cfi_offset 7, -8
 787              		.cfi_offset 14, -4
 788 0002 0546     		mov	r5, r0
 570:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   u32_t acc;
 789              		.loc 1 570 3 is_stmt 1 view .LVU239
 571:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   struct pbuf *q;
 790              		.loc 1 571 3 view .LVU240
 572:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int swapped = 0;
 791              		.loc 1 572 3 view .LVU241
 792              	.LVL99:
 573:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 574:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   acc = 0;
 793              		.loc 1 574 3 view .LVU242
 575:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   for (q = p; q != NULL; q = q->next) {
 794              		.loc 1 575 3 view .LVU243
 572:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   int swapped = 0;
 795              		.loc 1 572 7 is_stmt 0 view .LVU244
 796 0004 0027     		movs	r7, #0
 574:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   for (q = p; q != NULL; q = q->next) {
 797              		.loc 1 574 7 view .LVU245
 798 0006 3C46     		mov	r4, r7
 799              		.loc 1 575 3 view .LVU246
 800 0008 00E0     		b	.L40
 801              	.LVL100:
ARM GAS  /tmp/cckEb2gS.s 			page 26


 802              	.L41:
 803              		.loc 1 575 28 is_stmt 1 discriminator 2 view .LVU247
 804 000a 2D68     		ldr	r5, [r5]
 805              	.LVL101:
 806              	.L40:
 807              		.loc 1 575 17 discriminator 1 view .LVU248
 808 000c 9DB1     		cbz	r5, .L45
 576:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc += LWIP_CHKSUM(q->payload, q->len);
 809              		.loc 1 576 5 view .LVU249
 810              		.loc 1 576 37 is_stmt 0 view .LVU250
 811 000e 6E89     		ldrh	r6, [r5, #10]
 812              		.loc 1 576 12 view .LVU251
 813 0010 3146     		mov	r1, r6
 814 0012 6868     		ldr	r0, [r5, #4]
 815 0014 FFF7FEFF 		bl	lwip_standard_chksum
 816              	.LVL102:
 817              		.loc 1 576 9 discriminator 1 view .LVU252
 818 0018 0319     		adds	r3, r0, r4
 819              	.LVL103:
 577:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = FOLD_U32T(acc);
 820              		.loc 1 577 5 is_stmt 1 view .LVU253
 821              		.loc 1 577 11 is_stmt 0 view .LVU254
 822 001a 9CB2     		uxth	r4, r3
 823              		.loc 1 577 9 view .LVU255
 824 001c 04EB1344 		add	r4, r4, r3, lsr #16
 825              	.LVL104:
 578:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     if (q->len % 2 != 0) {
 826              		.loc 1 578 5 is_stmt 1 view .LVU256
 827              		.loc 1 578 8 is_stmt 0 view .LVU257
 828 0020 16F0010F 		tst	r6, #1
 829 0024 F1D0     		beq	.L41
 579:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       swapped = !swapped;
 830              		.loc 1 579 7 is_stmt 1 view .LVU258
 831              		.loc 1 579 17 is_stmt 0 view .LVU259
 832 0026 87F00107 		eor	r7, r7, #1
 833              	.LVL105:
 580:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****       acc = SWAP_BYTES_IN_WORD(acc);
 834              		.loc 1 580 7 is_stmt 1 view .LVU260
 835              		.loc 1 580 13 is_stmt 0 view .LVU261
 836 002a 2302     		lsls	r3, r4, #8
 837 002c 9BB2     		uxth	r3, r3
 838 002e C4F30724 		ubfx	r4, r4, #8, #8
 839              	.LVL106:
 840              		.loc 1 580 11 view .LVU262
 841 0032 1C43     		orrs	r4, r4, r3
 842              	.LVL107:
 843              		.loc 1 580 11 view .LVU263
 844 0034 E9E7     		b	.L41
 845              	.L45:
 581:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     }
 582:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 583:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** 
 584:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   if (swapped) {
 846              		.loc 1 584 3 is_stmt 1 view .LVU264
 847              		.loc 1 584 6 is_stmt 0 view .LVU265
 848 0036 27B1     		cbz	r7, .L43
 585:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****     acc = SWAP_BYTES_IN_WORD(acc);
ARM GAS  /tmp/cckEb2gS.s 			page 27


 849              		.loc 1 585 5 is_stmt 1 view .LVU266
 850              		.loc 1 585 11 is_stmt 0 view .LVU267
 851 0038 2302     		lsls	r3, r4, #8
 852 003a 9BB2     		uxth	r3, r3
 853 003c C4F30724 		ubfx	r4, r4, #8, #8
 854              	.LVL108:
 855              		.loc 1 585 9 view .LVU268
 856 0040 1C43     		orrs	r4, r4, r3
 857              	.LVL109:
 858              	.L43:
 586:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   }
 587:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c ****   return (u16_t)~(acc & 0xffffUL);
 859              		.loc 1 587 3 is_stmt 1 view .LVU269
 860              		.loc 1 587 10 is_stmt 0 view .LVU270
 861 0042 E043     		mvns	r0, r4
 588:Middlewares/Third_Party/LwIP/src/core/inet_chksum.c **** }
 862              		.loc 1 588 1 view .LVU271
 863 0044 80B2     		uxth	r0, r0
 864 0046 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 865              		.loc 1 588 1 view .LVU272
 866              		.cfi_endproc
 867              	.LFE178:
 869              		.text
 870              	.Letext0:
 871              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 872              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 873              		.file 4 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 874              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 875              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 876              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 877              		.file 8 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 878              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
ARM GAS  /tmp/cckEb2gS.s 			page 28


DEFINED SYMBOLS
                            *ABS*:00000000 inet_chksum.c
     /tmp/cckEb2gS.s:20     .text.lwip_standard_chksum:00000000 $t
     /tmp/cckEb2gS.s:26     .text.lwip_standard_chksum:00000000 lwip_standard_chksum
     /tmp/cckEb2gS.s:154    .text.inet_cksum_pseudo_base:00000000 $t
     /tmp/cckEb2gS.s:159    .text.inet_cksum_pseudo_base:00000000 inet_cksum_pseudo_base
     /tmp/cckEb2gS.s:292    .rodata.inet_cksum_pseudo_partial_base.str1.4:00000000 $d
     /tmp/cckEb2gS.s:303    .text.inet_cksum_pseudo_partial_base:00000000 $t
     /tmp/cckEb2gS.s:308    .text.inet_cksum_pseudo_partial_base:00000000 inet_cksum_pseudo_partial_base
     /tmp/cckEb2gS.s:492    .text.inet_cksum_pseudo_partial_base:000000a8 $d
     /tmp/cckEb2gS.s:499    .text.inet_chksum_pseudo:00000000 $t
     /tmp/cckEb2gS.s:505    .text.inet_chksum_pseudo:00000000 inet_chksum_pseudo
     /tmp/cckEb2gS.s:571    .text.ip_chksum_pseudo:00000000 $t
     /tmp/cckEb2gS.s:577    .text.ip_chksum_pseudo:00000000 ip_chksum_pseudo
     /tmp/cckEb2gS.s:610    .text.inet_chksum_pseudo_partial:00000000 $t
     /tmp/cckEb2gS.s:616    .text.inet_chksum_pseudo_partial:00000000 inet_chksum_pseudo_partial
     /tmp/cckEb2gS.s:692    .text.ip_chksum_pseudo_partial:00000000 $t
     /tmp/cckEb2gS.s:698    .text.ip_chksum_pseudo_partial:00000000 ip_chksum_pseudo_partial
     /tmp/cckEb2gS.s:733    .text.inet_chksum:00000000 $t
     /tmp/cckEb2gS.s:739    .text.inet_chksum:00000000 inet_chksum
     /tmp/cckEb2gS.s:765    .text.inet_chksum_pbuf:00000000 $t
     /tmp/cckEb2gS.s:771    .text.inet_chksum_pbuf:00000000 inet_chksum_pbuf

UNDEFINED SYMBOLS
lwip_htons
printf
