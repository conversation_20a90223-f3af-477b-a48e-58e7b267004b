ARM GAS  /tmp/ccSZflmK.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stream_buffer.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c"
  19              		.section	.text.prvBytesInBuffer,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	prvBytesInBuffer:
  26              	.LVL0:
  27              	.LFB24:
   1:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* Standard includes. */
  29:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #include <stdint.h>
  30:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #include <string.h>
  31:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 2


  32:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* Defining MPU_WRAPPERS_INCLUDED_FROM_API_FILE prevents task.h from redefining
  33:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** all the API functions to use the MPU wrappers.  That should only be done when
  34:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** task.h is included from an application file. */
  35:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #define MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  36:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  37:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* FreeRTOS includes. */
  38:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #include "FreeRTOS.h"
  39:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #include "task.h"
  40:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #include "stream_buffer.h"
  41:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  42:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if( configUSE_TASK_NOTIFICATIONS != 1 )
  43:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#error configUSE_TASK_NOTIFICATIONS must be set to 1 to build stream_buffer.c
  44:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif
  45:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* Lint e961, e9021 and e750 are suppressed as a MISRA exception justified
  47:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** because the MPU ports require MPU_WRAPPERS_INCLUDED_FROM_API_FILE to be defined
  48:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** for the header files above, but not in this file, in order to generate the
  49:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** correct privileged Vs unprivileged linkage and placement. */
  50:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #undef MPU_WRAPPERS_INCLUDED_FROM_API_FILE /*lint !e961 !e750 !e9021. */
  51:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  52:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* If the user has not provided application specific Rx notification macros,
  53:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** or #defined the notification macros away, them provide default implementations
  54:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** that uses task notifications. */
  55:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*lint -save -e9026 Function like macros allowed and needed here so they can be overidden. */
  56:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #ifndef sbRECEIVE_COMPLETED
  57:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#define sbRECEIVE_COMPLETED( pxStreamBuffer )										\
  58:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		vTaskSuspendAll();																\
  59:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{																				\
  60:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			if( ( pxStreamBuffer )->xTaskWaitingToSend != NULL )						\
  61:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{																			\
  62:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( void ) xTaskNotify( ( pxStreamBuffer )->xTaskWaitingToSend,			\
  63:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  ( uint32_t ) 0,									\
  64:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  eNoAction );										\
  65:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( pxStreamBuffer )->xTaskWaitingToSend = NULL;							\
  66:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}																			\
  67:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}																				\
  68:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) xTaskResumeAll();
  69:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* sbRECEIVE_COMPLETED */
  70:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  71:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #ifndef sbRECEIVE_COMPLETED_FROM_ISR
  72:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#define sbRECEIVE_COMPLETED_FROM_ISR( pxStreamBuffer,								\
  73:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  pxHigherPriorityTaskWoken )					\
  74:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{																					\
  75:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	UBaseType_t uxSavedInterruptStatus;													\
  76:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 																						\
  77:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		uxSavedInterruptStatus = ( UBaseType_t ) portSET_INTERRUPT_MASK_FROM_ISR();		\
  78:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{																				\
  79:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			if( ( pxStreamBuffer )->xTaskWaitingToSend != NULL )						\
  80:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{																			\
  81:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( void ) xTaskNotifyFromISR( ( pxStreamBuffer )->xTaskWaitingToSend,	\
  82:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											 ( uint32_t ) 0,							\
  83:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											 eNoAction,									\
  84:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											 pxHigherPriorityTaskWoken );				\
  85:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( pxStreamBuffer )->xTaskWaitingToSend = NULL;							\
  86:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}																			\
  87:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}																				\
  88:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		portCLEAR_INTERRUPT_MASK_FROM_ISR( uxSavedInterruptStatus );					\
ARM GAS  /tmp/ccSZflmK.s 			page 3


  89:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
  90:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* sbRECEIVE_COMPLETED_FROM_ISR */
  91:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
  92:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* If the user has not provided an application specific Tx notification macro,
  93:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** or #defined the notification macro away, them provide a default implementation
  94:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** that uses task notifications. */
  95:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #ifndef sbSEND_COMPLETED
  96:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#define sbSEND_COMPLETED( pxStreamBuffer )											\
  97:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		vTaskSuspendAll();																\
  98:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{																				\
  99:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			if( ( pxStreamBuffer )->xTaskWaitingToReceive != NULL )						\
 100:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{																			\
 101:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( void ) xTaskNotify( ( pxStreamBuffer )->xTaskWaitingToReceive,		\
 102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  ( uint32_t ) 0,									\
 103:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  eNoAction );										\
 104:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( pxStreamBuffer )->xTaskWaitingToReceive = NULL;						\
 105:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}																			\
 106:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}																				\
 107:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) xTaskResumeAll();
 108:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* sbSEND_COMPLETED */
 109:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #ifndef sbSEND_COMPLETE_FROM_ISR
 111:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#define sbSEND_COMPLETE_FROM_ISR( pxStreamBuffer, pxHigherPriorityTaskWoken )		\
 112:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{																					\
 113:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	UBaseType_t uxSavedInterruptStatus;													\
 114:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 																						\
 115:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		uxSavedInterruptStatus = ( UBaseType_t ) portSET_INTERRUPT_MASK_FROM_ISR();		\
 116:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{																				\
 117:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			if( ( pxStreamBuffer )->xTaskWaitingToReceive != NULL )						\
 118:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{																			\
 119:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( void ) xTaskNotifyFromISR( ( pxStreamBuffer )->xTaskWaitingToReceive,	\
 120:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											 ( uint32_t ) 0,							\
 121:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											 eNoAction,									\
 122:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											 pxHigherPriorityTaskWoken );				\
 123:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( pxStreamBuffer )->xTaskWaitingToReceive = NULL;						\
 124:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}																			\
 125:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}																				\
 126:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		portCLEAR_INTERRUPT_MASK_FROM_ISR( uxSavedInterruptStatus );					\
 127:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 128:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* sbSEND_COMPLETE_FROM_ISR */
 129:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*lint -restore (9026) */
 130:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 131:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* The number of bytes used to hold the length of a message in the buffer. */
 132:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #define sbBYTES_TO_STORE_MESSAGE_LENGTH ( sizeof( configMESSAGE_BUFFER_LENGTH_TYPE ) )
 133:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* Bits stored in the ucFlags field of the stream buffer. */
 135:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #define sbFLAGS_IS_MESSAGE_BUFFER		( ( uint8_t ) 1 ) /* Set if the stream buffer was created as a m
 136:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #define sbFLAGS_IS_STATICALLY_ALLOCATED ( ( uint8_t ) 2 ) /* Set if the stream buffer was created u
 137:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 138:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 139:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 140:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* Structure that hold state information on the buffer. */
 141:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** typedef struct StreamBufferDef_t /*lint !e9058 Style convention uses tag. */
 142:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 143:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	volatile size_t xTail;				/* Index to the next item to read within the buffer. */
 144:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	volatile size_t xHead;				/* Index to the next item to write within the buffer. */
 145:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	size_t xLength;						/* The length of the buffer pointed to by pucBuffer. */
ARM GAS  /tmp/ccSZflmK.s 			page 4


 146:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	size_t xTriggerLevelBytes;			/* The number of bytes that must be in the stream buffer before a tas
 147:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	volatile TaskHandle_t xTaskWaitingToReceive; /* Holds the handle of a task waiting for data, or NU
 148:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	volatile TaskHandle_t xTaskWaitingToSend;	/* Holds the handle of a task waiting to send data to a 
 149:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t *pucBuffer;					/* Points to the buffer itself - that is - the RAM that stores the data pa
 150:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t ucFlags;
 151:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#if ( configUSE_TRACE_FACILITY == 1 )
 153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		UBaseType_t uxStreamBufferNumber;		/* Used for tracing purposes. */
 154:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#endif
 155:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** } StreamBuffer_t;
 156:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 157:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
 158:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * The number of bytes available to be read from the buffer.
 159:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
 160:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvBytesInBuffer( const StreamBuffer_t * const pxStreamBuffer ) PRIVILEGED_FUNCTION;
 161:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 162:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
 163:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * Add xCount bytes from pucData into the pxStreamBuffer message buffer.
 164:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * Returns the number of bytes written, which will either equal xCount in the
 165:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * success case, or 0 if there was not enough space in the buffer (in which case
 166:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * no data is written into the buffer).
 167:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
 168:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvWriteBytesToBuffer( StreamBuffer_t * const pxStreamBuffer, const uint8_t *pucData,
 169:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 170:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
 171:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * If the stream buffer is being used as a message buffer, then reads an entire
 172:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * message out of the buffer.  If the stream buffer is being used as a stream
 173:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * buffer then read as many bytes as possible from the buffer.
 174:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * prvReadBytesFromBuffer() is called to actually extract the bytes from the
 175:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * buffer's data storage area.
 176:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
 177:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvReadMessageFromBuffer( StreamBuffer_t *pxStreamBuffer,
 178:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										void *pvRxData,
 179:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xBufferLengthBytes,
 180:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xBytesAvailable,
 181:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xBytesToStoreMessageLength ) PRIVILEGED_FUNCTION;
 182:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 183:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
 184:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * If the stream buffer is being used as a message buffer, then writes an entire
 185:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * message to the buffer.  If the stream buffer is being used as a stream
 186:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * buffer then write as many bytes as possible to the buffer.
 187:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * prvWriteBytestoBuffer() is called to actually send the bytes to the buffer's
 188:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * data storage area.
 189:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
 190:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvWriteMessageToBuffer(  StreamBuffer_t * const pxStreamBuffer,
 191:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										const void * pvTxData,
 192:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xDataLengthBytes,
 193:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xSpace,
 194:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xRequiredSpace ) PRIVILEGED_FUNCTION;
 195:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 196:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
 197:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * Read xMaxCount bytes from the pxStreamBuffer message buffer and write them
 198:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * to pucData.
 199:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
 200:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvReadBytesFromBuffer( StreamBuffer_t *pxStreamBuffer,
 201:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  uint8_t *pucData,
 202:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  size_t xMaxCount,
ARM GAS  /tmp/ccSZflmK.s 			page 5


 203:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									  size_t xBytesAvailable ) PRIVILEGED_FUNCTION;
 204:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 205:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*
 206:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * Called by both pxStreamBufferCreate() and pxStreamBufferCreateStatic() to
 207:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  * initialise the members of the newly created stream buffer structure.
 208:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c ****  */
 209:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static void prvInitialiseNewStreamBuffer( StreamBuffer_t * const pxStreamBuffer,
 210:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  uint8_t * const pucBuffer,
 211:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  size_t xBufferSizeBytes,
 212:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  size_t xTriggerLevelBytes,
 213:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  uint8_t ucFlags ) PRIVILEGED_FUNCTION;
 214:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 215:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 216:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 217:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if( configSUPPORT_DYNAMIC_ALLOCATION == 1 )
 218:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 219:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBufferHandle_t xStreamBufferGenericCreate( size_t xBufferSizeBytes, size_t xTriggerLevelByte
 220:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 221:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t *pucAllocatedMemory;
 222:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t ucFlags;
 223:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 224:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* In case the stream buffer is going to be used as a message buffer
 225:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		(that is, it will hold discrete messages with a little meta data that
 226:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		says how big the next message is) check the buffer will be large enough
 227:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		to hold at least one message. */
 228:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xIsMessageBuffer == pdTRUE )
 229:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 230:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Is a message buffer but not statically allocated. */
 231:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			ucFlags = sbFLAGS_IS_MESSAGE_BUFFER;
 232:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBufferSizeBytes > sbBYTES_TO_STORE_MESSAGE_LENGTH );
 233:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 234:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 235:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 236:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Not a message buffer and not statically allocated. */
 237:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			ucFlags = 0;
 238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBufferSizeBytes > 0 );
 239:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 241:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 242:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* A trigger level of 0 would cause a waiting task to unblock even when
 243:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		the buffer was empty. */
 244:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xTriggerLevelBytes == ( size_t ) 0 )
 245:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 246:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xTriggerLevelBytes = ( size_t ) 1;
 247:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 248:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 249:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* A stream buffer requires a StreamBuffer_t structure and a buffer.
 250:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		Both are allocated in a single call to pvPortMalloc().  The
 251:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		StreamBuffer_t structure is placed at the start of the allocated memory
 252:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		and the buffer follows immediately after.  The requested size is
 253:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		incremented so the free space is returned as the user would expect -
 254:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		this is a quirk of the implementation that means otherwise the free
 255:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		space would be reported as one byte smaller than would be logically
 256:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		expected. */
 257:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBufferSizeBytes++;
 258:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		pucAllocatedMemory = ( uint8_t * ) pvPortMalloc( xBufferSizeBytes + sizeof( StreamBuffer_t ) ); /
 259:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 6


 260:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( pucAllocatedMemory != NULL )
 261:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 262:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			prvInitialiseNewStreamBuffer( ( StreamBuffer_t * ) pucAllocatedMemory, /* Structure at the start
 263:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										   pucAllocatedMemory + sizeof( StreamBuffer_t ),  /* Storage area follows. */ /*lint !e9
 264:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										   xBufferSizeBytes,
 265:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										   xTriggerLevelBytes,
 266:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										   ucFlags );
 267:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 268:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceSTREAM_BUFFER_CREATE( ( ( StreamBuffer_t * ) pucAllocatedMemory ), xIsMessageBuffer );
 269:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 270:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 271:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 272:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceSTREAM_BUFFER_CREATE_FAILED( xIsMessageBuffer );
 273:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 274:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 275:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		return ( StreamBufferHandle_t ) pucAllocatedMemory; /*lint !e9087 !e826 Safe cast as allocated me
 276:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 277:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 278:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* configSUPPORT_DYNAMIC_ALLOCATION */
 279:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 280:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 281:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if( configSUPPORT_STATIC_ALLOCATION == 1 )
 282:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 283:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBufferHandle_t xStreamBufferGenericCreateStatic( size_t xBufferSizeBytes,
 284:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 														   size_t xTriggerLevelBytes,
 285:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 														   BaseType_t xIsMessageBuffer,
 286:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 														   uint8_t * const pucStreamBufferStorageArea,
 287:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 														   StaticStreamBuffer_t * const pxStaticStreamBuffer )
 288:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 289:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBuffer_t * const pxStreamBuffer = ( StreamBuffer_t * ) pxStaticStreamBuffer; /*lint !e740 !e
 290:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBufferHandle_t xReturn;
 291:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t ucFlags;
 292:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 293:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pucStreamBufferStorageArea );
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pxStaticStreamBuffer );
 295:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 296:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 297:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* A trigger level of 0 would cause a waiting task to unblock even when
 298:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		the buffer was empty. */
 299:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xTriggerLevelBytes == ( size_t ) 0 )
 300:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 301:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xTriggerLevelBytes = ( size_t ) 1;
 302:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 303:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 304:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xIsMessageBuffer != pdFALSE )
 305:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 306:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Statically allocated message buffer. */
 307:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			ucFlags = sbFLAGS_IS_MESSAGE_BUFFER | sbFLAGS_IS_STATICALLY_ALLOCATED;
 308:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 309:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 310:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 311:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Statically allocated stream buffer. */
 312:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			ucFlags = sbFLAGS_IS_STATICALLY_ALLOCATED;
 313:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 314:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 315:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* In case the stream buffer is going to be used as a message buffer
 316:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		(that is, it will hold discrete messages with a little meta data that
ARM GAS  /tmp/ccSZflmK.s 			page 7


 317:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		says how big the next message is) check the buffer will be large enough
 318:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		to hold at least one message. */
 319:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xBufferSizeBytes > sbBYTES_TO_STORE_MESSAGE_LENGTH );
 320:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 321:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		#if( configASSERT_DEFINED == 1 )
 322:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 323:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Sanity check that the size of the structure used to declare a
 324:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			variable of type StaticStreamBuffer_t equals the size of the real
 325:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			message buffer structure. */
 326:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			volatile size_t xSize = sizeof( StaticStreamBuffer_t );
 327:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xSize == sizeof( StreamBuffer_t ) );
 328:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} /*lint !e529 xSize is referenced is configASSERT() is defined. */
 329:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		#endif /* configASSERT_DEFINED */
 330:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 331:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( ( pucStreamBufferStorageArea != NULL ) && ( pxStaticStreamBuffer != NULL ) )
 332:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 333:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			prvInitialiseNewStreamBuffer( pxStreamBuffer,
 334:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  pucStreamBufferStorageArea,
 335:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  xBufferSizeBytes,
 336:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  xTriggerLevelBytes,
 337:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  ucFlags );
 338:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 339:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Remember this was statically allocated in case it is ever deleted
 340:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			again. */
 341:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->ucFlags |= sbFLAGS_IS_STATICALLY_ALLOCATED;
 342:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 343:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceSTREAM_BUFFER_CREATE( pxStreamBuffer, xIsMessageBuffer );
 344:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 345:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = ( StreamBufferHandle_t ) pxStaticStreamBuffer; /*lint !e9087 Data hiding requires cast
 346:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 347:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 348:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 349:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = NULL;
 350:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceSTREAM_BUFFER_CREATE_STATIC_FAILED( xReturn, xIsMessageBuffer );
 351:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 352:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 353:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		return xReturn;
 354:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 355:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 356:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* ( configSUPPORT_STATIC_ALLOCATION == 1 ) */
 357:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 358:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 359:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** void vStreamBufferDelete( StreamBufferHandle_t xStreamBuffer )
 360:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 361:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * pxStreamBuffer = xStreamBuffer;
 362:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 363:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 364:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 365:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	traceSTREAM_BUFFER_DELETE( xStreamBuffer );
 366:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 367:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_STATICALLY_ALLOCATED ) == ( uint8_t ) pdFALSE )
 368:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 369:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		#if( configSUPPORT_DYNAMIC_ALLOCATION == 1 )
 370:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 371:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Both the structure and the buffer were allocated using a single call
 372:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			to pvPortMalloc(), hence only one call to vPortFree() is required. */
 373:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			vPortFree( ( void * ) pxStreamBuffer ); /*lint !e9087 Standard free() semantics require void *, 
ARM GAS  /tmp/ccSZflmK.s 			page 8


 374:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 375:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		#else
 376:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 377:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Should not be possible to get here, ucFlags must be corrupt.
 378:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			Force an assert. */
 379:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xStreamBuffer == ( StreamBufferHandle_t ) ~0 );
 380:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 381:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		#endif
 382:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 383:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 384:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 385:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* The structure and buffer were not allocated dynamically and cannot be
 386:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		freed - just scrub the structure so future use will assert. */
 387:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memset( pxStreamBuffer, 0x00, sizeof( StreamBuffer_t ) );
 388:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 389:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 390:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 391:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 392:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xStreamBufferReset( StreamBufferHandle_t xStreamBuffer )
 393:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 394:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 395:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn = pdFAIL;
 396:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 397:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if( configUSE_TRACE_FACILITY == 1 )
 398:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	UBaseType_t uxStreamBufferNumber;
 399:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif
 400:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 401:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 402:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 403:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#if( configUSE_TRACE_FACILITY == 1 )
 404:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 405:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Store the stream buffer number so it can be restored after the
 406:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		reset. */
 407:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		uxStreamBufferNumber = pxStreamBuffer->uxStreamBufferNumber;
 408:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 409:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#endif
 410:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 411:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Can only reset a message buffer if there are no tasks blocked on it. */
 412:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	taskENTER_CRITICAL();
 413:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 414:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( pxStreamBuffer->xTaskWaitingToReceive == NULL )
 415:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 416:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			if( pxStreamBuffer->xTaskWaitingToSend == NULL )
 417:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 418:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				prvInitialiseNewStreamBuffer( pxStreamBuffer,
 419:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											  pxStreamBuffer->pucBuffer,
 420:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											  pxStreamBuffer->xLength,
 421:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											  pxStreamBuffer->xTriggerLevelBytes,
 422:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											  pxStreamBuffer->ucFlags );
 423:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				xReturn = pdPASS;
 424:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 425:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				#if( configUSE_TRACE_FACILITY == 1 )
 426:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				{
 427:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->uxStreamBufferNumber = uxStreamBufferNumber;
 428:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 429:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				#endif
 430:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 9


 431:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				traceSTREAM_BUFFER_RESET( xStreamBuffer );
 432:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 433:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 434:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 435:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	taskEXIT_CRITICAL();
 436:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 437:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 438:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 439:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 440:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 441:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xStreamBufferSetTriggerLevel( StreamBufferHandle_t xStreamBuffer, size_t xTriggerLevel )
 442:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 443:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 444:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 445:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 446:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 447:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 448:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* It is not valid for the trigger level to be 0. */
 449:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xTriggerLevel == ( size_t ) 0 )
 450:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 451:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xTriggerLevel = ( size_t ) 1;
 452:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 453:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 454:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* The trigger level is the number of bytes that must be in the stream
 455:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	buffer before a task that is waiting for data is unblocked. */
 456:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xTriggerLevel <= pxStreamBuffer->xLength )
 457:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 458:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		pxStreamBuffer->xTriggerLevelBytes = xTriggerLevel;
 459:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdPASS;
 460:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 461:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 462:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 463:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdFALSE;
 464:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 465:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 466:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 467:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 468:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 469:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 470:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferSpacesAvailable( StreamBufferHandle_t xStreamBuffer )
 471:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 472:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 473:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xSpace;
 474:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 475:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 476:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 477:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace = pxStreamBuffer->xLength + pxStreamBuffer->xTail;
 478:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= pxStreamBuffer->xHead;
 479:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= ( size_t ) 1;
 480:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 481:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xSpace >= pxStreamBuffer->xLength )
 482:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 483:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xSpace -= pxStreamBuffer->xLength;
 484:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 485:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 486:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 487:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
ARM GAS  /tmp/ccSZflmK.s 			page 10


 488:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 489:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 490:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xSpace;
 491:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 492:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 493:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 494:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferBytesAvailable( StreamBufferHandle_t xStreamBuffer )
 495:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 496:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 497:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn;
 498:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 499:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 500:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 501:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvBytesInBuffer( pxStreamBuffer );
 502:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 503:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 504:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 505:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 506:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferSend( StreamBufferHandle_t xStreamBuffer,
 507:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 						  const void *pvTxData,
 508:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 						  size_t xDataLengthBytes,
 509:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 						  TickType_t xTicksToWait )
 510:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 511:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 512:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn, xSpace = 0;
 513:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xRequiredSpace = xDataLengthBytes;
 514:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** TimeOut_t xTimeOut;
 515:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 516:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pvTxData );
 517:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 518:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 519:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* This send function is used to write to both message buffers and stream
 520:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	buffers.  If this is a message buffer then the space needed must be
 521:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	increased by the amount of bytes needed to store the length of the
 522:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	message. */
 523:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) != ( uint8_t ) 0 )
 524:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 525:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xRequiredSpace += sbBYTES_TO_STORE_MESSAGE_LENGTH;
 526:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 527:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Overflow? */
 528:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xRequiredSpace > xDataLengthBytes );
 529:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 530:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 531:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 532:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 533:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 534:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 535:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xTicksToWait != ( TickType_t ) 0 )
 536:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 537:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		vTaskSetTimeOutState( &xTimeOut );
 538:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 539:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		do
 540:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 541:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Wait until the required number of bytes are free in the message
 542:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			buffer. */
 543:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			taskENTER_CRITICAL();
 544:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
ARM GAS  /tmp/ccSZflmK.s 			page 11


 545:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				xSpace = xStreamBufferSpacesAvailable( pxStreamBuffer );
 546:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 547:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				if( xSpace < xRequiredSpace )
 548:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				{
 549:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					/* Clear notification state as going to wait for space. */
 550:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					( void ) xTaskNotifyStateClear( NULL );
 551:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 552:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					/* Should only be one writer. */
 553:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					configASSERT( pxStreamBuffer->xTaskWaitingToSend == NULL );
 554:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->xTaskWaitingToSend = xTaskGetCurrentTaskHandle();
 555:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 556:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				else
 557:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				{
 558:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					taskEXIT_CRITICAL();
 559:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					break;
 560:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 561:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 562:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			taskEXIT_CRITICAL();
 563:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 564:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceBLOCKING_ON_STREAM_BUFFER_SEND( xStreamBuffer );
 565:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) xTaskNotifyWait( ( uint32_t ) 0, ( uint32_t ) 0, NULL, xTicksToWait );
 566:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTaskWaitingToSend = NULL;
 567:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 568:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} while( xTaskCheckForTimeOut( &xTimeOut, &xTicksToWait ) == pdFALSE );
 569:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 570:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 571:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 572:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 573:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 574:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 575:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xSpace == ( size_t ) 0 )
 576:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 577:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xSpace = xStreamBufferSpacesAvailable( pxStreamBuffer );
 578:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 579:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 580:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 581:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 582:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 583:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 584:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvWriteMessageToBuffer( pxStreamBuffer, pvTxData, xDataLengthBytes, xSpace, xRequiredSp
 585:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 586:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xReturn > ( size_t ) 0 )
 587:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 588:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		traceSTREAM_BUFFER_SEND( xStreamBuffer, xReturn );
 589:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 590:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Was a task waiting for the data? */
 591:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( prvBytesInBuffer( pxStreamBuffer ) >= pxStreamBuffer->xTriggerLevelBytes )
 592:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			sbSEND_COMPLETED( pxStreamBuffer );
 594:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 595:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 596:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 597:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
 598:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 599:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 600:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 601:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
ARM GAS  /tmp/ccSZflmK.s 			page 12


 602:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 603:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		traceSTREAM_BUFFER_SEND_FAILED( xStreamBuffer );
 604:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 605:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 606:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 607:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 608:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 609:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 610:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferSendFromISR( StreamBufferHandle_t xStreamBuffer,
 611:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 								 const void *pvTxData,
 612:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 								 size_t xDataLengthBytes,
 613:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 								 BaseType_t * const pxHigherPriorityTaskWoken )
 614:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 615:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 616:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn, xSpace;
 617:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xRequiredSpace = xDataLengthBytes;
 618:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 619:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pvTxData );
 620:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 621:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 622:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* This send function is used to write to both message buffers and stream
 623:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	buffers.  If this is a message buffer then the space needed must be
 624:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	increased by the amount of bytes needed to store the length of the
 625:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	message. */
 626:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) != ( uint8_t ) 0 )
 627:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 628:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xRequiredSpace += sbBYTES_TO_STORE_MESSAGE_LENGTH;
 629:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 630:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 631:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 632:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 633:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 634:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 635:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace = xStreamBufferSpacesAvailable( pxStreamBuffer );
 636:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvWriteMessageToBuffer( pxStreamBuffer, pvTxData, xDataLengthBytes, xSpace, xRequiredSp
 637:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 638:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xReturn > ( size_t ) 0 )
 639:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 640:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Was a task waiting for the data? */
 641:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( prvBytesInBuffer( pxStreamBuffer ) >= pxStreamBuffer->xTriggerLevelBytes )
 642:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			sbSEND_COMPLETE_FROM_ISR( pxStreamBuffer, pxHigherPriorityTaskWoken );
 644:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 645:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 646:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 647:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
 648:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 649:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 650:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 651:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 652:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 653:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 654:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 655:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	traceSTREAM_BUFFER_SEND_FROM_ISR( xStreamBuffer, xReturn );
 656:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 657:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 658:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
ARM GAS  /tmp/ccSZflmK.s 			page 13


 659:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 660:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 661:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvWriteMessageToBuffer( StreamBuffer_t * const pxStreamBuffer,
 662:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									   const void * pvTxData,
 663:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									   size_t xDataLengthBytes,
 664:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									   size_t xSpace,
 665:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									   size_t xRequiredSpace )
 666:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 667:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	BaseType_t xShouldWrite;
 668:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	size_t xReturn;
 669:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 670:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xSpace == ( size_t ) 0 )
 671:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 672:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Doesn't matter if this is a stream buffer or a message buffer, there
 673:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		is no space to write. */
 674:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xShouldWrite = pdFALSE;
 675:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 676:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) == ( uint8_t ) 0 )
 677:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 678:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* This is a stream buffer, as opposed to a message buffer, so writing a
 679:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		stream of bytes rather than discrete messages.  Write as many bytes as
 680:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		possible. */
 681:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xShouldWrite = pdTRUE;
 682:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xDataLengthBytes = configMIN( xDataLengthBytes, xSpace );
 683:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 684:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else if( xSpace >= xRequiredSpace )
 685:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 686:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* This is a message buffer, as opposed to a stream buffer, and there
 687:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		is enough space to write both the message length and the message itself
 688:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		into the buffer.  Start by writing the length of the data, the data
 689:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		itself will be written later in this function. */
 690:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xShouldWrite = pdTRUE;
 691:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) prvWriteBytesToBuffer( pxStreamBuffer, ( const uint8_t * ) &( xDataLengthBytes ), sbBYTE
 692:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 693:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 694:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 695:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* There is space available, but not enough space. */
 696:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xShouldWrite = pdFALSE;
 697:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 698:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 699:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xShouldWrite != pdFALSE )
 700:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 701:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Writes the data itself. */
 702:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = prvWriteBytesToBuffer( pxStreamBuffer, ( const uint8_t * ) pvTxData, xDataLengthBytes )
 703:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 704:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 705:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 706:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = 0;
 707:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 708:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 709:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 710:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 711:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 712:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 713:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferReceive( StreamBufferHandle_t xStreamBuffer,
 714:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 							 void *pvRxData,
 715:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 							 size_t xBufferLengthBytes,
ARM GAS  /tmp/ccSZflmK.s 			page 14


 716:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 							 TickType_t xTicksToWait )
 717:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 718:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 719:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReceivedLength = 0, xBytesAvailable, xBytesToStoreMessageLength;
 720:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 721:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pvRxData );
 722:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 723:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 724:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* This receive function is used by both message buffers, which store
 725:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	discrete messages, and stream buffers, which store a continuous stream of
 726:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	bytes.  Discrete messages include an additional
 727:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	sbBYTES_TO_STORE_MESSAGE_LENGTH bytes that hold the length of the
 728:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	message. */
 729:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) != ( uint8_t ) 0 )
 730:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 731:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesToStoreMessageLength = sbBYTES_TO_STORE_MESSAGE_LENGTH;
 732:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 733:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 734:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 735:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesToStoreMessageLength = 0;
 736:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 737:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 738:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xTicksToWait != ( TickType_t ) 0 )
 739:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 740:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Checking if there is data and clearing the notification state must be
 741:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		performed atomically. */
 742:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		taskENTER_CRITICAL();
 743:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 744:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xBytesAvailable = prvBytesInBuffer( pxStreamBuffer );
 745:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 746:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* If this function was invoked by a message buffer read then
 747:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xBytesToStoreMessageLength holds the number of bytes used to hold
 748:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			the length of the next discrete message.  If this function was
 749:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			invoked by a stream buffer read then xBytesToStoreMessageLength will
 750:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			be 0. */
 751:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			if( xBytesAvailable <= xBytesToStoreMessageLength )
 752:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 753:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				/* Clear notification state as going to wait for data. */
 754:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				( void ) xTaskNotifyStateClear( NULL );
 755:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 756:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				/* Should only be one reader. */
 757:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				configASSERT( pxStreamBuffer->xTaskWaitingToReceive == NULL );
 758:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				pxStreamBuffer->xTaskWaitingToReceive = xTaskGetCurrentTaskHandle();
 759:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 760:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			else
 761:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 762:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				mtCOVERAGE_TEST_MARKER();
 763:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 764:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 765:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		taskEXIT_CRITICAL();
 766:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 767:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xBytesAvailable <= xBytesToStoreMessageLength )
 768:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 769:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Wait for data to be available. */
 770:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceBLOCKING_ON_STREAM_BUFFER_RECEIVE( xStreamBuffer );
 771:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) xTaskNotifyWait( ( uint32_t ) 0, ( uint32_t ) 0, NULL, xTicksToWait );
 772:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTaskWaitingToReceive = NULL;
ARM GAS  /tmp/ccSZflmK.s 			page 15


 773:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 774:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* Recheck the data available after blocking. */
 775:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xBytesAvailable = prvBytesInBuffer( pxStreamBuffer );
 776:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 777:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 778:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 779:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
 780:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 781:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 782:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 783:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 784:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesAvailable = prvBytesInBuffer( pxStreamBuffer );
 785:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 786:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 787:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Whether receiving a discrete message (where xBytesToStoreMessageLength
 788:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	holds the number of bytes used to store the message length) or a stream of
 789:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	bytes (where xBytesToStoreMessageLength is zero), the number of bytes
 790:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	available must be greater than xBytesToStoreMessageLength to be able to
 791:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	read bytes from the buffer. */
 792:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xBytesAvailable > xBytesToStoreMessageLength )
 793:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 794:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReceivedLength = prvReadMessageFromBuffer( pxStreamBuffer, pvRxData, xBufferLengthBytes, xBytesA
 795:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 796:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Was a task waiting for space in the buffer? */
 797:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xReceivedLength != ( size_t ) 0 )
 798:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 799:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			traceSTREAM_BUFFER_RECEIVE( xStreamBuffer, xReceivedLength );
 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			sbRECEIVE_COMPLETED( pxStreamBuffer );
 801:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 802:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 803:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 804:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
 805:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 806:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 807:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 808:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 809:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		traceSTREAM_BUFFER_RECEIVE_FAILED( xStreamBuffer );
 810:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 811:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 812:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 813:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReceivedLength;
 814:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 815:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 816:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 817:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferNextMessageLengthBytes( StreamBufferHandle_t xStreamBuffer )
 818:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 819:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 820:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn, xBytesAvailable, xOriginalTail;
 821:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** configMESSAGE_BUFFER_LENGTH_TYPE xTempReturn;
 822:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 823:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 824:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 825:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Ensure the stream buffer is being used as a message buffer. */
 826:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) != ( uint8_t ) 0 )
 827:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 828:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesAvailable = prvBytesInBuffer( pxStreamBuffer );
 829:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xBytesAvailable > sbBYTES_TO_STORE_MESSAGE_LENGTH )
ARM GAS  /tmp/ccSZflmK.s 			page 16


 830:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 831:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* The number of bytes available is greater than the number of bytes
 832:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			required to hold the length of the next message, so another message
 833:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			is available.  Return its length without removing the length bytes
 834:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			from the buffer.  A copy of the tail is stored so the buffer can be
 835:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			returned to its prior state as the message is not actually being
 836:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			removed from the buffer. */
 837:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xOriginalTail = pxStreamBuffer->xTail;
 838:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) &xTempReturn, sbBYTES_TO_STORE_ME
 839:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = ( size_t ) xTempReturn;
 840:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTail = xOriginalTail;
 841:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 842:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 843:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 844:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* The minimum amount of bytes in a message buffer is
 845:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( sbBYTES_TO_STORE_MESSAGE_LENGTH + 1 ), so if xBytesAvailable is
 846:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			less than sbBYTES_TO_STORE_MESSAGE_LENGTH the only other valid
 847:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			value is 0. */
 848:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBytesAvailable == 0 );
 849:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = 0;
 850:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 851:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 852:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 853:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 854:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = 0;
 855:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 856:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 857:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 858:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 859:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 860:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 861:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xStreamBufferReceiveFromISR( StreamBufferHandle_t xStreamBuffer,
 862:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									void *pvRxData,
 863:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									size_t xBufferLengthBytes,
 864:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 									BaseType_t * const pxHigherPriorityTaskWoken )
 865:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 866:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 867:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReceivedLength = 0, xBytesAvailable, xBytesToStoreMessageLength;
 868:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 869:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pvRxData );
 870:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 871:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 872:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* This receive function is used by both message buffers, which store
 873:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	discrete messages, and stream buffers, which store a continuous stream of
 874:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	bytes.  Discrete messages include an additional
 875:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	sbBYTES_TO_STORE_MESSAGE_LENGTH bytes that hold the length of the
 876:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	message. */
 877:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) != ( uint8_t ) 0 )
 878:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 879:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesToStoreMessageLength = sbBYTES_TO_STORE_MESSAGE_LENGTH;
 880:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 881:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 882:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 883:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesToStoreMessageLength = 0;
 884:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 885:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 886:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xBytesAvailable = prvBytesInBuffer( pxStreamBuffer );
ARM GAS  /tmp/ccSZflmK.s 			page 17


 887:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 888:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Whether receiving a discrete message (where xBytesToStoreMessageLength
 889:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	holds the number of bytes used to store the message length) or a stream of
 890:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	bytes (where xBytesToStoreMessageLength is zero), the number of bytes
 891:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	available must be greater than xBytesToStoreMessageLength to be able to
 892:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	read bytes from the buffer. */
 893:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xBytesAvailable > xBytesToStoreMessageLength )
 894:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 895:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReceivedLength = prvReadMessageFromBuffer( pxStreamBuffer, pvRxData, xBufferLengthBytes, xBytesA
 896:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 897:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Was a task waiting for space in the buffer? */
 898:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xReceivedLength != ( size_t ) 0 )
 899:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			sbRECEIVE_COMPLETED_FROM_ISR( pxStreamBuffer, pxHigherPriorityTaskWoken );
 901:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 902:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 903:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 904:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
 905:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 906:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 907:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 908:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 909:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 910:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 911:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 912:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	traceSTREAM_BUFFER_RECEIVE_FROM_ISR( xStreamBuffer, xReceivedLength );
 913:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 914:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReceivedLength;
 915:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 916:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 917:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 918:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvReadMessageFromBuffer( StreamBuffer_t *pxStreamBuffer,
 919:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										void *pvRxData,
 920:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xBufferLengthBytes,
 921:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xBytesAvailable,
 922:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										size_t xBytesToStoreMessageLength )
 923:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 924:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xOriginalTail, xReceivedLength, xNextMessageLength;
 925:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** configMESSAGE_BUFFER_LENGTH_TYPE xTempNextMessageLength;
 926:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 927:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xBytesToStoreMessageLength != ( size_t ) 0 )
 928:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 929:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* A discrete message is being received.  First receive the length
 930:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		of the message.  A copy of the tail is stored so the buffer can be
 931:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		returned to its prior state if the length of the message is too
 932:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		large for the provided buffer. */
 933:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xOriginalTail = pxStreamBuffer->xTail;
 934:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) &xTempNextMessageLength, xBytesToS
 935:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextMessageLength = ( size_t ) xTempNextMessageLength;
 936:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 937:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Reduce the number of bytes available by the number of bytes just
 938:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		read out. */
 939:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesAvailable -= xBytesToStoreMessageLength;
 940:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 941:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Check there is enough space in the buffer provided by the
 942:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		user. */
 943:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xNextMessageLength > xBufferLengthBytes )
ARM GAS  /tmp/ccSZflmK.s 			page 18


 944:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 945:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/* The user has provided insufficient space to read the message
 946:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			so return the buffer to its previous state (so the length of
 947:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			the message is in the buffer again). */
 948:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTail = xOriginalTail;
 949:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xNextMessageLength = 0;
 950:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 951:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
 952:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 953:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
 954:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 955:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 956:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 957:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 958:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* A stream of bytes is being received (as opposed to a discrete
 959:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		message), so read as many bytes as possible. */
 960:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextMessageLength = xBufferLengthBytes;
 961:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 962:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 963:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Read the actual data. */
 964:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReceivedLength = prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) pvRxData, xNextMessageLeng
 965:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 966:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReceivedLength;
 967:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 968:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 969:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 970:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xStreamBufferIsEmpty( StreamBufferHandle_t xStreamBuffer )
 971:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 972:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 973:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 974:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xTail;
 975:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 976:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 977:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 978:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* True if no bytes are available. */
 979:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xTail = pxStreamBuffer->xTail;
 980:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( pxStreamBuffer->xHead == xTail )
 981:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 982:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdTRUE;
 983:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 984:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
 985:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 986:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdFALSE;
 987:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 988:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 989:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 990:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 991:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 992:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 993:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xStreamBufferIsFull( StreamBufferHandle_t xStreamBuffer )
 994:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
 995:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 996:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xBytesToStoreMessageLength;
 997:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 998:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 999:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
1000:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 19


1001:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* This generic version of the receive function is used by both message
1002:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	buffers, which store discrete messages, and stream buffers, which store a
1003:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	continuous stream of bytes.  Discrete messages include an additional
1004:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	sbBYTES_TO_STORE_MESSAGE_LENGTH bytes that hold the length of the message. */
1005:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( ( pxStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER ) != ( uint8_t ) 0 )
1006:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1007:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesToStoreMessageLength = sbBYTES_TO_STORE_MESSAGE_LENGTH;
1008:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1009:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
1010:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1011:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xBytesToStoreMessageLength = 0;
1012:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1013:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1014:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* True if the available space equals zero. */
1015:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xStreamBufferSpacesAvailable( xStreamBuffer ) <= xBytesToStoreMessageLength )
1016:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1017:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdTRUE;
1018:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1019:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
1020:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1021:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdFALSE;
1022:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1023:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1024:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
1025:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
1026:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1027:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1028:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xStreamBufferSendCompletedFromISR( StreamBufferHandle_t xStreamBuffer, BaseType_t *pxHig
1029:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
1030:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
1031:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
1032:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** UBaseType_t uxSavedInterruptStatus;
1033:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1034:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
1035:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1036:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uxSavedInterruptStatus = ( UBaseType_t ) portSET_INTERRUPT_MASK_FROM_ISR();
1037:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1038:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( ( pxStreamBuffer )->xTaskWaitingToReceive != NULL )
1039:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1040:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) xTaskNotifyFromISR( ( pxStreamBuffer )->xTaskWaitingToReceive,
1041:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
1042:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 eNoAction,
1043:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 pxHigherPriorityTaskWoken );
1044:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( pxStreamBuffer )->xTaskWaitingToReceive = NULL;
1045:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdTRUE;
1046:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1047:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
1048:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1049:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdFALSE;
1050:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1051:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1052:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	portCLEAR_INTERRUPT_MASK_FROM_ISR( uxSavedInterruptStatus );
1053:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1054:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
1055:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
1056:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1057:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 20


1058:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xStreamBufferReceiveCompletedFromISR( StreamBufferHandle_t xStreamBuffer, BaseType_t *px
1059:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
1060:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
1061:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
1062:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** UBaseType_t uxSavedInterruptStatus;
1063:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1064:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
1065:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1066:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uxSavedInterruptStatus = ( UBaseType_t ) portSET_INTERRUPT_MASK_FROM_ISR();
1067:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1068:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( ( pxStreamBuffer )->xTaskWaitingToSend != NULL )
1069:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1070:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) xTaskNotifyFromISR( ( pxStreamBuffer )->xTaskWaitingToSend,
1071:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
1072:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 eNoAction,
1073:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 pxHigherPriorityTaskWoken );
1074:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( pxStreamBuffer )->xTaskWaitingToSend = NULL;
1075:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdTRUE;
1076:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1077:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
1078:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1079:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdFALSE;
1080:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1081:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1082:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	portCLEAR_INTERRUPT_MASK_FROM_ISR( uxSavedInterruptStatus );
1083:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1084:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
1085:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
1086:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1087:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1088:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvWriteBytesToBuffer( StreamBuffer_t * const pxStreamBuffer, const uint8_t *pucData,
1089:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
1090:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xNextHead, xFirstLength;
1091:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1092:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( xCount > ( size_t ) 0 );
1093:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1094:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xNextHead = pxStreamBuffer->xHead;
1095:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1096:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Calculate the number of bytes that can be added in the first write -
1097:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	which may be less than the total number of bytes that need to be added if
1098:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	the buffer will wrap back to the beginning. */
1099:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xFirstLength = configMIN( pxStreamBuffer->xLength - xNextHead, xCount );
1100:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1101:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Write as many bytes as can be written in the first write. */
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( ( xNextHead + xFirstLength ) <= pxStreamBuffer->xLength );
1103:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
1104:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1105:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* If the number of bytes written was less than the number that could be
1106:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	written in the first write... */
1107:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xCount > xFirstLength )
1108:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1109:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* ...then write the remaining bytes to the start of the buffer. */
1110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xCount - xFirstLength ) <= pxStreamBuffer->xLength );
1111:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pxStreamBuffer->pucBuffer, ( const void * ) &( pucData[ xFirstLength 
1112:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1113:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
1114:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
ARM GAS  /tmp/ccSZflmK.s 			page 21


1115:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
1116:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1117:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1118:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xNextHead += xCount;
1119:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xNextHead >= pxStreamBuffer->xLength )
1120:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1121:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextHead -= pxStreamBuffer->xLength;
1122:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1123:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
1124:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1125:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
1126:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1127:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1128:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	pxStreamBuffer->xHead = xNextHead;
1129:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1130:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xCount;
1131:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
1132:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1133:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1134:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvReadBytesFromBuffer( StreamBuffer_t *pxStreamBuffer, uint8_t *pucData, size_t xMax
1135:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
1136:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xCount, xFirstLength, xNextTail;
1137:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1138:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Use the minimum of the wanted bytes and the available bytes. */
1139:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xCount = configMIN( xBytesAvailable, xMaxCount );
1140:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1141:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xCount > ( size_t ) 0 )
1142:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1143:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextTail = pxStreamBuffer->xTail;
1144:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1145:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Calculate the number of bytes that can be read - which may be
1146:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		less than the number wanted if the data wraps around to the start of
1147:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		the buffer. */
1148:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xFirstLength = configMIN( pxStreamBuffer->xLength - xNextTail, xCount );
1149:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1150:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Obtain the number of bytes it is possible to obtain in the first
1151:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		read.  Asserts check bounds of read and write. */
1152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xFirstLength <= xMaxCount );
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xNextTail + xFirstLength ) <= pxStreamBuffer->xLength );
1154:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
1155:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1156:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* If the total number of wanted bytes is greater than the number
1157:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		that could be read in the first read... */
1158:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xCount > xFirstLength )
1159:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1160:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			/*...then read the remaining bytes from the start of the buffer. */
1161:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xCount <= xMaxCount );
1162:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) memcpy( ( void * ) &( pucData[ xFirstLength ] ), ( void * ) ( pxStreamBuffer->pucBuffer
1163:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1164:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		else
1165:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1166:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			mtCOVERAGE_TEST_MARKER();
1167:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1168:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1169:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* Move the tail pointer to effectively remove the data read from
1170:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		the buffer. */
1171:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextTail += xCount;
ARM GAS  /tmp/ccSZflmK.s 			page 22


1172:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1173:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xNextTail >= pxStreamBuffer->xLength )
1174:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
1175:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xNextTail -= pxStreamBuffer->xLength;
1176:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
1177:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1178:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		pxStreamBuffer->xTail = xNextTail;
1179:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1180:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
1181:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1182:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
1183:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1184:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1185:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xCount;
1186:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
1187:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1188:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1189:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static size_t prvBytesInBuffer( const StreamBuffer_t * const pxStreamBuffer )
1190:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
  28              		.loc 1 1190 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
1191:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /* Returns the distance between xTail and xHead. */
1192:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xCount;
  33              		.loc 1 1192 1 view .LVU1
1193:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1194:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xCount = pxStreamBuffer->xLength + pxStreamBuffer->xHead;
  34              		.loc 1 1194 2 view .LVU2
  35              		.loc 1 1194 25 is_stmt 0 view .LVU3
  36 0000 8268     		ldr	r2, [r0, #8]
  37              		.loc 1 1194 51 view .LVU4
  38 0002 4368     		ldr	r3, [r0, #4]
  39              		.loc 1 1194 9 view .LVU5
  40 0004 1344     		add	r3, r3, r2
  41              	.LVL1:
1195:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xCount -= pxStreamBuffer->xTail;
  42              		.loc 1 1195 2 is_stmt 1 view .LVU6
  43              		.loc 1 1195 26 is_stmt 0 view .LVU7
  44 0006 0068     		ldr	r0, [r0]
  45              	.LVL2:
  46              		.loc 1 1195 9 view .LVU8
  47 0008 181A     		subs	r0, r3, r0
  48              	.LVL3:
1196:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if ( xCount >= pxStreamBuffer->xLength )
  49              		.loc 1 1196 2 is_stmt 1 view .LVU9
  50              		.loc 1 1196 5 is_stmt 0 view .LVU10
  51 000a 8242     		cmp	r2, r0
  52 000c 00D8     		bhi	.L1
1197:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1198:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xCount -= pxStreamBuffer->xLength;
  53              		.loc 1 1198 3 is_stmt 1 view .LVU11
  54              		.loc 1 1198 10 is_stmt 0 view .LVU12
  55 000e 801A     		subs	r0, r0, r2
  56              	.LVL4:
1199:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
ARM GAS  /tmp/ccSZflmK.s 			page 23


1200:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	else
1201:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1202:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
  57              		.loc 1 1202 27 is_stmt 1 view .LVU13
1203:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
1204:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1205:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xCount;
  58              		.loc 1 1205 2 view .LVU14
  59              	.L1:
1206:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
  60              		.loc 1 1206 1 is_stmt 0 view .LVU15
  61 0010 7047     		bx	lr
  62              		.cfi_endproc
  63              	.LFE24:
  65              		.section	.text.prvInitialiseNewStreamBuffer,"ax",%progbits
  66              		.align	1
  67              		.syntax unified
  68              		.thumb
  69              		.thumb_func
  71              	prvInitialiseNewStreamBuffer:
  72              	.LVL5:
  73              	.LFB25:
1207:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1208:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1209:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** static void prvInitialiseNewStreamBuffer( StreamBuffer_t * const pxStreamBuffer,
1210:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  uint8_t * const pucBuffer,
1211:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  size_t xBufferSizeBytes,
1212:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  size_t xTriggerLevelBytes,
1213:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  uint8_t ucFlags )
1214:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** {
  74              		.loc 1 1214 1 is_stmt 1 view -0
  75              		.cfi_startproc
  76              		@ args = 4, pretend = 0, frame = 0
  77              		@ frame_needed = 0, uses_anonymous_args = 0
  78              		.loc 1 1214 1 is_stmt 0 view .LVU17
  79 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
  80              	.LCFI0:
  81              		.cfi_def_cfa_offset 24
  82              		.cfi_offset 3, -24
  83              		.cfi_offset 4, -20
  84              		.cfi_offset 5, -16
  85              		.cfi_offset 6, -12
  86              		.cfi_offset 7, -8
  87              		.cfi_offset 14, -4
  88 0002 0746     		mov	r7, r0
  89 0004 0C46     		mov	r4, r1
  90 0006 1646     		mov	r6, r2
  91 0008 1D46     		mov	r5, r3
  92              	.LBB96:
1215:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	/* Assert here is deliberately writing to the entire buffer to ensure it can
1216:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	be written to without generating exceptions, and is setting the buffer to a
1217:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	known value to assist in development/debugging. */
1218:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#if( configASSERT_DEFINED == 1 )
1219:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
1220:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		/* The value written just has to be identifiable when looking at the
1221:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		memory.  Don't use 0xA5 as that is the stack fill value and could
1222:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		result in confusion as to what is actually being observed. */
ARM GAS  /tmp/ccSZflmK.s 			page 24


1223:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		const BaseType_t xWriteValue = 0x55;
  93              		.loc 1 1223 3 is_stmt 1 view .LVU18
  94              	.LVL6:
1224:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( memset( pucBuffer, ( int ) xWriteValue, xBufferSizeBytes ) == pucBuffer );
  95              		.loc 1 1224 3 view .LVU19
  96 000a 5521     		movs	r1, #85
  97              	.LVL7:
  98              		.loc 1 1224 3 is_stmt 0 view .LVU20
  99 000c 2046     		mov	r0, r4
 100              	.LVL8:
 101              		.loc 1 1224 3 view .LVU21
 102 000e FFF7FEFF 		bl	memset
 103              	.LVL9:
 104              		.loc 1 1224 3 discriminator 1 view .LVU22
 105 0012 8442     		cmp	r4, r0
 106 0014 08D0     		beq	.L4
 107              		.loc 1 1224 3 is_stmt 1 discriminator 1 view .LVU23
 108              	.LBB97:
 109              	.LBI97:
 110              		.file 2 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
   1:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  29:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef PORTMACRO_H
  30:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define PORTMACRO_H
  31:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  32:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef __cplusplus
  33:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern "C" {
  34:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  35:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------
  37:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Port specific definitions.
ARM GAS  /tmp/ccSZflmK.s 			page 25


  38:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  39:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The settings in this file configure FreeRTOS correctly for the
  40:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * given hardware and compiler.
  41:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  42:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * These settings should not be altered.
  43:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *-----------------------------------------------------------
  44:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  45:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Type definitions. */
  47:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCHAR		char
  48:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portFLOAT		float
  49:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDOUBLE		double
  50:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portLONG		long
  51:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSHORT		short
  52:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_TYPE	uint32_t
  53:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBASE_TYPE	long
  54:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef portSTACK_TYPE StackType_t;
  56:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef long BaseType_t;
  57:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef unsigned long UBaseType_t;
  58:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  59:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if( configUSE_16_BIT_TICKS == 1 )
  60:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint16_t TickType_t;
  61:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffff
  62:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #else
  63:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint32_t TickType_t;
  64:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffffffffUL
  65:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  66:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* 32-bit tick type on a 32-bit architecture, so reads of the tick count do
  67:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	not need to be guarded with a critical section. */
  68:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portTICK_TYPE_IS_ATOMIC 1
  69:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  70:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  71:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specifics. */
  73:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_GROWTH			( -1 )
  74:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTICK_PERIOD_MS			( ( TickType_t ) 1000 / configTICK_RATE_HZ )
  75:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBYTE_ALIGNMENT			8
  76:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  77:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  78:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Scheduler utilities. */
  79:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD() 															\
  80:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {																				\
  81:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Set a PendSV to request a context switch. */								\
  82:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	portNVIC_INT_CTRL_REG = portNVIC_PENDSVSET_BIT;								\
  83:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 																				\
  84:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Barriers are normally not required but do ensure the code is completely	\
  85:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	within the specified behaviour for the architecture. */						\
  86:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "dsb" ::: "memory" );										\
  87:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "isb" );													\
  88:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
  89:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_INT_CTRL_REG		( * ( ( volatile uint32_t * ) 0xe000ed04 ) )
  91:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_PENDSVSET_BIT		( 1UL << 28UL )
  92:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEND_SWITCHING_ISR( xSwitchRequired ) if( xSwitchRequired != pdFALSE ) portYIELD()
  93:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD_FROM_ISR( x ) portEND_SWITCHING_ISR( x )
  94:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
ARM GAS  /tmp/ccSZflmK.s 			page 26


  95:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  96:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Critical section management. */
  97:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortEnterCritical( void );
  98:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortExitCritical( void );
  99:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSET_INTERRUPT_MASK_FROM_ISR()		ulPortRaiseBASEPRI()
 100:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCLEAR_INTERRUPT_MASK_FROM_ISR(x)	vPortSetBASEPRI(x)
 101:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDISABLE_INTERRUPTS()				vPortRaiseBASEPRI()
 102:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENABLE_INTERRUPTS()					vPortSetBASEPRI(0)
 103:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENTER_CRITICAL()					vPortEnterCritical()
 104:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEXIT_CRITICAL()						vPortExitCritical()
 105:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 106:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 107:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 108:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Task function macros as described on the FreeRTOS.org WEB site.  These are
 109:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** not necessary for to use this port.  They are defined so the common demo files
 110:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** (which build with all the ports) will build. */
 111:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION_PROTO( vFunction, pvParameters ) void vFunction( void *pvParameters )
 112:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION( vFunction, pvParameters ) void vFunction( void *pvParameters )
 113:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 114:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 115:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Tickless idle/low power functionality. */
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portSUPPRESS_TICKS_AND_SLEEP
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	extern void vPortSuppressTicksAndSleep( TickType_t xExpectedIdleTime );
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portSUPPRESS_TICKS_AND_SLEEP( xExpectedIdleTime ) vPortSuppressTicksAndSleep( xExpectedIdl
 119:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 121:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 122:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specific optimisations. */
 123:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef configUSE_PORT_OPTIMISED_TASK_SELECTION
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define configUSE_PORT_OPTIMISED_TASK_SELECTION 1
 125:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 127:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if configUSE_PORT_OPTIMISED_TASK_SELECTION == 1
 128:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 129:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Generic helper function. */
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__attribute__( ( always_inline ) ) static inline uint8_t ucPortCountLeadingZeros( uint32_t ulBitma
 131:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 132:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	uint8_t ucReturn;
 133:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		__asm volatile ( "clz %0, %1" : "=r" ( ucReturn ) : "r" ( ulBitmap ) : "memory" );
 135:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		return ucReturn;
 136:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 138:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Check the configuration. */
 139:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#if( configMAX_PRIORITIES > 32 )
 140:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		#error configUSE_PORT_OPTIMISED_TASK_SELECTION can only be set to 1 when configMAX_PRIORITIES is 
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#endif
 142:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Store/clear the ready priorities in a bit map. */
 144:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRECORD_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) |= ( 1UL 
 145:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRESET_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) &= ~( 1UL 
 146:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/*-----------------------------------------------------------*/
 148:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 149:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portGET_HIGHEST_PRIORITY( uxTopPriority, uxReadyPriorities ) uxTopPriority = ( 31UL - ( ui
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif /* configUSE_PORT_OPTIMISED_TASK_SELECTION */
ARM GAS  /tmp/ccSZflmK.s 			page 27


 152:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 153:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 154:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 155:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef configASSERT
 156:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	void vPortValidateInterruptPriority( void );
 157:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portASSERT_IF_INTERRUPT_PRIORITY_INVALID() 	vPortValidateInterruptPriority()
 158:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 159:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* portNOP() is not required by this port. */
 161:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNOP()
 162:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portINLINE	__inline
 164:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 165:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portFORCE_INLINE
 166:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portFORCE_INLINE inline __attribute__(( always_inline))
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static BaseType_t xPortIsInsideInterrupt( void )
 170:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulCurrentInterrupt;
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** BaseType_t xReturn;
 173:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 174:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Obtain the number of the currently executing interrupt. */
 175:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "mrs %0, ipsr" : "=r"( ulCurrentInterrupt ) :: "memory" );
 176:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	if( ulCurrentInterrupt == 0 )
 178:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 179:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdFALSE;
 180:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	else
 182:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 183:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdTRUE;
 184:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 186:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return xReturn;
 187:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 188:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 190:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortRaiseBASEPRI( void )
 111              		.loc 2 191 30 view .LVU24
 112              	.LBB98:
 192:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulNewBASEPRI;
 113              		.loc 2 193 1 view .LVU25
 194:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 114              		.loc 2 195 2 view .LVU26
 115              		.syntax unified
 116              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 117 0016 4FF05003 			mov r3, #80												
 118 001a 83F31188 		msr basepri, r3											
 119 001e BFF36F8F 		isb														
 120 0022 BFF34F8F 		dsb														
 121              	
 122              	@ 0 "" 2
 123              		.thumb
ARM GAS  /tmp/ccSZflmK.s 			page 28


 124              		.syntax unified
 125              	.L5:
 126              	.LBE98:
 127              	.LBE97:
 128              		.loc 1 1224 3 discriminator 3 view .LVU27
 129              		.loc 1 1224 3 discriminator 3 view .LVU28
 130 0026 FEE7     		b	.L5
 131              	.L4:
 132              		.loc 1 1224 90 discriminator 2 view .LVU29
 133              	.LBE96:
1225:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	} /*lint !e529 !e438 xWriteValue is only used if configASSERT() is defined. */
1226:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	#endif
1227:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1228:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memset( ( void * ) pxStreamBuffer, 0x00, sizeof( StreamBuffer_t ) ); /*lint !e9087 memset
 134              		.loc 1 1228 2 view .LVU30
 135              		.loc 1 1228 11 is_stmt 0 view .LVU31
 136 0028 2422     		movs	r2, #36
 137 002a 0021     		movs	r1, #0
 138 002c 3846     		mov	r0, r7
 139 002e FFF7FEFF 		bl	memset
 140              	.LVL10:
1229:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	pxStreamBuffer->pucBuffer = pucBuffer;
 141              		.loc 1 1229 2 is_stmt 1 view .LVU32
 142              		.loc 1 1229 28 is_stmt 0 view .LVU33
 143 0032 BC61     		str	r4, [r7, #24]
1230:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	pxStreamBuffer->xLength = xBufferSizeBytes;
 144              		.loc 1 1230 2 is_stmt 1 view .LVU34
 145              		.loc 1 1230 26 is_stmt 0 view .LVU35
 146 0034 BE60     		str	r6, [r7, #8]
1231:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	pxStreamBuffer->xTriggerLevelBytes = xTriggerLevelBytes;
 147              		.loc 1 1231 2 is_stmt 1 view .LVU36
 148              		.loc 1 1231 37 is_stmt 0 view .LVU37
 149 0036 FD60     		str	r5, [r7, #12]
1232:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	pxStreamBuffer->ucFlags = ucFlags;
 150              		.loc 1 1232 2 is_stmt 1 view .LVU38
 151              		.loc 1 1232 26 is_stmt 0 view .LVU39
 152 0038 9DF81830 		ldrb	r3, [sp, #24]	@ zero_extendqisi2
 153 003c 3B77     		strb	r3, [r7, #28]
1233:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 154              		.loc 1 1233 1 view .LVU40
 155 003e F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 156              		.loc 1 1233 1 view .LVU41
 157              		.cfi_endproc
 158              	.LFE25:
 160              		.section	.text.prvWriteBytesToBuffer,"ax",%progbits
 161              		.align	1
 162              		.syntax unified
 163              		.thumb
 164              		.thumb_func
 166              	prvWriteBytesToBuffer:
 167              	.LVL11:
 168              	.LFB22:
1089:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xNextHead, xFirstLength;
 169              		.loc 1 1089 1 is_stmt 1 view -0
 170              		.cfi_startproc
 171              		@ args = 0, pretend = 0, frame = 0
 172              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  /tmp/ccSZflmK.s 			page 29


1090:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 173              		.loc 1 1090 1 view .LVU43
1092:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 174              		.loc 1 1092 2 view .LVU44
 175 0000 42B9     		cbnz	r2, .L8
1092:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 176              		.loc 1 1092 2 discriminator 1 view .LVU45
 177              	.LBB99:
 178              	.LBI99:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 179              		.loc 2 191 30 view .LVU46
 180              	.LBB100:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 181              		.loc 2 193 1 view .LVU47
 182              		.loc 2 195 2 view .LVU48
 183              		.syntax unified
 184              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 185 0002 4FF05003 			mov r3, #80												
 186 0006 83F31188 		msr basepri, r3											
 187 000a BFF36F8F 		isb														
 188 000e BFF34F8F 		dsb														
 189              	
 190              	@ 0 "" 2
 191              		.thumb
 192              		.syntax unified
 193              	.L9:
 194              	.LBE100:
 195              	.LBE99:
1092:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 196              		.loc 1 1092 2 discriminator 3 view .LVU49
1092:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 197              		.loc 1 1092 2 discriminator 3 view .LVU50
 198 0012 FEE7     		b	.L9
 199              	.L8:
1089:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xNextHead, xFirstLength;
 200              		.loc 1 1089 1 is_stmt 0 view .LVU51
 201 0014 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 202              	.LCFI1:
 203              		.cfi_def_cfa_offset 24
 204              		.cfi_offset 4, -24
 205              		.cfi_offset 5, -20
 206              		.cfi_offset 6, -16
 207              		.cfi_offset 7, -12
 208              		.cfi_offset 8, -8
 209              		.cfi_offset 14, -4
 210 0018 0446     		mov	r4, r0
 211 001a 0E46     		mov	r6, r1
 212 001c 1746     		mov	r7, r2
1092:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 213              		.loc 1 1092 39 is_stmt 1 discriminator 2 view .LVU52
1094:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 214              		.loc 1 1094 2 view .LVU53
1094:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 215              		.loc 1 1094 12 is_stmt 0 view .LVU54
 216 001e D0F80480 		ldr	r8, [r0, #4]
 217              	.LVL12:
1099:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 30


 218              		.loc 1 1099 2 is_stmt 1 view .LVU55
1099:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 219              		.loc 1 1099 17 is_stmt 0 view .LVU56
 220 0022 8368     		ldr	r3, [r0, #8]
 221 0024 A3EB0805 		sub	r5, r3, r8
1099:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 222              		.loc 1 1099 15 view .LVU57
 223 0028 9542     		cmp	r5, r2
 224 002a 28BF     		it	cs
 225 002c 1546     		movcs	r5, r2
 226              	.LVL13:
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
 227              		.loc 1 1102 2 is_stmt 1 view .LVU58
 228 002e 08EB0502 		add	r2, r8, r5
 229              	.LVL14:
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
 230              		.loc 1 1102 2 is_stmt 0 view .LVU59
 231 0032 9342     		cmp	r3, r2
 232 0034 08D2     		bcs	.L10
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
 233              		.loc 1 1102 2 is_stmt 1 discriminator 1 view .LVU60
 234              	.LBB101:
 235              	.LBI101:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 236              		.loc 2 191 30 view .LVU61
 237              	.LBB102:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 238              		.loc 2 193 1 view .LVU62
 239              		.loc 2 195 2 view .LVU63
 240              		.syntax unified
 241              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 242 0036 4FF05003 			mov r3, #80												
 243 003a 83F31188 		msr basepri, r3											
 244 003e BFF36F8F 		isb														
 245 0042 BFF34F8F 		dsb														
 246              	
 247              	@ 0 "" 2
 248              		.thumb
 249              		.syntax unified
 250              	.L11:
 251              	.LBE102:
 252              	.LBE101:
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
 253              		.loc 1 1102 2 discriminator 3 view .LVU64
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
 254              		.loc 1 1102 2 discriminator 3 view .LVU65
 255 0046 FEE7     		b	.L11
 256              	.L10:
1102:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	( void ) memcpy( ( void* ) ( &( pxStreamBuffer->pucBuffer[ xNextHead ] ) ), ( const void * ) pucDa
 257              		.loc 1 1102 73 discriminator 2 view .LVU66
1103:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 258              		.loc 1 1103 2 view .LVU67
1103:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 259              		.loc 1 1103 48 is_stmt 0 view .LVU68
 260 0048 8069     		ldr	r0, [r0, #24]
 261              	.LVL15:
1103:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 31


 262              		.loc 1 1103 11 view .LVU69
 263 004a 2A46     		mov	r2, r5
 264 004c 4044     		add	r0, r0, r8
 265 004e FFF7FEFF 		bl	memcpy
 266              	.LVL16:
1107:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 267              		.loc 1 1107 2 is_stmt 1 view .LVU70
1107:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 268              		.loc 1 1107 4 is_stmt 0 view .LVU71
 269 0052 AF42     		cmp	r7, r5
 270 0054 10D9     		bls	.L12
1110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pxStreamBuffer->pucBuffer, ( const void * ) &( pucData[ xFirstLength 
 271              		.loc 1 1110 3 is_stmt 1 view .LVU72
 272 0056 7A1B     		subs	r2, r7, r5
 273 0058 A368     		ldr	r3, [r4, #8]
 274 005a 9A42     		cmp	r2, r3
 275 005c 08D9     		bls	.L13
1110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pxStreamBuffer->pucBuffer, ( const void * ) &( pucData[ xFirstLength 
 276              		.loc 1 1110 3 discriminator 1 view .LVU73
 277              	.LBB103:
 278              	.LBI103:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 279              		.loc 2 191 30 view .LVU74
 280              	.LBB104:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 281              		.loc 2 193 1 view .LVU75
 282              		.loc 2 195 2 view .LVU76
 283              		.syntax unified
 284              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 285 005e 4FF05003 			mov r3, #80												
 286 0062 83F31188 		msr basepri, r3											
 287 0066 BFF36F8F 		isb														
 288 006a BFF34F8F 		dsb														
 289              	
 290              	@ 0 "" 2
 291              		.thumb
 292              		.syntax unified
 293              	.L14:
 294              	.LBE104:
 295              	.LBE103:
1110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pxStreamBuffer->pucBuffer, ( const void * ) &( pucData[ xFirstLength 
 296              		.loc 1 1110 3 discriminator 3 view .LVU77
1110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pxStreamBuffer->pucBuffer, ( const void * ) &( pucData[ xFirstLength 
 297              		.loc 1 1110 3 discriminator 3 view .LVU78
 298 006e FEE7     		b	.L14
 299              	.L13:
1110:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pxStreamBuffer->pucBuffer, ( const void * ) &( pucData[ xFirstLength 
 300              		.loc 1 1110 71 discriminator 2 view .LVU79
1111:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 301              		.loc 1 1111 3 view .LVU80
1111:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 302              		.loc 1 1111 12 is_stmt 0 view .LVU81
 303 0070 7119     		adds	r1, r6, r5
 304 0072 A069     		ldr	r0, [r4, #24]
 305 0074 FFF7FEFF 		bl	memcpy
 306              	.LVL17:
 307              	.L12:
ARM GAS  /tmp/ccSZflmK.s 			page 32


1115:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 308              		.loc 1 1115 27 is_stmt 1 view .LVU82
1118:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xNextHead >= pxStreamBuffer->xLength )
 309              		.loc 1 1118 2 view .LVU83
1118:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( xNextHead >= pxStreamBuffer->xLength )
 310              		.loc 1 1118 12 is_stmt 0 view .LVU84
 311 0078 07EB0803 		add	r3, r7, r8
 312              	.LVL18:
1119:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 313              		.loc 1 1119 2 is_stmt 1 view .LVU85
1119:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 314              		.loc 1 1119 33 is_stmt 0 view .LVU86
 315 007c A268     		ldr	r2, [r4, #8]
1119:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 316              		.loc 1 1119 4 view .LVU87
 317 007e 9A42     		cmp	r2, r3
 318 0080 00D8     		bhi	.L15
1121:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 319              		.loc 1 1121 3 is_stmt 1 view .LVU88
1121:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 320              		.loc 1 1121 13 is_stmt 0 view .LVU89
 321 0082 9B1A     		subs	r3, r3, r2
 322              	.LVL19:
 323              	.L15:
1125:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 324              		.loc 1 1125 27 is_stmt 1 view .LVU90
1128:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 325              		.loc 1 1128 2 view .LVU91
1128:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 326              		.loc 1 1128 24 is_stmt 0 view .LVU92
 327 0084 6360     		str	r3, [r4, #4]
1130:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 328              		.loc 1 1130 2 is_stmt 1 view .LVU93
1131:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 329              		.loc 1 1131 1 is_stmt 0 view .LVU94
 330 0086 3846     		mov	r0, r7
 331 0088 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
1131:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 332              		.loc 1 1131 1 view .LVU95
 333              		.cfi_endproc
 334              	.LFE22:
 336              		.section	.text.prvWriteMessageToBuffer,"ax",%progbits
 337              		.align	1
 338              		.syntax unified
 339              		.thumb
 340              		.thumb_func
 342              	prvWriteMessageToBuffer:
 343              	.LVL20:
 344              	.LFB13:
 666:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	BaseType_t xShouldWrite;
 345              		.loc 1 666 1 is_stmt 1 view -0
 346              		.cfi_startproc
 347              		@ args = 4, pretend = 0, frame = 8
 348              		@ frame_needed = 0, uses_anonymous_args = 0
 666:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	BaseType_t xShouldWrite;
 349              		.loc 1 666 1 is_stmt 0 view .LVU97
 350 0000 30B5     		push	{r4, r5, lr}
ARM GAS  /tmp/ccSZflmK.s 			page 33


 351              	.LCFI2:
 352              		.cfi_def_cfa_offset 12
 353              		.cfi_offset 4, -12
 354              		.cfi_offset 5, -8
 355              		.cfi_offset 14, -4
 356 0002 83B0     		sub	sp, sp, #12
 357              	.LCFI3:
 358              		.cfi_def_cfa_offset 24
 359 0004 0546     		mov	r5, r0
 360 0006 0192     		str	r2, [sp, #4]
 667:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	size_t xReturn;
 361              		.loc 1 667 2 is_stmt 1 view .LVU98
 668:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 362              		.loc 1 668 2 view .LVU99
 670:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 363              		.loc 1 670 2 view .LVU100
 670:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 364              		.loc 1 670 4 is_stmt 0 view .LVU101
 365 0008 1846     		mov	r0, r3
 366              	.LVL21:
 670:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 367              		.loc 1 670 4 view .LVU102
 368 000a 6BB1     		cbz	r3, .L19
 369 000c 0C46     		mov	r4, r1
 676:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 370              		.loc 1 676 7 is_stmt 1 view .LVU103
 676:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 371              		.loc 1 676 27 is_stmt 0 view .LVU104
 372 000e 2B7F     		ldrb	r3, [r5, #28]	@ zero_extendqisi2
 373              	.LVL22:
 676:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 374              		.loc 1 676 9 view .LVU105
 375 0010 13F0010F 		tst	r3, #1
 376 0014 0AD1     		bne	.L20
 681:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xDataLengthBytes = configMIN( xDataLengthBytes, xSpace );
 377              		.loc 1 681 3 is_stmt 1 view .LVU106
 378              	.LVL23:
 682:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 379              		.loc 1 682 3 view .LVU107
 682:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 380              		.loc 1 682 22 is_stmt 0 view .LVU108
 381 0016 9042     		cmp	r0, r2
 382 0018 28BF     		it	cs
 383 001a 1046     		movcs	r0, r2
 384              	.LVL24:
 682:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 385              		.loc 1 682 20 view .LVU109
 386 001c 0190     		str	r0, [sp, #4]
 699:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 387              		.loc 1 699 2 is_stmt 1 view .LVU110
 388              	.LVL25:
 389              	.L21:
 702:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 390              		.loc 1 702 3 view .LVU111
 702:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 391              		.loc 1 702 13 is_stmt 0 view .LVU112
 392 001e 019A     		ldr	r2, [sp, #4]
ARM GAS  /tmp/ccSZflmK.s 			page 34


 393 0020 2146     		mov	r1, r4
 394 0022 2846     		mov	r0, r5
 395 0024 FFF7FEFF 		bl	prvWriteBytesToBuffer
 396              	.LVL26:
 397              	.L19:
 709:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 398              		.loc 1 709 2 is_stmt 1 view .LVU113
 710:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 399              		.loc 1 710 1 is_stmt 0 view .LVU114
 400 0028 03B0     		add	sp, sp, #12
 401              	.LCFI4:
 402              		.cfi_remember_state
 403              		.cfi_def_cfa_offset 12
 404              		@ sp needed
 405 002a 30BD     		pop	{r4, r5, pc}
 406              	.LVL27:
 407              	.L20:
 408              	.LCFI5:
 409              		.cfi_restore_state
 684:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 410              		.loc 1 684 7 is_stmt 1 view .LVU115
 684:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 411              		.loc 1 684 9 is_stmt 0 view .LVU116
 412 002c 069B     		ldr	r3, [sp, #24]
 413 002e 9842     		cmp	r0, r3
 414 0030 01D2     		bcs	.L24
 706:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 415              		.loc 1 706 11 view .LVU117
 416 0032 0020     		movs	r0, #0
 417              	.LVL28:
 706:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 418              		.loc 1 706 11 view .LVU118
 419 0034 F8E7     		b	.L19
 420              	.LVL29:
 421              	.L24:
 690:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) prvWriteBytesToBuffer( pxStreamBuffer, ( const uint8_t * ) &( xDataLengthBytes ), sbBYTE
 422              		.loc 1 690 3 is_stmt 1 view .LVU119
 691:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 423              		.loc 1 691 3 view .LVU120
 691:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 424              		.loc 1 691 12 is_stmt 0 view .LVU121
 425 0036 0422     		movs	r2, #4
 426              	.LVL30:
 691:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 427              		.loc 1 691 12 view .LVU122
 428 0038 0DEB0201 		add	r1, sp, r2
 429              	.LVL31:
 691:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 430              		.loc 1 691 12 view .LVU123
 431 003c 2846     		mov	r0, r5
 432              	.LVL32:
 691:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 433              		.loc 1 691 12 view .LVU124
 434 003e FFF7FEFF 		bl	prvWriteBytesToBuffer
 435              	.LVL33:
 699:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 436              		.loc 1 699 2 is_stmt 1 view .LVU125
ARM GAS  /tmp/ccSZflmK.s 			page 35


 437 0042 ECE7     		b	.L21
 438              		.cfi_endproc
 439              	.LFE13:
 441              		.section	.text.prvReadBytesFromBuffer,"ax",%progbits
 442              		.align	1
 443              		.syntax unified
 444              		.thumb
 445              		.thumb_func
 447              	prvReadBytesFromBuffer:
 448              	.LVL34:
 449              	.LFB23:
1135:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xCount, xFirstLength, xNextTail;
 450              		.loc 1 1135 1 view -0
 451              		.cfi_startproc
 452              		@ args = 0, pretend = 0, frame = 0
 453              		@ frame_needed = 0, uses_anonymous_args = 0
1135:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xCount, xFirstLength, xNextTail;
 454              		.loc 1 1135 1 is_stmt 0 view .LVU127
 455 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 456              	.LCFI6:
 457              		.cfi_def_cfa_offset 24
 458              		.cfi_offset 4, -24
 459              		.cfi_offset 5, -20
 460              		.cfi_offset 6, -16
 461              		.cfi_offset 7, -12
 462              		.cfi_offset 8, -8
 463              		.cfi_offset 14, -4
1136:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 464              		.loc 1 1136 1 is_stmt 1 view .LVU128
1139:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 465              		.loc 1 1139 2 view .LVU129
1139:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 466              		.loc 1 1139 9 is_stmt 0 view .LVU130
 467 0004 9342     		cmp	r3, r2
 468 0006 28BF     		it	cs
 469 0008 1346     		movcs	r3, r2
 470              	.LVL35:
1139:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 471              		.loc 1 1139 9 view .LVU131
 472 000a 1C46     		mov	r4, r3
 473              	.LVL36:
1141:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 474              		.loc 1 1141 2 is_stmt 1 view .LVU132
1141:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 475              		.loc 1 1141 4 is_stmt 0 view .LVU133
 476 000c 83B3     		cbz	r3, .L25
 477 000e 0646     		mov	r6, r0
 478 0010 0F46     		mov	r7, r1
1143:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 479              		.loc 1 1143 3 is_stmt 1 view .LVU134
1143:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 480              		.loc 1 1143 13 is_stmt 0 view .LVU135
 481 0012 D0F80080 		ldr	r8, [r0]
 482              	.LVL37:
1148:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 483              		.loc 1 1148 3 is_stmt 1 view .LVU136
1148:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 36


 484              		.loc 1 1148 18 is_stmt 0 view .LVU137
 485 0016 8368     		ldr	r3, [r0, #8]
 486              	.LVL38:
1148:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 487              		.loc 1 1148 18 view .LVU138
 488 0018 A3EB0805 		sub	r5, r3, r8
1148:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 489              		.loc 1 1148 16 view .LVU139
 490 001c A542     		cmp	r5, r4
 491 001e 28BF     		it	cs
 492 0020 2546     		movcs	r5, r4
 493              	.LVL39:
1152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xNextTail + xFirstLength ) <= pxStreamBuffer->xLength );
 494              		.loc 1 1152 3 is_stmt 1 view .LVU140
 495 0022 AA42     		cmp	r2, r5
 496 0024 08D2     		bcs	.L27
1152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xNextTail + xFirstLength ) <= pxStreamBuffer->xLength );
 497              		.loc 1 1152 3 discriminator 1 view .LVU141
 498              	.LBB105:
 499              	.LBI105:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 500              		.loc 2 191 30 view .LVU142
 501              	.LBB106:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 502              		.loc 2 193 1 view .LVU143
 503              		.loc 2 195 2 view .LVU144
 504              		.syntax unified
 505              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 506 0026 4FF05003 			mov r3, #80												
 507 002a 83F31188 		msr basepri, r3											
 508 002e BFF36F8F 		isb														
 509 0032 BFF34F8F 		dsb														
 510              	
 511              	@ 0 "" 2
 512              		.thumb
 513              		.syntax unified
 514              	.L28:
 515              	.LBE106:
 516              	.LBE105:
1152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xNextTail + xFirstLength ) <= pxStreamBuffer->xLength );
 517              		.loc 1 1152 3 discriminator 3 view .LVU145
1152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xNextTail + xFirstLength ) <= pxStreamBuffer->xLength );
 518              		.loc 1 1152 3 discriminator 3 view .LVU146
 519 0036 FEE7     		b	.L28
 520              	.L27:
1152:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( ( xNextTail + xFirstLength ) <= pxStreamBuffer->xLength );
 521              		.loc 1 1152 44 discriminator 2 view .LVU147
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
 522              		.loc 1 1153 3 view .LVU148
 523 0038 08EB0502 		add	r2, r8, r5
 524              	.LVL40:
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
 525              		.loc 1 1153 3 is_stmt 0 view .LVU149
 526 003c 9342     		cmp	r3, r2
 527 003e 08D2     		bcs	.L29
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
 528              		.loc 1 1153 3 is_stmt 1 discriminator 1 view .LVU150
ARM GAS  /tmp/ccSZflmK.s 			page 37


 529              	.LBB107:
 530              	.LBI107:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 531              		.loc 2 191 30 view .LVU151
 532              	.LBB108:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 533              		.loc 2 193 1 view .LVU152
 534              		.loc 2 195 2 view .LVU153
 535              		.syntax unified
 536              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 537 0040 4FF05003 			mov r3, #80												
 538 0044 83F31188 		msr basepri, r3											
 539 0048 BFF36F8F 		isb														
 540 004c BFF34F8F 		dsb														
 541              	
 542              	@ 0 "" 2
 543              		.thumb
 544              		.syntax unified
 545              	.L30:
 546              	.LBE108:
 547              	.LBE107:
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
 548              		.loc 1 1153 3 discriminator 3 view .LVU154
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
 549              		.loc 1 1153 3 discriminator 3 view .LVU155
 550 0050 FEE7     		b	.L30
 551              	.L29:
1153:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) memcpy( ( void * ) pucData, ( const void * ) &( pxStreamBuffer->pucBuffer[ xNextTail ] )
 552              		.loc 1 1153 74 discriminator 2 view .LVU156
1154:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 553              		.loc 1 1154 3 view .LVU157
1154:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 554              		.loc 1 1154 74 is_stmt 0 view .LVU158
 555 0052 8169     		ldr	r1, [r0, #24]
 556              	.LVL41:
1154:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 557              		.loc 1 1154 12 view .LVU159
 558 0054 2A46     		mov	r2, r5
 559 0056 4144     		add	r1, r1, r8
 560 0058 3846     		mov	r0, r7
 561              	.LVL42:
1154:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 562              		.loc 1 1154 12 view .LVU160
 563 005a FFF7FEFF 		bl	memcpy
 564              	.LVL43:
1158:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 565              		.loc 1 1158 3 is_stmt 1 view .LVU161
1158:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 566              		.loc 1 1158 5 is_stmt 0 view .LVU162
 567 005e AC42     		cmp	r4, r5
 568 0060 09D8     		bhi	.L34
 569              	.L31:
1166:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 570              		.loc 1 1166 28 is_stmt 1 view .LVU163
1171:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 571              		.loc 1 1171 3 view .LVU164
1171:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 38


 572              		.loc 1 1171 13 is_stmt 0 view .LVU165
 573 0062 04EB0803 		add	r3, r4, r8
 574              	.LVL44:
1173:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 575              		.loc 1 1173 3 is_stmt 1 view .LVU166
1173:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 576              		.loc 1 1173 34 is_stmt 0 view .LVU167
 577 0066 B268     		ldr	r2, [r6, #8]
1173:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 578              		.loc 1 1173 5 view .LVU168
 579 0068 9A42     		cmp	r2, r3
 580 006a 00D8     		bhi	.L32
1175:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 581              		.loc 1 1175 4 is_stmt 1 view .LVU169
1175:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 582              		.loc 1 1175 14 is_stmt 0 view .LVU170
 583 006c 9B1A     		subs	r3, r3, r2
 584              	.LVL45:
 585              	.L32:
1178:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 586              		.loc 1 1178 3 is_stmt 1 view .LVU171
1178:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 587              		.loc 1 1178 25 is_stmt 0 view .LVU172
 588 006e 3360     		str	r3, [r6]
1182:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 589              		.loc 1 1182 27 is_stmt 1 view .LVU173
1185:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 590              		.loc 1 1185 2 view .LVU174
 591              	.LVL46:
 592              	.L25:
1186:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 593              		.loc 1 1186 1 is_stmt 0 view .LVU175
 594 0070 2046     		mov	r0, r4
 595 0072 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 596              	.LVL47:
 597              	.L34:
1161:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) memcpy( ( void * ) &( pucData[ xFirstLength ] ), ( void * ) ( pxStreamBuffer->pucBuffer
 598              		.loc 1 1161 4 is_stmt 1 view .LVU176
1161:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) memcpy( ( void * ) &( pucData[ xFirstLength ] ), ( void * ) ( pxStreamBuffer->pucBuffer
 599              		.loc 1 1161 39 discriminator 2 view .LVU177
1162:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 600              		.loc 1 1162 4 view .LVU178
1162:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 601              		.loc 1 1162 13 is_stmt 0 view .LVU179
 602 0076 621B     		subs	r2, r4, r5
 603 0078 B169     		ldr	r1, [r6, #24]
 604 007a 7819     		adds	r0, r7, r5
 605 007c FFF7FEFF 		bl	memcpy
 606              	.LVL48:
 607 0080 EFE7     		b	.L31
 608              		.cfi_endproc
 609              	.LFE23:
 611              		.section	.text.prvReadMessageFromBuffer,"ax",%progbits
 612              		.align	1
 613              		.syntax unified
 614              		.thumb
 615              		.thumb_func
ARM GAS  /tmp/ccSZflmK.s 			page 39


 617              	prvReadMessageFromBuffer:
 618              	.LVL49:
 619              	.LFB17:
 923:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xOriginalTail, xReceivedLength, xNextMessageLength;
 620              		.loc 1 923 1 is_stmt 1 view -0
 621              		.cfi_startproc
 622              		@ args = 4, pretend = 0, frame = 8
 623              		@ frame_needed = 0, uses_anonymous_args = 0
 923:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xOriginalTail, xReceivedLength, xNextMessageLength;
 624              		.loc 1 923 1 is_stmt 0 view .LVU181
 625 0000 2DE9F043 		push	{r4, r5, r6, r7, r8, r9, lr}
 626              	.LCFI7:
 627              		.cfi_def_cfa_offset 28
 628              		.cfi_offset 4, -28
 629              		.cfi_offset 5, -24
 630              		.cfi_offset 6, -20
 631              		.cfi_offset 7, -16
 632              		.cfi_offset 8, -12
 633              		.cfi_offset 9, -8
 634              		.cfi_offset 14, -4
 635 0004 83B0     		sub	sp, sp, #12
 636              	.LCFI8:
 637              		.cfi_def_cfa_offset 40
 638 0006 0546     		mov	r5, r0
 639 0008 0F46     		mov	r7, r1
 640 000a 1646     		mov	r6, r2
 641 000c 1C46     		mov	r4, r3
 642 000e DDF82880 		ldr	r8, [sp, #40]
 924:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** configMESSAGE_BUFFER_LENGTH_TYPE xTempNextMessageLength;
 643              		.loc 1 924 1 is_stmt 1 view .LVU182
 925:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 644              		.loc 1 925 1 view .LVU183
 927:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 645              		.loc 1 927 2 view .LVU184
 927:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 646              		.loc 1 927 4 is_stmt 0 view .LVU185
 647 0012 B8F1000F 		cmp	r8, #0
 648 0016 08D1     		bne	.L39
 649              	.LVL50:
 650              	.L36:
 964:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 651              		.loc 1 964 2 is_stmt 1 view .LVU186
 964:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 652              		.loc 1 964 20 is_stmt 0 view .LVU187
 653 0018 2346     		mov	r3, r4
 654 001a 3246     		mov	r2, r6
 655 001c 3946     		mov	r1, r7
 656 001e 2846     		mov	r0, r5
 657 0020 FFF7FEFF 		bl	prvReadBytesFromBuffer
 658              	.LVL51:
 966:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 659              		.loc 1 966 2 is_stmt 1 view .LVU188
 967:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 660              		.loc 1 967 1 is_stmt 0 view .LVU189
 661 0024 03B0     		add	sp, sp, #12
 662              	.LCFI9:
 663              		.cfi_remember_state
ARM GAS  /tmp/ccSZflmK.s 			page 40


 664              		.cfi_def_cfa_offset 28
 665              		@ sp needed
 666 0026 BDE8F083 		pop	{r4, r5, r6, r7, r8, r9, pc}
 667              	.LVL52:
 668              	.L39:
 669              	.LCFI10:
 670              		.cfi_restore_state
 933:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) &xTempNextMessageLength, xBytesToS
 671              		.loc 1 933 3 is_stmt 1 view .LVU190
 933:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		( void ) prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) &xTempNextMessageLength, xBytesToS
 672              		.loc 1 933 17 is_stmt 0 view .LVU191
 673 002a D0F80090 		ldr	r9, [r0]
 674              	.LVL53:
 934:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextMessageLength = ( size_t ) xTempNextMessageLength;
 675              		.loc 1 934 3 is_stmt 1 view .LVU192
 934:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextMessageLength = ( size_t ) xTempNextMessageLength;
 676              		.loc 1 934 12 is_stmt 0 view .LVU193
 677 002e 4246     		mov	r2, r8
 678              	.LVL54:
 934:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextMessageLength = ( size_t ) xTempNextMessageLength;
 679              		.loc 1 934 12 view .LVU194
 680 0030 01A9     		add	r1, sp, #4
 681              	.LVL55:
 934:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xNextMessageLength = ( size_t ) xTempNextMessageLength;
 682              		.loc 1 934 12 view .LVU195
 683 0032 FFF7FEFF 		bl	prvReadBytesFromBuffer
 684              	.LVL56:
 935:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 685              		.loc 1 935 3 is_stmt 1 view .LVU196
 935:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 686              		.loc 1 935 22 is_stmt 0 view .LVU197
 687 0036 019B     		ldr	r3, [sp, #4]
 688              	.LVL57:
 939:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 689              		.loc 1 939 3 is_stmt 1 view .LVU198
 939:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 690              		.loc 1 939 19 is_stmt 0 view .LVU199
 691 0038 A4EB0804 		sub	r4, r4, r8
 692              	.LVL58:
 943:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 693              		.loc 1 943 3 is_stmt 1 view .LVU200
 943:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 694              		.loc 1 943 5 is_stmt 0 view .LVU201
 695 003c 9E42     		cmp	r6, r3
 696 003e 03D2     		bcs	.L37
 948:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xNextMessageLength = 0;
 697              		.loc 1 948 4 is_stmt 1 view .LVU202
 948:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xNextMessageLength = 0;
 698              		.loc 1 948 26 is_stmt 0 view .LVU203
 699 0040 C5F80090 		str	r9, [r5]
 949:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 700              		.loc 1 949 4 is_stmt 1 view .LVU204
 701              	.LVL59:
 949:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 702              		.loc 1 949 23 is_stmt 0 view .LVU205
 703 0044 0026     		movs	r6, #0
 704              	.LVL60:
ARM GAS  /tmp/ccSZflmK.s 			page 41


 949:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 705              		.loc 1 949 23 view .LVU206
 706 0046 E7E7     		b	.L36
 707              	.LVL61:
 708              	.L37:
 935:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 709              		.loc 1 935 22 view .LVU207
 710 0048 1E46     		mov	r6, r3
 711              	.LVL62:
 935:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 712              		.loc 1 935 22 view .LVU208
 713 004a E5E7     		b	.L36
 714              		.cfi_endproc
 715              	.LFE17:
 717              		.section	.text.xStreamBufferGenericCreate,"ax",%progbits
 718              		.align	1
 719              		.global	xStreamBufferGenericCreate
 720              		.syntax unified
 721              		.thumb
 722              		.thumb_func
 724              	xStreamBufferGenericCreate:
 725              	.LVL63:
 726              	.LFB4:
 220:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t *pucAllocatedMemory;
 727              		.loc 1 220 2 is_stmt 1 view -0
 728              		.cfi_startproc
 729              		@ args = 0, pretend = 0, frame = 0
 730              		@ frame_needed = 0, uses_anonymous_args = 0
 220:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t *pucAllocatedMemory;
 731              		.loc 1 220 2 is_stmt 0 view .LVU210
 732 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 733              	.LCFI11:
 734              		.cfi_def_cfa_offset 20
 735              		.cfi_offset 4, -20
 736              		.cfi_offset 5, -16
 737              		.cfi_offset 6, -12
 738              		.cfi_offset 7, -8
 739              		.cfi_offset 14, -4
 740 0002 83B0     		sub	sp, sp, #12
 741              	.LCFI12:
 742              		.cfi_def_cfa_offset 32
 743 0004 0C46     		mov	r4, r1
 221:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t ucFlags;
 744              		.loc 1 221 2 is_stmt 1 view .LVU211
 222:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 745              		.loc 1 222 2 view .LVU212
 228:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 746              		.loc 1 228 3 view .LVU213
 228:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 747              		.loc 1 228 5 is_stmt 0 view .LVU214
 748 0006 012A     		cmp	r2, #1
 749 0008 09D0     		beq	.L53
 237:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBufferSizeBytes > 0 );
 750              		.loc 1 237 4 is_stmt 1 view .LVU215
 751              	.LVL64:
 238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 752              		.loc 1 238 4 view .LVU216
ARM GAS  /tmp/ccSZflmK.s 			page 42


 753 000a A8B9     		cbnz	r0, .L50
 238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 754              		.loc 1 238 4 discriminator 1 view .LVU217
 755              	.LBB109:
 756              	.LBI109:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 757              		.loc 2 191 30 view .LVU218
 758              	.LBB110:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 759              		.loc 2 193 1 view .LVU219
 760              		.loc 2 195 2 view .LVU220
 761              		.syntax unified
 762              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 763 000c 4FF05003 			mov r3, #80												
 764 0010 83F31188 		msr basepri, r3											
 765 0014 BFF36F8F 		isb														
 766 0018 BFF34F8F 		dsb														
 767              	
 768              	@ 0 "" 2
 769              		.thumb
 770              		.syntax unified
 771              	.L44:
 772              	.LBE110:
 773              	.LBE109:
 238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 774              		.loc 1 238 4 discriminator 3 view .LVU221
 238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 775              		.loc 1 238 4 discriminator 3 view .LVU222
 776 001c FEE7     		b	.L44
 777              	.LVL65:
 778              	.L53:
 231:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBufferSizeBytes > sbBYTES_TO_STORE_MESSAGE_LENGTH );
 779              		.loc 1 231 4 view .LVU223
 232:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 780              		.loc 1 232 4 view .LVU224
 781 001e 0428     		cmp	r0, #4
 782 0020 08D8     		bhi	.L49
 232:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 783              		.loc 1 232 4 discriminator 1 view .LVU225
 784              	.LBB111:
 785              	.LBI111:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 786              		.loc 2 191 30 view .LVU226
 787              	.LBB112:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 788              		.loc 2 193 1 view .LVU227
 789              		.loc 2 195 2 view .LVU228
 790              		.syntax unified
 791              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 792 0022 4FF05003 			mov r3, #80												
 793 0026 83F31188 		msr basepri, r3											
 794 002a BFF36F8F 		isb														
 795 002e BFF34F8F 		dsb														
 796              	
 797              	@ 0 "" 2
 798              		.thumb
 799              		.syntax unified
ARM GAS  /tmp/ccSZflmK.s 			page 43


 800              	.L43:
 801              	.LBE112:
 802              	.LBE111:
 232:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 803              		.loc 1 232 4 discriminator 3 view .LVU229
 232:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 804              		.loc 1 232 4 discriminator 3 view .LVU230
 805 0032 FEE7     		b	.L43
 806              	.L49:
 231:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBufferSizeBytes > sbBYTES_TO_STORE_MESSAGE_LENGTH );
 807              		.loc 1 231 12 is_stmt 0 view .LVU231
 808 0034 0126     		movs	r6, #1
 809 0036 00E0     		b	.L42
 810              	.LVL66:
 811              	.L50:
 237:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xBufferSizeBytes > 0 );
 812              		.loc 1 237 12 view .LVU232
 813 0038 0026     		movs	r6, #0
 814              	.LVL67:
 815              	.L42:
 238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 816              		.loc 1 238 40 is_stmt 1 discriminator 2 view .LVU233
 240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 817              		.loc 1 240 3 view .LVU234
 818 003a A042     		cmp	r0, r4
 819 003c 08D2     		bcs	.L45
 240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 820              		.loc 1 240 3 discriminator 1 view .LVU235
 821              	.LBB113:
 822              	.LBI113:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 823              		.loc 2 191 30 view .LVU236
 824              	.LBB114:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 825              		.loc 2 193 1 view .LVU237
 826              		.loc 2 195 2 view .LVU238
 827              		.syntax unified
 828              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 829 003e 4FF05003 			mov r3, #80												
 830 0042 83F31188 		msr basepri, r3											
 831 0046 BFF36F8F 		isb														
 832 004a BFF34F8F 		dsb														
 833              	
 834              	@ 0 "" 2
 835              		.thumb
 836              		.syntax unified
 837              	.L46:
 838              	.LBE114:
 839              	.LBE113:
 240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 840              		.loc 1 240 3 discriminator 3 view .LVU239
 240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 841              		.loc 1 240 3 discriminator 3 view .LVU240
 842 004e FEE7     		b	.L46
 843              	.L45:
 240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 844              		.loc 1 240 57 discriminator 2 view .LVU241
ARM GAS  /tmp/ccSZflmK.s 			page 44


 244:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 845              		.loc 1 244 3 view .LVU242
 244:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 846              		.loc 1 244 5 is_stmt 0 view .LVU243
 847 0050 04B9     		cbnz	r4, .L47
 246:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 848              		.loc 1 246 23 view .LVU244
 849 0052 0124     		movs	r4, #1
 850              	.L47:
 851              	.LVL68:
 257:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		pucAllocatedMemory = ( uint8_t * ) pvPortMalloc( xBufferSizeBytes + sizeof( StreamBuffer_t ) ); /
 852              		.loc 1 257 3 is_stmt 1 view .LVU245
 257:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		pucAllocatedMemory = ( uint8_t * ) pvPortMalloc( xBufferSizeBytes + sizeof( StreamBuffer_t ) ); /
 853              		.loc 1 257 19 is_stmt 0 view .LVU246
 854 0054 471C     		adds	r7, r0, #1
 855              	.LVL69:
 258:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 856              		.loc 1 258 3 is_stmt 1 view .LVU247
 258:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 857              		.loc 1 258 38 is_stmt 0 view .LVU248
 858 0056 2530     		adds	r0, r0, #37
 859 0058 FFF7FEFF 		bl	pvPortMalloc
 860              	.LVL70:
 260:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 861              		.loc 1 260 3 is_stmt 1 view .LVU249
 260:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 862              		.loc 1 260 5 is_stmt 0 view .LVU250
 863 005c 0546     		mov	r5, r0
 864 005e 30B1     		cbz	r0, .L40
 262:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										   pucAllocatedMemory + sizeof( StreamBuffer_t ),  /* Storage area follows. */ /*lint !e9
 865              		.loc 1 262 4 is_stmt 1 view .LVU251
 866 0060 0096     		str	r6, [sp]
 867 0062 2346     		mov	r3, r4
 868 0064 3A46     		mov	r2, r7
 869 0066 00F12401 		add	r1, r0, #36
 870 006a FFF7FEFF 		bl	prvInitialiseNewStreamBuffer
 871              	.LVL71:
 268:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 872              		.loc 1 268 94 view .LVU252
 272:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 873              		.loc 1 272 56 view .LVU253
 275:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 874              		.loc 1 275 3 view .LVU254
 875              	.L40:
 276:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 876              		.loc 1 276 2 is_stmt 0 view .LVU255
 877 006e 2846     		mov	r0, r5
 878 0070 03B0     		add	sp, sp, #12
 879              	.LCFI13:
 880              		.cfi_def_cfa_offset 20
 881              		@ sp needed
 882 0072 F0BD     		pop	{r4, r5, r6, r7, pc}
 276:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 883              		.loc 1 276 2 view .LVU256
 884              		.cfi_endproc
 885              	.LFE4:
 887              		.section	.text.xStreamBufferGenericCreateStatic,"ax",%progbits
ARM GAS  /tmp/ccSZflmK.s 			page 45


 888              		.align	1
 889              		.global	xStreamBufferGenericCreateStatic
 890              		.syntax unified
 891              		.thumb
 892              		.thumb_func
 894              	xStreamBufferGenericCreateStatic:
 895              	.LVL72:
 896              	.LFB5:
 288:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBuffer_t * const pxStreamBuffer = ( StreamBuffer_t * ) pxStaticStreamBuffer; /*lint !e740 !e
 897              		.loc 1 288 2 is_stmt 1 view -0
 898              		.cfi_startproc
 899              		@ args = 4, pretend = 0, frame = 8
 900              		@ frame_needed = 0, uses_anonymous_args = 0
 289:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBufferHandle_t xReturn;
 901              		.loc 1 289 2 view .LVU258
 290:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t ucFlags;
 902              		.loc 1 290 2 view .LVU259
 291:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 903              		.loc 1 291 2 view .LVU260
 293:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pxStaticStreamBuffer );
 904              		.loc 1 293 3 view .LVU261
 905 0000 7BB1     		cbz	r3, .L71
 288:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	StreamBuffer_t * const pxStreamBuffer = ( StreamBuffer_t * ) pxStaticStreamBuffer; /*lint !e740 !e
 906              		.loc 1 288 2 is_stmt 0 view .LVU262
 907 0002 00B5     		push	{lr}
 908              	.LCFI14:
 909              		.cfi_def_cfa_offset 4
 910              		.cfi_offset 14, -4
 911 0004 85B0     		sub	sp, sp, #20
 912              	.LCFI15:
 913              		.cfi_def_cfa_offset 24
 914 0006 9C46     		mov	ip, r3
 293:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pxStaticStreamBuffer );
 915              		.loc 1 293 45 is_stmt 1 discriminator 2 view .LVU263
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 916              		.loc 1 294 3 view .LVU264
 917 0008 069B     		ldr	r3, [sp, #24]
 918              	.LVL73:
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 919              		.loc 1 294 3 is_stmt 0 view .LVU265
 920 000a 9BB1     		cbz	r3, .L72
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 921              		.loc 1 294 39 is_stmt 1 discriminator 2 view .LVU266
 295:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 922              		.loc 1 295 3 view .LVU267
 923 000c 8142     		cmp	r1, r0
 924 000e 1AD9     		bls	.L59
 295:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 925              		.loc 1 295 3 discriminator 1 view .LVU268
 926              	.LBB115:
 927              	.LBI115:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 928              		.loc 2 191 30 view .LVU269
 929              	.LBB116:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 930              		.loc 2 193 1 view .LVU270
 931              		.loc 2 195 2 view .LVU271
ARM GAS  /tmp/ccSZflmK.s 			page 46


 932              		.syntax unified
 933              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 934 0010 4FF05003 			mov r3, #80												
 935 0014 83F31188 		msr basepri, r3											
 936 0018 BFF36F8F 		isb														
 937 001c BFF34F8F 		dsb														
 938              	
 939              	@ 0 "" 2
 940              		.thumb
 941              		.syntax unified
 942              	.L60:
 943              	.LBE116:
 944              	.LBE115:
 295:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 945              		.loc 1 295 3 discriminator 3 view .LVU272
 295:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 946              		.loc 1 295 3 discriminator 3 view .LVU273
 947 0020 FEE7     		b	.L60
 948              	.LVL74:
 949              	.L71:
 950              	.LCFI16:
 951              		.cfi_def_cfa_offset 0
 952              		.cfi_restore 14
 293:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pxStaticStreamBuffer );
 953              		.loc 1 293 3 discriminator 1 view .LVU274
 954              	.LBB117:
 955              	.LBI117:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 956              		.loc 2 191 30 view .LVU275
 957              	.LBB118:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 958              		.loc 2 193 1 view .LVU276
 959              		.loc 2 195 2 view .LVU277
 960              		.syntax unified
 961              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 962 0022 4FF05003 			mov r3, #80												
 963 0026 83F31188 		msr basepri, r3											
 964 002a BFF36F8F 		isb														
 965 002e BFF34F8F 		dsb														
 966              	
 967              	@ 0 "" 2
 968              	.LVL75:
 969              		.thumb
 970              		.syntax unified
 971              	.L56:
 972              		.loc 2 195 2 is_stmt 0 view .LVU278
 973              	.LBE118:
 974              	.LBE117:
 293:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pxStaticStreamBuffer );
 975              		.loc 1 293 3 is_stmt 1 discriminator 3 view .LVU279
 293:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( pxStaticStreamBuffer );
 976              		.loc 1 293 3 discriminator 3 view .LVU280
 977 0032 FEE7     		b	.L56
 978              	.LVL76:
 979              	.L72:
 980              	.LCFI17:
 981              		.cfi_def_cfa_offset 24
ARM GAS  /tmp/ccSZflmK.s 			page 47


 982              		.cfi_offset 14, -4
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 983              		.loc 1 294 3 discriminator 1 view .LVU281
 984              	.LBB119:
 985              	.LBI119:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 986              		.loc 2 191 30 view .LVU282
 987              	.LBB120:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 988              		.loc 2 193 1 view .LVU283
 989              		.loc 2 195 2 view .LVU284
 990              		.syntax unified
 991              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 992 0034 4FF05003 			mov r3, #80												
 993 0038 83F31188 		msr basepri, r3											
 994 003c BFF36F8F 		isb														
 995 0040 BFF34F8F 		dsb														
 996              	
 997              	@ 0 "" 2
 998              		.thumb
 999              		.syntax unified
 1000              	.L58:
 1001              	.LBE120:
 1002              	.LBE119:
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 1003              		.loc 1 294 3 discriminator 3 view .LVU285
 294:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		configASSERT( xTriggerLevelBytes <= xBufferSizeBytes );
 1004              		.loc 1 294 3 discriminator 3 view .LVU286
 1005 0044 FEE7     		b	.L58
 1006              	.L59:
 295:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1007              		.loc 1 295 57 discriminator 2 view .LVU287
 299:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1008              		.loc 1 299 3 view .LVU288
 299:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1009              		.loc 1 299 5 is_stmt 0 view .LVU289
 1010 0046 01B9     		cbnz	r1, .L61
 301:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1011              		.loc 1 301 23 view .LVU290
 1012 0048 0121     		movs	r1, #1
 1013              	.LVL77:
 1014              	.L61:
 304:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1015              		.loc 1 304 3 is_stmt 1 view .LVU291
 304:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1016              		.loc 1 304 5 is_stmt 0 view .LVU292
 1017 004a 5AB1     		cbz	r2, .L68
 307:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1018              		.loc 1 307 12 view .LVU293
 1019 004c 0323     		movs	r3, #3
 1020              	.L62:
 1021              	.LVL78:
 319:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1022              		.loc 1 319 3 is_stmt 1 view .LVU294
 1023 004e 0428     		cmp	r0, #4
 1024 0050 0AD8     		bhi	.L63
 319:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 48


 1025              		.loc 1 319 3 discriminator 1 view .LVU295
 1026              	.LBB121:
 1027              	.LBI121:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1028              		.loc 2 191 30 view .LVU296
 1029              	.LBB122:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1030              		.loc 2 193 1 view .LVU297
 1031              		.loc 2 195 2 view .LVU298
 1032              		.syntax unified
 1033              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1034 0052 4FF05003 			mov r3, #80												
 1035 0056 83F31188 		msr basepri, r3											
 1036 005a BFF36F8F 		isb														
 1037 005e BFF34F8F 		dsb														
 1038              	
 1039              	@ 0 "" 2
 1040              	.LVL79:
 1041              		.thumb
 1042              		.syntax unified
 1043              	.L64:
 1044              		.loc 2 195 2 is_stmt 0 view .LVU299
 1045              	.LBE122:
 1046              	.LBE121:
 319:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1047              		.loc 1 319 3 is_stmt 1 discriminator 3 view .LVU300
 319:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1048              		.loc 1 319 3 discriminator 3 view .LVU301
 1049 0062 FEE7     		b	.L64
 1050              	.LVL80:
 1051              	.L68:
 312:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1052              		.loc 1 312 12 is_stmt 0 view .LVU302
 1053 0064 0223     		movs	r3, #2
 1054 0066 F2E7     		b	.L62
 1055              	.LVL81:
 1056              	.L63:
 319:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1057              		.loc 1 319 69 is_stmt 1 discriminator 2 view .LVU303
 1058              	.LBB123:
 326:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xSize == sizeof( StreamBuffer_t ) );
 1059              		.loc 1 326 4 view .LVU304
 326:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xSize == sizeof( StreamBuffer_t ) );
 1060              		.loc 1 326 20 is_stmt 0 view .LVU305
 1061 0068 2422     		movs	r2, #36
 1062              	.LVL82:
 326:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			configASSERT( xSize == sizeof( StreamBuffer_t ) );
 1063              		.loc 1 326 20 view .LVU306
 1064 006a 0392     		str	r2, [sp, #12]
 327:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} /*lint !e529 xSize is referenced is configASSERT() is defined. */
 1065              		.loc 1 327 4 is_stmt 1 view .LVU307
 1066 006c 039A     		ldr	r2, [sp, #12]
 1067 006e 242A     		cmp	r2, #36
 1068 0070 08D0     		beq	.L65
 327:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} /*lint !e529 xSize is referenced is configASSERT() is defined. */
 1069              		.loc 1 327 4 discriminator 1 view .LVU308
 1070              	.LBB124:
ARM GAS  /tmp/ccSZflmK.s 			page 49


 1071              	.LBI124:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1072              		.loc 2 191 30 view .LVU309
 1073              	.LBB125:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1074              		.loc 2 193 1 view .LVU310
 1075              		.loc 2 195 2 view .LVU311
 1076              		.syntax unified
 1077              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1078 0072 4FF05003 			mov r3, #80												
 1079 0076 83F31188 		msr basepri, r3											
 1080 007a BFF36F8F 		isb														
 1081 007e BFF34F8F 		dsb														
 1082              	
 1083              	@ 0 "" 2
 1084              	.LVL83:
 1085              		.thumb
 1086              		.syntax unified
 1087              	.L66:
 1088              		.loc 2 195 2 is_stmt 0 view .LVU312
 1089              	.LBE125:
 1090              	.LBE124:
 327:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} /*lint !e529 xSize is referenced is configASSERT() is defined. */
 1091              		.loc 1 327 4 is_stmt 1 discriminator 3 view .LVU313
 327:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} /*lint !e529 xSize is referenced is configASSERT() is defined. */
 1092              		.loc 1 327 4 discriminator 3 view .LVU314
 1093 0082 FEE7     		b	.L66
 1094              	.LVL84:
 1095              	.L65:
 327:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		} /*lint !e529 xSize is referenced is configASSERT() is defined. */
 1096              		.loc 1 327 53 discriminator 2 view .LVU315
 1097              	.LBE123:
 331:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1098              		.loc 1 331 3 view .LVU316
 333:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  pucStreamBufferStorageArea,
 1099              		.loc 1 333 4 view .LVU317
 1100 0084 0093     		str	r3, [sp]
 1101 0086 0B46     		mov	r3, r1
 1102              	.LVL85:
 333:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  pucStreamBufferStorageArea,
 1103              		.loc 1 333 4 is_stmt 0 view .LVU318
 1104 0088 0246     		mov	r2, r0
 1105 008a 6146     		mov	r1, ip
 1106              	.LVL86:
 333:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  pucStreamBufferStorageArea,
 1107              		.loc 1 333 4 view .LVU319
 1108 008c 0698     		ldr	r0, [sp, #24]
 1109              	.LVL87:
 333:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										  pucStreamBufferStorageArea,
 1110              		.loc 1 333 4 view .LVU320
 1111 008e FFF7FEFF 		bl	prvInitialiseNewStreamBuffer
 1112              	.LVL88:
 341:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1113              		.loc 1 341 4 is_stmt 1 view .LVU321
 341:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1114              		.loc 1 341 18 is_stmt 0 view .LVU322
 1115 0092 069B     		ldr	r3, [sp, #24]
ARM GAS  /tmp/ccSZflmK.s 			page 50


 1116 0094 1B7F     		ldrb	r3, [r3, #28]	@ zero_extendqisi2
 341:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1117              		.loc 1 341 28 view .LVU323
 1118 0096 43F00203 		orr	r3, r3, #2
 1119 009a 069A     		ldr	r2, [sp, #24]
 1120 009c 1377     		strb	r3, [r2, #28]
 343:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1121              		.loc 1 343 65 is_stmt 1 view .LVU324
 345:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1122              		.loc 1 345 4 view .LVU325
 1123              	.LVL89:
 350:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1124              		.loc 1 350 72 view .LVU326
 353:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1125              		.loc 1 353 3 view .LVU327
 354:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1126              		.loc 1 354 2 is_stmt 0 view .LVU328
 1127 009e 1046     		mov	r0, r2
 1128 00a0 05B0     		add	sp, sp, #20
 1129              	.LCFI18:
 1130              		.cfi_def_cfa_offset 4
 1131              		@ sp needed
 1132 00a2 5DF804FB 		ldr	pc, [sp], #4
 354:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1133              		.loc 1 354 2 view .LVU329
 1134              		.cfi_endproc
 1135              	.LFE5:
 1137              		.section	.text.vStreamBufferDelete,"ax",%progbits
 1138              		.align	1
 1139              		.global	vStreamBufferDelete
 1140              		.syntax unified
 1141              		.thumb
 1142              		.thumb_func
 1144              	vStreamBufferDelete:
 1145              	.LVL90:
 1146              	.LFB6:
 360:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * pxStreamBuffer = xStreamBuffer;
 1147              		.loc 1 360 1 is_stmt 1 view -0
 1148              		.cfi_startproc
 1149              		@ args = 0, pretend = 0, frame = 0
 1150              		@ frame_needed = 0, uses_anonymous_args = 0
 360:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * pxStreamBuffer = xStreamBuffer;
 1151              		.loc 1 360 1 is_stmt 0 view .LVU331
 1152 0000 08B5     		push	{r3, lr}
 1153              	.LCFI19:
 1154              		.cfi_def_cfa_offset 8
 1155              		.cfi_offset 3, -8
 1156              		.cfi_offset 14, -4
 361:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1157              		.loc 1 361 1 is_stmt 1 view .LVU332
 1158              	.LVL91:
 363:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1159              		.loc 1 363 2 view .LVU333
 1160 0002 30B1     		cbz	r0, .L79
 363:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1161              		.loc 1 363 32 discriminator 2 view .LVU334
 365:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 51


 1162              		.loc 1 365 44 view .LVU335
 367:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1163              		.loc 1 367 2 view .LVU336
 367:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1164              		.loc 1 367 22 is_stmt 0 view .LVU337
 1165 0004 027F     		ldrb	r2, [r0, #28]	@ zero_extendqisi2
 367:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1166              		.loc 1 367 4 view .LVU338
 1167 0006 12F0020F 		tst	r2, #2
 1168 000a 0BD1     		bne	.L76
 373:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1169              		.loc 1 373 4 is_stmt 1 view .LVU339
 1170 000c FFF7FEFF 		bl	vPortFree
 1171              	.LVL92:
 1172              	.L73:
 389:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1173              		.loc 1 389 1 is_stmt 0 view .LVU340
 1174 0010 08BD     		pop	{r3, pc}
 1175              	.LVL93:
 1176              	.L79:
 363:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1177              		.loc 1 363 2 is_stmt 1 discriminator 1 view .LVU341
 1178              	.LBB126:
 1179              	.LBI126:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1180              		.loc 2 191 30 view .LVU342
 1181              	.LBB127:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1182              		.loc 2 193 1 view .LVU343
 1183              		.loc 2 195 2 view .LVU344
 1184              		.syntax unified
 1185              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1186 0012 4FF05003 			mov r3, #80												
 1187 0016 83F31188 		msr basepri, r3											
 1188 001a BFF36F8F 		isb														
 1189 001e BFF34F8F 		dsb														
 1190              	
 1191              	@ 0 "" 2
 1192              		.thumb
 1193              		.syntax unified
 1194              	.L75:
 1195              	.LBE127:
 1196              	.LBE126:
 363:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1197              		.loc 1 363 2 discriminator 3 view .LVU345
 363:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1198              		.loc 1 363 2 discriminator 3 view .LVU346
 1199 0022 FEE7     		b	.L75
 1200              	.L76:
 387:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1201              		.loc 1 387 3 view .LVU347
 387:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1202              		.loc 1 387 12 is_stmt 0 view .LVU348
 1203 0024 2422     		movs	r2, #36
 1204 0026 0021     		movs	r1, #0
 1205 0028 FFF7FEFF 		bl	memset
 1206              	.LVL94:
ARM GAS  /tmp/ccSZflmK.s 			page 52


 389:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1207              		.loc 1 389 1 view .LVU349
 1208 002c F0E7     		b	.L73
 1209              		.cfi_endproc
 1210              	.LFE6:
 1212              		.section	.text.xStreamBufferReset,"ax",%progbits
 1213              		.align	1
 1214              		.global	xStreamBufferReset
 1215              		.syntax unified
 1216              		.thumb
 1217              		.thumb_func
 1219              	xStreamBufferReset:
 1220              	.LVL95:
 1221              	.LFB7:
 393:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1222              		.loc 1 393 1 is_stmt 1 view -0
 1223              		.cfi_startproc
 1224              		@ args = 0, pretend = 0, frame = 0
 1225              		@ frame_needed = 0, uses_anonymous_args = 0
 394:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn = pdFAIL;
 1226              		.loc 1 394 1 view .LVU351
 395:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1227              		.loc 1 395 1 view .LVU352
 398:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif
 1228              		.loc 1 398 2 view .LVU353
 401:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1229              		.loc 1 401 2 view .LVU354
 1230 0000 68B1     		cbz	r0, .L88
 393:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1231              		.loc 1 393 1 is_stmt 0 view .LVU355
 1232 0002 30B5     		push	{r4, r5, lr}
 1233              	.LCFI20:
 1234              		.cfi_def_cfa_offset 12
 1235              		.cfi_offset 4, -12
 1236              		.cfi_offset 5, -8
 1237              		.cfi_offset 14, -4
 1238 0004 83B0     		sub	sp, sp, #12
 1239              	.LCFI21:
 1240              		.cfi_def_cfa_offset 24
 1241 0006 0446     		mov	r4, r0
 401:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1242              		.loc 1 401 32 is_stmt 1 discriminator 2 view .LVU356
 407:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1243              		.loc 1 407 3 view .LVU357
 407:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1244              		.loc 1 407 24 is_stmt 0 view .LVU358
 1245 0008 056A     		ldr	r5, [r0, #32]
 1246              	.LVL96:
 412:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1247              		.loc 1 412 2 is_stmt 1 view .LVU359
 1248 000a FFF7FEFF 		bl	vPortEnterCritical
 1249              	.LVL97:
 414:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1250              		.loc 1 414 3 view .LVU360
 414:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1251              		.loc 1 414 21 is_stmt 0 view .LVU361
 1252 000e 2369     		ldr	r3, [r4, #16]
ARM GAS  /tmp/ccSZflmK.s 			page 53


 414:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1253              		.loc 1 414 5 view .LVU362
 1254 0010 73B1     		cbz	r3, .L89
 395:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1255              		.loc 1 395 12 view .LVU363
 1256 0012 0024     		movs	r4, #0
 1257              	.LVL98:
 1258              	.L83:
 431:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 1259              		.loc 1 431 46 is_stmt 1 view .LVU364
 435:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1260              		.loc 1 435 2 view .LVU365
 1261 0014 FFF7FEFF 		bl	vPortExitCritical
 1262              	.LVL99:
 437:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 1263              		.loc 1 437 2 view .LVU366
 438:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1264              		.loc 1 438 1 is_stmt 0 view .LVU367
 1265 0018 2046     		mov	r0, r4
 1266 001a 03B0     		add	sp, sp, #12
 1267              	.LCFI22:
 1268              		.cfi_def_cfa_offset 12
 1269              		@ sp needed
 1270 001c 30BD     		pop	{r4, r5, pc}
 1271              	.LVL100:
 1272              	.L88:
 1273              	.LCFI23:
 1274              		.cfi_def_cfa_offset 0
 1275              		.cfi_restore 4
 1276              		.cfi_restore 5
 1277              		.cfi_restore 14
 401:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1278              		.loc 1 401 2 is_stmt 1 discriminator 1 view .LVU368
 1279              	.LBB128:
 1280              	.LBI128:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1281              		.loc 2 191 30 view .LVU369
 1282              	.LBB129:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1283              		.loc 2 193 1 view .LVU370
 1284              		.loc 2 195 2 view .LVU371
 1285              		.syntax unified
 1286              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1287 001e 4FF05003 			mov r3, #80												
 1288 0022 83F31188 		msr basepri, r3											
 1289 0026 BFF36F8F 		isb														
 1290 002a BFF34F8F 		dsb														
 1291              	
 1292              	@ 0 "" 2
 1293              		.thumb
 1294              		.syntax unified
 1295              	.L82:
 1296              	.LBE129:
 1297              	.LBE128:
 401:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1298              		.loc 1 401 2 discriminator 3 view .LVU372
 401:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 54


 1299              		.loc 1 401 2 discriminator 3 view .LVU373
 1300 002e FEE7     		b	.L82
 1301              	.LVL101:
 1302              	.L89:
 1303              	.LCFI24:
 1304              		.cfi_def_cfa_offset 24
 1305              		.cfi_offset 4, -12
 1306              		.cfi_offset 5, -8
 1307              		.cfi_offset 14, -4
 416:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 1308              		.loc 1 416 4 view .LVU374
 416:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 1309              		.loc 1 416 22 is_stmt 0 view .LVU375
 1310 0030 6369     		ldr	r3, [r4, #20]
 416:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 1311              		.loc 1 416 6 view .LVU376
 1312 0032 0BB1     		cbz	r3, .L90
 395:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1313              		.loc 1 395 12 view .LVU377
 1314 0034 0024     		movs	r4, #0
 1315              	.LVL102:
 395:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1316              		.loc 1 395 12 view .LVU378
 1317 0036 EDE7     		b	.L83
 1318              	.LVL103:
 1319              	.L90:
 418:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 											  pxStreamBuffer->pucBuffer,
 1320              		.loc 1 418 5 is_stmt 1 view .LVU379
 1321 0038 237F     		ldrb	r3, [r4, #28]	@ zero_extendqisi2
 1322 003a 0093     		str	r3, [sp]
 1323 003c E368     		ldr	r3, [r4, #12]
 1324 003e A268     		ldr	r2, [r4, #8]
 1325 0040 A169     		ldr	r1, [r4, #24]
 1326 0042 2046     		mov	r0, r4
 1327 0044 FFF7FEFF 		bl	prvInitialiseNewStreamBuffer
 1328              	.LVL104:
 423:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1329              		.loc 1 423 5 view .LVU380
 427:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 1330              		.loc 1 427 6 view .LVU381
 427:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 1331              		.loc 1 427 43 is_stmt 0 view .LVU382
 1332 0048 2562     		str	r5, [r4, #32]
 423:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1333              		.loc 1 423 13 view .LVU383
 1334 004a 0124     		movs	r4, #1
 1335              	.LVL105:
 423:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1336              		.loc 1 423 13 view .LVU384
 1337 004c E2E7     		b	.L83
 1338              		.cfi_endproc
 1339              	.LFE7:
 1341              		.section	.text.xStreamBufferSetTriggerLevel,"ax",%progbits
 1342              		.align	1
 1343              		.global	xStreamBufferSetTriggerLevel
 1344              		.syntax unified
 1345              		.thumb
ARM GAS  /tmp/ccSZflmK.s 			page 55


 1346              		.thumb_func
 1348              	xStreamBufferSetTriggerLevel:
 1349              	.LVL106:
 1350              	.LFB8:
 442:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1351              		.loc 1 442 1 is_stmt 1 view -0
 1352              		.cfi_startproc
 1353              		@ args = 0, pretend = 0, frame = 0
 1354              		@ frame_needed = 0, uses_anonymous_args = 0
 1355              		@ link register save eliminated.
 443:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 1356              		.loc 1 443 1 view .LVU386
 444:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1357              		.loc 1 444 1 view .LVU387
 446:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1358              		.loc 1 446 2 view .LVU388
 1359 0000 0346     		mov	r3, r0
 1360 0002 38B1     		cbz	r0, .L98
 446:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1361              		.loc 1 446 32 discriminator 2 view .LVU389
 449:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1362              		.loc 1 449 2 view .LVU390
 449:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1363              		.loc 1 449 4 is_stmt 0 view .LVU391
 1364 0004 01B9     		cbnz	r1, .L94
 451:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1365              		.loc 1 451 17 view .LVU392
 1366 0006 0121     		movs	r1, #1
 1367              	.LVL107:
 1368              	.L94:
 456:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1369              		.loc 1 456 2 is_stmt 1 view .LVU393
 456:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1370              		.loc 1 456 37 is_stmt 0 view .LVU394
 1371 0008 9A68     		ldr	r2, [r3, #8]
 456:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1372              		.loc 1 456 4 view .LVU395
 1373 000a 8A42     		cmp	r2, r1
 1374 000c 0BD3     		bcc	.L97
 458:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdPASS;
 1375              		.loc 1 458 3 is_stmt 1 view .LVU396
 458:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xReturn = pdPASS;
 1376              		.loc 1 458 38 is_stmt 0 view .LVU397
 1377 000e D960     		str	r1, [r3, #12]
 459:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1378              		.loc 1 459 3 is_stmt 1 view .LVU398
 1379              	.LVL108:
 459:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1380              		.loc 1 459 11 is_stmt 0 view .LVU399
 1381 0010 0120     		movs	r0, #1
 1382              	.LVL109:
 459:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1383              		.loc 1 459 11 view .LVU400
 1384 0012 7047     		bx	lr
 1385              	.LVL110:
 1386              	.L98:
 446:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 56


 1387              		.loc 1 446 2 is_stmt 1 discriminator 1 view .LVU401
 1388              	.LBB130:
 1389              	.LBI130:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1390              		.loc 2 191 30 view .LVU402
 1391              	.LBB131:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1392              		.loc 2 193 1 view .LVU403
 1393              		.loc 2 195 2 view .LVU404
 1394              		.syntax unified
 1395              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1396 0014 4FF05003 			mov r3, #80												
 1397 0018 83F31188 		msr basepri, r3											
 1398 001c BFF36F8F 		isb														
 1399 0020 BFF34F8F 		dsb														
 1400              	
 1401              	@ 0 "" 2
 1402              		.thumb
 1403              		.syntax unified
 1404              	.L93:
 1405              	.LBE131:
 1406              	.LBE130:
 446:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1407              		.loc 1 446 2 discriminator 3 view .LVU405
 446:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1408              		.loc 1 446 2 discriminator 3 view .LVU406
 1409 0024 FEE7     		b	.L93
 1410              	.L97:
 463:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1411              		.loc 1 463 11 is_stmt 0 view .LVU407
 1412 0026 0020     		movs	r0, #0
 1413              	.LVL111:
 466:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 1414              		.loc 1 466 2 is_stmt 1 view .LVU408
 467:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1415              		.loc 1 467 1 is_stmt 0 view .LVU409
 1416 0028 7047     		bx	lr
 1417              		.cfi_endproc
 1418              	.LFE8:
 1420              		.section	.text.xStreamBufferSpacesAvailable,"ax",%progbits
 1421              		.align	1
 1422              		.global	xStreamBufferSpacesAvailable
 1423              		.syntax unified
 1424              		.thumb
 1425              		.thumb_func
 1427              	xStreamBufferSpacesAvailable:
 1428              	.LVL112:
 1429              	.LFB9:
 471:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1430              		.loc 1 471 1 is_stmt 1 view -0
 1431              		.cfi_startproc
 1432              		@ args = 0, pretend = 0, frame = 0
 1433              		@ frame_needed = 0, uses_anonymous_args = 0
 1434              		@ link register save eliminated.
 472:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xSpace;
 1435              		.loc 1 472 1 view .LVU411
 473:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 57


 1436              		.loc 1 473 1 view .LVU412
 475:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1437              		.loc 1 475 2 view .LVU413
 1438 0000 0346     		mov	r3, r0
 1439 0002 48B1     		cbz	r0, .L103
 475:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1440              		.loc 1 475 32 discriminator 2 view .LVU414
 477:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= pxStreamBuffer->xHead;
 1441              		.loc 1 477 2 view .LVU415
 477:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= pxStreamBuffer->xHead;
 1442              		.loc 1 477 25 is_stmt 0 view .LVU416
 1443 0004 8268     		ldr	r2, [r0, #8]
 477:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= pxStreamBuffer->xHead;
 1444              		.loc 1 477 51 view .LVU417
 1445 0006 0068     		ldr	r0, [r0]
 1446              	.LVL113:
 477:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= pxStreamBuffer->xHead;
 1447              		.loc 1 477 9 view .LVU418
 1448 0008 1044     		add	r0, r0, r2
 1449              	.LVL114:
 478:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= ( size_t ) 1;
 1450              		.loc 1 478 2 is_stmt 1 view .LVU419
 478:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= ( size_t ) 1;
 1451              		.loc 1 478 26 is_stmt 0 view .LVU420
 1452 000a 5B68     		ldr	r3, [r3, #4]
 1453              	.LVL115:
 478:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xSpace -= ( size_t ) 1;
 1454              		.loc 1 478 9 view .LVU421
 1455 000c C01A     		subs	r0, r0, r3
 1456              	.LVL116:
 479:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1457              		.loc 1 479 2 is_stmt 1 view .LVU422
 479:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1458              		.loc 1 479 9 is_stmt 0 view .LVU423
 1459 000e 0138     		subs	r0, r0, #1
 1460              	.LVL117:
 481:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1461              		.loc 1 481 2 is_stmt 1 view .LVU424
 481:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1462              		.loc 1 481 4 is_stmt 0 view .LVU425
 1463 0010 8242     		cmp	r2, r0
 1464 0012 00D8     		bhi	.L99
 483:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1465              		.loc 1 483 3 is_stmt 1 view .LVU426
 483:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1466              		.loc 1 483 10 is_stmt 0 view .LVU427
 1467 0014 801A     		subs	r0, r0, r2
 1468              	.LVL118:
 487:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1469              		.loc 1 487 27 is_stmt 1 view .LVU428
 490:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 1470              		.loc 1 490 2 view .LVU429
 1471              	.L99:
 491:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1472              		.loc 1 491 1 is_stmt 0 view .LVU430
 1473 0016 7047     		bx	lr
 1474              	.LVL119:
ARM GAS  /tmp/ccSZflmK.s 			page 58


 1475              	.L103:
 475:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1476              		.loc 1 475 2 is_stmt 1 discriminator 1 view .LVU431
 1477              	.LBB132:
 1478              	.LBI132:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1479              		.loc 2 191 30 view .LVU432
 1480              	.LBB133:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1481              		.loc 2 193 1 view .LVU433
 1482              		.loc 2 195 2 view .LVU434
 1483              		.syntax unified
 1484              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1485 0018 4FF05003 			mov r3, #80												
 1486 001c 83F31188 		msr basepri, r3											
 1487 0020 BFF36F8F 		isb														
 1488 0024 BFF34F8F 		dsb														
 1489              	
 1490              	@ 0 "" 2
 1491              		.thumb
 1492              		.syntax unified
 1493              	.L101:
 1494              	.LBE133:
 1495              	.LBE132:
 475:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1496              		.loc 1 475 2 discriminator 3 view .LVU435
 475:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1497              		.loc 1 475 2 discriminator 3 view .LVU436
 1498 0028 FEE7     		b	.L101
 1499              		.cfi_endproc
 1500              	.LFE9:
 1502              		.section	.text.xStreamBufferBytesAvailable,"ax",%progbits
 1503              		.align	1
 1504              		.global	xStreamBufferBytesAvailable
 1505              		.syntax unified
 1506              		.thumb
 1507              		.thumb_func
 1509              	xStreamBufferBytesAvailable:
 1510              	.LVL120:
 1511              	.LFB10:
 495:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1512              		.loc 1 495 1 view -0
 1513              		.cfi_startproc
 1514              		@ args = 0, pretend = 0, frame = 0
 1515              		@ frame_needed = 0, uses_anonymous_args = 0
 495:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1516              		.loc 1 495 1 is_stmt 0 view .LVU438
 1517 0000 08B5     		push	{r3, lr}
 1518              	.LCFI25:
 1519              		.cfi_def_cfa_offset 8
 1520              		.cfi_offset 3, -8
 1521              		.cfi_offset 14, -4
 496:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn;
 1522              		.loc 1 496 1 is_stmt 1 view .LVU439
 1523              	.LVL121:
 497:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1524              		.loc 1 497 1 view .LVU440
ARM GAS  /tmp/ccSZflmK.s 			page 59


 499:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1525              		.loc 1 499 2 view .LVU441
 1526 0002 10B1     		cbz	r0, .L108
 499:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1527              		.loc 1 499 32 discriminator 2 view .LVU442
 501:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 1528              		.loc 1 501 2 view .LVU443
 501:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	return xReturn;
 1529              		.loc 1 501 12 is_stmt 0 view .LVU444
 1530 0004 FFF7FEFF 		bl	prvBytesInBuffer
 1531              	.LVL122:
 502:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 1532              		.loc 1 502 2 is_stmt 1 view .LVU445
 503:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1533              		.loc 1 503 1 is_stmt 0 view .LVU446
 1534 0008 08BD     		pop	{r3, pc}
 1535              	.LVL123:
 1536              	.L108:
 499:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1537              		.loc 1 499 2 is_stmt 1 discriminator 1 view .LVU447
 1538              	.LBB134:
 1539              	.LBI134:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1540              		.loc 2 191 30 view .LVU448
 1541              	.LBB135:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1542              		.loc 2 193 1 view .LVU449
 1543              		.loc 2 195 2 view .LVU450
 1544              		.syntax unified
 1545              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1546 000a 4FF05003 			mov r3, #80												
 1547 000e 83F31188 		msr basepri, r3											
 1548 0012 BFF36F8F 		isb														
 1549 0016 BFF34F8F 		dsb														
 1550              	
 1551              	@ 0 "" 2
 1552              		.thumb
 1553              		.syntax unified
 1554              	.L106:
 1555              	.LBE135:
 1556              	.LBE134:
 499:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1557              		.loc 1 499 2 discriminator 3 view .LVU451
 499:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1558              		.loc 1 499 2 discriminator 3 view .LVU452
 1559 001a FEE7     		b	.L106
 1560              		.cfi_endproc
 1561              	.LFE10:
 1563              		.section	.text.xStreamBufferSend,"ax",%progbits
 1564              		.align	1
 1565              		.global	xStreamBufferSend
 1566              		.syntax unified
 1567              		.thumb
 1568              		.thumb_func
 1570              	xStreamBufferSend:
 1571              	.LVL124:
 1572              	.LFB11:
ARM GAS  /tmp/ccSZflmK.s 			page 60


 510:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1573              		.loc 1 510 1 view -0
 1574              		.cfi_startproc
 1575              		@ args = 0, pretend = 0, frame = 16
 1576              		@ frame_needed = 0, uses_anonymous_args = 0
 510:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1577              		.loc 1 510 1 is_stmt 0 view .LVU454
 1578 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 1579              	.LCFI26:
 1580              		.cfi_def_cfa_offset 24
 1581              		.cfi_offset 4, -24
 1582              		.cfi_offset 5, -20
 1583              		.cfi_offset 6, -16
 1584              		.cfi_offset 7, -12
 1585              		.cfi_offset 8, -8
 1586              		.cfi_offset 14, -4
 1587 0004 86B0     		sub	sp, sp, #24
 1588              	.LCFI27:
 1589              		.cfi_def_cfa_offset 48
 1590 0006 0393     		str	r3, [sp, #12]
 511:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn, xSpace = 0;
 1591              		.loc 1 511 1 is_stmt 1 view .LVU455
 1592              	.LVL125:
 512:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xRequiredSpace = xDataLengthBytes;
 1593              		.loc 1 512 1 view .LVU456
 513:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** TimeOut_t xTimeOut;
 1594              		.loc 1 513 1 view .LVU457
 514:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1595              		.loc 1 514 1 view .LVU458
 516:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1596              		.loc 1 516 2 view .LVU459
 1597 0008 99B1     		cbz	r1, .L127
 1598 000a 0446     		mov	r4, r0
 1599 000c 1646     		mov	r6, r2
 1600 000e 8846     		mov	r8, r1
 516:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1601              		.loc 1 516 26 discriminator 2 view .LVU460
 517:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1602              		.loc 1 517 2 view .LVU461
 1603 0010 C0B1     		cbz	r0, .L128
 517:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1604              		.loc 1 517 32 discriminator 2 view .LVU462
 523:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1605              		.loc 1 523 2 view .LVU463
 523:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1606              		.loc 1 523 22 is_stmt 0 view .LVU464
 1607 0012 037F     		ldrb	r3, [r0, #28]	@ zero_extendqisi2
 1608              	.LVL126:
 523:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1609              		.loc 1 523 4 view .LVU465
 1610 0014 13F0010F 		tst	r3, #1
 1611 0018 1DD0     		beq	.L125
 525:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1612              		.loc 1 525 3 is_stmt 1 view .LVU466
 525:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1613              		.loc 1 525 18 is_stmt 0 view .LVU467
 1614 001a 171D     		adds	r7, r2, #4
ARM GAS  /tmp/ccSZflmK.s 			page 61


 1615              	.LVL127:
 528:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1616              		.loc 1 528 3 is_stmt 1 view .LVU468
 1617 001c BA42     		cmp	r2, r7
 1618 001e 1BD3     		bcc	.L114
 528:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1619              		.loc 1 528 3 discriminator 1 view .LVU469
 1620              	.LBB136:
 1621              	.LBI136:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1622              		.loc 2 191 30 view .LVU470
 1623              	.LBB137:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1624              		.loc 2 193 1 view .LVU471
 1625              		.loc 2 195 2 view .LVU472
 1626              		.syntax unified
 1627              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1628 0020 4FF05003 			mov r3, #80												
 1629 0024 83F31188 		msr basepri, r3											
 1630 0028 BFF36F8F 		isb														
 1631 002c BFF34F8F 		dsb														
 1632              	
 1633              	@ 0 "" 2
 1634              		.thumb
 1635              		.syntax unified
 1636              	.L115:
 1637              	.LBE137:
 1638              	.LBE136:
 528:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1639              		.loc 1 528 3 discriminator 3 view .LVU473
 528:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1640              		.loc 1 528 3 discriminator 3 view .LVU474
 1641 0030 FEE7     		b	.L115
 1642              	.LVL128:
 1643              	.L127:
 516:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1644              		.loc 1 516 2 discriminator 1 view .LVU475
 1645              	.LBB138:
 1646              	.LBI138:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1647              		.loc 2 191 30 view .LVU476
 1648              	.LBB139:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1649              		.loc 2 193 1 view .LVU477
 1650              		.loc 2 195 2 view .LVU478
 1651              		.syntax unified
 1652              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1653 0032 4FF05003 			mov r3, #80												
 1654 0036 83F31188 		msr basepri, r3											
 1655 003a BFF36F8F 		isb														
 1656 003e BFF34F8F 		dsb														
 1657              	
 1658              	@ 0 "" 2
 1659              	.LVL129:
 1660              		.thumb
 1661              		.syntax unified
 1662              	.L111:
ARM GAS  /tmp/ccSZflmK.s 			page 62


 1663              		.loc 2 195 2 is_stmt 0 view .LVU479
 1664              	.LBE139:
 1665              	.LBE138:
 516:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1666              		.loc 1 516 2 is_stmt 1 discriminator 3 view .LVU480
 516:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1667              		.loc 1 516 2 discriminator 3 view .LVU481
 1668 0042 FEE7     		b	.L111
 1669              	.LVL130:
 1670              	.L128:
 517:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1671              		.loc 1 517 2 discriminator 1 view .LVU482
 1672              	.LBB140:
 1673              	.LBI140:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1674              		.loc 2 191 30 view .LVU483
 1675              	.LBB141:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1676              		.loc 2 193 1 view .LVU484
 1677              		.loc 2 195 2 view .LVU485
 1678              		.syntax unified
 1679              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1680 0044 4FF05003 			mov r3, #80												
 1681 0048 83F31188 		msr basepri, r3											
 1682 004c BFF36F8F 		isb														
 1683 0050 BFF34F8F 		dsb														
 1684              	
 1685              	@ 0 "" 2
 1686              	.LVL131:
 1687              		.thumb
 1688              		.syntax unified
 1689              	.L113:
 1690              		.loc 2 195 2 is_stmt 0 view .LVU486
 1691              	.LBE141:
 1692              	.LBE140:
 517:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1693              		.loc 1 517 2 is_stmt 1 discriminator 3 view .LVU487
 517:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1694              		.loc 1 517 2 discriminator 3 view .LVU488
 1695 0054 FEE7     		b	.L113
 1696              	.L125:
 513:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** TimeOut_t xTimeOut;
 1697              		.loc 1 513 8 is_stmt 0 view .LVU489
 1698 0056 1746     		mov	r7, r2
 1699              	.LVL132:
 1700              	.L114:
 532:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1701              		.loc 1 532 27 is_stmt 1 view .LVU490
 535:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1702              		.loc 1 535 2 view .LVU491
 535:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1703              		.loc 1 535 19 is_stmt 0 view .LVU492
 1704 0058 039B     		ldr	r3, [sp, #12]
 535:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1705              		.loc 1 535 4 view .LVU493
 1706 005a 7BB3     		cbz	r3, .L116
 537:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 63


 1707              		.loc 1 537 3 is_stmt 1 view .LVU494
 1708 005c 04A8     		add	r0, sp, #16
 1709              	.LVL133:
 537:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1710              		.loc 1 537 3 is_stmt 0 view .LVU495
 1711 005e FFF7FEFF 		bl	vTaskSetTimeOutState
 1712              	.LVL134:
 1713              	.L120:
 539:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1714              		.loc 1 539 3 is_stmt 1 view .LVU496
 543:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 1715              		.loc 1 543 4 view .LVU497
 1716 0062 FFF7FEFF 		bl	vPortEnterCritical
 1717              	.LVL135:
 545:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1718              		.loc 1 545 5 view .LVU498
 545:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1719              		.loc 1 545 14 is_stmt 0 view .LVU499
 1720 0066 2046     		mov	r0, r4
 1721 0068 FFF7FEFF 		bl	xStreamBufferSpacesAvailable
 1722              	.LVL136:
 1723 006c 0546     		mov	r5, r0
 1724              	.LVL137:
 547:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				{
 1725              		.loc 1 547 5 is_stmt 1 view .LVU500
 547:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				{
 1726              		.loc 1 547 7 is_stmt 0 view .LVU501
 1727 006e 8742     		cmp	r7, r0
 1728 0070 21D9     		bls	.L117
 550:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1729              		.loc 1 550 6 is_stmt 1 view .LVU502
 550:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1730              		.loc 1 550 15 is_stmt 0 view .LVU503
 1731 0072 0020     		movs	r0, #0
 1732              	.LVL138:
 550:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1733              		.loc 1 550 15 view .LVU504
 1734 0074 FFF7FEFF 		bl	xTaskNotifyStateClear
 1735              	.LVL139:
 553:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->xTaskWaitingToSend = xTaskGetCurrentTaskHandle();
 1736              		.loc 1 553 6 is_stmt 1 view .LVU505
 1737 0078 6369     		ldr	r3, [r4, #20]
 1738 007a 9BB9     		cbnz	r3, .L129
 553:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->xTaskWaitingToSend = xTaskGetCurrentTaskHandle();
 1739              		.loc 1 553 64 discriminator 2 view .LVU506
 554:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 1740              		.loc 1 554 6 view .LVU507
 554:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 1741              		.loc 1 554 43 is_stmt 0 view .LVU508
 1742 007c FFF7FEFF 		bl	xTaskGetCurrentTaskHandle
 1743              	.LVL140:
 554:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 1744              		.loc 1 554 41 discriminator 1 view .LVU509
 1745 0080 6061     		str	r0, [r4, #20]
 562:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1746              		.loc 1 562 4 is_stmt 1 view .LVU510
 1747 0082 FFF7FEFF 		bl	vPortExitCritical
ARM GAS  /tmp/ccSZflmK.s 			page 64


 1748              	.LVL141:
 564:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) xTaskNotifyWait( ( uint32_t ) 0, ( uint32_t ) 0, NULL, xTicksToWait );
 1749              		.loc 1 564 56 view .LVU511
 565:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTaskWaitingToSend = NULL;
 1750              		.loc 1 565 4 view .LVU512
 565:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTaskWaitingToSend = NULL;
 1751              		.loc 1 565 13 is_stmt 0 view .LVU513
 1752 0086 039B     		ldr	r3, [sp, #12]
 1753 0088 0022     		movs	r2, #0
 1754 008a 1146     		mov	r1, r2
 1755 008c 1046     		mov	r0, r2
 1756 008e FFF7FEFF 		bl	xTaskNotifyWait
 1757              	.LVL142:
 566:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1758              		.loc 1 566 4 is_stmt 1 view .LVU514
 566:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1759              		.loc 1 566 39 is_stmt 0 view .LVU515
 1760 0092 0023     		movs	r3, #0
 1761 0094 6361     		str	r3, [r4, #20]
 568:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1762              		.loc 1 568 61 is_stmt 1 view .LVU516
 568:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1763              		.loc 1 568 12 is_stmt 0 view .LVU517
 1764 0096 03A9     		add	r1, sp, #12
 1765 0098 04A8     		add	r0, sp, #16
 1766 009a FFF7FEFF 		bl	xTaskCheckForTimeOut
 1767              	.LVL143:
 568:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1768              		.loc 1 568 61 discriminator 1 view .LVU518
 1769 009e 0028     		cmp	r0, #0
 1770 00a0 DFD0     		beq	.L120
 1771 00a2 0AE0     		b	.L121
 1772              	.L129:
 553:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->xTaskWaitingToSend = xTaskGetCurrentTaskHandle();
 1773              		.loc 1 553 6 is_stmt 1 discriminator 1 view .LVU519
 1774              	.LBB142:
 1775              	.LBI142:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1776              		.loc 2 191 30 view .LVU520
 1777              	.LBB143:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1778              		.loc 2 193 1 view .LVU521
 1779              		.loc 2 195 2 view .LVU522
 1780              		.syntax unified
 1781              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1782 00a4 4FF05003 			mov r3, #80												
 1783 00a8 83F31188 		msr basepri, r3											
 1784 00ac BFF36F8F 		isb														
 1785 00b0 BFF34F8F 		dsb														
 1786              	
 1787              	@ 0 "" 2
 1788              		.thumb
 1789              		.syntax unified
 1790              	.L119:
 1791              	.LBE143:
 1792              	.LBE142:
 553:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->xTaskWaitingToSend = xTaskGetCurrentTaskHandle();
ARM GAS  /tmp/ccSZflmK.s 			page 65


 1793              		.loc 1 553 6 discriminator 3 view .LVU523
 553:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					pxStreamBuffer->xTaskWaitingToSend = xTaskGetCurrentTaskHandle();
 1794              		.loc 1 553 6 discriminator 3 view .LVU524
 1795 00b4 FEE7     		b	.L119
 1796              	.LVL144:
 1797              	.L117:
 558:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 					break;
 1798              		.loc 1 558 6 view .LVU525
 1799 00b6 FFF7FEFF 		bl	vPortExitCritical
 1800              	.LVL145:
 559:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				}
 1801              		.loc 1 559 6 view .LVU526
 1802              	.L121:
 572:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1803              		.loc 1 572 27 view .LVU527
 575:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1804              		.loc 1 575 2 view .LVU528
 575:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1805              		.loc 1 575 4 is_stmt 0 view .LVU529
 1806 00ba 1DB9     		cbnz	r5, .L122
 1807              	.LVL146:
 1808              	.L116:
 577:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1809              		.loc 1 577 3 is_stmt 1 view .LVU530
 577:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1810              		.loc 1 577 12 is_stmt 0 view .LVU531
 1811 00bc 2046     		mov	r0, r4
 1812 00be FFF7FEFF 		bl	xStreamBufferSpacesAvailable
 1813              	.LVL147:
 1814 00c2 0546     		mov	r5, r0
 1815              	.LVL148:
 1816              	.L122:
 581:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1817              		.loc 1 581 27 is_stmt 1 view .LVU532
 584:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1818              		.loc 1 584 2 view .LVU533
 584:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1819              		.loc 1 584 12 is_stmt 0 view .LVU534
 1820 00c4 0097     		str	r7, [sp]
 1821 00c6 2B46     		mov	r3, r5
 1822 00c8 3246     		mov	r2, r6
 1823 00ca 4146     		mov	r1, r8
 1824 00cc 2046     		mov	r0, r4
 1825 00ce FFF7FEFF 		bl	prvWriteMessageToBuffer
 1826              	.LVL149:
 586:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1827              		.loc 1 586 2 is_stmt 1 view .LVU535
 586:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1828              		.loc 1 586 4 is_stmt 0 view .LVU536
 1829 00d2 0546     		mov	r5, r0
 1830              	.LVL150:
 586:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1831              		.loc 1 586 4 view .LVU537
 1832 00d4 18B9     		cbnz	r0, .L130
 1833              	.LVL151:
 1834              	.L109:
 607:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
ARM GAS  /tmp/ccSZflmK.s 			page 66


 1835              		.loc 1 607 1 view .LVU538
 1836 00d6 2846     		mov	r0, r5
 1837 00d8 06B0     		add	sp, sp, #24
 1838              	.LCFI28:
 1839              		.cfi_remember_state
 1840              		.cfi_def_cfa_offset 24
 1841              		@ sp needed
 1842 00da BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 1843              	.LVL152:
 1844              	.L130:
 1845              	.LCFI29:
 1846              		.cfi_restore_state
 588:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1847              		.loc 1 588 52 is_stmt 1 view .LVU539
 591:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1848              		.loc 1 591 3 view .LVU540
 591:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1849              		.loc 1 591 7 is_stmt 0 view .LVU541
 1850 00de 2046     		mov	r0, r4
 1851              	.LVL153:
 591:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1852              		.loc 1 591 7 view .LVU542
 1853 00e0 FFF7FEFF 		bl	prvBytesInBuffer
 1854              	.LVL154:
 591:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1855              		.loc 1 591 59 discriminator 1 view .LVU543
 1856 00e4 E368     		ldr	r3, [r4, #12]
 591:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 1857              		.loc 1 591 5 discriminator 1 view .LVU544
 1858 00e6 9842     		cmp	r0, r3
 1859 00e8 F5D3     		bcc	.L109
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1860              		.loc 1 593 4 is_stmt 1 view .LVU545
 1861 00ea FFF7FEFF 		bl	vTaskSuspendAll
 1862              	.LVL155:
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1863              		.loc 1 593 4 discriminator 1 view .LVU546
 1864 00ee 2369     		ldr	r3, [r4, #16]
 1865 00f0 3BB1     		cbz	r3, .L124
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1866              		.loc 1 593 4 discriminator 1 view .LVU547
 1867 00f2 2069     		ldr	r0, [r4, #16]
 1868 00f4 0023     		movs	r3, #0
 1869 00f6 1A46     		mov	r2, r3
 1870 00f8 1946     		mov	r1, r3
 1871 00fa FFF7FEFF 		bl	xTaskGenericNotify
 1872              	.LVL156:
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1873              		.loc 1 593 4 discriminator 1 view .LVU548
 1874 00fe 0023     		movs	r3, #0
 1875 0100 2361     		str	r3, [r4, #16]
 1876              	.L124:
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 1877              		.loc 1 593 4 discriminator 3 view .LVU549
 1878 0102 FFF7FEFF 		bl	xTaskResumeAll
 1879              	.LVL157:
 593:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
ARM GAS  /tmp/ccSZflmK.s 			page 67


 1880              		.loc 1 593 38 discriminator 1 view .LVU550
 602:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		traceSTREAM_BUFFER_SEND_FAILED( xStreamBuffer );
 1881              		.loc 1 602 27 view .LVU551
 603:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1882              		.loc 1 603 50 view .LVU552
 606:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 1883              		.loc 1 606 2 view .LVU553
 606:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 1884              		.loc 1 606 9 is_stmt 0 view .LVU554
 1885 0106 E6E7     		b	.L109
 1886              		.cfi_endproc
 1887              	.LFE11:
 1889              		.section	.text.xStreamBufferSendFromISR,"ax",%progbits
 1890              		.align	1
 1891              		.global	xStreamBufferSendFromISR
 1892              		.syntax unified
 1893              		.thumb
 1894              		.thumb_func
 1896              	xStreamBufferSendFromISR:
 1897              	.LVL158:
 1898              	.LFB12:
 614:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1899              		.loc 1 614 1 is_stmt 1 view -0
 1900              		.cfi_startproc
 1901              		@ args = 0, pretend = 0, frame = 0
 1902              		@ frame_needed = 0, uses_anonymous_args = 0
 615:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn, xSpace;
 1903              		.loc 1 615 1 view .LVU556
 616:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xRequiredSpace = xDataLengthBytes;
 1904              		.loc 1 616 1 view .LVU557
 617:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1905              		.loc 1 617 1 view .LVU558
 619:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1906              		.loc 1 619 2 view .LVU559
 1907 0000 F1B1     		cbz	r1, .L142
 614:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 1908              		.loc 1 614 1 is_stmt 0 view .LVU560
 1909 0002 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 1910              	.LCFI30:
 1911              		.cfi_def_cfa_offset 24
 1912              		.cfi_offset 4, -24
 1913              		.cfi_offset 5, -20
 1914              		.cfi_offset 6, -16
 1915              		.cfi_offset 7, -12
 1916              		.cfi_offset 8, -8
 1917              		.cfi_offset 14, -4
 1918 0006 82B0     		sub	sp, sp, #8
 1919              	.LCFI31:
 1920              		.cfi_def_cfa_offset 32
 1921 0008 0446     		mov	r4, r0
 1922 000a 1546     		mov	r5, r2
 1923 000c 1E46     		mov	r6, r3
 1924 000e 0F46     		mov	r7, r1
 619:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1925              		.loc 1 619 26 is_stmt 1 discriminator 2 view .LVU561
 620:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1926              		.loc 1 620 2 view .LVU562
ARM GAS  /tmp/ccSZflmK.s 			page 68


 1927 0010 F8B1     		cbz	r0, .L143
 620:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1928              		.loc 1 620 32 discriminator 2 view .LVU563
 626:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1929              		.loc 1 626 2 view .LVU564
 626:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1930              		.loc 1 626 22 is_stmt 0 view .LVU565
 1931 0012 037F     		ldrb	r3, [r0, #28]	@ zero_extendqisi2
 1932              	.LVL159:
 626:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1933              		.loc 1 626 4 view .LVU566
 1934 0014 13F0010F 		tst	r3, #1
 1935 0018 24D0     		beq	.L139
 628:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1936              		.loc 1 628 3 is_stmt 1 view .LVU567
 628:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1937              		.loc 1 628 18 is_stmt 0 view .LVU568
 1938 001a 02F10408 		add	r8, r2, #4
 1939              	.LVL160:
 1940              	.L136:
 632:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 1941              		.loc 1 632 27 is_stmt 1 view .LVU569
 635:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvWriteMessageToBuffer( pxStreamBuffer, pvTxData, xDataLengthBytes, xSpace, xRequiredSp
 1942              		.loc 1 635 2 view .LVU570
 635:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvWriteMessageToBuffer( pxStreamBuffer, pvTxData, xDataLengthBytes, xSpace, xRequiredSp
 1943              		.loc 1 635 11 is_stmt 0 view .LVU571
 1944 001e 2046     		mov	r0, r4
 1945              	.LVL161:
 635:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvWriteMessageToBuffer( pxStreamBuffer, pvTxData, xDataLengthBytes, xSpace, xRequiredSp
 1946              		.loc 1 635 11 view .LVU572
 1947 0020 FFF7FEFF 		bl	xStreamBufferSpacesAvailable
 1948              	.LVL162:
 635:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	xReturn = prvWriteMessageToBuffer( pxStreamBuffer, pvTxData, xDataLengthBytes, xSpace, xRequiredSp
 1949              		.loc 1 635 11 view .LVU573
 1950 0024 0346     		mov	r3, r0
 1951              	.LVL163:
 636:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1952              		.loc 1 636 2 is_stmt 1 view .LVU574
 636:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1953              		.loc 1 636 12 is_stmt 0 view .LVU575
 1954 0026 CDF80080 		str	r8, [sp]
 1955 002a 2A46     		mov	r2, r5
 1956 002c 3946     		mov	r1, r7
 1957 002e 2046     		mov	r0, r4
 1958              	.LVL164:
 636:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 1959              		.loc 1 636 12 view .LVU576
 1960 0030 FFF7FEFF 		bl	prvWriteMessageToBuffer
 1961              	.LVL165:
 638:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1962              		.loc 1 638 2 is_stmt 1 view .LVU577
 638:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1963              		.loc 1 638 4 is_stmt 0 view .LVU578
 1964 0034 0546     		mov	r5, r0
 1965              	.LVL166:
 638:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 1966              		.loc 1 638 4 view .LVU579
ARM GAS  /tmp/ccSZflmK.s 			page 69


 1967 0036 B8B9     		cbnz	r0, .L144
 1968              	.LVL167:
 1969              	.L131:
 658:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 1970              		.loc 1 658 1 view .LVU580
 1971 0038 2846     		mov	r0, r5
 1972 003a 02B0     		add	sp, sp, #8
 1973              	.LCFI32:
 1974              		.cfi_def_cfa_offset 24
 1975              		@ sp needed
 1976 003c BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 1977              	.LVL168:
 1978              	.L142:
 1979              	.LCFI33:
 1980              		.cfi_def_cfa_offset 0
 1981              		.cfi_restore 4
 1982              		.cfi_restore 5
 1983              		.cfi_restore 6
 1984              		.cfi_restore 7
 1985              		.cfi_restore 8
 1986              		.cfi_restore 14
 619:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 1987              		.loc 1 619 2 is_stmt 1 discriminator 1 view .LVU581
 1988              	.LBB144:
 1989              	.LBI144:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1990              		.loc 2 191 30 view .LVU582
 1991              	.LBB145:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1992              		.loc 2 193 1 view .LVU583
 1993              		.loc 2 195 2 view .LVU584
 1994              		.syntax unified
 1995              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1996 0040 4FF05003 			mov r3, #80												
 1997 0044 83F31188 		msr basepri, r3											
 1998 0048 BFF36F8F 		isb														
 1999 004c BFF34F8F 		dsb														
 2000              	
 2001              	@ 0 "" 2
 2002              	.LVL169:
 2003              		.thumb
 2004              		.syntax unified
 2005              	.L133:
 2006              		.loc 2 195 2 is_stmt 0 view .LVU585
 2007              	.LBE145:
 2008              	.LBE144:
 619:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2009              		.loc 1 619 2 is_stmt 1 discriminator 3 view .LVU586
 619:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2010              		.loc 1 619 2 discriminator 3 view .LVU587
 2011 0050 FEE7     		b	.L133
 2012              	.LVL170:
 2013              	.L143:
 2014              	.LCFI34:
 2015              		.cfi_def_cfa_offset 32
 2016              		.cfi_offset 4, -24
 2017              		.cfi_offset 5, -20
ARM GAS  /tmp/ccSZflmK.s 			page 70


 2018              		.cfi_offset 6, -16
 2019              		.cfi_offset 7, -12
 2020              		.cfi_offset 8, -8
 2021              		.cfi_offset 14, -4
 620:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2022              		.loc 1 620 2 discriminator 1 view .LVU588
 2023              	.LBB146:
 2024              	.LBI146:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2025              		.loc 2 191 30 view .LVU589
 2026              	.LBB147:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2027              		.loc 2 193 1 view .LVU590
 2028              		.loc 2 195 2 view .LVU591
 2029              		.syntax unified
 2030              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2031 0052 4FF05003 			mov r3, #80												
 2032 0056 83F31188 		msr basepri, r3											
 2033 005a BFF36F8F 		isb														
 2034 005e BFF34F8F 		dsb														
 2035              	
 2036              	@ 0 "" 2
 2037              	.LVL171:
 2038              		.thumb
 2039              		.syntax unified
 2040              	.L135:
 2041              		.loc 2 195 2 is_stmt 0 view .LVU592
 2042              	.LBE147:
 2043              	.LBE146:
 620:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2044              		.loc 1 620 2 is_stmt 1 discriminator 3 view .LVU593
 620:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2045              		.loc 1 620 2 discriminator 3 view .LVU594
 2046 0062 FEE7     		b	.L135
 2047              	.L139:
 617:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2048              		.loc 1 617 8 is_stmt 0 view .LVU595
 2049 0064 9046     		mov	r8, r2
 2050 0066 DAE7     		b	.L136
 2051              	.LVL172:
 2052              	.L144:
 641:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2053              		.loc 1 641 3 is_stmt 1 view .LVU596
 641:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2054              		.loc 1 641 7 is_stmt 0 view .LVU597
 2055 0068 2046     		mov	r0, r4
 2056              	.LVL173:
 641:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2057              		.loc 1 641 7 view .LVU598
 2058 006a FFF7FEFF 		bl	prvBytesInBuffer
 2059              	.LVL174:
 641:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2060              		.loc 1 641 59 discriminator 1 view .LVU599
 2061 006e E368     		ldr	r3, [r4, #12]
 641:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2062              		.loc 1 641 5 discriminator 1 view .LVU600
 2063 0070 9842     		cmp	r0, r3
ARM GAS  /tmp/ccSZflmK.s 			page 71


 2064 0072 E1D3     		bcc	.L131
 2065              	.LBB148:
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2066              		.loc 1 643 4 is_stmt 1 view .LVU601
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2067              		.loc 1 643 4 view .LVU602
 2068              	.LBB149:
 2069              	.LBI149:
 196:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 197:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	mov %0, %1												\n"	\
 198:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	msr basepri, %0											\n" \
 199:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	isb														\n" \
 200:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	dsb														\n" \
 201:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		:"=r" (ulNewBASEPRI) : "i" ( configMAX_SYSCALL_INTERRUPT_PRIORITY ) : "memory"
 202:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	);
 203:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 204:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 205:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 206:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 207:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static uint32_t ulPortRaiseBASEPRI( void )
 2070              		.loc 2 207 34 view .LVU603
 2071              	.LBB150:
 208:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 209:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulOriginalBASEPRI, ulNewBASEPRI;
 2072              		.loc 2 209 1 view .LVU604
 210:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 2073              		.loc 2 211 2 view .LVU605
 2074              		.syntax unified
 2075              	@ 211 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2076 0074 EFF31187 			mrs r7, basepri											
 2077 0078 4FF05003 		mov r3, #80												
 2078 007c 83F31188 		msr basepri, r3											
 2079 0080 BFF36F8F 		isb														
 2080 0084 BFF34F8F 		dsb														
 2081              	
 2082              	@ 0 "" 2
 2083              	.LVL175:
 212:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 213:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	mrs %0, basepri											\n" \
 214:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	mov %1, %2												\n"	\
 215:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	msr basepri, %1											\n" \
 216:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	isb														\n" \
 217:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	dsb														\n" \
 218:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		:"=r" (ulOriginalBASEPRI), "=r" (ulNewBASEPRI) : "i" ( configMAX_SYSCALL_INTERRUPT_PRIORITY ) : "
 219:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	);
 220:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 221:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* This return will not be reached but is necessary to prevent compiler
 222:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	warnings. */
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return ulOriginalBASEPRI;
 2084              		.loc 2 223 2 view .LVU606
 2085              		.loc 2 223 2 is_stmt 0 view .LVU607
 2086              		.thumb
 2087              		.syntax unified
 2088              	.LBE150:
 2089              	.LBE149:
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
ARM GAS  /tmp/ccSZflmK.s 			page 72


 2090              		.loc 1 643 4 is_stmt 1 discriminator 1 view .LVU608
 2091 0088 2369     		ldr	r3, [r4, #16]
 2092 008a 43B1     		cbz	r3, .L138
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2093              		.loc 1 643 4 discriminator 1 view .LVU609
 2094 008c 2069     		ldr	r0, [r4, #16]
 2095 008e 0096     		str	r6, [sp]
 2096 0090 0023     		movs	r3, #0
 2097 0092 1A46     		mov	r2, r3
 2098 0094 1946     		mov	r1, r3
 2099 0096 FFF7FEFF 		bl	xTaskGenericNotifyFromISR
 2100              	.LVL176:
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2101              		.loc 1 643 4 discriminator 1 view .LVU610
 2102 009a 0023     		movs	r3, #0
 2103 009c 2361     		str	r3, [r4, #16]
 2104              	.L138:
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2105              		.loc 1 643 4 discriminator 3 view .LVU611
 2106              	.LBB151:
 2107              	.LBI151:
 224:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 225:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 226:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 227:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortSetBASEPRI( uint32_t ulNewMaskValue )
 2108              		.loc 2 227 30 view .LVU612
 2109              	.LBB152:
 228:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 229:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 2110              		.loc 2 229 2 view .LVU613
 2111              		.syntax unified
 2112              	@ 229 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2113 009e 87F31188 			msr basepri, r7	
 2114              	@ 0 "" 2
 2115              		.thumb
 2116              		.syntax unified
 2117              	.LBE152:
 2118              	.LBE151:
 2119              	.LBE148:
 643:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2120              		.loc 1 643 73 discriminator 1 view .LVU614
 652:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2121              		.loc 1 652 27 view .LVU615
 655:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2122              		.loc 1 655 60 view .LVU616
 657:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2123              		.loc 1 657 2 view .LVU617
 657:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2124              		.loc 1 657 9 is_stmt 0 view .LVU618
 2125 00a2 C9E7     		b	.L131
 2126              		.cfi_endproc
 2127              	.LFE12:
 2129              		.section	.text.xStreamBufferReceive,"ax",%progbits
 2130              		.align	1
 2131              		.global	xStreamBufferReceive
 2132              		.syntax unified
 2133              		.thumb
ARM GAS  /tmp/ccSZflmK.s 			page 73


 2134              		.thumb_func
 2136              	xStreamBufferReceive:
 2137              	.LVL177:
 2138              	.LFB14:
 717:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2139              		.loc 1 717 1 is_stmt 1 view -0
 2140              		.cfi_startproc
 2141              		@ args = 0, pretend = 0, frame = 0
 2142              		@ frame_needed = 0, uses_anonymous_args = 0
 718:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReceivedLength = 0, xBytesAvailable, xBytesToStoreMessageLength;
 2143              		.loc 1 718 1 view .LVU620
 719:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2144              		.loc 1 719 1 view .LVU621
 721:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2145              		.loc 1 721 2 view .LVU622
 2146 0000 29B3     		cbz	r1, .L162
 717:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2147              		.loc 1 717 1 is_stmt 0 view .LVU623
 2148 0002 2DE9F043 		push	{r4, r5, r6, r7, r8, r9, lr}
 2149              	.LCFI35:
 2150              		.cfi_def_cfa_offset 28
 2151              		.cfi_offset 4, -28
 2152              		.cfi_offset 5, -24
 2153              		.cfi_offset 6, -20
 2154              		.cfi_offset 7, -16
 2155              		.cfi_offset 8, -12
 2156              		.cfi_offset 9, -8
 2157              		.cfi_offset 14, -4
 2158 0006 83B0     		sub	sp, sp, #12
 2159              	.LCFI36:
 2160              		.cfi_def_cfa_offset 40
 2161 0008 0446     		mov	r4, r0
 2162 000a 1746     		mov	r7, r2
 2163 000c 1E46     		mov	r6, r3
 2164 000e 8846     		mov	r8, r1
 721:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2165              		.loc 1 721 26 is_stmt 1 discriminator 2 view .LVU624
 722:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2166              		.loc 1 722 2 view .LVU625
 2167 0010 30B3     		cbz	r0, .L163
 722:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2168              		.loc 1 722 32 discriminator 2 view .LVU626
 729:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2169              		.loc 1 729 2 view .LVU627
 729:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2170              		.loc 1 729 22 is_stmt 0 view .LVU628
 2171 0012 037F     		ldrb	r3, [r0, #28]	@ zero_extendqisi2
 2172              	.LVL178:
 729:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2173              		.loc 1 729 4 view .LVU629
 2174 0014 13F0010F 		tst	r3, #1
 2175 0018 2BD0     		beq	.L158
 731:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2176              		.loc 1 731 30 view .LVU630
 2177 001a 4FF00409 		mov	r9, #4
 2178              	.L150:
 2179              	.LVL179:
ARM GAS  /tmp/ccSZflmK.s 			page 74


 738:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2180              		.loc 1 738 2 is_stmt 1 view .LVU631
 738:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2181              		.loc 1 738 4 is_stmt 0 view .LVU632
 2182 001e 002E     		cmp	r6, #0
 2183 0020 3ED0     		beq	.L151
 742:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2184              		.loc 1 742 3 is_stmt 1 view .LVU633
 2185 0022 FFF7FEFF 		bl	vPortEnterCritical
 2186              	.LVL180:
 744:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2187              		.loc 1 744 4 view .LVU634
 744:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2188              		.loc 1 744 22 is_stmt 0 view .LVU635
 2189 0026 2046     		mov	r0, r4
 2190 0028 FFF7FEFF 		bl	prvBytesInBuffer
 2191              	.LVL181:
 2192 002c 0546     		mov	r5, r0
 2193              	.LVL182:
 751:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 2194              		.loc 1 751 4 is_stmt 1 view .LVU636
 751:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			{
 2195              		.loc 1 751 6 is_stmt 0 view .LVU637
 2196 002e 8145     		cmp	r9, r0
 2197 0030 25D3     		bcc	.L152
 754:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2198              		.loc 1 754 5 is_stmt 1 view .LVU638
 754:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2199              		.loc 1 754 14 is_stmt 0 view .LVU639
 2200 0032 0020     		movs	r0, #0
 2201              	.LVL183:
 754:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2202              		.loc 1 754 14 view .LVU640
 2203 0034 FFF7FEFF 		bl	xTaskNotifyStateClear
 2204              	.LVL184:
 757:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				pxStreamBuffer->xTaskWaitingToReceive = xTaskGetCurrentTaskHandle();
 2205              		.loc 1 757 5 is_stmt 1 view .LVU641
 2206 0038 2369     		ldr	r3, [r4, #16]
 2207 003a EBB1     		cbz	r3, .L153
 757:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				pxStreamBuffer->xTaskWaitingToReceive = xTaskGetCurrentTaskHandle();
 2208              		.loc 1 757 5 discriminator 1 view .LVU642
 2209              	.LBB153:
 2210              	.LBI153:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2211              		.loc 2 191 30 view .LVU643
 2212              	.LBB154:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2213              		.loc 2 193 1 view .LVU644
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2214              		.loc 2 195 2 view .LVU645
 2215              		.syntax unified
 2216              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2217 003c 4FF05003 			mov r3, #80												
 2218 0040 83F31188 		msr basepri, r3											
 2219 0044 BFF36F8F 		isb														
 2220 0048 BFF34F8F 		dsb														
 2221              	
ARM GAS  /tmp/ccSZflmK.s 			page 75


 2222              	@ 0 "" 2
 2223              		.thumb
 2224              		.syntax unified
 2225              	.L154:
 2226              	.LBE154:
 2227              	.LBE153:
 757:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				pxStreamBuffer->xTaskWaitingToReceive = xTaskGetCurrentTaskHandle();
 2228              		.loc 1 757 5 discriminator 3 view .LVU646
 757:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				pxStreamBuffer->xTaskWaitingToReceive = xTaskGetCurrentTaskHandle();
 2229              		.loc 1 757 5 discriminator 3 view .LVU647
 2230 004c FEE7     		b	.L154
 2231              	.LVL185:
 2232              	.L162:
 2233              	.LCFI37:
 2234              		.cfi_def_cfa_offset 0
 2235              		.cfi_restore 4
 2236              		.cfi_restore 5
 2237              		.cfi_restore 6
 2238              		.cfi_restore 7
 2239              		.cfi_restore 8
 2240              		.cfi_restore 9
 2241              		.cfi_restore 14
 721:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2242              		.loc 1 721 2 discriminator 1 view .LVU648
 2243              	.LBB155:
 2244              	.LBI155:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2245              		.loc 2 191 30 view .LVU649
 2246              	.LBB156:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2247              		.loc 2 193 1 view .LVU650
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2248              		.loc 2 195 2 view .LVU651
 2249              		.syntax unified
 2250              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2251 004e 4FF05003 			mov r3, #80												
 2252 0052 83F31188 		msr basepri, r3											
 2253 0056 BFF36F8F 		isb														
 2254 005a BFF34F8F 		dsb														
 2255              	
 2256              	@ 0 "" 2
 2257              	.LVL186:
 2258              		.thumb
 2259              		.syntax unified
 2260              	.L147:
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2261              		.loc 2 195 2 is_stmt 0 view .LVU652
 2262              	.LBE156:
 2263              	.LBE155:
 721:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2264              		.loc 1 721 2 is_stmt 1 discriminator 3 view .LVU653
 721:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2265              		.loc 1 721 2 discriminator 3 view .LVU654
 2266 005e FEE7     		b	.L147
 2267              	.LVL187:
 2268              	.L163:
 2269              	.LCFI38:
ARM GAS  /tmp/ccSZflmK.s 			page 76


 2270              		.cfi_def_cfa_offset 40
 2271              		.cfi_offset 4, -28
 2272              		.cfi_offset 5, -24
 2273              		.cfi_offset 6, -20
 2274              		.cfi_offset 7, -16
 2275              		.cfi_offset 8, -12
 2276              		.cfi_offset 9, -8
 2277              		.cfi_offset 14, -4
 722:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2278              		.loc 1 722 2 discriminator 1 view .LVU655
 2279              	.LBB157:
 2280              	.LBI157:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2281              		.loc 2 191 30 view .LVU656
 2282              	.LBB158:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2283              		.loc 2 193 1 view .LVU657
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2284              		.loc 2 195 2 view .LVU658
 2285              		.syntax unified
 2286              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2287 0060 4FF05003 			mov r3, #80												
 2288 0064 83F31188 		msr basepri, r3											
 2289 0068 BFF36F8F 		isb														
 2290 006c BFF34F8F 		dsb														
 2291              	
 2292              	@ 0 "" 2
 2293              	.LVL188:
 2294              		.thumb
 2295              		.syntax unified
 2296              	.L149:
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2297              		.loc 2 195 2 is_stmt 0 view .LVU659
 2298              	.LBE158:
 2299              	.LBE157:
 722:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2300              		.loc 1 722 2 is_stmt 1 discriminator 3 view .LVU660
 722:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2301              		.loc 1 722 2 discriminator 3 view .LVU661
 2302 0070 FEE7     		b	.L149
 2303              	.L158:
 735:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2304              		.loc 1 735 30 is_stmt 0 view .LVU662
 2305 0072 4FF00009 		mov	r9, #0
 2306 0076 D2E7     		b	.L150
 2307              	.LVL189:
 2308              	.L153:
 757:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 				pxStreamBuffer->xTaskWaitingToReceive = xTaskGetCurrentTaskHandle();
 2309              		.loc 1 757 66 is_stmt 1 discriminator 2 view .LVU663
 758:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 2310              		.loc 1 758 5 view .LVU664
 758:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 2311              		.loc 1 758 45 is_stmt 0 view .LVU665
 2312 0078 FFF7FEFF 		bl	xTaskGetCurrentTaskHandle
 2313              	.LVL190:
 758:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 2314              		.loc 1 758 43 discriminator 1 view .LVU666
ARM GAS  /tmp/ccSZflmK.s 			page 77


 2315 007c 2061     		str	r0, [r4, #16]
 2316              	.L152:
 762:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			}
 2317              		.loc 1 762 29 is_stmt 1 view .LVU667
 765:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2318              		.loc 1 765 3 view .LVU668
 2319 007e FFF7FEFF 		bl	vPortExitCritical
 2320              	.LVL191:
 767:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2321              		.loc 1 767 3 view .LVU669
 767:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2322              		.loc 1 767 5 is_stmt 0 view .LVU670
 2323 0082 A945     		cmp	r9, r5
 2324 0084 10D3     		bcc	.L155
 770:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) xTaskNotifyWait( ( uint32_t ) 0, ( uint32_t ) 0, NULL, xTicksToWait );
 2325              		.loc 1 770 59 is_stmt 1 view .LVU671
 771:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTaskWaitingToReceive = NULL;
 2326              		.loc 1 771 4 view .LVU672
 771:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTaskWaitingToReceive = NULL;
 2327              		.loc 1 771 13 is_stmt 0 view .LVU673
 2328 0086 3346     		mov	r3, r6
 2329 0088 0022     		movs	r2, #0
 2330 008a 1146     		mov	r1, r2
 2331 008c 1046     		mov	r0, r2
 2332 008e FFF7FEFF 		bl	xTaskNotifyWait
 2333              	.LVL192:
 772:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2334              		.loc 1 772 4 is_stmt 1 view .LVU674
 772:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2335              		.loc 1 772 42 is_stmt 0 view .LVU675
 2336 0092 0023     		movs	r3, #0
 2337 0094 2361     		str	r3, [r4, #16]
 775:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2338              		.loc 1 775 4 is_stmt 1 view .LVU676
 775:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2339              		.loc 1 775 22 is_stmt 0 view .LVU677
 2340 0096 2046     		mov	r0, r4
 2341 0098 FFF7FEFF 		bl	prvBytesInBuffer
 2342              	.LVL193:
 2343 009c 0546     		mov	r5, r0
 2344              	.LVL194:
 775:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2345              		.loc 1 775 22 view .LVU678
 2346 009e 03E0     		b	.L155
 2347              	.LVL195:
 2348              	.L151:
 784:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2349              		.loc 1 784 3 is_stmt 1 view .LVU679
 784:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2350              		.loc 1 784 21 is_stmt 0 view .LVU680
 2351 00a0 2046     		mov	r0, r4
 2352              	.LVL196:
 784:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2353              		.loc 1 784 21 view .LVU681
 2354 00a2 FFF7FEFF 		bl	prvBytesInBuffer
 2355              	.LVL197:
 784:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
ARM GAS  /tmp/ccSZflmK.s 			page 78


 2356              		.loc 1 784 21 view .LVU682
 2357 00a6 0546     		mov	r5, r0
 2358              	.LVL198:
 2359              	.L155:
 792:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2360              		.loc 1 792 2 is_stmt 1 view .LVU683
 792:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2361              		.loc 1 792 4 is_stmt 0 view .LVU684
 2362 00a8 4D45     		cmp	r5, r9
 2363 00aa 04D8     		bhi	.L164
 719:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2364              		.loc 1 719 8 view .LVU685
 2365 00ac 0025     		movs	r5, #0
 2366              	.LVL199:
 809:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		mtCOVERAGE_TEST_MARKER();
 2367              		.loc 1 809 53 is_stmt 1 view .LVU686
 810:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2368              		.loc 1 810 27 view .LVU687
 813:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2369              		.loc 1 813 2 view .LVU688
 2370              	.L145:
 814:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 2371              		.loc 1 814 1 is_stmt 0 view .LVU689
 2372 00ae 2846     		mov	r0, r5
 2373 00b0 03B0     		add	sp, sp, #12
 2374              	.LCFI39:
 2375              		.cfi_remember_state
 2376              		.cfi_def_cfa_offset 28
 2377              		@ sp needed
 2378 00b2 BDE8F083 		pop	{r4, r5, r6, r7, r8, r9, pc}
 2379              	.LVL200:
 2380              	.L164:
 2381              	.LCFI40:
 2382              		.cfi_restore_state
 794:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2383              		.loc 1 794 3 is_stmt 1 view .LVU690
 794:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2384              		.loc 1 794 21 is_stmt 0 view .LVU691
 2385 00b6 CDF80090 		str	r9, [sp]
 2386 00ba 2B46     		mov	r3, r5
 2387 00bc 3A46     		mov	r2, r7
 2388 00be 4146     		mov	r1, r8
 2389 00c0 2046     		mov	r0, r4
 2390 00c2 FFF7FEFF 		bl	prvReadMessageFromBuffer
 2391              	.LVL201:
 797:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2392              		.loc 1 797 3 is_stmt 1 view .LVU692
 797:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2393              		.loc 1 797 5 is_stmt 0 view .LVU693
 2394 00c6 0546     		mov	r5, r0
 2395              	.LVL202:
 797:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2396              		.loc 1 797 5 view .LVU694
 2397 00c8 0028     		cmp	r0, #0
 2398 00ca F0D0     		beq	.L145
 799:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			sbRECEIVE_COMPLETED( pxStreamBuffer );
 2399              		.loc 1 799 64 is_stmt 1 view .LVU695
ARM GAS  /tmp/ccSZflmK.s 			page 79


 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2400              		.loc 1 800 4 view .LVU696
 2401 00cc FFF7FEFF 		bl	vTaskSuspendAll
 2402              	.LVL203:
 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2403              		.loc 1 800 4 discriminator 1 view .LVU697
 2404 00d0 6369     		ldr	r3, [r4, #20]
 2405 00d2 3BB1     		cbz	r3, .L157
 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2406              		.loc 1 800 4 discriminator 1 view .LVU698
 2407 00d4 6069     		ldr	r0, [r4, #20]
 2408 00d6 0023     		movs	r3, #0
 2409 00d8 1A46     		mov	r2, r3
 2410 00da 1946     		mov	r1, r3
 2411 00dc FFF7FEFF 		bl	xTaskGenericNotify
 2412              	.LVL204:
 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2413              		.loc 1 800 4 discriminator 1 view .LVU699
 2414 00e0 0023     		movs	r3, #0
 2415 00e2 6361     		str	r3, [r4, #20]
 2416              	.L157:
 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2417              		.loc 1 800 4 discriminator 3 view .LVU700
 2418 00e4 FFF7FEFF 		bl	xTaskResumeAll
 2419              	.LVL205:
 800:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2420              		.loc 1 800 41 discriminator 1 view .LVU701
 2421 00e8 E1E7     		b	.L145
 2422              		.cfi_endproc
 2423              	.LFE14:
 2425              		.section	.text.xStreamBufferNextMessageLengthBytes,"ax",%progbits
 2426              		.align	1
 2427              		.global	xStreamBufferNextMessageLengthBytes
 2428              		.syntax unified
 2429              		.thumb
 2430              		.thumb_func
 2432              	xStreamBufferNextMessageLengthBytes:
 2433              	.LVL206:
 2434              	.LFB15:
 818:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2435              		.loc 1 818 1 view -0
 2436              		.cfi_startproc
 2437              		@ args = 0, pretend = 0, frame = 8
 2438              		@ frame_needed = 0, uses_anonymous_args = 0
 819:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReturn, xBytesAvailable, xOriginalTail;
 2439              		.loc 1 819 1 view .LVU703
 820:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** configMESSAGE_BUFFER_LENGTH_TYPE xTempReturn;
 2440              		.loc 1 820 1 view .LVU704
 821:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2441              		.loc 1 821 1 view .LVU705
 823:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2442              		.loc 1 823 2 view .LVU706
 2443 0000 48B1     		cbz	r0, .L174
 818:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2444              		.loc 1 818 1 is_stmt 0 view .LVU707
 2445 0002 30B5     		push	{r4, r5, lr}
 2446              	.LCFI41:
ARM GAS  /tmp/ccSZflmK.s 			page 80


 2447              		.cfi_def_cfa_offset 12
 2448              		.cfi_offset 4, -12
 2449              		.cfi_offset 5, -8
 2450              		.cfi_offset 14, -4
 2451 0004 83B0     		sub	sp, sp, #12
 2452              	.LCFI42:
 2453              		.cfi_def_cfa_offset 24
 2454 0006 0446     		mov	r4, r0
 823:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2455              		.loc 1 823 32 is_stmt 1 discriminator 2 view .LVU708
 826:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2456              		.loc 1 826 2 view .LVU709
 826:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2457              		.loc 1 826 22 is_stmt 0 view .LVU710
 2458 0008 037F     		ldrb	r3, [r0, #28]	@ zero_extendqisi2
 826:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2459              		.loc 1 826 4 view .LVU711
 2460 000a 13F0010F 		tst	r3, #1
 2461 000e 0BD1     		bne	.L175
 854:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2462              		.loc 1 854 11 view .LVU712
 2463 0010 0020     		movs	r0, #0
 2464              	.LVL207:
 857:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2465              		.loc 1 857 2 is_stmt 1 view .LVU713
 2466              	.L165:
 858:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 2467              		.loc 1 858 1 is_stmt 0 view .LVU714
 2468 0012 03B0     		add	sp, sp, #12
 2469              	.LCFI43:
 2470              		.cfi_def_cfa_offset 12
 2471              		@ sp needed
 2472 0014 30BD     		pop	{r4, r5, pc}
 2473              	.LVL208:
 2474              	.L174:
 2475              	.LCFI44:
 2476              		.cfi_def_cfa_offset 0
 2477              		.cfi_restore 4
 2478              		.cfi_restore 5
 2479              		.cfi_restore 14
 823:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2480              		.loc 1 823 2 is_stmt 1 discriminator 1 view .LVU715
 2481              	.LBB159:
 2482              	.LBI159:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2483              		.loc 2 191 30 view .LVU716
 2484              	.LBB160:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2485              		.loc 2 193 1 view .LVU717
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2486              		.loc 2 195 2 view .LVU718
 2487              		.syntax unified
 2488              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2489 0016 4FF05003 			mov r3, #80												
 2490 001a 83F31188 		msr basepri, r3											
 2491 001e BFF36F8F 		isb														
 2492 0022 BFF34F8F 		dsb														
ARM GAS  /tmp/ccSZflmK.s 			page 81


 2493              	
 2494              	@ 0 "" 2
 2495              		.thumb
 2496              		.syntax unified
 2497              	.L167:
 2498              	.LBE160:
 2499              	.LBE159:
 823:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2500              		.loc 1 823 2 discriminator 3 view .LVU719
 823:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2501              		.loc 1 823 2 discriminator 3 view .LVU720
 2502 0026 FEE7     		b	.L167
 2503              	.L175:
 2504              	.LCFI45:
 2505              		.cfi_def_cfa_offset 24
 2506              		.cfi_offset 4, -12
 2507              		.cfi_offset 5, -8
 2508              		.cfi_offset 14, -4
 828:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xBytesAvailable > sbBYTES_TO_STORE_MESSAGE_LENGTH )
 2509              		.loc 1 828 3 view .LVU721
 828:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		if( xBytesAvailable > sbBYTES_TO_STORE_MESSAGE_LENGTH )
 2510              		.loc 1 828 21 is_stmt 0 view .LVU722
 2511 0028 FFF7FEFF 		bl	prvBytesInBuffer
 2512              	.LVL209:
 829:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2513              		.loc 1 829 3 is_stmt 1 view .LVU723
 829:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2514              		.loc 1 829 5 is_stmt 0 view .LVU724
 2515 002c 0428     		cmp	r0, #4
 2516 002e 0AD8     		bhi	.L176
 848:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = 0;
 2517              		.loc 1 848 4 is_stmt 1 view .LVU725
 2518 0030 0028     		cmp	r0, #0
 2519 0032 EED0     		beq	.L165
 848:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = 0;
 2520              		.loc 1 848 4 discriminator 1 view .LVU726
 2521              	.LBB161:
 2522              	.LBI161:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2523              		.loc 2 191 30 view .LVU727
 2524              	.LBB162:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2525              		.loc 2 193 1 view .LVU728
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2526              		.loc 2 195 2 view .LVU729
 2527              		.syntax unified
 2528              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2529 0034 4FF05003 			mov r3, #80												
 2530 0038 83F31188 		msr basepri, r3											
 2531 003c BFF36F8F 		isb														
 2532 0040 BFF34F8F 		dsb														
 2533              	
 2534              	@ 0 "" 2
 2535              		.thumb
 2536              		.syntax unified
 2537              	.L170:
 2538              	.LBE162:
ARM GAS  /tmp/ccSZflmK.s 			page 82


 2539              	.LBE161:
 848:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = 0;
 2540              		.loc 1 848 4 discriminator 3 view .LVU730
 848:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = 0;
 2541              		.loc 1 848 4 discriminator 3 view .LVU731
 2542 0044 FEE7     		b	.L170
 2543              	.L176:
 837:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) &xTempReturn, sbBYTES_TO_STORE_ME
 2544              		.loc 1 837 4 view .LVU732
 837:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			( void ) prvReadBytesFromBuffer( pxStreamBuffer, ( uint8_t * ) &xTempReturn, sbBYTES_TO_STORE_ME
 2545              		.loc 1 837 18 is_stmt 0 view .LVU733
 2546 0046 2568     		ldr	r5, [r4]
 2547              	.LVL210:
 838:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = ( size_t ) xTempReturn;
 2548              		.loc 1 838 4 is_stmt 1 view .LVU734
 838:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = ( size_t ) xTempReturn;
 2549              		.loc 1 838 13 is_stmt 0 view .LVU735
 2550 0048 0346     		mov	r3, r0
 2551 004a 0422     		movs	r2, #4
 2552 004c 0DEB0201 		add	r1, sp, r2
 2553 0050 2046     		mov	r0, r4
 2554              	.LVL211:
 838:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = ( size_t ) xTempReturn;
 2555              		.loc 1 838 13 view .LVU736
 2556 0052 FFF7FEFF 		bl	prvReadBytesFromBuffer
 2557              	.LVL212:
 839:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTail = xOriginalTail;
 2558              		.loc 1 839 4 is_stmt 1 view .LVU737
 839:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			pxStreamBuffer->xTail = xOriginalTail;
 2559              		.loc 1 839 12 is_stmt 0 view .LVU738
 2560 0056 0198     		ldr	r0, [sp, #4]
 2561              	.LVL213:
 840:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2562              		.loc 1 840 4 is_stmt 1 view .LVU739
 840:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2563              		.loc 1 840 26 is_stmt 0 view .LVU740
 2564 0058 2560     		str	r5, [r4]
 2565 005a DAE7     		b	.L165
 2566              		.cfi_endproc
 2567              	.LFE15:
 2569              		.section	.text.xStreamBufferReceiveFromISR,"ax",%progbits
 2570              		.align	1
 2571              		.global	xStreamBufferReceiveFromISR
 2572              		.syntax unified
 2573              		.thumb
 2574              		.thumb_func
 2576              	xStreamBufferReceiveFromISR:
 2577              	.LVL214:
 2578              	.LFB16:
 865:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2579              		.loc 1 865 1 is_stmt 1 view -0
 2580              		.cfi_startproc
 2581              		@ args = 0, pretend = 0, frame = 0
 2582              		@ frame_needed = 0, uses_anonymous_args = 0
 866:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xReceivedLength = 0, xBytesAvailable, xBytesToStoreMessageLength;
 2583              		.loc 1 866 1 view .LVU742
 867:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 83


 2584              		.loc 1 867 1 view .LVU743
 869:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2585              		.loc 1 869 2 view .LVU744
 2586 0000 B9B1     		cbz	r1, .L189
 865:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2587              		.loc 1 865 1 is_stmt 0 view .LVU745
 2588 0002 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 2589              	.LCFI46:
 2590              		.cfi_def_cfa_offset 24
 2591              		.cfi_offset 4, -24
 2592              		.cfi_offset 5, -20
 2593              		.cfi_offset 6, -16
 2594              		.cfi_offset 7, -12
 2595              		.cfi_offset 8, -8
 2596              		.cfi_offset 14, -4
 2597 0006 82B0     		sub	sp, sp, #8
 2598              	.LCFI47:
 2599              		.cfi_def_cfa_offset 32
 2600 0008 0446     		mov	r4, r0
 2601 000a 1646     		mov	r6, r2
 2602 000c 1D46     		mov	r5, r3
 2603 000e 0F46     		mov	r7, r1
 869:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2604              		.loc 1 869 26 is_stmt 1 discriminator 2 view .LVU746
 870:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2605              		.loc 1 870 2 view .LVU747
 2606 0010 C0B1     		cbz	r0, .L190
 870:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2607              		.loc 1 870 32 discriminator 2 view .LVU748
 877:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2608              		.loc 1 877 2 view .LVU749
 877:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2609              		.loc 1 877 22 is_stmt 0 view .LVU750
 2610 0012 037F     		ldrb	r3, [r0, #28]	@ zero_extendqisi2
 2611              	.LVL215:
 877:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2612              		.loc 1 877 4 view .LVU751
 2613 0014 13F0010F 		tst	r3, #1
 2614 0018 1DD0     		beq	.L185
 879:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2615              		.loc 1 879 30 view .LVU752
 2616 001a 4FF00408 		mov	r8, #4
 2617              	.L182:
 2618              	.LVL216:
 886:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2619              		.loc 1 886 2 is_stmt 1 view .LVU753
 886:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2620              		.loc 1 886 20 is_stmt 0 view .LVU754
 2621 001e 2046     		mov	r0, r4
 2622              	.LVL217:
 886:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2623              		.loc 1 886 20 view .LVU755
 2624 0020 FFF7FEFF 		bl	prvBytesInBuffer
 2625              	.LVL218:
 893:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2626              		.loc 1 893 2 is_stmt 1 view .LVU756
 893:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
ARM GAS  /tmp/ccSZflmK.s 			page 84


 2627              		.loc 1 893 4 is_stmt 0 view .LVU757
 2628 0024 8045     		cmp	r8, r0
 2629 0026 19D3     		bcc	.L191
 867:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2630              		.loc 1 867 8 view .LVU758
 2631 0028 0026     		movs	r6, #0
 2632              	.LVL219:
 909:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2633              		.loc 1 909 27 is_stmt 1 view .LVU759
 912:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2634              		.loc 1 912 71 view .LVU760
 914:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2635              		.loc 1 914 2 view .LVU761
 2636              	.L177:
 915:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 2637              		.loc 1 915 1 is_stmt 0 view .LVU762
 2638 002a 3046     		mov	r0, r6
 2639 002c 02B0     		add	sp, sp, #8
 2640              	.LCFI48:
 2641              		.cfi_def_cfa_offset 24
 2642              		@ sp needed
 2643 002e BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 2644              	.LVL220:
 2645              	.L189:
 2646              	.LCFI49:
 2647              		.cfi_def_cfa_offset 0
 2648              		.cfi_restore 4
 2649              		.cfi_restore 5
 2650              		.cfi_restore 6
 2651              		.cfi_restore 7
 2652              		.cfi_restore 8
 2653              		.cfi_restore 14
 869:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2654              		.loc 1 869 2 is_stmt 1 discriminator 1 view .LVU763
 2655              	.LBB163:
 2656              	.LBI163:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2657              		.loc 2 191 30 view .LVU764
 2658              	.LBB164:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2659              		.loc 2 193 1 view .LVU765
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2660              		.loc 2 195 2 view .LVU766
 2661              		.syntax unified
 2662              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2663 0032 4FF05003 			mov r3, #80												
 2664 0036 83F31188 		msr basepri, r3											
 2665 003a BFF36F8F 		isb														
 2666 003e BFF34F8F 		dsb														
 2667              	
 2668              	@ 0 "" 2
 2669              	.LVL221:
 2670              		.thumb
 2671              		.syntax unified
 2672              	.L179:
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2673              		.loc 2 195 2 is_stmt 0 view .LVU767
ARM GAS  /tmp/ccSZflmK.s 			page 85


 2674              	.LBE164:
 2675              	.LBE163:
 869:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2676              		.loc 1 869 2 is_stmt 1 discriminator 3 view .LVU768
 869:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	configASSERT( pxStreamBuffer );
 2677              		.loc 1 869 2 discriminator 3 view .LVU769
 2678 0042 FEE7     		b	.L179
 2679              	.LVL222:
 2680              	.L190:
 2681              	.LCFI50:
 2682              		.cfi_def_cfa_offset 32
 2683              		.cfi_offset 4, -24
 2684              		.cfi_offset 5, -20
 2685              		.cfi_offset 6, -16
 2686              		.cfi_offset 7, -12
 2687              		.cfi_offset 8, -8
 2688              		.cfi_offset 14, -4
 870:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2689              		.loc 1 870 2 discriminator 1 view .LVU770
 2690              	.LBB165:
 2691              	.LBI165:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2692              		.loc 2 191 30 view .LVU771
 2693              	.LBB166:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2694              		.loc 2 193 1 view .LVU772
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2695              		.loc 2 195 2 view .LVU773
 2696              		.syntax unified
 2697              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2698 0044 4FF05003 			mov r3, #80												
 2699 0048 83F31188 		msr basepri, r3											
 2700 004c BFF36F8F 		isb														
 2701 0050 BFF34F8F 		dsb														
 2702              	
 2703              	@ 0 "" 2
 2704              	.LVL223:
 2705              		.thumb
 2706              		.syntax unified
 2707              	.L181:
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2708              		.loc 2 195 2 is_stmt 0 view .LVU774
 2709              	.LBE166:
 2710              	.LBE165:
 870:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2711              		.loc 1 870 2 is_stmt 1 discriminator 3 view .LVU775
 870:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2712              		.loc 1 870 2 discriminator 3 view .LVU776
 2713 0054 FEE7     		b	.L181
 2714              	.L185:
 883:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2715              		.loc 1 883 30 is_stmt 0 view .LVU777
 2716 0056 4FF00008 		mov	r8, #0
 2717 005a E0E7     		b	.L182
 2718              	.LVL224:
 2719              	.L191:
 895:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
ARM GAS  /tmp/ccSZflmK.s 			page 86


 2720              		.loc 1 895 3 is_stmt 1 view .LVU778
 895:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2721              		.loc 1 895 21 is_stmt 0 view .LVU779
 2722 005c CDF80080 		str	r8, [sp]
 2723 0060 0346     		mov	r3, r0
 2724 0062 3246     		mov	r2, r6
 2725 0064 3946     		mov	r1, r7
 2726 0066 2046     		mov	r0, r4
 2727              	.LVL225:
 895:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2728              		.loc 1 895 21 view .LVU780
 2729 0068 FFF7FEFF 		bl	prvReadMessageFromBuffer
 2730              	.LVL226:
 898:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2731              		.loc 1 898 3 is_stmt 1 view .LVU781
 898:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2732              		.loc 1 898 5 is_stmt 0 view .LVU782
 2733 006c 0646     		mov	r6, r0
 2734              	.LVL227:
 898:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 2735              		.loc 1 898 5 view .LVU783
 2736 006e 0028     		cmp	r0, #0
 2737 0070 DBD0     		beq	.L177
 2738              	.LBB167:
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2739              		.loc 1 900 4 is_stmt 1 view .LVU784
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2740              		.loc 1 900 4 view .LVU785
 2741              	.LBB168:
 2742              	.LBI168:
 207:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2743              		.loc 2 207 34 view .LVU786
 2744              	.LBB169:
 209:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2745              		.loc 2 209 1 view .LVU787
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 2746              		.loc 2 211 2 view .LVU788
 2747              		.syntax unified
 2748              	@ 211 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2749 0072 EFF31187 			mrs r7, basepri											
 2750 0076 4FF05003 		mov r3, #80												
 2751 007a 83F31188 		msr basepri, r3											
 2752 007e BFF36F8F 		isb														
 2753 0082 BFF34F8F 		dsb														
 2754              	
 2755              	@ 0 "" 2
 2756              	.LVL228:
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 2757              		.loc 2 223 2 view .LVU789
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 2758              		.loc 2 223 2 is_stmt 0 view .LVU790
 2759              		.thumb
 2760              		.syntax unified
 2761              	.LBE169:
 2762              	.LBE168:
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2763              		.loc 1 900 4 is_stmt 1 discriminator 1 view .LVU791
ARM GAS  /tmp/ccSZflmK.s 			page 87


 2764 0086 6369     		ldr	r3, [r4, #20]
 2765 0088 43B1     		cbz	r3, .L184
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2766              		.loc 1 900 4 discriminator 1 view .LVU792
 2767 008a 6069     		ldr	r0, [r4, #20]
 2768              	.LVL229:
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2769              		.loc 1 900 4 is_stmt 0 discriminator 1 view .LVU793
 2770 008c 0095     		str	r5, [sp]
 2771 008e 0023     		movs	r3, #0
 2772 0090 1A46     		mov	r2, r3
 2773 0092 1946     		mov	r1, r3
 2774 0094 FFF7FEFF 		bl	xTaskGenericNotifyFromISR
 2775              	.LVL230:
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2776              		.loc 1 900 4 is_stmt 1 discriminator 1 view .LVU794
 2777 0098 0023     		movs	r3, #0
 2778 009a 6361     		str	r3, [r4, #20]
 2779              	.L184:
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2780              		.loc 1 900 4 discriminator 3 view .LVU795
 2781              	.LBB170:
 2782              	.LBI170:
 227:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2783              		.loc 2 227 30 view .LVU796
 2784              	.LBB171:
 2785              		.loc 2 229 2 view .LVU797
 2786              		.syntax unified
 2787              	@ 229 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2788 009c 87F31188 			msr basepri, r7	
 2789              	@ 0 "" 2
 2790              		.thumb
 2791              		.syntax unified
 2792              	.LBE171:
 2793              	.LBE170:
 2794              	.LBE167:
 900:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 2795              		.loc 1 900 77 discriminator 1 view .LVU798
 2796              	.LBB174:
 2797              	.LBB173:
 2798              	.LBB172:
 230:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 231:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		"	msr basepri, %0	" :: "r" ( ulNewMaskValue ) : "memory"
 232:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	);
 233:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 2799              		.loc 2 233 1 is_stmt 0 view .LVU799
 2800 00a0 C3E7     		b	.L177
 2801              	.LBE172:
 2802              	.LBE173:
 2803              	.LBE174:
 2804              		.cfi_endproc
 2805              	.LFE16:
 2807              		.section	.text.xStreamBufferIsEmpty,"ax",%progbits
 2808              		.align	1
 2809              		.global	xStreamBufferIsEmpty
 2810              		.syntax unified
 2811              		.thumb
ARM GAS  /tmp/ccSZflmK.s 			page 88


 2812              		.thumb_func
 2814              	xStreamBufferIsEmpty:
 2815              	.LVL231:
 2816              	.LFB18:
 971:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2817              		.loc 1 971 1 is_stmt 1 view -0
 2818              		.cfi_startproc
 2819              		@ args = 0, pretend = 0, frame = 0
 2820              		@ frame_needed = 0, uses_anonymous_args = 0
 2821              		@ link register save eliminated.
 972:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 2822              		.loc 1 972 1 view .LVU801
 973:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xTail;
 2823              		.loc 1 973 1 view .LVU802
 974:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2824              		.loc 1 974 1 view .LVU803
 976:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2825              		.loc 1 976 2 view .LVU804
 2826 0000 28B1     		cbz	r0, .L197
 976:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2827              		.loc 1 976 32 discriminator 2 view .LVU805
 979:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( pxStreamBuffer->xHead == xTail )
 2828              		.loc 1 979 2 view .LVU806
 979:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	if( pxStreamBuffer->xHead == xTail )
 2829              		.loc 1 979 8 is_stmt 0 view .LVU807
 2830 0002 0268     		ldr	r2, [r0]
 2831              	.LVL232:
 980:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2832              		.loc 1 980 2 is_stmt 1 view .LVU808
 980:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2833              		.loc 1 980 20 is_stmt 0 view .LVU809
 2834 0004 4368     		ldr	r3, [r0, #4]
 980:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2835              		.loc 1 980 4 view .LVU810
 2836 0006 9342     		cmp	r3, r2
 2837 0008 0AD0     		beq	.L198
 986:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2838              		.loc 1 986 11 view .LVU811
 2839 000a 0020     		movs	r0, #0
 2840              	.LVL233:
 989:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2841              		.loc 1 989 2 is_stmt 1 view .LVU812
 990:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 2842              		.loc 1 990 1 is_stmt 0 view .LVU813
 2843 000c 7047     		bx	lr
 2844              	.LVL234:
 2845              	.L197:
 976:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2846              		.loc 1 976 2 is_stmt 1 discriminator 1 view .LVU814
 2847              	.LBB175:
 2848              	.LBI175:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2849              		.loc 2 191 30 view .LVU815
 2850              	.LBB176:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2851              		.loc 2 193 1 view .LVU816
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
ARM GAS  /tmp/ccSZflmK.s 			page 89


 2852              		.loc 2 195 2 view .LVU817
 2853              		.syntax unified
 2854              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2855 000e 4FF05003 			mov r3, #80												
 2856 0012 83F31188 		msr basepri, r3											
 2857 0016 BFF36F8F 		isb														
 2858 001a BFF34F8F 		dsb														
 2859              	
 2860              	@ 0 "" 2
 2861              		.thumb
 2862              		.syntax unified
 2863              	.L194:
 2864              	.LBE176:
 2865              	.LBE175:
 976:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2866              		.loc 1 976 2 discriminator 3 view .LVU818
 976:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2867              		.loc 1 976 2 discriminator 3 view .LVU819
 2868 001e FEE7     		b	.L194
 2869              	.LVL235:
 2870              	.L198:
 982:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2871              		.loc 1 982 11 is_stmt 0 view .LVU820
 2872 0020 0120     		movs	r0, #1
 2873              	.LVL236:
 982:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2874              		.loc 1 982 11 view .LVU821
 2875 0022 7047     		bx	lr
 2876              		.cfi_endproc
 2877              	.LFE18:
 2879              		.section	.text.xStreamBufferIsFull,"ax",%progbits
 2880              		.align	1
 2881              		.global	xStreamBufferIsFull
 2882              		.syntax unified
 2883              		.thumb
 2884              		.thumb_func
 2886              	xStreamBufferIsFull:
 2887              	.LVL237:
 2888              	.LFB19:
 994:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 2889              		.loc 1 994 1 is_stmt 1 view -0
 2890              		.cfi_startproc
 2891              		@ args = 0, pretend = 0, frame = 0
 2892              		@ frame_needed = 0, uses_anonymous_args = 0
 995:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** size_t xBytesToStoreMessageLength;
 2893              		.loc 1 995 1 view .LVU823
 996:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** const StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2894              		.loc 1 996 1 view .LVU824
 997:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2895              		.loc 1 997 1 view .LVU825
 999:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2896              		.loc 1 999 2 view .LVU826
 2897 0000 68B1     		cbz	r0, .L208
 994:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 2898              		.loc 1 994 1 is_stmt 0 view .LVU827
 2899 0002 10B5     		push	{r4, lr}
 2900              	.LCFI51:
ARM GAS  /tmp/ccSZflmK.s 			page 90


 2901              		.cfi_def_cfa_offset 8
 2902              		.cfi_offset 4, -8
 2903              		.cfi_offset 14, -4
 2904 0004 0346     		mov	r3, r0
 999:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2905              		.loc 1 999 32 is_stmt 1 discriminator 2 view .LVU828
1005:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2906              		.loc 1 1005 2 view .LVU829
1005:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2907              		.loc 1 1005 22 is_stmt 0 view .LVU830
 2908 0006 027F     		ldrb	r2, [r0, #28]	@ zero_extendqisi2
1005:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2909              		.loc 1 1005 4 view .LVU831
 2910 0008 12F0010F 		tst	r2, #1
 2911 000c 10D0     		beq	.L204
1007:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2912              		.loc 1 1007 30 view .LVU832
 2913 000e 0424     		movs	r4, #4
 2914              	.L202:
 2915              	.LVL238:
1015:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2916              		.loc 1 1015 2 is_stmt 1 view .LVU833
1015:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2917              		.loc 1 1015 6 is_stmt 0 view .LVU834
 2918 0010 1846     		mov	r0, r3
 2919              	.LVL239:
1015:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2920              		.loc 1 1015 6 view .LVU835
 2921 0012 FFF7FEFF 		bl	xStreamBufferSpacesAvailable
 2922              	.LVL240:
1015:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 2923              		.loc 1 1015 4 discriminator 1 view .LVU836
 2924 0016 A042     		cmp	r0, r4
 2925 0018 0CD8     		bhi	.L205
1017:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2926              		.loc 1 1017 11 view .LVU837
 2927 001a 0120     		movs	r0, #1
 2928              	.L199:
1025:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 2929              		.loc 1 1025 1 view .LVU838
 2930 001c 10BD     		pop	{r4, pc}
 2931              	.LVL241:
 2932              	.L208:
 2933              	.LCFI52:
 2934              		.cfi_def_cfa_offset 0
 2935              		.cfi_restore 4
 2936              		.cfi_restore 14
 999:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2937              		.loc 1 999 2 is_stmt 1 discriminator 1 view .LVU839
 2938              	.LBB177:
 2939              	.LBI177:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2940              		.loc 2 191 30 view .LVU840
 2941              	.LBB178:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2942              		.loc 2 193 1 view .LVU841
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
ARM GAS  /tmp/ccSZflmK.s 			page 91


 2943              		.loc 2 195 2 view .LVU842
 2944              		.syntax unified
 2945              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2946 001e 4FF05003 			mov r3, #80												
 2947 0022 83F31188 		msr basepri, r3											
 2948 0026 BFF36F8F 		isb														
 2949 002a BFF34F8F 		dsb														
 2950              	
 2951              	@ 0 "" 2
 2952              		.thumb
 2953              		.syntax unified
 2954              	.L201:
 2955              	.LBE178:
 2956              	.LBE177:
 999:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2957              		.loc 1 999 2 discriminator 3 view .LVU843
 999:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2958              		.loc 1 999 2 discriminator 3 view .LVU844
 2959 002e FEE7     		b	.L201
 2960              	.L204:
 2961              	.LCFI53:
 2962              		.cfi_def_cfa_offset 8
 2963              		.cfi_offset 4, -8
 2964              		.cfi_offset 14, -4
1011:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2965              		.loc 1 1011 30 is_stmt 0 view .LVU845
 2966 0030 0024     		movs	r4, #0
 2967 0032 EDE7     		b	.L202
 2968              	.LVL242:
 2969              	.L205:
1021:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 2970              		.loc 1 1021 11 view .LVU846
 2971 0034 0020     		movs	r0, #0
 2972              	.LVL243:
1024:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2973              		.loc 1 1024 2 is_stmt 1 view .LVU847
1024:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 2974              		.loc 1 1024 9 is_stmt 0 view .LVU848
 2975 0036 F1E7     		b	.L199
 2976              		.cfi_endproc
 2977              	.LFE19:
 2979              		.section	.text.xStreamBufferSendCompletedFromISR,"ax",%progbits
 2980              		.align	1
 2981              		.global	xStreamBufferSendCompletedFromISR
 2982              		.syntax unified
 2983              		.thumb
 2984              		.thumb_func
 2986              	xStreamBufferSendCompletedFromISR:
 2987              	.LVL244:
 2988              	.LFB20:
1029:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2989              		.loc 1 1029 1 is_stmt 1 view -0
 2990              		.cfi_startproc
 2991              		@ args = 0, pretend = 0, frame = 0
 2992              		@ frame_needed = 0, uses_anonymous_args = 0
1030:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 2993              		.loc 1 1030 1 view .LVU850
ARM GAS  /tmp/ccSZflmK.s 			page 92


1031:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** UBaseType_t uxSavedInterruptStatus;
 2994              		.loc 1 1031 1 view .LVU851
1032:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2995              		.loc 1 1032 1 view .LVU852
1034:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 2996              		.loc 1 1034 2 view .LVU853
 2997 0000 E0B1     		cbz	r0, .L216
1029:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 2998              		.loc 1 1029 1 is_stmt 0 view .LVU854
 2999 0002 30B5     		push	{r4, r5, lr}
 3000              	.LCFI54:
 3001              		.cfi_def_cfa_offset 12
 3002              		.cfi_offset 4, -12
 3003              		.cfi_offset 5, -8
 3004              		.cfi_offset 14, -4
 3005 0004 83B0     		sub	sp, sp, #12
 3006              	.LCFI55:
 3007              		.cfi_def_cfa_offset 24
 3008 0006 0546     		mov	r5, r0
1034:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3009              		.loc 1 1034 32 is_stmt 1 discriminator 2 view .LVU855
1036:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 3010              		.loc 1 1036 2 view .LVU856
 3011              	.LBB179:
 3012              	.LBI179:
 207:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 3013              		.loc 2 207 34 view .LVU857
 3014              	.LBB180:
 209:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 3015              		.loc 2 209 1 view .LVU858
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 3016              		.loc 2 211 2 view .LVU859
 3017              		.syntax unified
 3018              	@ 211 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 3019 0008 EFF31184 			mrs r4, basepri											
 3020 000c 4FF05003 		mov r3, #80												
 3021 0010 83F31188 		msr basepri, r3											
 3022 0014 BFF36F8F 		isb														
 3023 0018 BFF34F8F 		dsb														
 3024              	
 3025              	@ 0 "" 2
 3026              	.LVL245:
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 3027              		.loc 2 223 2 view .LVU860
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 3028              		.loc 2 223 2 is_stmt 0 view .LVU861
 3029              		.thumb
 3030              		.syntax unified
 3031              	.LBE180:
 3032              	.LBE179:
1038:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 3033              		.loc 1 1038 3 is_stmt 1 view .LVU862
1038:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 3034              		.loc 1 1038 25 is_stmt 0 view .LVU863
 3035 001c 0369     		ldr	r3, [r0, #16]
1038:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 3036              		.loc 1 1038 5 view .LVU864
ARM GAS  /tmp/ccSZflmK.s 			page 93


 3037 001e B3B1     		cbz	r3, .L213
1040:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3038              		.loc 1 1040 4 is_stmt 1 view .LVU865
1040:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3039              		.loc 1 1040 13 is_stmt 0 view .LVU866
 3040 0020 0069     		ldr	r0, [r0, #16]
 3041              	.LVL246:
1040:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3042              		.loc 1 1040 13 view .LVU867
 3043 0022 0091     		str	r1, [sp]
 3044 0024 0023     		movs	r3, #0
 3045 0026 1A46     		mov	r2, r3
 3046 0028 1946     		mov	r1, r3
 3047              	.LVL247:
1040:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3048              		.loc 1 1040 13 view .LVU868
 3049 002a FFF7FEFF 		bl	xTaskGenericNotifyFromISR
 3050              	.LVL248:
1044:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdTRUE;
 3051              		.loc 1 1044 4 is_stmt 1 view .LVU869
1044:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdTRUE;
 3052              		.loc 1 1044 46 is_stmt 0 view .LVU870
 3053 002e 0023     		movs	r3, #0
 3054 0030 2B61     		str	r3, [r5, #16]
1045:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3055              		.loc 1 1045 4 is_stmt 1 view .LVU871
 3056              	.LVL249:
1045:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3057              		.loc 1 1045 12 is_stmt 0 view .LVU872
 3058 0032 0120     		movs	r0, #1
 3059              	.LVL250:
 3060              	.L212:
1052:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3061              		.loc 1 1052 2 is_stmt 1 view .LVU873
 3062              	.LBB181:
 3063              	.LBI181:
 227:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 3064              		.loc 2 227 30 view .LVU874
 3065              	.LBB182:
 229:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 3066              		.loc 2 229 2 view .LVU875
 3067              		.syntax unified
 3068              	@ 229 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 3069 0034 84F31188 			msr basepri, r4	
 3070              	@ 0 "" 2
 3071              		.thumb
 3072              		.syntax unified
 3073              	.LBE182:
 3074              	.LBE181:
1054:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 3075              		.loc 1 1054 2 view .LVU876
1055:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 3076              		.loc 1 1055 1 is_stmt 0 view .LVU877
 3077 0038 03B0     		add	sp, sp, #12
 3078              	.LCFI56:
 3079              		.cfi_def_cfa_offset 12
 3080              		@ sp needed
ARM GAS  /tmp/ccSZflmK.s 			page 94


 3081 003a 30BD     		pop	{r4, r5, pc}
 3082              	.LVL251:
 3083              	.L216:
 3084              	.LCFI57:
 3085              		.cfi_def_cfa_offset 0
 3086              		.cfi_restore 4
 3087              		.cfi_restore 5
 3088              		.cfi_restore 14
1034:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3089              		.loc 1 1034 2 is_stmt 1 discriminator 1 view .LVU878
 3090              	.LBB183:
 3091              	.LBI183:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 3092              		.loc 2 191 30 view .LVU879
 3093              	.LBB184:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 3094              		.loc 2 193 1 view .LVU880
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 3095              		.loc 2 195 2 view .LVU881
 3096              		.syntax unified
 3097              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 3098 003c 4FF05003 			mov r3, #80												
 3099 0040 83F31188 		msr basepri, r3											
 3100 0044 BFF36F8F 		isb														
 3101 0048 BFF34F8F 		dsb														
 3102              	
 3103              	@ 0 "" 2
 3104              		.thumb
 3105              		.syntax unified
 3106              	.L211:
 3107              	.LBE184:
 3108              	.LBE183:
1034:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3109              		.loc 1 1034 2 discriminator 3 view .LVU882
1034:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3110              		.loc 1 1034 2 discriminator 3 view .LVU883
 3111 004c FEE7     		b	.L211
 3112              	.LVL252:
 3113              	.L213:
 3114              	.LCFI58:
 3115              		.cfi_def_cfa_offset 24
 3116              		.cfi_offset 4, -12
 3117              		.cfi_offset 5, -8
 3118              		.cfi_offset 14, -4
1049:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3119              		.loc 1 1049 12 is_stmt 0 view .LVU884
 3120 004e 0020     		movs	r0, #0
 3121              	.LVL253:
1049:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3122              		.loc 1 1049 12 view .LVU885
 3123 0050 F0E7     		b	.L212
 3124              		.cfi_endproc
 3125              	.LFE20:
 3127              		.section	.text.xStreamBufferReceiveCompletedFromISR,"ax",%progbits
 3128              		.align	1
 3129              		.global	xStreamBufferReceiveCompletedFromISR
 3130              		.syntax unified
ARM GAS  /tmp/ccSZflmK.s 			page 95


 3131              		.thumb
 3132              		.thumb_func
 3134              	xStreamBufferReceiveCompletedFromISR:
 3135              	.LVL254:
 3136              	.LFB21:
1059:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 3137              		.loc 1 1059 1 is_stmt 1 view -0
 3138              		.cfi_startproc
 3139              		@ args = 0, pretend = 0, frame = 0
 3140              		@ frame_needed = 0, uses_anonymous_args = 0
1060:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** BaseType_t xReturn;
 3141              		.loc 1 1060 1 view .LVU887
1061:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** UBaseType_t uxSavedInterruptStatus;
 3142              		.loc 1 1061 1 view .LVU888
1062:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3143              		.loc 1 1062 1 view .LVU889
1064:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3144              		.loc 1 1064 2 view .LVU890
 3145 0000 E0B1     		cbz	r0, .L224
1059:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** StreamBuffer_t * const pxStreamBuffer = xStreamBuffer;
 3146              		.loc 1 1059 1 is_stmt 0 view .LVU891
 3147 0002 30B5     		push	{r4, r5, lr}
 3148              	.LCFI59:
 3149              		.cfi_def_cfa_offset 12
 3150              		.cfi_offset 4, -12
 3151              		.cfi_offset 5, -8
 3152              		.cfi_offset 14, -4
 3153 0004 83B0     		sub	sp, sp, #12
 3154              	.LCFI60:
 3155              		.cfi_def_cfa_offset 24
 3156 0006 0546     		mov	r5, r0
1064:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3157              		.loc 1 1064 32 is_stmt 1 discriminator 2 view .LVU892
1066:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 3158              		.loc 1 1066 2 view .LVU893
 3159              	.LBB185:
 3160              	.LBI185:
 207:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 3161              		.loc 2 207 34 view .LVU894
 3162              	.LBB186:
 209:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 3163              		.loc 2 209 1 view .LVU895
 211:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 3164              		.loc 2 211 2 view .LVU896
 3165              		.syntax unified
 3166              	@ 211 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 3167 0008 EFF31184 			mrs r4, basepri											
 3168 000c 4FF05003 		mov r3, #80												
 3169 0010 83F31188 		msr basepri, r3											
 3170 0014 BFF36F8F 		isb														
 3171 0018 BFF34F8F 		dsb														
 3172              	
 3173              	@ 0 "" 2
 3174              	.LVL255:
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 3175              		.loc 2 223 2 view .LVU897
 223:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
ARM GAS  /tmp/ccSZflmK.s 			page 96


 3176              		.loc 2 223 2 is_stmt 0 view .LVU898
 3177              		.thumb
 3178              		.syntax unified
 3179              	.LBE186:
 3180              	.LBE185:
1068:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 3181              		.loc 1 1068 3 is_stmt 1 view .LVU899
1068:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 3182              		.loc 1 1068 25 is_stmt 0 view .LVU900
 3183 001c 4369     		ldr	r3, [r0, #20]
1068:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		{
 3184              		.loc 1 1068 5 view .LVU901
 3185 001e B3B1     		cbz	r3, .L221
1070:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3186              		.loc 1 1070 4 is_stmt 1 view .LVU902
1070:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3187              		.loc 1 1070 13 is_stmt 0 view .LVU903
 3188 0020 4069     		ldr	r0, [r0, #20]
 3189              	.LVL256:
1070:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3190              		.loc 1 1070 13 view .LVU904
 3191 0022 0091     		str	r1, [sp]
 3192 0024 0023     		movs	r3, #0
 3193 0026 1A46     		mov	r2, r3
 3194 0028 1946     		mov	r1, r3
 3195              	.LVL257:
1070:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 										 ( uint32_t ) 0,
 3196              		.loc 1 1070 13 view .LVU905
 3197 002a FFF7FEFF 		bl	xTaskGenericNotifyFromISR
 3198              	.LVL258:
1074:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdTRUE;
 3199              		.loc 1 1074 4 is_stmt 1 view .LVU906
1074:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 			xReturn = pdTRUE;
 3200              		.loc 1 1074 43 is_stmt 0 view .LVU907
 3201 002e 0023     		movs	r3, #0
 3202 0030 6B61     		str	r3, [r5, #20]
1075:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3203              		.loc 1 1075 4 is_stmt 1 view .LVU908
 3204              	.LVL259:
1075:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3205              		.loc 1 1075 12 is_stmt 0 view .LVU909
 3206 0032 0120     		movs	r0, #1
 3207              	.LVL260:
 3208              	.L220:
1082:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3209              		.loc 1 1082 2 is_stmt 1 view .LVU910
 3210              	.LBB187:
 3211              	.LBI187:
 227:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 3212              		.loc 2 227 30 view .LVU911
 3213              	.LBB188:
 229:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 3214              		.loc 2 229 2 view .LVU912
 3215              		.syntax unified
 3216              	@ 229 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 3217 0034 84F31188 			msr basepri, r4	
 3218              	@ 0 "" 2
ARM GAS  /tmp/ccSZflmK.s 			page 97


 3219              		.thumb
 3220              		.syntax unified
 3221              	.LBE188:
 3222              	.LBE187:
1084:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** }
 3223              		.loc 1 1084 2 view .LVU913
1085:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
 3224              		.loc 1 1085 1 is_stmt 0 view .LVU914
 3225 0038 03B0     		add	sp, sp, #12
 3226              	.LCFI61:
 3227              		.cfi_def_cfa_offset 12
 3228              		@ sp needed
 3229 003a 30BD     		pop	{r4, r5, pc}
 3230              	.LVL261:
 3231              	.L224:
 3232              	.LCFI62:
 3233              		.cfi_def_cfa_offset 0
 3234              		.cfi_restore 4
 3235              		.cfi_restore 5
 3236              		.cfi_restore 14
1064:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3237              		.loc 1 1064 2 is_stmt 1 discriminator 1 view .LVU915
 3238              	.LBB189:
 3239              	.LBI189:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 3240              		.loc 2 191 30 view .LVU916
 3241              	.LBB190:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 3242              		.loc 2 193 1 view .LVU917
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	(
 3243              		.loc 2 195 2 view .LVU918
 3244              		.syntax unified
 3245              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 3246 003c 4FF05003 			mov r3, #80												
 3247 0040 83F31188 		msr basepri, r3											
 3248 0044 BFF36F8F 		isb														
 3249 0048 BFF34F8F 		dsb														
 3250              	
 3251              	@ 0 "" 2
 3252              		.thumb
 3253              		.syntax unified
 3254              	.L219:
 3255              	.LBE190:
 3256              	.LBE189:
1064:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3257              		.loc 1 1064 2 discriminator 3 view .LVU919
1064:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
 3258              		.loc 1 1064 2 discriminator 3 view .LVU920
 3259 004c FEE7     		b	.L219
 3260              	.LVL262:
 3261              	.L221:
 3262              	.LCFI63:
 3263              		.cfi_def_cfa_offset 24
 3264              		.cfi_offset 4, -12
 3265              		.cfi_offset 5, -8
 3266              		.cfi_offset 14, -4
1079:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
ARM GAS  /tmp/ccSZflmK.s 			page 98


 3267              		.loc 1 1079 12 is_stmt 0 view .LVU921
 3268 004e 0020     		movs	r0, #0
 3269              	.LVL263:
1079:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		}
 3270              		.loc 1 1079 12 view .LVU922
 3271 0050 F0E7     		b	.L220
 3272              		.cfi_endproc
 3273              	.LFE21:
 3275              		.section	.text.uxStreamBufferGetStreamBufferNumber,"ax",%progbits
 3276              		.align	1
 3277              		.global	uxStreamBufferGetStreamBufferNumber
 3278              		.syntax unified
 3279              		.thumb
 3280              		.thumb_func
 3282              	uxStreamBufferGetStreamBufferNumber:
 3283              	.LVL264:
 3284              	.LFB26:
1234:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1235:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if ( configUSE_TRACE_FACILITY == 1 )
1236:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1237:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	UBaseType_t uxStreamBufferGetStreamBufferNumber( StreamBufferHandle_t xStreamBuffer )
1238:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 3285              		.loc 1 1238 2 is_stmt 1 view -0
 3286              		.cfi_startproc
 3287              		@ args = 0, pretend = 0, frame = 0
 3288              		@ frame_needed = 0, uses_anonymous_args = 0
 3289              		@ link register save eliminated.
1239:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		return xStreamBuffer->uxStreamBufferNumber;
 3290              		.loc 1 1239 3 view .LVU924
1240:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 3291              		.loc 1 1240 2 is_stmt 0 view .LVU925
 3292 0000 006A     		ldr	r0, [r0, #32]
 3293              	.LVL265:
 3294              		.loc 1 1240 2 view .LVU926
 3295 0002 7047     		bx	lr
 3296              		.cfi_endproc
 3297              	.LFE26:
 3299              		.section	.text.vStreamBufferSetStreamBufferNumber,"ax",%progbits
 3300              		.align	1
 3301              		.global	vStreamBufferSetStreamBufferNumber
 3302              		.syntax unified
 3303              		.thumb
 3304              		.thumb_func
 3306              	vStreamBufferSetStreamBufferNumber:
 3307              	.LVL266:
 3308              	.LFB27:
1241:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1242:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* configUSE_TRACE_FACILITY */
1243:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1244:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1245:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if ( configUSE_TRACE_FACILITY == 1 )
1246:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1247:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	void vStreamBufferSetStreamBufferNumber( StreamBufferHandle_t xStreamBuffer, UBaseType_t uxStreamB
1248:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 3309              		.loc 1 1248 2 is_stmt 1 view -0
 3310              		.cfi_startproc
 3311              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/ccSZflmK.s 			page 99


 3312              		@ frame_needed = 0, uses_anonymous_args = 0
 3313              		@ link register save eliminated.
1249:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		xStreamBuffer->uxStreamBufferNumber = uxStreamBufferNumber;
 3314              		.loc 1 1249 3 view .LVU928
 3315              		.loc 1 1249 39 is_stmt 0 view .LVU929
 3316 0000 0162     		str	r1, [r0, #32]
1250:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 3317              		.loc 1 1250 2 view .LVU930
 3318 0002 7047     		bx	lr
 3319              		.cfi_endproc
 3320              	.LFE27:
 3322              		.section	.text.ucStreamBufferGetStreamBufferType,"ax",%progbits
 3323              		.align	1
 3324              		.global	ucStreamBufferGetStreamBufferType
 3325              		.syntax unified
 3326              		.thumb
 3327              		.thumb_func
 3329              	ucStreamBufferGetStreamBufferType:
 3330              	.LVL267:
 3331              	.LFB28:
1251:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1252:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #endif /* configUSE_TRACE_FACILITY */
1253:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** /*-----------------------------------------------------------*/
1254:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1255:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** #if ( configUSE_TRACE_FACILITY == 1 )
1256:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 
1257:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	uint8_t ucStreamBufferGetStreamBufferType( StreamBufferHandle_t xStreamBuffer )
1258:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	{
 3332              		.loc 1 1258 2 is_stmt 1 view -0
 3333              		.cfi_startproc
 3334              		@ args = 0, pretend = 0, frame = 0
 3335              		@ frame_needed = 0, uses_anonymous_args = 0
 3336              		@ link register save eliminated.
1259:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 		return ( xStreamBuffer->ucFlags & sbFLAGS_IS_MESSAGE_BUFFER );
 3337              		.loc 1 1259 3 view .LVU932
 3338              		.loc 1 1259 25 is_stmt 0 view .LVU933
 3339 0000 007F     		ldrb	r0, [r0, #28]	@ zero_extendqisi2
 3340              	.LVL268:
1260:Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c **** 	}
 3341              		.loc 1 1260 2 view .LVU934
 3342 0002 00F00100 		and	r0, r0, #1
 3343 0006 7047     		bx	lr
 3344              		.cfi_endproc
 3345              	.LFE28:
 3347              		.text
 3348              	.Letext0:
 3349              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 3350              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 3351              		.file 5 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 3352              		.file 6 "Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h"
 3353              		.file 7 "Middlewares/Third_Party/FreeRTOS/Source/include/task.h"
 3354              		.file 8 "Middlewares/Third_Party/FreeRTOS/Source/include/stream_buffer.h"
 3355              		.file 9 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 3356              		.file 10 "Middlewares/Third_Party/FreeRTOS/Source/include/portable.h"
 3357              		.file 11 "<built-in>"
ARM GAS  /tmp/ccSZflmK.s 			page 100


DEFINED SYMBOLS
                            *ABS*:00000000 stream_buffer.c
     /tmp/ccSZflmK.s:20     .text.prvBytesInBuffer:00000000 $t
     /tmp/ccSZflmK.s:25     .text.prvBytesInBuffer:00000000 prvBytesInBuffer
     /tmp/ccSZflmK.s:66     .text.prvInitialiseNewStreamBuffer:00000000 $t
     /tmp/ccSZflmK.s:71     .text.prvInitialiseNewStreamBuffer:00000000 prvInitialiseNewStreamBuffer
     /tmp/ccSZflmK.s:161    .text.prvWriteBytesToBuffer:00000000 $t
     /tmp/ccSZflmK.s:166    .text.prvWriteBytesToBuffer:00000000 prvWriteBytesToBuffer
     /tmp/ccSZflmK.s:337    .text.prvWriteMessageToBuffer:00000000 $t
     /tmp/ccSZflmK.s:342    .text.prvWriteMessageToBuffer:00000000 prvWriteMessageToBuffer
     /tmp/ccSZflmK.s:442    .text.prvReadBytesFromBuffer:00000000 $t
     /tmp/ccSZflmK.s:447    .text.prvReadBytesFromBuffer:00000000 prvReadBytesFromBuffer
     /tmp/ccSZflmK.s:612    .text.prvReadMessageFromBuffer:00000000 $t
     /tmp/ccSZflmK.s:617    .text.prvReadMessageFromBuffer:00000000 prvReadMessageFromBuffer
     /tmp/ccSZflmK.s:718    .text.xStreamBufferGenericCreate:00000000 $t
     /tmp/ccSZflmK.s:724    .text.xStreamBufferGenericCreate:00000000 xStreamBufferGenericCreate
     /tmp/ccSZflmK.s:888    .text.xStreamBufferGenericCreateStatic:00000000 $t
     /tmp/ccSZflmK.s:894    .text.xStreamBufferGenericCreateStatic:00000000 xStreamBufferGenericCreateStatic
     /tmp/ccSZflmK.s:1138   .text.vStreamBufferDelete:00000000 $t
     /tmp/ccSZflmK.s:1144   .text.vStreamBufferDelete:00000000 vStreamBufferDelete
     /tmp/ccSZflmK.s:1213   .text.xStreamBufferReset:00000000 $t
     /tmp/ccSZflmK.s:1219   .text.xStreamBufferReset:00000000 xStreamBufferReset
     /tmp/ccSZflmK.s:1342   .text.xStreamBufferSetTriggerLevel:00000000 $t
     /tmp/ccSZflmK.s:1348   .text.xStreamBufferSetTriggerLevel:00000000 xStreamBufferSetTriggerLevel
     /tmp/ccSZflmK.s:1421   .text.xStreamBufferSpacesAvailable:00000000 $t
     /tmp/ccSZflmK.s:1427   .text.xStreamBufferSpacesAvailable:00000000 xStreamBufferSpacesAvailable
     /tmp/ccSZflmK.s:1503   .text.xStreamBufferBytesAvailable:00000000 $t
     /tmp/ccSZflmK.s:1509   .text.xStreamBufferBytesAvailable:00000000 xStreamBufferBytesAvailable
     /tmp/ccSZflmK.s:1564   .text.xStreamBufferSend:00000000 $t
     /tmp/ccSZflmK.s:1570   .text.xStreamBufferSend:00000000 xStreamBufferSend
     /tmp/ccSZflmK.s:1890   .text.xStreamBufferSendFromISR:00000000 $t
     /tmp/ccSZflmK.s:1896   .text.xStreamBufferSendFromISR:00000000 xStreamBufferSendFromISR
     /tmp/ccSZflmK.s:2130   .text.xStreamBufferReceive:00000000 $t
     /tmp/ccSZflmK.s:2136   .text.xStreamBufferReceive:00000000 xStreamBufferReceive
     /tmp/ccSZflmK.s:2426   .text.xStreamBufferNextMessageLengthBytes:00000000 $t
     /tmp/ccSZflmK.s:2432   .text.xStreamBufferNextMessageLengthBytes:00000000 xStreamBufferNextMessageLengthBytes
     /tmp/ccSZflmK.s:2570   .text.xStreamBufferReceiveFromISR:00000000 $t
     /tmp/ccSZflmK.s:2576   .text.xStreamBufferReceiveFromISR:00000000 xStreamBufferReceiveFromISR
     /tmp/ccSZflmK.s:2808   .text.xStreamBufferIsEmpty:00000000 $t
     /tmp/ccSZflmK.s:2814   .text.xStreamBufferIsEmpty:00000000 xStreamBufferIsEmpty
     /tmp/ccSZflmK.s:2880   .text.xStreamBufferIsFull:00000000 $t
     /tmp/ccSZflmK.s:2886   .text.xStreamBufferIsFull:00000000 xStreamBufferIsFull
     /tmp/ccSZflmK.s:2980   .text.xStreamBufferSendCompletedFromISR:00000000 $t
     /tmp/ccSZflmK.s:2986   .text.xStreamBufferSendCompletedFromISR:00000000 xStreamBufferSendCompletedFromISR
     /tmp/ccSZflmK.s:3128   .text.xStreamBufferReceiveCompletedFromISR:00000000 $t
     /tmp/ccSZflmK.s:3134   .text.xStreamBufferReceiveCompletedFromISR:00000000 xStreamBufferReceiveCompletedFromISR
     /tmp/ccSZflmK.s:3276   .text.uxStreamBufferGetStreamBufferNumber:00000000 $t
     /tmp/ccSZflmK.s:3282   .text.uxStreamBufferGetStreamBufferNumber:00000000 uxStreamBufferGetStreamBufferNumber
     /tmp/ccSZflmK.s:3300   .text.vStreamBufferSetStreamBufferNumber:00000000 $t
     /tmp/ccSZflmK.s:3306   .text.vStreamBufferSetStreamBufferNumber:00000000 vStreamBufferSetStreamBufferNumber
     /tmp/ccSZflmK.s:3323   .text.ucStreamBufferGetStreamBufferType:00000000 $t
     /tmp/ccSZflmK.s:3329   .text.ucStreamBufferGetStreamBufferType:00000000 ucStreamBufferGetStreamBufferType

UNDEFINED SYMBOLS
memset
memcpy
pvPortMalloc
ARM GAS  /tmp/ccSZflmK.s 			page 101


vPortFree
vPortEnterCritical
vPortExitCritical
vTaskSetTimeOutState
xTaskNotifyStateClear
xTaskGetCurrentTaskHandle
xTaskNotifyWait
xTaskCheckForTimeOut
vTaskSuspendAll
xTaskGenericNotify
xTaskResumeAll
xTaskGenericNotifyFromISR
