ARM GAS  /tmp/ccwyId6Y.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"mem.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/mem.c"
  19              		.section	.text.ptr_to_mem,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	ptr_to_mem:
  26              	.LVL0:
  27              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Dynamic memory manager
   4:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * This is a lightweight replacement for the standard C library malloc().
   6:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
   7:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * If you want to use the standard C library malloc() instead, define
   8:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * MEM_LIBC_MALLOC to 1 in your lwipopts.h
   9:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  10:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * To let mem_malloc() use pools (prevents fragmentation and is much faster than
  11:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * a heap but might waste some memory), define MEM_USE_POOLS to 1, define
  12:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * MEMP_USE_CUSTOM_POOLS to 1 and create a file "lwippools.h" that includes a list
  13:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * of pools like this (more pools can be added between _START and _END):
  14:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  15:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Define three pools with sizes 256, 512, and 1512 bytes
  16:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * LWIP_MALLOC_MEMPOOL_START
  17:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * LWIP_MALLOC_MEMPOOL(20, 256)
  18:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * LWIP_MALLOC_MEMPOOL(10, 512)
  19:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * LWIP_MALLOC_MEMPOOL(5, 1512)
  20:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * LWIP_MALLOC_MEMPOOL_END
  21:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
  22:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  23:Middlewares/Third_Party/LwIP/src/core/mem.c **** /*
  24:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  25:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * All rights reserved.
  26:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  27:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Redistribution and use in source and binary forms, with or without modification,
  28:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * are permitted provided that the following conditions are met:
  29:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  30:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  31:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *    this list of conditions and the following disclaimer.
ARM GAS  /tmp/ccwyId6Y.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  33:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *    this list of conditions and the following disclaimer in the documentation
  34:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *    and/or other materials provided with the distribution.
  35:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * 3. The name of the author may not be used to endorse or promote products
  36:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *    derived from this software without specific prior written permission.
  37:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  38:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  39:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  40:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  41:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  42:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  43:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  44:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  45:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  46:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  47:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * OF SUCH DAMAGE.
  48:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  49:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * This file is part of the lwIP TCP/IP stack.
  50:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  51:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Author: Adam Dunkels <<EMAIL>>
  52:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *         Simon Goldschmidt
  53:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  54:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
  55:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  56:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include "lwip/opt.h"
  57:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include "lwip/mem.h"
  58:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include "lwip/def.h"
  59:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include "lwip/sys.h"
  60:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include "lwip/stats.h"
  61:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include "lwip/err.h"
  62:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  63:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include <string.h>
  64:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  65:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_LIBC_MALLOC
  66:Middlewares/Third_Party/LwIP/src/core/mem.c **** #include <stdlib.h> /* for malloc()/free() */
  67:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
  68:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  69:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* This is overridable for tests only... */
  70:Middlewares/Third_Party/LwIP/src/core/mem.c **** #ifndef LWIP_MEM_ILLEGAL_FREE
  71:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ILLEGAL_FREE(msg)         LWIP_ASSERT(msg, 0)
  72:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
  73:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  74:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_STATS_INC_LOCKED(x)         SYS_ARCH_LOCKED(MEM_STATS_INC(x))
  75:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_STATS_INC_USED_LOCKED(x, y) SYS_ARCH_LOCKED(MEM_STATS_INC_USED(x, y))
  76:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_STATS_DEC_USED_LOCKED(x, y) SYS_ARCH_LOCKED(MEM_STATS_DEC_USED(x, y))
  77:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  78:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
  79:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SANITY_OFFSET   MEM_SANITY_REGION_BEFORE_ALIGNED
  80:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SANITY_OVERHEAD (MEM_SANITY_REGION_BEFORE_ALIGNED + MEM_SANITY_REGION_AFTER_ALIGNED)
  81:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else
  82:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SANITY_OFFSET   0
  83:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SANITY_OVERHEAD 0
  84:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
  85:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
  86:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK || MEMP_OVERFLOW_CHECK
  87:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
  88:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Check if a mep element was victim of an overflow or underflow
ARM GAS  /tmp/ccwyId6Y.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * (e.g. the restricted area after/before it has been altered)
  90:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
  91:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param p the mem element to check
  92:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param size allocated size of the element
  93:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param descr1 description of the element source shown on error
  94:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param descr2 description of the element source shown on error
  95:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
  96:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
  97:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_overflow_check_raw(void *p, size_t size, const char *descr1, const char *descr2)
  98:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
  99:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_REGION_AFTER_ALIGNED || MEM_SANITY_REGION_BEFORE_ALIGNED
 100:Middlewares/Third_Party/LwIP/src/core/mem.c ****   u16_t k;
 101:Middlewares/Third_Party/LwIP/src/core/mem.c ****   u8_t *m;
 102:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 103:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_REGION_AFTER_ALIGNED > 0
 104:Middlewares/Third_Party/LwIP/src/core/mem.c ****   m = (u8_t *)p + size;
 105:Middlewares/Third_Party/LwIP/src/core/mem.c ****   for (k = 0; k < MEM_SANITY_REGION_AFTER_ALIGNED; k++) {
 106:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (m[k] != 0xcd) {
 107:Middlewares/Third_Party/LwIP/src/core/mem.c ****       char errstr[128];
 108:Middlewares/Third_Party/LwIP/src/core/mem.c ****       snprintf(errstr, sizeof(errstr), "detected mem overflow in %s%s", descr1, descr2);
 109:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_ASSERT(errstr, 0);
 110:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 111:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 112:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_SANITY_REGION_AFTER_ALIGNED > 0 */
 113:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 114:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_REGION_BEFORE_ALIGNED > 0
 115:Middlewares/Third_Party/LwIP/src/core/mem.c ****   m = (u8_t *)p - MEM_SANITY_REGION_BEFORE_ALIGNED;
 116:Middlewares/Third_Party/LwIP/src/core/mem.c ****   for (k = 0; k < MEM_SANITY_REGION_BEFORE_ALIGNED; k++) {
 117:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (m[k] != 0xcd) {
 118:Middlewares/Third_Party/LwIP/src/core/mem.c ****       char errstr[128];
 119:Middlewares/Third_Party/LwIP/src/core/mem.c ****       snprintf(errstr, sizeof(errstr), "detected mem underflow in %s%s", descr1, descr2);
 120:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_ASSERT(errstr, 0);
 121:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 122:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 123:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_SANITY_REGION_BEFORE_ALIGNED > 0 */
 124:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else
 125:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_UNUSED_ARG(p);
 126:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_UNUSED_ARG(desc);
 127:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_UNUSED_ARG(descr);
 128:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 129:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 130:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 131:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 132:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Initialize the restricted area of a mem element.
 133:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 134:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
 135:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_overflow_init_raw(void *p, size_t size)
 136:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 137:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_REGION_BEFORE_ALIGNED > 0 || MEM_SANITY_REGION_AFTER_ALIGNED > 0
 138:Middlewares/Third_Party/LwIP/src/core/mem.c ****   u8_t *m;
 139:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_REGION_BEFORE_ALIGNED > 0
 140:Middlewares/Third_Party/LwIP/src/core/mem.c ****   m = (u8_t *)p - MEM_SANITY_REGION_BEFORE_ALIGNED;
 141:Middlewares/Third_Party/LwIP/src/core/mem.c ****   memset(m, 0xcd, MEM_SANITY_REGION_BEFORE_ALIGNED);
 142:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 143:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_REGION_AFTER_ALIGNED > 0
 144:Middlewares/Third_Party/LwIP/src/core/mem.c ****   m = (u8_t *)p + size;
 145:Middlewares/Third_Party/LwIP/src/core/mem.c ****   memset(m, 0xcd, MEM_SANITY_REGION_AFTER_ALIGNED);
ARM GAS  /tmp/ccwyId6Y.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 147:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else /* MEM_SANITY_REGION_BEFORE_ALIGNED > 0 || MEM_SANITY_REGION_AFTER_ALIGNED > 0 */
 148:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_UNUSED_ARG(p);
 149:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_UNUSED_ARG(desc);
 150:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_SANITY_REGION_BEFORE_ALIGNED > 0 || MEM_SANITY_REGION_AFTER_ALIGNED > 0 */
 151:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 152:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_OVERFLOW_CHECK || MEMP_OVERFLOW_CHECK */
 153:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 154:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_LIBC_MALLOC || MEM_USE_POOLS
 155:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 156:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** mem_init is not used when using pools instead of a heap or using
 157:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * C library malloc().
 158:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 159:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
 160:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_init(void)
 161:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 162:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 163:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 164:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** mem_trim is not used when using pools instead of a heap or using
 165:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * C library malloc(): we can't free part of a pool element and the stack
 166:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * support mem_trim() to return a different pointer
 167:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 168:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 169:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_trim(void *mem, mem_size_t size)
 170:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 171:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_UNUSED_ARG(size);
 172:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return mem;
 173:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 174:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_LIBC_MALLOC || MEM_USE_POOLS */
 175:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 176:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_LIBC_MALLOC
 177:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* lwIP heap implemented using C library malloc() */
 178:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 179:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* in case C library malloc() needs extra protection,
 180:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * allow these defines to be overridden.
 181:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 182:Middlewares/Third_Party/LwIP/src/core/mem.c **** #ifndef mem_clib_free
 183:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define mem_clib_free free
 184:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 185:Middlewares/Third_Party/LwIP/src/core/mem.c **** #ifndef mem_clib_malloc
 186:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define mem_clib_malloc malloc
 187:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 188:Middlewares/Third_Party/LwIP/src/core/mem.c **** #ifndef mem_clib_calloc
 189:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define mem_clib_calloc calloc
 190:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 191:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 192:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_STATS && MEM_STATS
 193:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_LIBC_STATSHELPER_SIZE LWIP_MEM_ALIGN_SIZE(sizeof(mem_size_t))
 194:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else
 195:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_LIBC_STATSHELPER_SIZE 0
 196:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 197:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 198:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 199:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Allocate a block of memory with a minimum of 'size' bytes.
 200:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 201:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param size is the minimum size of the requested block in bytes.
 202:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @return pointer to allocated memory or NULL if no free memory was found.
ARM GAS  /tmp/ccwyId6Y.s 			page 5


 203:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 204:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Note that the returned value must always be aligned (as defined by MEM_ALIGNMENT).
 205:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 206:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 207:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_malloc(mem_size_t size)
 208:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 209:Middlewares/Third_Party/LwIP/src/core/mem.c ****   void *ret = mem_clib_malloc(size + MEM_LIBC_STATSHELPER_SIZE);
 210:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (ret == NULL) {
 211:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(err);
 212:Middlewares/Third_Party/LwIP/src/core/mem.c ****   } else {
 213:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("malloc() must return aligned memory", LWIP_MEM_ALIGN(ret) == ret);
 214:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_STATS && MEM_STATS
 215:Middlewares/Third_Party/LwIP/src/core/mem.c ****     *(mem_size_t *)ret = size;
 216:Middlewares/Third_Party/LwIP/src/core/mem.c ****     ret = (u8_t *)ret + MEM_LIBC_STATSHELPER_SIZE;
 217:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_USED_LOCKED(used, size);
 218:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 219:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 220:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return ret;
 221:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 222:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 223:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** Put memory back on the heap
 224:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 225:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param rmem is the pointer as returned by a previous call to mem_malloc()
 226:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 227:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
 228:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_free(void *rmem)
 229:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 230:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("rmem != NULL", (rmem != NULL));
 231:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("rmem == MEM_ALIGN(rmem)", (rmem == LWIP_MEM_ALIGN(rmem)));
 232:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_STATS && MEM_STATS
 233:Middlewares/Third_Party/LwIP/src/core/mem.c ****   rmem = (u8_t *)rmem - MEM_LIBC_STATSHELPER_SIZE;
 234:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_STATS_DEC_USED_LOCKED(used, *(mem_size_t *)rmem);
 235:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 236:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_clib_free(rmem);
 237:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 238:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 239:Middlewares/Third_Party/LwIP/src/core/mem.c **** #elif MEM_USE_POOLS
 240:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 241:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* lwIP heap implemented with different sized pools */
 242:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 244:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Allocate memory: determine the smallest pool that is big enough
 245:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * to contain an element of 'size' and get an element from that pool.
 246:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 247:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param size the size in bytes of the memory needed
 248:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @return a pointer to the allocated memory or NULL if the pool is empty
 249:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 250:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 251:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_malloc(mem_size_t size)
 252:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 253:Middlewares/Third_Party/LwIP/src/core/mem.c ****   void *ret;
 254:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct memp_malloc_helper *element = NULL;
 255:Middlewares/Third_Party/LwIP/src/core/mem.c ****   memp_t poolnr;
 256:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t required_size = size + LWIP_MEM_ALIGN_SIZE(sizeof(struct memp_malloc_helper));
 257:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 258:Middlewares/Third_Party/LwIP/src/core/mem.c ****   for (poolnr = MEMP_POOL_FIRST; poolnr <= MEMP_POOL_LAST; poolnr = (memp_t)(poolnr + 1)) {
 259:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* is this pool big enough to hold an element of the required size
ARM GAS  /tmp/ccwyId6Y.s 			page 6


 260:Middlewares/Third_Party/LwIP/src/core/mem.c ****        plus a struct memp_malloc_helper that saves the pool this element came from? */
 261:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (required_size <= memp_pools[poolnr]->size) {
 262:Middlewares/Third_Party/LwIP/src/core/mem.c ****       element = (struct memp_malloc_helper *)memp_malloc(poolnr);
 263:Middlewares/Third_Party/LwIP/src/core/mem.c ****       if (element == NULL) {
 264:Middlewares/Third_Party/LwIP/src/core/mem.c ****         /* No need to DEBUGF or ASSERT: This error is already taken care of in memp.c */
 265:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_USE_POOLS_TRY_BIGGER_POOL
 266:Middlewares/Third_Party/LwIP/src/core/mem.c ****         /** Try a bigger pool if this one is empty! */
 267:Middlewares/Third_Party/LwIP/src/core/mem.c ****         if (poolnr < MEMP_POOL_LAST) {
 268:Middlewares/Third_Party/LwIP/src/core/mem.c ****           continue;
 269:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 270:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_USE_POOLS_TRY_BIGGER_POOL */
 271:Middlewares/Third_Party/LwIP/src/core/mem.c ****         MEM_STATS_INC_LOCKED(err);
 272:Middlewares/Third_Party/LwIP/src/core/mem.c ****         return NULL;
 273:Middlewares/Third_Party/LwIP/src/core/mem.c ****       }
 274:Middlewares/Third_Party/LwIP/src/core/mem.c ****       break;
 275:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 276:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 277:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (poolnr > MEMP_POOL_LAST) {
 278:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("mem_malloc(): no pool is that big!", 0);
 279:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(err);
 280:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return NULL;
 281:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 282:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 283:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* save the pool number this element came from */
 284:Middlewares/Third_Party/LwIP/src/core/mem.c ****   element->poolnr = poolnr;
 285:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* and return a pointer to the memory directly after the struct memp_malloc_helper */
 286:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ret = (u8_t *)element + LWIP_MEM_ALIGN_SIZE(sizeof(struct memp_malloc_helper));
 287:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 288:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEMP_OVERFLOW_CHECK || (LWIP_STATS && MEM_STATS)
 289:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* truncating to u16_t is safe because struct memp_desc::size is u16_t */
 290:Middlewares/Third_Party/LwIP/src/core/mem.c ****   element->size = (u16_t)size;
 291:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_STATS_INC_USED_LOCKED(used, element->size);
 292:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEMP_OVERFLOW_CHECK || (LWIP_STATS && MEM_STATS) */
 293:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEMP_OVERFLOW_CHECK
 294:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize unused memory (diff between requested size and selected pool's size) */
 295:Middlewares/Third_Party/LwIP/src/core/mem.c ****   memset((u8_t *)ret + size, 0xcd, memp_pools[poolnr]->size - size);
 296:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEMP_OVERFLOW_CHECK */
 297:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return ret;
 298:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 299:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 300:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 301:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Free memory previously allocated by mem_malloc. Loads the pool number
 302:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * and calls memp_free with that pool number to put the element back into
 303:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * its pool
 304:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 305:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param rmem the memory element to free
 306:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 307:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
 308:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_free(void *rmem)
 309:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 310:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct memp_malloc_helper *hmem;
 311:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 312:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("rmem != NULL", (rmem != NULL));
 313:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("rmem == MEM_ALIGN(rmem)", (rmem == LWIP_MEM_ALIGN(rmem)));
 314:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 315:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* get the original struct memp_malloc_helper */
 316:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* cast through void* to get rid of alignment warnings */
ARM GAS  /tmp/ccwyId6Y.s 			page 7


 317:Middlewares/Third_Party/LwIP/src/core/mem.c ****   hmem = (struct memp_malloc_helper *)(void *)((u8_t *)rmem - LWIP_MEM_ALIGN_SIZE(sizeof(struct mem
 318:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 319:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("hmem != NULL", (hmem != NULL));
 320:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("hmem == MEM_ALIGN(hmem)", (hmem == LWIP_MEM_ALIGN(hmem)));
 321:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("hmem->poolnr < MEMP_MAX", (hmem->poolnr < MEMP_MAX));
 322:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 323:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_STATS_DEC_USED_LOCKED(used, hmem->size);
 324:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEMP_OVERFLOW_CHECK
 325:Middlewares/Third_Party/LwIP/src/core/mem.c ****   {
 326:Middlewares/Third_Party/LwIP/src/core/mem.c ****     u16_t i;
 327:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("MEM_USE_POOLS: invalid chunk size",
 328:Middlewares/Third_Party/LwIP/src/core/mem.c ****                 hmem->size <= memp_pools[hmem->poolnr]->size);
 329:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* check that unused memory remained untouched (diff between requested size and selected pool's
 330:Middlewares/Third_Party/LwIP/src/core/mem.c ****     for (i = hmem->size; i < memp_pools[hmem->poolnr]->size; i++) {
 331:Middlewares/Third_Party/LwIP/src/core/mem.c ****       u8_t data = *((u8_t *)rmem + i);
 332:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_ASSERT("MEM_USE_POOLS: mem overflow detected", data == 0xcd);
 333:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 334:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 335:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEMP_OVERFLOW_CHECK */
 336:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 337:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* and put it in the pool we saved earlier */
 338:Middlewares/Third_Party/LwIP/src/core/mem.c ****   memp_free(hmem->poolnr, hmem);
 339:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 340:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 341:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else /* MEM_USE_POOLS */
 342:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* lwIP replacement for your libc malloc() */
 343:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 344:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 345:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * The heap is made up as a list of structs of this type.
 346:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * This does not have to be aligned since for getting its size,
 347:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * we only use the macro SIZEOF_STRUCT_MEM, which automatically aligns.
 348:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 349:Middlewares/Third_Party/LwIP/src/core/mem.c **** struct mem {
 350:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /** index (-> ram[next]) of the next struct */
 351:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t next;
 352:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /** index (-> ram[prev]) of the previous struct */
 353:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t prev;
 354:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /** 1: this area is used; 0: this area is unused */
 355:Middlewares/Third_Party/LwIP/src/core/mem.c ****   u8_t used;
 356:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 357:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /** this keeps track of the user allocation size for guard checks */
 358:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t user_size;
 359:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 360:Middlewares/Third_Party/LwIP/src/core/mem.c **** };
 361:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 362:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** All allocated blocks will be MIN_SIZE bytes big, at least!
 363:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * MIN_SIZE can be overridden to suit your needs. Smaller values save space,
 364:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * larger values could prevent too small blocks to fragment the RAM too much. */
 365:Middlewares/Third_Party/LwIP/src/core/mem.c **** #ifndef MIN_SIZE
 366:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MIN_SIZE             12
 367:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MIN_SIZE */
 368:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* some alignment macros: we define them here for better source code layout */
 369:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MIN_SIZE_ALIGNED     LWIP_MEM_ALIGN_SIZE(MIN_SIZE)
 370:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define SIZEOF_STRUCT_MEM    LWIP_MEM_ALIGN_SIZE(sizeof(struct mem))
 371:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SIZE_ALIGNED     LWIP_MEM_ALIGN_SIZE(MEM_SIZE)
 372:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 373:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** If you want to relocate the heap to external memory, simply define
ARM GAS  /tmp/ccwyId6Y.s 			page 8


 374:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * LWIP_RAM_HEAP_POINTER as a void-pointer to that location.
 375:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * If so, make sure the memory at that location is big enough (see below on
 376:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * how that space is calculated). */
 377:Middlewares/Third_Party/LwIP/src/core/mem.c **** #ifndef LWIP_RAM_HEAP_POINTER
 378:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** the heap. we need one struct mem at the end and some room for alignment */
 379:Middlewares/Third_Party/LwIP/src/core/mem.c **** LWIP_DECLARE_MEMORY_ALIGNED(ram_heap, MEM_SIZE_ALIGNED + (2U * SIZEOF_STRUCT_MEM));
 380:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_RAM_HEAP_POINTER ram_heap
 381:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_RAM_HEAP_POINTER */
 382:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 383:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** pointer to the heap (ram_heap): for alignment, ram is now a pointer instead of an array */
 384:Middlewares/Third_Party/LwIP/src/core/mem.c **** static u8_t *ram;
 385:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** the last entry, always unused! */
 386:Middlewares/Third_Party/LwIP/src/core/mem.c **** static struct mem *ram_end;
 387:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 388:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** concurrent access protection */
 389:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if !NO_SYS
 390:Middlewares/Third_Party/LwIP/src/core/mem.c **** static sys_mutex_t mem_mutex;
 391:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 392:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 393:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 394:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 395:Middlewares/Third_Party/LwIP/src/core/mem.c **** static volatile u8_t mem_free_count;
 396:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 397:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* Allow mem_free from other (e.g. interrupt) context */
 398:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_FREE_DECL_PROTECT()  SYS_ARCH_DECL_PROTECT(lev_free)
 399:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_FREE_PROTECT()       SYS_ARCH_PROTECT(lev_free)
 400:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_FREE_UNPROTECT()     SYS_ARCH_UNPROTECT(lev_free)
 401:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ALLOC_DECL_PROTECT() SYS_ARCH_DECL_PROTECT(lev_alloc)
 402:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ALLOC_PROTECT()      SYS_ARCH_PROTECT(lev_alloc)
 403:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ALLOC_UNPROTECT()    SYS_ARCH_UNPROTECT(lev_alloc)
 404:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_LFREE_VOLATILE       volatile
 405:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 406:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 407:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 408:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* Protect the heap only by using a mutex */
 409:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_FREE_DECL_PROTECT()
 410:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_FREE_PROTECT()    sys_mutex_lock(&mem_mutex)
 411:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_FREE_UNPROTECT()  sys_mutex_unlock(&mem_mutex)
 412:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* mem_malloc is protected using mutex AND LWIP_MEM_ALLOC_PROTECT */
 413:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ALLOC_DECL_PROTECT()
 414:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ALLOC_PROTECT()
 415:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_ALLOC_UNPROTECT()
 416:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define LWIP_MEM_LFREE_VOLATILE
 417:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 418:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 419:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 420:Middlewares/Third_Party/LwIP/src/core/mem.c **** /** pointer to the lowest free block, this is used for faster search */
 421:Middlewares/Third_Party/LwIP/src/core/mem.c **** static struct mem * LWIP_MEM_LFREE_VOLATILE lfree;
 422:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 423:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_CHECK
 424:Middlewares/Third_Party/LwIP/src/core/mem.c **** static void mem_sanity(void);
 425:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SANITY() mem_sanity()
 426:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else
 427:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define MEM_SANITY()
 428:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 429:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 430:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
ARM GAS  /tmp/ccwyId6Y.s 			page 9


 431:Middlewares/Third_Party/LwIP/src/core/mem.c **** static void
 432:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_overflow_init_element(struct mem *mem, mem_size_t user_size)
 433:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 434:Middlewares/Third_Party/LwIP/src/core/mem.c ****   void *p = (u8_t *)mem + SIZEOF_STRUCT_MEM + MEM_SANITY_OFFSET;
 435:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->user_size = user_size;
 436:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_overflow_init_raw(p, user_size);
 437:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 438:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 439:Middlewares/Third_Party/LwIP/src/core/mem.c **** static void
 440:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_overflow_check_element(struct mem *mem)
 441:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 442:Middlewares/Third_Party/LwIP/src/core/mem.c ****   void *p = (u8_t *)mem + SIZEOF_STRUCT_MEM + MEM_SANITY_OFFSET;
 443:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_overflow_check_raw(p, mem->user_size, "heap", "");
 444:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 445:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else /* MEM_OVERFLOW_CHECK */
 446:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define mem_overflow_init_element(mem, size)
 447:Middlewares/Third_Party/LwIP/src/core/mem.c **** #define mem_overflow_check_element(mem)
 448:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_OVERFLOW_CHECK */
 449:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 450:Middlewares/Third_Party/LwIP/src/core/mem.c **** static struct mem *
 451:Middlewares/Third_Party/LwIP/src/core/mem.c **** ptr_to_mem(mem_size_t ptr)
 452:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
  28              		.loc 1 452 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 453:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return (struct mem *)(void *)&ram[ptr];
  33              		.loc 1 453 3 view .LVU1
  34              		.loc 1 453 32 is_stmt 0 view .LVU2
  35 0000 014B     		ldr	r3, .L2
  36 0002 1B68     		ldr	r3, [r3]
 454:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
  37              		.loc 1 454 1 view .LVU3
  38 0004 1844     		add	r0, r0, r3
  39              	.LVL1:
  40              		.loc 1 454 1 view .LVU4
  41 0006 7047     		bx	lr
  42              	.L3:
  43              		.align	2
  44              	.L2:
  45 0008 00000000 		.word	ram
  46              		.cfi_endproc
  47              	.LFE174:
  49              		.section	.text.mem_to_ptr,"ax",%progbits
  50              		.align	1
  51              		.syntax unified
  52              		.thumb
  53              		.thumb_func
  55              	mem_to_ptr:
  56              	.LVL2:
  57              	.LFB175:
 455:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 456:Middlewares/Third_Party/LwIP/src/core/mem.c **** static mem_size_t
 457:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_to_ptr(void *mem)
 458:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
  58              		.loc 1 458 1 is_stmt 1 view -0
ARM GAS  /tmp/ccwyId6Y.s 			page 10


  59              		.cfi_startproc
  60              		@ args = 0, pretend = 0, frame = 0
  61              		@ frame_needed = 0, uses_anonymous_args = 0
  62              		@ link register save eliminated.
 459:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return (mem_size_t)((u8_t *)mem - ram);
  63              		.loc 1 459 3 view .LVU6
  64              		.loc 1 459 35 is_stmt 0 view .LVU7
  65 0000 024B     		ldr	r3, .L5
  66 0002 1B68     		ldr	r3, [r3]
  67 0004 C01A     		subs	r0, r0, r3
  68              	.LVL3:
 460:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
  69              		.loc 1 460 1 view .LVU8
  70 0006 80B2     		uxth	r0, r0
  71 0008 7047     		bx	lr
  72              	.L6:
  73 000a 00BF     		.align	2
  74              	.L5:
  75 000c 00000000 		.word	ram
  76              		.cfi_endproc
  77              	.LFE175:
  79              		.section	.text.mem_link_valid,"ax",%progbits
  80              		.align	1
  81              		.syntax unified
  82              		.thumb
  83              		.thumb_func
  85              	mem_link_valid:
  86              	.LVL4:
  87              	.LFB178:
 461:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 462:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 463:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * "Plug holes" by combining adjacent empty struct mems.
 464:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * After this function is through, there should not exist
 465:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * one empty struct mem pointing to another empty struct mem.
 466:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 467:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param mem this points to a struct mem which just has been freed
 468:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @internal this function is only called by mem_free() and mem_trim()
 469:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 470:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * This assumes access to the heap is protected by the calling function
 471:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * already.
 472:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 473:Middlewares/Third_Party/LwIP/src/core/mem.c **** static void
 474:Middlewares/Third_Party/LwIP/src/core/mem.c **** plug_holes(struct mem *mem)
 475:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 476:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *nmem;
 477:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *pmem;
 478:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem >= ram", (u8_t *)mem >= ram);
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 481:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 482:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 483:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* plug hole forward */
 484:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->next <= MEM_SIZE_ALIGNED", mem->next <= MEM_SIZE_ALIGNED);
 485:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 486:Middlewares/Third_Party/LwIP/src/core/mem.c ****   nmem = ptr_to_mem(mem->next);
 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (mem != nmem && nmem->used == 0 && (u8_t *)nmem != (u8_t *)ram_end) {
 488:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
ARM GAS  /tmp/ccwyId6Y.s 			page 11


 489:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (lfree == nmem) {
 490:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem;
 491:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 492:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem->next = nmem->next;
 493:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (nmem->next != MEM_SIZE_ALIGNED) {
 494:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(nmem->next)->prev = mem_to_ptr(mem);
 495:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 496:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 497:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 498:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* plug hole backward */
 499:Middlewares/Third_Party/LwIP/src/core/mem.c ****   pmem = ptr_to_mem(mem->prev);
 500:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (pmem != mem && pmem->used == 0) {
 501:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->prev is unused, combine mem and mem->prev */
 502:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (lfree == mem) {
 503:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = pmem;
 504:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 505:Middlewares/Third_Party/LwIP/src/core/mem.c ****     pmem->next = mem->next;
 506:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem->next != MEM_SIZE_ALIGNED) {
 507:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem->next)->prev = mem_to_ptr(pmem);
 508:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 509:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 510:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 511:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 512:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 513:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Zero the heap and initialize start, end and lowest-free
 514:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 515:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
 516:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_init(void)
 517:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 518:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem;
 519:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 520:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("Sanity check alignment",
 521:Middlewares/Third_Party/LwIP/src/core/mem.c ****               (SIZEOF_STRUCT_MEM & (MEM_ALIGNMENT - 1)) == 0);
 522:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 523:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* align the heap */
 524:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram = (u8_t *)LWIP_MEM_ALIGN(LWIP_RAM_HEAP_POINTER);
 525:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the start of the heap */
 526:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem = (struct mem *)(void *)ram;
 527:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->next = MEM_SIZE_ALIGNED;
 528:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->prev = 0;
 529:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->used = 0;
 530:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the end of the heap */
 531:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end = ptr_to_mem(MEM_SIZE_ALIGNED);
 532:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->used = 1;
 533:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->next = MEM_SIZE_ALIGNED;
 534:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->prev = MEM_SIZE_ALIGNED;
 535:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_SANITY();
 536:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 537:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the lowest-free pointer to the start of the heap */
 538:Middlewares/Third_Party/LwIP/src/core/mem.c ****   lfree = (struct mem *)(void *)ram;
 539:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 540:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_STATS_AVAIL(avail, MEM_SIZE_ALIGNED);
 541:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 542:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (sys_mutex_new(&mem_mutex) != ERR_OK) {
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("failed to create mem_mutex", 0);
 544:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 545:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
ARM GAS  /tmp/ccwyId6Y.s 			page 12


 546:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 547:Middlewares/Third_Party/LwIP/src/core/mem.c **** /* Check if a struct mem is correctly linked.
 548:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * If not, double-free is a possible reason.
 549:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 550:Middlewares/Third_Party/LwIP/src/core/mem.c **** static int
 551:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_link_valid(struct mem *mem)
 552:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
  88              		.loc 1 552 1 is_stmt 1 view -0
  89              		.cfi_startproc
  90              		@ args = 0, pretend = 0, frame = 0
  91              		@ frame_needed = 0, uses_anonymous_args = 0
  92              		.loc 1 552 1 is_stmt 0 view .LVU10
  93 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
  94              	.LCFI0:
  95              		.cfi_def_cfa_offset 24
  96              		.cfi_offset 3, -24
  97              		.cfi_offset 4, -20
  98              		.cfi_offset 5, -16
  99              		.cfi_offset 6, -12
 100              		.cfi_offset 7, -8
 101              		.cfi_offset 14, -4
 102 0002 0446     		mov	r4, r0
 553:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *nmem, *pmem;
 103              		.loc 1 553 3 is_stmt 1 view .LVU11
 554:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t rmem_idx;
 104              		.loc 1 554 3 view .LVU12
 555:Middlewares/Third_Party/LwIP/src/core/mem.c ****   rmem_idx = mem_to_ptr(mem);
 105              		.loc 1 555 3 view .LVU13
 106              		.loc 1 555 14 is_stmt 0 view .LVU14
 107 0004 FFF7FEFF 		bl	mem_to_ptr
 108              	.LVL5:
 109              		.loc 1 555 14 view .LVU15
 110 0008 0746     		mov	r7, r0
 111              	.LVL6:
 556:Middlewares/Third_Party/LwIP/src/core/mem.c ****   nmem = ptr_to_mem(mem->next);
 112              		.loc 1 556 3 is_stmt 1 view .LVU16
 113              		.loc 1 556 10 is_stmt 0 view .LVU17
 114 000a 2588     		ldrh	r5, [r4]
 115 000c 2846     		mov	r0, r5
 116 000e FFF7FEFF 		bl	ptr_to_mem
 117              	.LVL7:
 118 0012 0646     		mov	r6, r0
 119              	.LVL8:
 557:Middlewares/Third_Party/LwIP/src/core/mem.c ****   pmem = ptr_to_mem(mem->prev);
 120              		.loc 1 557 3 is_stmt 1 view .LVU18
 121              		.loc 1 557 10 is_stmt 0 view .LVU19
 122 0014 6488     		ldrh	r4, [r4, #2]
 123              	.LVL9:
 124              		.loc 1 557 10 view .LVU20
 125 0016 2046     		mov	r0, r4
 126              	.LVL10:
 127              		.loc 1 557 10 view .LVU21
 128 0018 FFF7FEFF 		bl	ptr_to_mem
 129              	.LVL11:
 558:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((mem->next > MEM_SIZE_ALIGNED) || (mem->prev > MEM_SIZE_ALIGNED) ||
 130              		.loc 1 558 3 is_stmt 1 view .LVU22
 131              		.loc 1 558 6 is_stmt 0 view .LVU23
ARM GAS  /tmp/ccwyId6Y.s 			page 13


 132 001c B5F5C86F 		cmp	r5, #1600
 133 0020 10D8     		bhi	.L10
 134              		.loc 1 558 38 discriminator 1 view .LVU24
 135 0022 B4F5C86F 		cmp	r4, #1600
 136 0026 0FD8     		bhi	.L11
 137              		.loc 1 558 72 discriminator 2 view .LVU25
 138 0028 BC42     		cmp	r4, r7
 139 002a 02D0     		beq	.L9
 559:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ((mem->prev != rmem_idx) && (pmem->next != rmem_idx)) ||
 140              		.loc 1 559 40 view .LVU26
 141 002c 0388     		ldrh	r3, [r0]
 142              		.loc 1 559 32 view .LVU27
 143 002e BB42     		cmp	r3, r7
 144 0030 0CD1     		bne	.L12
 145              	.L9:
 560:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ((nmem != ram_end) && (nmem->prev != rmem_idx))) {
 146              		.loc 1 560 14 view .LVU28
 147 0032 094B     		ldr	r3, .L16
 148 0034 1B68     		ldr	r3, [r3]
 559:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ((mem->prev != rmem_idx) && (pmem->next != rmem_idx)) ||
 149              		.loc 1 559 61 discriminator 1 view .LVU29
 150 0036 B342     		cmp	r3, r6
 151 0038 0AD0     		beq	.L13
 152              		.loc 1 560 34 view .LVU30
 153 003a 7388     		ldrh	r3, [r6, #2]
 154              		.loc 1 560 26 view .LVU31
 155 003c BB42     		cmp	r3, r7
 156 003e 09D1     		bne	.L14
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 562:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 563:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return 1;
 157              		.loc 1 563 10 view .LVU32
 158 0040 0120     		movs	r0, #1
 159              	.LVL12:
 160              		.loc 1 563 10 view .LVU33
 161 0042 00E0     		b	.L7
 162              	.LVL13:
 163              	.L10:
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 164              		.loc 1 561 12 view .LVU34
 165 0044 0020     		movs	r0, #0
 166              	.LVL14:
 167              	.L7:
 564:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 168              		.loc 1 564 1 view .LVU35
 169 0046 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 170              	.LVL15:
 171              	.L11:
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 172              		.loc 1 561 12 view .LVU36
 173 0048 0020     		movs	r0, #0
 174              	.LVL16:
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 175              		.loc 1 561 12 view .LVU37
 176 004a FCE7     		b	.L7
 177              	.LVL17:
 178              	.L12:
ARM GAS  /tmp/ccwyId6Y.s 			page 14


 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 179              		.loc 1 561 12 view .LVU38
 180 004c 0020     		movs	r0, #0
 181              	.LVL18:
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 182              		.loc 1 561 12 view .LVU39
 183 004e FAE7     		b	.L7
 184              	.LVL19:
 185              	.L13:
 563:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 186              		.loc 1 563 10 view .LVU40
 187 0050 0120     		movs	r0, #1
 188              	.LVL20:
 563:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 189              		.loc 1 563 10 view .LVU41
 190 0052 F8E7     		b	.L7
 191              	.LVL21:
 192              	.L14:
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 193              		.loc 1 561 12 view .LVU42
 194 0054 0020     		movs	r0, #0
 195              	.LVL22:
 561:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return 0;
 196              		.loc 1 561 12 view .LVU43
 197 0056 F6E7     		b	.L7
 198              	.L17:
 199              		.align	2
 200              	.L16:
 201 0058 00000000 		.word	ram_end
 202              		.cfi_endproc
 203              	.LFE178:
 205              		.section	.rodata.plug_holes.str1.4,"aMS",%progbits,1
 206              		.align	2
 207              	.LC0:
 208 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/mem.c\000"
 208      6C657761 
 208      7265732F 
 208      54686972 
 208      645F5061 
 209              		.align	2
 210              	.LC1:
 211 002c 706C7567 		.ascii	"plug_holes: mem >= ram\000"
 211      5F686F6C 
 211      65733A20 
 211      6D656D20 
 211      3E3D2072 
 212 0043 00       		.align	2
 213              	.LC2:
 214 0044 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 214      7274696F 
 214      6E202225 
 214      73222066 
 214      61696C65 
 215              		.align	2
 216              	.LC3:
 217 006c 706C7567 		.ascii	"plug_holes: mem < ram_end\000"
 217      5F686F6C 
ARM GAS  /tmp/ccwyId6Y.s 			page 15


 217      65733A20 
 217      6D656D20 
 217      3C207261 
 218 0086 0000     		.align	2
 219              	.LC4:
 220 0088 706C7567 		.ascii	"plug_holes: mem->used == 0\000"
 220      5F686F6C 
 220      65733A20 
 220      6D656D2D 
 220      3E757365 
 221 00a3 00       		.align	2
 222              	.LC5:
 223 00a4 706C7567 		.ascii	"plug_holes: mem->next <= MEM_SIZE_ALIGNED\000"
 223      5F686F6C 
 223      65733A20 
 223      6D656D2D 
 223      3E6E6578 
 224              		.section	.text.plug_holes,"ax",%progbits
 225              		.align	1
 226              		.syntax unified
 227              		.thumb
 228              		.thumb_func
 230              	plug_holes:
 231              	.LVL23:
 232              	.LFB176:
 475:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *nmem;
 233              		.loc 1 475 1 is_stmt 1 view -0
 234              		.cfi_startproc
 235              		@ args = 0, pretend = 0, frame = 0
 236              		@ frame_needed = 0, uses_anonymous_args = 0
 475:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *nmem;
 237              		.loc 1 475 1 is_stmt 0 view .LVU45
 238 0000 38B5     		push	{r3, r4, r5, lr}
 239              	.LCFI1:
 240              		.cfi_def_cfa_offset 16
 241              		.cfi_offset 3, -16
 242              		.cfi_offset 4, -12
 243              		.cfi_offset 5, -8
 244              		.cfi_offset 14, -4
 245 0002 0446     		mov	r4, r0
 476:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *pmem;
 246              		.loc 1 476 3 is_stmt 1 view .LVU46
 477:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 247              		.loc 1 477 3 view .LVU47
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 248              		.loc 1 479 3 view .LVU48
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 249              		.loc 1 479 3 view .LVU49
 250 0004 344B     		ldr	r3, .L34
 251 0006 1B68     		ldr	r3, [r3]
 252 0008 8342     		cmp	r3, r0
 253 000a 3ED8     		bhi	.L28
 254              	.LVL24:
 255              	.L19:
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 256              		.loc 1 479 3 discriminator 3 view .LVU50
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
ARM GAS  /tmp/ccwyId6Y.s 			page 16


 257              		.loc 1 479 3 discriminator 3 view .LVU51
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 258              		.loc 1 480 3 view .LVU52
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 259              		.loc 1 480 3 view .LVU53
 260 000c 334B     		ldr	r3, .L34+4
 261 000e 1B68     		ldr	r3, [r3]
 262 0010 A342     		cmp	r3, r4
 263 0012 42D9     		bls	.L29
 264              	.L20:
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 265              		.loc 1 480 3 discriminator 3 view .LVU54
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 266              		.loc 1 480 3 discriminator 3 view .LVU55
 481:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 267              		.loc 1 481 3 view .LVU56
 481:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 268              		.loc 1 481 3 view .LVU57
 269 0014 2379     		ldrb	r3, [r4, #4]	@ zero_extendqisi2
 270 0016 002B     		cmp	r3, #0
 271 0018 47D1     		bne	.L30
 272              	.L21:
 481:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 273              		.loc 1 481 3 discriminator 3 view .LVU58
 481:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 274              		.loc 1 481 3 discriminator 3 view .LVU59
 484:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 275              		.loc 1 484 3 view .LVU60
 484:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 276              		.loc 1 484 3 view .LVU61
 277 001a 2388     		ldrh	r3, [r4]
 278 001c B3F5C86F 		cmp	r3, #1600
 279 0020 4BD8     		bhi	.L31
 280              	.L22:
 484:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 281              		.loc 1 484 3 discriminator 3 view .LVU62
 484:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 282              		.loc 1 484 3 discriminator 3 view .LVU63
 486:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (mem != nmem && nmem->used == 0 && (u8_t *)nmem != (u8_t *)ram_end) {
 283              		.loc 1 486 3 view .LVU64
 486:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (mem != nmem && nmem->used == 0 && (u8_t *)nmem != (u8_t *)ram_end) {
 284              		.loc 1 486 10 is_stmt 0 view .LVU65
 285 0022 2088     		ldrh	r0, [r4]
 286 0024 FFF7FEFF 		bl	ptr_to_mem
 287              	.LVL25:
 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
 288              		.loc 1 487 3 is_stmt 1 view .LVU66
 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
 289              		.loc 1 487 6 is_stmt 0 view .LVU67
 290 0028 8442     		cmp	r4, r0
 291 002a 15D0     		beq	.L23
 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
 292              		.loc 1 487 26 discriminator 1 view .LVU68
 293 002c 0379     		ldrb	r3, [r0, #4]	@ zero_extendqisi2
 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
 294              		.loc 1 487 19 discriminator 1 view .LVU69
 295 002e 9BB9     		cbnz	r3, .L23
ARM GAS  /tmp/ccwyId6Y.s 			page 17


 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
 296              		.loc 1 487 57 discriminator 2 view .LVU70
 297 0030 2A4B     		ldr	r3, .L34+4
 298 0032 1B68     		ldr	r3, [r3]
 487:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->next is unused and not end of ram, combine mem and mem->next */
 299              		.loc 1 487 38 discriminator 2 view .LVU71
 300 0034 8342     		cmp	r3, r0
 301 0036 0FD0     		beq	.L23
 489:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem;
 302              		.loc 1 489 5 is_stmt 1 view .LVU72
 489:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem;
 303              		.loc 1 489 15 is_stmt 0 view .LVU73
 304 0038 294B     		ldr	r3, .L34+8
 305 003a 1B68     		ldr	r3, [r3]
 489:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem;
 306              		.loc 1 489 8 view .LVU74
 307 003c 8342     		cmp	r3, r0
 308 003e 44D0     		beq	.L32
 309              	.L24:
 492:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (nmem->next != MEM_SIZE_ALIGNED) {
 310              		.loc 1 492 5 is_stmt 1 view .LVU75
 492:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (nmem->next != MEM_SIZE_ALIGNED) {
 311              		.loc 1 492 21 is_stmt 0 view .LVU76
 312 0040 0088     		ldrh	r0, [r0]
 313              	.LVL26:
 492:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (nmem->next != MEM_SIZE_ALIGNED) {
 314              		.loc 1 492 15 view .LVU77
 315 0042 2080     		strh	r0, [r4]	@ movhi
 493:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(nmem->next)->prev = mem_to_ptr(mem);
 316              		.loc 1 493 5 is_stmt 1 view .LVU78
 493:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(nmem->next)->prev = mem_to_ptr(mem);
 317              		.loc 1 493 8 is_stmt 0 view .LVU79
 318 0044 B0F5C86F 		cmp	r0, #1600
 319 0048 06D0     		beq	.L23
 494:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 320              		.loc 1 494 7 is_stmt 1 view .LVU80
 321 004a FFF7FEFF 		bl	ptr_to_mem
 322              	.LVL27:
 323 004e 0546     		mov	r5, r0
 494:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 324              		.loc 1 494 38 is_stmt 0 discriminator 1 view .LVU81
 325 0050 2046     		mov	r0, r4
 326 0052 FFF7FEFF 		bl	mem_to_ptr
 327              	.LVL28:
 494:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 328              		.loc 1 494 36 discriminator 2 view .LVU82
 329 0056 6880     		strh	r0, [r5, #2]	@ movhi
 330              	.L23:
 499:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (pmem != mem && pmem->used == 0) {
 331              		.loc 1 499 3 is_stmt 1 view .LVU83
 499:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (pmem != mem && pmem->used == 0) {
 332              		.loc 1 499 10 is_stmt 0 view .LVU84
 333 0058 6088     		ldrh	r0, [r4, #2]
 334 005a FFF7FEFF 		bl	ptr_to_mem
 335              	.LVL29:
 336 005e 0546     		mov	r5, r0
 337              	.LVL30:
ARM GAS  /tmp/ccwyId6Y.s 			page 18


 500:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->prev is unused, combine mem and mem->prev */
 338              		.loc 1 500 3 is_stmt 1 view .LVU85
 500:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->prev is unused, combine mem and mem->prev */
 339              		.loc 1 500 6 is_stmt 0 view .LVU86
 340 0060 8442     		cmp	r4, r0
 341 0062 11D0     		beq	.L18
 500:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->prev is unused, combine mem and mem->prev */
 342              		.loc 1 500 26 discriminator 1 view .LVU87
 343 0064 0379     		ldrb	r3, [r0, #4]	@ zero_extendqisi2
 500:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if mem->prev is unused, combine mem and mem->prev */
 344              		.loc 1 500 19 discriminator 1 view .LVU88
 345 0066 7BB9     		cbnz	r3, .L18
 502:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = pmem;
 346              		.loc 1 502 5 is_stmt 1 view .LVU89
 502:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = pmem;
 347              		.loc 1 502 15 is_stmt 0 view .LVU90
 348 0068 1D4B     		ldr	r3, .L34+8
 349 006a 1B68     		ldr	r3, [r3]
 502:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = pmem;
 350              		.loc 1 502 8 view .LVU91
 351 006c A342     		cmp	r3, r4
 352 006e 2FD0     		beq	.L33
 353              	.L26:
 505:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem->next != MEM_SIZE_ALIGNED) {
 354              		.loc 1 505 5 is_stmt 1 view .LVU92
 505:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem->next != MEM_SIZE_ALIGNED) {
 355              		.loc 1 505 21 is_stmt 0 view .LVU93
 356 0070 2088     		ldrh	r0, [r4]
 357              	.LVL31:
 505:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem->next != MEM_SIZE_ALIGNED) {
 358              		.loc 1 505 16 view .LVU94
 359 0072 2880     		strh	r0, [r5]	@ movhi
 506:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem->next)->prev = mem_to_ptr(pmem);
 360              		.loc 1 506 5 is_stmt 1 view .LVU95
 506:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem->next)->prev = mem_to_ptr(pmem);
 361              		.loc 1 506 8 is_stmt 0 view .LVU96
 362 0074 B0F5C86F 		cmp	r0, #1600
 363 0078 06D0     		beq	.L18
 507:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 364              		.loc 1 507 7 is_stmt 1 view .LVU97
 365 007a FFF7FEFF 		bl	ptr_to_mem
 366              	.LVL32:
 367 007e 0446     		mov	r4, r0
 368              	.LVL33:
 507:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 369              		.loc 1 507 37 is_stmt 0 discriminator 1 view .LVU98
 370 0080 2846     		mov	r0, r5
 371 0082 FFF7FEFF 		bl	mem_to_ptr
 372              	.LVL34:
 507:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 373              		.loc 1 507 35 discriminator 2 view .LVU99
 374 0086 6080     		strh	r0, [r4, #2]	@ movhi
 375              	.L18:
 510:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 376              		.loc 1 510 1 view .LVU100
 377 0088 38BD     		pop	{r3, r4, r5, pc}
 378              	.LVL35:
ARM GAS  /tmp/ccwyId6Y.s 			page 19


 379              	.L28:
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 380              		.loc 1 479 3 is_stmt 1 discriminator 1 view .LVU101
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 381              		.loc 1 479 3 discriminator 1 view .LVU102
 382 008a 164B     		ldr	r3, .L34+12
 383 008c 40F2DF12 		movw	r2, #479
 384 0090 1549     		ldr	r1, .L34+16
 385 0092 1648     		ldr	r0, .L34+20
 386              	.LVL36:
 479:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem < ram_end", (u8_t *)mem < (u8_t *)ram_end);
 387              		.loc 1 479 3 is_stmt 0 discriminator 1 view .LVU103
 388 0094 FFF7FEFF 		bl	printf
 389              	.LVL37:
 390 0098 B8E7     		b	.L19
 391              	.L29:
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 392              		.loc 1 480 3 is_stmt 1 discriminator 1 view .LVU104
 480:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("plug_holes: mem->used == 0", mem->used == 0);
 393              		.loc 1 480 3 discriminator 1 view .LVU105
 394 009a 124B     		ldr	r3, .L34+12
 395 009c 4FF4F072 		mov	r2, #480
 396 00a0 1349     		ldr	r1, .L34+24
 397 00a2 1248     		ldr	r0, .L34+20
 398 00a4 FFF7FEFF 		bl	printf
 399              	.LVL38:
 400 00a8 B4E7     		b	.L20
 401              	.L30:
 481:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 402              		.loc 1 481 3 discriminator 1 view .LVU106
 481:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 403              		.loc 1 481 3 discriminator 1 view .LVU107
 404 00aa 0E4B     		ldr	r3, .L34+12
 405 00ac 40F2E112 		movw	r2, #481
 406 00b0 1049     		ldr	r1, .L34+28
 407 00b2 0E48     		ldr	r0, .L34+20
 408 00b4 FFF7FEFF 		bl	printf
 409              	.LVL39:
 410 00b8 AFE7     		b	.L21
 411              	.L31:
 484:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 412              		.loc 1 484 3 discriminator 1 view .LVU108
 484:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 413              		.loc 1 484 3 discriminator 1 view .LVU109
 414 00ba 0A4B     		ldr	r3, .L34+12
 415 00bc 4FF4F272 		mov	r2, #484
 416 00c0 0D49     		ldr	r1, .L34+32
 417 00c2 0A48     		ldr	r0, .L34+20
 418 00c4 FFF7FEFF 		bl	printf
 419              	.LVL40:
 420 00c8 ABE7     		b	.L22
 421              	.LVL41:
 422              	.L32:
 490:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 423              		.loc 1 490 7 view .LVU110
 490:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 424              		.loc 1 490 13 is_stmt 0 view .LVU111
ARM GAS  /tmp/ccwyId6Y.s 			page 20


 425 00ca 054B     		ldr	r3, .L34+8
 426 00cc 1C60     		str	r4, [r3]
 427 00ce B7E7     		b	.L24
 428              	.LVL42:
 429              	.L33:
 503:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 430              		.loc 1 503 7 is_stmt 1 view .LVU112
 503:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 431              		.loc 1 503 13 is_stmt 0 view .LVU113
 432 00d0 034B     		ldr	r3, .L34+8
 433 00d2 1860     		str	r0, [r3]
 434 00d4 CCE7     		b	.L26
 435              	.L35:
 436 00d6 00BF     		.align	2
 437              	.L34:
 438 00d8 00000000 		.word	ram
 439 00dc 00000000 		.word	ram_end
 440 00e0 00000000 		.word	lfree
 441 00e4 00000000 		.word	.LC0
 442 00e8 2C000000 		.word	.LC1
 443 00ec 44000000 		.word	.LC2
 444 00f0 6C000000 		.word	.LC3
 445 00f4 88000000 		.word	.LC4
 446 00f8 ******** 		.word	.LC5
 447              		.cfi_endproc
 448              	.LFE176:
 450              		.section	.rodata.mem_init.str1.4,"aMS",%progbits,1
 451              		.align	2
 452              	.LC6:
 453 0000 6661696C 		.ascii	"failed to create mem_mutex\000"
 453      65642074 
 453      6F206372 
 453      65617465 
 453      206D656D 
 454              		.section	.text.mem_init,"ax",%progbits
 455              		.align	1
 456              		.global	mem_init
 457              		.syntax unified
 458              		.thumb
 459              		.thumb_func
 461              	mem_init:
 462              	.LFB177:
 517:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem;
 463              		.loc 1 517 1 is_stmt 1 view -0
 464              		.cfi_startproc
 465              		@ args = 0, pretend = 0, frame = 0
 466              		@ frame_needed = 0, uses_anonymous_args = 0
 467 0000 38B5     		push	{r3, r4, r5, lr}
 468              	.LCFI2:
 469              		.cfi_def_cfa_offset 16
 470              		.cfi_offset 3, -16
 471              		.cfi_offset 4, -12
 472              		.cfi_offset 5, -8
 473              		.cfi_offset 14, -4
 518:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 474              		.loc 1 518 3 view .LVU115
 520:Middlewares/Third_Party/LwIP/src/core/mem.c ****               (SIZEOF_STRUCT_MEM & (MEM_ALIGNMENT - 1)) == 0);
ARM GAS  /tmp/ccwyId6Y.s 			page 21


 475              		.loc 1 520 3 view .LVU116
 520:Middlewares/Third_Party/LwIP/src/core/mem.c ****               (SIZEOF_STRUCT_MEM & (MEM_ALIGNMENT - 1)) == 0);
 476              		.loc 1 520 3 view .LVU117
 520:Middlewares/Third_Party/LwIP/src/core/mem.c ****               (SIZEOF_STRUCT_MEM & (MEM_ALIGNMENT - 1)) == 0);
 477              		.loc 1 520 3 discriminator 3 view .LVU118
 520:Middlewares/Third_Party/LwIP/src/core/mem.c ****               (SIZEOF_STRUCT_MEM & (MEM_ALIGNMENT - 1)) == 0);
 478              		.loc 1 520 3 discriminator 3 view .LVU119
 524:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the start of the heap */
 479              		.loc 1 524 3 view .LVU120
 524:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the start of the heap */
 480              		.loc 1 524 7 is_stmt 0 view .LVU121
 481 0002 114D     		ldr	r5, .L40
 482 0004 114B     		ldr	r3, .L40+4
 483 0006 2B60     		str	r3, [r5]
 526:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->next = MEM_SIZE_ALIGNED;
 484              		.loc 1 526 3 is_stmt 1 view .LVU122
 485              	.LVL43:
 527:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->prev = 0;
 486              		.loc 1 527 3 view .LVU123
 527:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->prev = 0;
 487              		.loc 1 527 13 is_stmt 0 view .LVU124
 488 0008 4FF4C864 		mov	r4, #1600
 489 000c 1C80     		strh	r4, [r3]	@ movhi
 528:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->used = 0;
 490              		.loc 1 528 3 is_stmt 1 view .LVU125
 528:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->used = 0;
 491              		.loc 1 528 13 is_stmt 0 view .LVU126
 492 000e 0022     		movs	r2, #0
 493 0010 5A80     		strh	r2, [r3, #2]	@ movhi
 529:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the end of the heap */
 494              		.loc 1 529 3 is_stmt 1 view .LVU127
 529:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* initialize the end of the heap */
 495              		.loc 1 529 13 is_stmt 0 view .LVU128
 496 0012 1A71     		strb	r2, [r3, #4]
 531:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->used = 1;
 497              		.loc 1 531 3 is_stmt 1 view .LVU129
 531:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->used = 1;
 498              		.loc 1 531 13 is_stmt 0 view .LVU130
 499 0014 2046     		mov	r0, r4
 500 0016 FFF7FEFF 		bl	ptr_to_mem
 501              	.LVL44:
 531:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->used = 1;
 502              		.loc 1 531 11 discriminator 1 view .LVU131
 503 001a 0D4B     		ldr	r3, .L40+8
 504 001c 1860     		str	r0, [r3]
 532:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->next = MEM_SIZE_ALIGNED;
 505              		.loc 1 532 3 is_stmt 1 view .LVU132
 532:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->next = MEM_SIZE_ALIGNED;
 506              		.loc 1 532 17 is_stmt 0 view .LVU133
 507 001e 0123     		movs	r3, #1
 508 0020 0371     		strb	r3, [r0, #4]
 533:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->prev = MEM_SIZE_ALIGNED;
 509              		.loc 1 533 3 is_stmt 1 view .LVU134
 533:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ram_end->prev = MEM_SIZE_ALIGNED;
 510              		.loc 1 533 17 is_stmt 0 view .LVU135
 511 0022 0480     		strh	r4, [r0]	@ movhi
 534:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_SANITY();
ARM GAS  /tmp/ccwyId6Y.s 			page 22


 512              		.loc 1 534 3 is_stmt 1 view .LVU136
 534:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_SANITY();
 513              		.loc 1 534 17 is_stmt 0 view .LVU137
 514 0024 4480     		strh	r4, [r0, #2]	@ movhi
 535:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 515              		.loc 1 535 15 is_stmt 1 view .LVU138
 538:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 516              		.loc 1 538 3 view .LVU139
 538:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 517              		.loc 1 538 9 is_stmt 0 view .LVU140
 518 0026 2A68     		ldr	r2, [r5]
 519 0028 0A4B     		ldr	r3, .L40+12
 520 002a 1A60     		str	r2, [r3]
 540:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 521              		.loc 1 540 43 is_stmt 1 view .LVU141
 542:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("failed to create mem_mutex", 0);
 522              		.loc 1 542 3 view .LVU142
 542:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("failed to create mem_mutex", 0);
 523              		.loc 1 542 7 is_stmt 0 view .LVU143
 524 002c 0A48     		ldr	r0, .L40+16
 525 002e FFF7FEFF 		bl	sys_mutex_new
 526              	.LVL45:
 542:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("failed to create mem_mutex", 0);
 527              		.loc 1 542 6 discriminator 1 view .LVU144
 528 0032 00B9     		cbnz	r0, .L39
 529              	.L36:
 545:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 530              		.loc 1 545 1 view .LVU145
 531 0034 38BD     		pop	{r3, r4, r5, pc}
 532              	.L39:
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 533              		.loc 1 543 5 is_stmt 1 view .LVU146
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 534              		.loc 1 543 5 view .LVU147
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 535              		.loc 1 543 5 discriminator 1 view .LVU148
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 536              		.loc 1 543 5 discriminator 1 view .LVU149
 537 0036 094B     		ldr	r3, .L40+20
 538 0038 40F21F22 		movw	r2, #543
 539 003c 0849     		ldr	r1, .L40+24
 540 003e 0948     		ldr	r0, .L40+28
 541 0040 FFF7FEFF 		bl	printf
 542              	.LVL46:
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 543              		.loc 1 543 5 discriminator 3 view .LVU150
 543:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 544              		.loc 1 543 5 discriminator 3 view .LVU151
 545:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 545              		.loc 1 545 1 is_stmt 0 view .LVU152
 546 0044 F6E7     		b	.L36
 547              	.L41:
 548 0046 00BF     		.align	2
 549              	.L40:
 550 0048 00000000 		.word	ram
 551 004c 00400030 		.word	805322752
 552 0050 00000000 		.word	ram_end
ARM GAS  /tmp/ccwyId6Y.s 			page 23


 553 0054 00000000 		.word	lfree
 554 0058 00000000 		.word	mem_mutex
 555 005c 00000000 		.word	.LC0
 556 0060 00000000 		.word	.LC6
 557 0064 44000000 		.word	.LC2
 558              		.cfi_endproc
 559              	.LFE177:
 561              		.section	.rodata.mem_free.str1.4,"aMS",%progbits,1
 562              		.align	2
 563              	.LC7:
 564 0000 6D656D5F 		.ascii	"mem_free: sanity check alignment\000"
 564      66726565 
 564      3A207361 
 564      6E697479 
 564      20636865 
 565 0021 000000   		.align	2
 566              	.LC8:
 567 0024 6D656D5F 		.ascii	"mem_free: illegal memory\000"
 567      66726565 
 567      3A20696C 
 567      6C656761 
 567      6C206D65 
 568 003d 000000   		.align	2
 569              	.LC9:
 570 0040 6D656D5F 		.ascii	"mem_free: illegal memory: double free\000"
 570      66726565 
 570      3A20696C 
 570      6C656761 
 570      6C206D65 
 571 0066 0000     		.align	2
 572              	.LC10:
 573 0068 6D656D5F 		.ascii	"mem_free: illegal memory: non-linked: double free\000"
 573      66726565 
 573      3A20696C 
 573      6C656761 
 573      6C206D65 
 574              		.section	.text.mem_free,"ax",%progbits
 575              		.align	1
 576              		.global	mem_free
 577              		.syntax unified
 578              		.thumb
 579              		.thumb_func
 581              	mem_free:
 582              	.LVL47:
 583              	.LFB179:
 565:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 566:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_SANITY_CHECK
 567:Middlewares/Third_Party/LwIP/src/core/mem.c **** static void
 568:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_sanity(void)
 569:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 570:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem;
 571:Middlewares/Third_Party/LwIP/src/core/mem.c ****   u8_t last_used;
 572:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 573:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* begin with first element here */
 574:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem = (struct mem *)ram;
 575:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element used valid", (mem->used == 0) || (mem->used == 1));
 576:Middlewares/Third_Party/LwIP/src/core/mem.c ****   last_used = mem->used;
ARM GAS  /tmp/ccwyId6Y.s 			page 24


 577:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element prev ptr valid", mem->prev == 0);
 578:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element next ptr valid", mem->next <= MEM_SIZE_ALIGNED);
 579:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element next ptr aligned", LWIP_MEM_ALIGN(ptr_to_mem(mem->next) == ptr_to_mem(m
 580:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 581:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* check all elements before the end of the heap */
 582:Middlewares/Third_Party/LwIP/src/core/mem.c ****   for (mem = ptr_to_mem(mem->next);
 583:Middlewares/Third_Party/LwIP/src/core/mem.c ****        ((u8_t *)mem > ram) && (mem < ram_end);
 584:Middlewares/Third_Party/LwIP/src/core/mem.c ****        mem = ptr_to_mem(mem->next)) {
 585:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("heap element aligned", LWIP_MEM_ALIGN(mem) == mem);
 586:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("heap element prev ptr valid", mem->prev <= MEM_SIZE_ALIGNED);
 587:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("heap element next ptr valid", mem->next <= MEM_SIZE_ALIGNED);
 588:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("heap element prev ptr aligned", LWIP_MEM_ALIGN(ptr_to_mem(mem->prev) == ptr_to_mem
 589:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("heap element next ptr aligned", LWIP_MEM_ALIGN(ptr_to_mem(mem->next) == ptr_to_mem
 590:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 591:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (last_used == 0) {
 592:Middlewares/Third_Party/LwIP/src/core/mem.c ****       /* 2 unused elements in a row? */
 593:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_ASSERT("heap element unused?", mem->used == 1);
 594:Middlewares/Third_Party/LwIP/src/core/mem.c ****     } else {
 595:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_ASSERT("heap element unused member", (mem->used == 0) || (mem->used == 1));
 596:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 597:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 598:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("heap element link valid", mem_link_valid(mem));
 599:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 600:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* used/unused altering */
 601:Middlewares/Third_Party/LwIP/src/core/mem.c ****     last_used = mem->used;
 602:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 603:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap end ptr sanity", mem == ptr_to_mem(MEM_SIZE_ALIGNED));
 604:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element used valid", mem->used == 1);
 605:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element prev ptr valid", mem->prev == MEM_SIZE_ALIGNED);
 606:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("heap element next ptr valid", mem->next == MEM_SIZE_ALIGNED);
 607:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 608:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_SANITY_CHECK */
 609:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 610:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 611:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Put a struct mem back on the heap
 612:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 613:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param rmem is the data portion of a struct mem as returned by a previous
 614:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *             call to mem_malloc()
 615:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 616:Middlewares/Third_Party/LwIP/src/core/mem.c **** void
 617:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_free(void *rmem)
 618:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 584              		.loc 1 618 1 is_stmt 1 view -0
 585              		.cfi_startproc
 586              		@ args = 0, pretend = 0, frame = 0
 587              		@ frame_needed = 0, uses_anonymous_args = 0
 619:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem;
 588              		.loc 1 619 3 view .LVU154
 620:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_FREE_DECL_PROTECT();
 589              		.loc 1 620 31 view .LVU155
 621:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 622:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (rmem == NULL) {
 590              		.loc 1 622 3 view .LVU156
 591              		.loc 1 622 6 is_stmt 0 view .LVU157
 592 0000 0028     		cmp	r0, #0
 593 0002 60D0     		beq	.L51
 618:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem;
ARM GAS  /tmp/ccwyId6Y.s 			page 25


 594              		.loc 1 618 1 view .LVU158
 595 0004 38B5     		push	{r3, r4, r5, lr}
 596              	.LCFI3:
 597              		.cfi_def_cfa_offset 16
 598              		.cfi_offset 3, -16
 599              		.cfi_offset 4, -12
 600              		.cfi_offset 5, -8
 601              		.cfi_offset 14, -4
 602 0006 0446     		mov	r4, r0
 623:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS, ("mem_free(p == NULL) was call
 624:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 625:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 626:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((((mem_ptr_t)rmem) & (MEM_ALIGNMENT - 1)) != 0) {
 603              		.loc 1 626 3 is_stmt 1 view .LVU159
 604              		.loc 1 626 6 is_stmt 0 view .LVU160
 605 0008 10F0030F 		tst	r0, #3
 606 000c 17D1     		bne	.L54
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: sanity check alignment");
 628:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 629:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(illegal);
 631:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 632:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 633:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 634:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* Get the corresponding struct mem: */
 635:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* cast through void* to get rid of alignment warnings */
 636:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem = (struct mem *)(void *)((u8_t *)rmem - (SIZEOF_STRUCT_MEM + MEM_SANITY_OFFSET));
 607              		.loc 1 636 3 is_stmt 1 view .LVU161
 608              		.loc 1 636 7 is_stmt 0 view .LVU162
 609 000e A0F10805 		sub	r5, r0, #8
 610              	.LVL48:
 637:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 638:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((u8_t *)mem < ram || (u8_t *)rmem + MIN_SIZE_ALIGNED > (u8_t *)ram_end) {
 611              		.loc 1 638 3 is_stmt 1 view .LVU163
 612              		.loc 1 638 19 is_stmt 0 view .LVU164
 613 0012 2D4B     		ldr	r3, .L57
 614 0014 1B68     		ldr	r3, [r3]
 615              		.loc 1 638 6 view .LVU165
 616 0016 AB42     		cmp	r3, r5
 617 0018 05D8     		bhi	.L45
 618              		.loc 1 638 41 discriminator 1 view .LVU166
 619 001a 00F10C03 		add	r3, r0, #12
 620              		.loc 1 638 62 discriminator 1 view .LVU167
 621 001e 2B4A     		ldr	r2, .L57+4
 622 0020 1268     		ldr	r2, [r2]
 623              		.loc 1 638 25 discriminator 1 view .LVU168
 624 0022 9342     		cmp	r3, r2
 625 0024 17D9     		bls	.L46
 626              	.L45:
 639:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory");
 627              		.loc 1 639 5 is_stmt 1 view .LVU169
 628              		.loc 1 639 5 view .LVU170
 629              		.loc 1 639 5 discriminator 1 view .LVU171
 630              		.loc 1 639 5 discriminator 1 view .LVU172
 631 0026 2A4B     		ldr	r3, .L57+8
 632 0028 40F27F22 		movw	r2, #639
 633 002c 2949     		ldr	r1, .L57+12
ARM GAS  /tmp/ccwyId6Y.s 			page 26


 634 002e 2A48     		ldr	r0, .L57+16
 635              	.LVL49:
 636              		.loc 1 639 5 is_stmt 0 discriminator 1 view .LVU173
 637 0030 FFF7FEFF 		bl	printf
 638              	.LVL50:
 639              		.loc 1 639 5 is_stmt 1 discriminator 3 view .LVU174
 640              		.loc 1 639 5 discriminator 3 view .LVU175
 640:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: illegal memory\n"));
 641              		.loc 1 640 83 view .LVU176
 641:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 642:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(illegal);
 642              		.loc 1 642 5 view .LVU177
 643              	.LBB2:
 644              		.loc 1 642 5 view .LVU178
 645              		.loc 1 642 5 view .LVU179
 646 0034 FFF7FEFF 		bl	sys_arch_protect
 647              	.LVL51:
 648              		.loc 1 642 5 discriminator 1 view .LVU180
 649              		.loc 1 642 5 discriminator 1 view .LVU181
 650 0038 FFF7FEFF 		bl	sys_arch_unprotect
 651              	.LVL52:
 652              		.loc 1 642 5 is_stmt 0 discriminator 1 view .LVU182
 653              	.LBE2:
 654              		.loc 1 642 5 is_stmt 1 discriminator 2 view .LVU183
 643:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 655              		.loc 1 643 5 view .LVU184
 656              	.L42:
 644:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 645:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 646:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_overflow_check_element(mem);
 647:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 648:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* protect the heap from concurrent access */
 649:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_FREE_PROTECT();
 650:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* mem has to be in a used state */
 651:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (!mem->used) {
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: double free");
 653:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 654:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: illegal memory: double free?\n"));
 655:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(illegal);
 657:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 658:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 659:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 660:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (!mem_link_valid(mem)) {
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: non-linked: double free");
 662:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 663:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: illegal memory: non-linked: double f
 664:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(illegal);
 666:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 667:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 668:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 669:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* mem is now unused. */
 670:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem->used = 0;
 671:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 672:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (mem < lfree) {
 673:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* the newly freed struct is now the lowest */
ARM GAS  /tmp/ccwyId6Y.s 			page 27


 674:Middlewares/Third_Party/LwIP/src/core/mem.c ****     lfree = mem;
 675:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 676:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 677:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_STATS_DEC_USED(used, mem->next - (mem_size_t)(((u8_t *)mem - ram)));
 678:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 679:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* finally, see if prev or next are free also */
 680:Middlewares/Third_Party/LwIP/src/core/mem.c ****   plug_holes(mem);
 681:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_SANITY();
 682:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 683:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_free_count = 1;
 684:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 685:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_FREE_UNPROTECT();
 686:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 657              		.loc 1 686 1 is_stmt 0 view .LVU185
 658 003c 38BD     		pop	{r3, r4, r5, pc}
 659              	.LVL53:
 660              	.L54:
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 661              		.loc 1 627 5 is_stmt 1 view .LVU186
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 662              		.loc 1 627 5 view .LVU187
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 663              		.loc 1 627 5 discriminator 1 view .LVU188
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 664              		.loc 1 627 5 discriminator 1 view .LVU189
 665 003e 244B     		ldr	r3, .L57+8
 666 0040 40F27322 		movw	r2, #627
 667 0044 2549     		ldr	r1, .L57+20
 668 0046 2448     		ldr	r0, .L57+16
 669              	.LVL54:
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 670              		.loc 1 627 5 is_stmt 0 discriminator 1 view .LVU190
 671 0048 FFF7FEFF 		bl	printf
 672              	.LVL55:
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 673              		.loc 1 627 5 is_stmt 1 discriminator 3 view .LVU191
 627:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: sanity check alignment\n"));
 674              		.loc 1 627 5 discriminator 3 view .LVU192
 628:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 675              		.loc 1 628 91 view .LVU193
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 676              		.loc 1 630 5 view .LVU194
 677              	.LBB3:
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 678              		.loc 1 630 5 view .LVU195
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 679              		.loc 1 630 5 view .LVU196
 680 004c FFF7FEFF 		bl	sys_arch_protect
 681              	.LVL56:
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 682              		.loc 1 630 5 discriminator 1 view .LVU197
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 683              		.loc 1 630 5 discriminator 1 view .LVU198
 684 0050 FFF7FEFF 		bl	sys_arch_unprotect
 685              	.LVL57:
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 686              		.loc 1 630 5 is_stmt 0 discriminator 1 view .LVU199
ARM GAS  /tmp/ccwyId6Y.s 			page 28


 687              	.LBE3:
 630:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 688              		.loc 1 630 5 is_stmt 1 discriminator 2 view .LVU200
 631:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 689              		.loc 1 631 5 view .LVU201
 690 0054 F2E7     		b	.L42
 691              	.LVL58:
 692              	.L46:
 649:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* mem has to be in a used state */
 693              		.loc 1 649 3 view .LVU202
 694 0056 2248     		ldr	r0, .L57+24
 695              	.LVL59:
 649:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* mem has to be in a used state */
 696              		.loc 1 649 3 is_stmt 0 view .LVU203
 697 0058 FFF7FEFF 		bl	sys_mutex_lock
 698              	.LVL60:
 651:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: double free");
 699              		.loc 1 651 3 is_stmt 1 view .LVU204
 651:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: double free");
 700              		.loc 1 651 11 is_stmt 0 view .LVU205
 701 005c 14F8043C 		ldrb	r3, [r4, #-4]	@ zero_extendqisi2
 651:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: double free");
 702              		.loc 1 651 6 view .LVU206
 703 0060 9BB1     		cbz	r3, .L55
 660:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: non-linked: double free");
 704              		.loc 1 660 3 is_stmt 1 view .LVU207
 660:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: non-linked: double free");
 705              		.loc 1 660 8 is_stmt 0 view .LVU208
 706 0062 2846     		mov	r0, r5
 707 0064 FFF7FEFF 		bl	mem_link_valid
 708              	.LVL61:
 660:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_ILLEGAL_FREE("mem_free: illegal memory: non-linked: double free");
 709              		.loc 1 660 6 discriminator 1 view .LVU209
 710 0068 F0B1     		cbz	r0, .L56
 670:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 711              		.loc 1 670 3 is_stmt 1 view .LVU210
 670:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 712              		.loc 1 670 13 is_stmt 0 view .LVU211
 713 006a 0023     		movs	r3, #0
 714 006c 04F8043C 		strb	r3, [r4, #-4]
 672:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* the newly freed struct is now the lowest */
 715              		.loc 1 672 3 is_stmt 1 view .LVU212
 672:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* the newly freed struct is now the lowest */
 716              		.loc 1 672 11 is_stmt 0 view .LVU213
 717 0070 1C4B     		ldr	r3, .L57+28
 718 0072 1B68     		ldr	r3, [r3]
 672:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* the newly freed struct is now the lowest */
 719              		.loc 1 672 6 view .LVU214
 720 0074 AB42     		cmp	r3, r5
 721 0076 01D9     		bls	.L49
 674:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 722              		.loc 1 674 5 is_stmt 1 view .LVU215
 674:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 723              		.loc 1 674 11 is_stmt 0 view .LVU216
 724 0078 1A4B     		ldr	r3, .L57+28
 725 007a 1D60     		str	r5, [r3]
 726              	.L49:
ARM GAS  /tmp/ccwyId6Y.s 			page 29


 677:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 727              		.loc 1 677 74 is_stmt 1 view .LVU217
 680:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_SANITY();
 728              		.loc 1 680 3 view .LVU218
 729 007c 2846     		mov	r0, r5
 730 007e FFF7FEFF 		bl	plug_holes
 731              	.LVL62:
 681:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 732              		.loc 1 681 15 view .LVU219
 685:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 733              		.loc 1 685 3 view .LVU220
 734 0082 1748     		ldr	r0, .L57+24
 735 0084 FFF7FEFF 		bl	sys_mutex_unlock
 736              	.LVL63:
 737 0088 D8E7     		b	.L42
 738              	.L55:
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 739              		.loc 1 652 5 view .LVU221
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 740              		.loc 1 652 5 view .LVU222
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 741              		.loc 1 652 5 discriminator 1 view .LVU223
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 742              		.loc 1 652 5 discriminator 1 view .LVU224
 743 008a 114B     		ldr	r3, .L57+8
 744 008c 4FF42372 		mov	r2, #652
 745 0090 1549     		ldr	r1, .L57+32
 746 0092 1148     		ldr	r0, .L57+16
 747 0094 FFF7FEFF 		bl	printf
 748              	.LVL64:
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 749              		.loc 1 652 5 discriminator 3 view .LVU225
 652:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 750              		.loc 1 652 5 discriminator 3 view .LVU226
 653:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: illegal memory: double free?\n"));
 751              		.loc 1 653 5 view .LVU227
 752 0098 1148     		ldr	r0, .L57+24
 753 009a FFF7FEFF 		bl	sys_mutex_unlock
 754              	.LVL65:
 654:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 755              		.loc 1 654 97 view .LVU228
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 756              		.loc 1 656 5 view .LVU229
 757              	.LBB4:
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 758              		.loc 1 656 5 view .LVU230
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 759              		.loc 1 656 5 view .LVU231
 760 009e FFF7FEFF 		bl	sys_arch_protect
 761              	.LVL66:
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 762              		.loc 1 656 5 discriminator 1 view .LVU232
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 763              		.loc 1 656 5 discriminator 1 view .LVU233
 764 00a2 FFF7FEFF 		bl	sys_arch_unprotect
 765              	.LVL67:
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
ARM GAS  /tmp/ccwyId6Y.s 			page 30


 766              		.loc 1 656 5 is_stmt 0 discriminator 1 view .LVU234
 767              	.LBE4:
 656:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 768              		.loc 1 656 5 is_stmt 1 discriminator 2 view .LVU235
 657:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 769              		.loc 1 657 5 view .LVU236
 770 00a6 C9E7     		b	.L42
 771              	.LVL68:
 772              	.L56:
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 773              		.loc 1 661 5 view .LVU237
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 774              		.loc 1 661 5 view .LVU238
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 775              		.loc 1 661 5 discriminator 1 view .LVU239
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 776              		.loc 1 661 5 discriminator 1 view .LVU240
 777 00a8 094B     		ldr	r3, .L57+8
 778 00aa 40F29522 		movw	r2, #661
 779 00ae 0F49     		ldr	r1, .L57+36
 780 00b0 0948     		ldr	r0, .L57+16
 781 00b2 FFF7FEFF 		bl	printf
 782              	.LVL69:
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 783              		.loc 1 661 5 discriminator 3 view .LVU241
 661:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_MEM_FREE_UNPROTECT();
 784              		.loc 1 661 5 discriminator 3 view .LVU242
 662:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_free: illegal memory: non-linked: double f
 785              		.loc 1 662 5 view .LVU243
 786 00b6 0A48     		ldr	r0, .L57+24
 787 00b8 FFF7FEFF 		bl	sys_mutex_unlock
 788              	.LVL70:
 663:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 789              		.loc 1 663 109 view .LVU244
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 790              		.loc 1 665 5 view .LVU245
 791              	.LBB5:
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 792              		.loc 1 665 5 view .LVU246
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 793              		.loc 1 665 5 view .LVU247
 794 00bc FFF7FEFF 		bl	sys_arch_protect
 795              	.LVL71:
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 796              		.loc 1 665 5 discriminator 1 view .LVU248
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 797              		.loc 1 665 5 discriminator 1 view .LVU249
 798 00c0 FFF7FEFF 		bl	sys_arch_unprotect
 799              	.LVL72:
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 800              		.loc 1 665 5 is_stmt 0 discriminator 1 view .LVU250
 801              	.LBE5:
 665:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return;
 802              		.loc 1 665 5 is_stmt 1 discriminator 2 view .LVU251
 666:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 803              		.loc 1 666 5 view .LVU252
 804 00c4 BAE7     		b	.L42
ARM GAS  /tmp/ccwyId6Y.s 			page 31


 805              	.LVL73:
 806              	.L51:
 807              	.LCFI4:
 808              		.cfi_def_cfa_offset 0
 809              		.cfi_restore 3
 810              		.cfi_restore 4
 811              		.cfi_restore 5
 812              		.cfi_restore 14
 666:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 813              		.loc 1 666 5 is_stmt 0 view .LVU253
 814 00c6 7047     		bx	lr
 815              	.L58:
 816              		.align	2
 817              	.L57:
 818 00c8 00000000 		.word	ram
 819 00cc 00000000 		.word	ram_end
 820 00d0 00000000 		.word	.LC0
 821 00d4 24000000 		.word	.LC8
 822 00d8 44000000 		.word	.LC2
 823 00dc 00000000 		.word	.LC7
 824 00e0 00000000 		.word	mem_mutex
 825 00e4 00000000 		.word	lfree
 826 00e8 40000000 		.word	.LC9
 827 00ec 68000000 		.word	.LC10
 828              		.cfi_endproc
 829              	.LFE179:
 831              		.section	.rodata.mem_trim.str1.4,"aMS",%progbits,1
 832              		.align	2
 833              	.LC11:
 834 0000 6D656D5F 		.ascii	"mem_trim: legal memory\000"
 834      7472696D 
 834      3A206C65 
 834      67616C20 
 834      6D656D6F 
 835 0017 00       		.align	2
 836              	.LC12:
 837 0018 6D656D5F 		.ascii	"mem_trim can only shrink memory\000"
 837      7472696D 
 837      2063616E 
 837      206F6E6C 
 837      79207368 
 838              		.align	2
 839              	.LC13:
 840 0038 696E7661 		.ascii	"invalid next ptr\000"
 840      6C696420 
 840      6E657874 
 840      20707472 
 840      00
 841              		.section	.text.mem_trim,"ax",%progbits
 842              		.align	1
 843              		.global	mem_trim
 844              		.syntax unified
 845              		.thumb
 846              		.thumb_func
 848              	mem_trim:
 849              	.LVL74:
 850              	.LFB180:
ARM GAS  /tmp/ccwyId6Y.s 			page 32


 687:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 688:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 689:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Shrink memory returned by mem_malloc().
 690:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 691:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param rmem pointer to memory allocated by mem_malloc the is to be shrinked
 692:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param new_size required size after shrinking (needs to be smaller than or
 693:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *                equal to the previous size)
 694:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @return for compatibility reasons: is always == rmem, at the moment
 695:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *         or NULL if newsize is > old size, in which case rmem is NOT touched
 696:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *         or freed!
 697:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 698:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 699:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_trim(void *rmem, mem_size_t new_size)
 700:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 851              		.loc 1 700 1 is_stmt 1 view -0
 852              		.cfi_startproc
 853              		@ args = 0, pretend = 0, frame = 0
 854              		@ frame_needed = 0, uses_anonymous_args = 0
 855              		.loc 1 700 1 is_stmt 0 view .LVU255
 856 0000 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 857              	.LCFI5:
 858              		.cfi_def_cfa_offset 32
 859              		.cfi_offset 3, -32
 860              		.cfi_offset 4, -28
 861              		.cfi_offset 5, -24
 862              		.cfi_offset 6, -20
 863              		.cfi_offset 7, -16
 864              		.cfi_offset 8, -12
 865              		.cfi_offset 9, -8
 866              		.cfi_offset 14, -4
 867 0004 0546     		mov	r5, r0
 701:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t size, newsize;
 868              		.loc 1 701 3 is_stmt 1 view .LVU256
 702:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t ptr, ptr2;
 869              		.loc 1 702 3 view .LVU257
 703:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem, *mem2;
 870              		.loc 1 703 3 view .LVU258
 704:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* use the FREE_PROTECT here: it protects with sem OR SYS_ARCH_PROTECT */
 705:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_FREE_DECL_PROTECT();
 871              		.loc 1 705 31 view .LVU259
 706:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 707:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* Expand the size of the allocated memory region so that we can
 708:Middlewares/Third_Party/LwIP/src/core/mem.c ****      adjust for alignment. */
 709:Middlewares/Third_Party/LwIP/src/core/mem.c ****   newsize = (mem_size_t)LWIP_MEM_ALIGN_SIZE(new_size);
 872              		.loc 1 709 3 view .LVU260
 873              		.loc 1 709 25 is_stmt 0 view .LVU261
 874 0006 CC1C     		adds	r4, r1, #3
 875 0008 A4B2     		uxth	r4, r4
 876              		.loc 1 709 11 view .LVU262
 877 000a 24F00304 		bic	r4, r4, #3
 878 000e A4B2     		uxth	r4, r4
 879              	.LVL75:
 710:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (newsize < MIN_SIZE_ALIGNED) {
 880              		.loc 1 710 3 is_stmt 1 view .LVU263
 881              		.loc 1 710 6 is_stmt 0 view .LVU264
 882 0010 0B2C     		cmp	r4, #11
 883 0012 00D8     		bhi	.L60
ARM GAS  /tmp/ccwyId6Y.s 			page 33


 711:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* every data block must be at least MIN_SIZE_ALIGNED long */
 712:Middlewares/Third_Party/LwIP/src/core/mem.c ****     newsize = MIN_SIZE_ALIGNED;
 884              		.loc 1 712 13 view .LVU265
 885 0014 0C24     		movs	r4, #12
 886              	.LVL76:
 887              	.L60:
 713:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 714:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 715:Middlewares/Third_Party/LwIP/src/core/mem.c ****   newsize += MEM_SANITY_REGION_BEFORE_ALIGNED + MEM_SANITY_REGION_AFTER_ALIGNED;
 716:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 717:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((newsize > MEM_SIZE_ALIGNED) || (newsize < new_size)) {
 888              		.loc 1 717 3 is_stmt 1 view .LVU266
 889              		.loc 1 717 48 is_stmt 0 view .LVU267
 890 0016 8C42     		cmp	r4, r1
 891 0018 2CBF     		ite	cs
 892 001a 0021     		movcs	r1, #0
 893              	.LVL77:
 894              		.loc 1 717 48 view .LVU268
 895 001c 0121     		movcc	r1, #1
 896              		.loc 1 717 36 view .LVU269
 897 001e B4F5C86F 		cmp	r4, #1600
 898 0022 88BF     		it	hi
 899 0024 41F00101 		orrhi	r1, r1, #1
 900              		.loc 1 717 6 view .LVU270
 901 0028 0029     		cmp	r1, #0
 902 002a 40F0A280 		bne	.L74
 718:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return NULL;
 719:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 720:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 721:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("mem_trim: legal memory", (u8_t *)rmem >= (u8_t *)ram &&
 903              		.loc 1 721 3 is_stmt 1 view .LVU271
 904              		.loc 1 721 3 view .LVU272
 905 002e 544B     		ldr	r3, .L82
 906 0030 1B68     		ldr	r3, [r3]
 907 0032 AB42     		cmp	r3, r5
 908 0034 03D8     		bhi	.L62
 909              		.loc 1 721 3 is_stmt 0 discriminator 2 view .LVU273
 910 0036 534B     		ldr	r3, .L82+4
 911 0038 1B68     		ldr	r3, [r3]
 912 003a AB42     		cmp	r3, r5
 913 003c 06D8     		bhi	.L63
 914              	.L62:
 915              		.loc 1 721 3 is_stmt 1 discriminator 3 view .LVU274
 916              		.loc 1 721 3 discriminator 3 view .LVU275
 917 003e 524B     		ldr	r3, .L82+8
 918 0040 40F2D122 		movw	r2, #721
 919 0044 5149     		ldr	r1, .L82+12
 920 0046 5248     		ldr	r0, .L82+16
 921              	.LVL78:
 922              		.loc 1 721 3 is_stmt 0 discriminator 3 view .LVU276
 923 0048 FFF7FEFF 		bl	printf
 924              	.LVL79:
 925              	.L63:
 926              		.loc 1 721 3 is_stmt 1 discriminator 5 view .LVU277
 927              		.loc 1 721 3 discriminator 5 view .LVU278
 722:Middlewares/Third_Party/LwIP/src/core/mem.c ****               (u8_t *)rmem < (u8_t *)ram_end);
 723:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
ARM GAS  /tmp/ccwyId6Y.s 			page 34


 724:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((u8_t *)rmem < (u8_t *)ram || (u8_t *)rmem >= (u8_t *)ram_end) {
 928              		.loc 1 724 3 view .LVU279
 929              		.loc 1 724 20 is_stmt 0 view .LVU280
 930 004c 4C4B     		ldr	r3, .L82
 931 004e 1B68     		ldr	r3, [r3]
 932              		.loc 1 724 6 view .LVU281
 933 0050 AB42     		cmp	r3, r5
 934 0052 45D8     		bhi	.L64
 935              		.loc 1 724 53 discriminator 1 view .LVU282
 936 0054 4B4B     		ldr	r3, .L82+4
 937 0056 1B68     		ldr	r3, [r3]
 938              		.loc 1 724 34 discriminator 1 view .LVU283
 939 0058 AB42     		cmp	r3, r5
 940 005a 41D9     		bls	.L64
 725:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SEVERE, ("mem_trim: illegal memory\n"));
 726:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_INC_LOCKED(illegal);
 728:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 729:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 730:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* Get the corresponding struct mem ... */
 731:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* cast through void* to get rid of alignment warnings */
 732:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem = (struct mem *)(void *)((u8_t *)rmem - (SIZEOF_STRUCT_MEM + MEM_SANITY_OFFSET));
 941              		.loc 1 732 3 is_stmt 1 view .LVU284
 942              	.LVL80:
 733:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 734:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_overflow_check_element(mem);
 735:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 736:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* ... and its offset pointer */
 737:Middlewares/Third_Party/LwIP/src/core/mem.c ****   ptr = mem_to_ptr(mem);
 943              		.loc 1 737 3 view .LVU285
 944              		.loc 1 737 9 is_stmt 0 view .LVU286
 945 005c A5F10800 		sub	r0, r5, #8
 946              	.LVL81:
 947              		.loc 1 737 9 view .LVU287
 948 0060 FFF7FEFF 		bl	mem_to_ptr
 949              	.LVL82:
 950              		.loc 1 737 9 view .LVU288
 951 0064 0646     		mov	r6, r0
 952              	.LVL83:
 738:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 739:Middlewares/Third_Party/LwIP/src/core/mem.c ****   size = (mem_size_t)((mem_size_t)(mem->next - ptr) - (SIZEOF_STRUCT_MEM + MEM_SANITY_OVERHEAD));
 953              		.loc 1 739 3 is_stmt 1 view .LVU289
 954              		.loc 1 739 39 is_stmt 0 view .LVU290
 955 0066 35F8088C 		ldrh	r8, [r5, #-8]
 956              		.loc 1 739 23 view .LVU291
 957 006a A8EB0008 		sub	r8, r8, r0
 958 006e 1FFA88F8 		uxth	r8, r8
 959              		.loc 1 739 8 view .LVU292
 960 0072 A8F10808 		sub	r8, r8, #8
 961 0076 1FFA88F8 		uxth	r8, r8
 962              	.LVL84:
 740:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_ASSERT("mem_trim can only shrink memory", newsize <= size);
 963              		.loc 1 740 3 is_stmt 1 view .LVU293
 964              		.loc 1 740 3 view .LVU294
 965 007a 4445     		cmp	r4, r8
 966 007c 37D8     		bhi	.L78
 967              	.LVL85:
ARM GAS  /tmp/ccwyId6Y.s 			page 35


 968              	.L66:
 969              		.loc 1 740 3 discriminator 3 view .LVU295
 970              		.loc 1 740 3 discriminator 3 view .LVU296
 741:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (newsize > size) {
 971              		.loc 1 741 3 view .LVU297
 972              		.loc 1 741 6 is_stmt 0 view .LVU298
 973 007e 4445     		cmp	r4, r8
 974 0080 79D8     		bhi	.L75
 742:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* not supported */
 743:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return NULL;
 744:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 745:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (newsize == size) {
 975              		.loc 1 745 3 is_stmt 1 view .LVU299
 976              		.loc 1 745 6 is_stmt 0 view .LVU300
 977 0082 7AD0     		beq	.L76
 746:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* No change in size, simply return */
 747:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 748:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 749:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 750:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* protect the heap from concurrent access */
 751:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_FREE_PROTECT();
 978              		.loc 1 751 3 is_stmt 1 view .LVU301
 979 0084 4348     		ldr	r0, .L82+20
 980 0086 FFF7FEFF 		bl	sys_mutex_lock
 981              	.LVL86:
 752:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 753:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem2 = ptr_to_mem(mem->next);
 982              		.loc 1 753 3 view .LVU302
 983              		.loc 1 753 10 is_stmt 0 view .LVU303
 984 008a 35F8087C 		ldrh	r7, [r5, #-8]
 985 008e 3846     		mov	r0, r7
 986 0090 FFF7FEFF 		bl	ptr_to_mem
 987              	.LVL87:
 988 0094 8146     		mov	r9, r0
 989              	.LVL88:
 754:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (mem2->used == 0) {
 990              		.loc 1 754 3 is_stmt 1 view .LVU304
 991              		.loc 1 754 11 is_stmt 0 view .LVU305
 992 0096 0379     		ldrb	r3, [r0, #4]	@ zero_extendqisi2
 993              		.loc 1 754 6 view .LVU306
 994 0098 002B     		cmp	r3, #0
 995 009a 3ED1     		bne	.L67
 996              	.LBB6:
 755:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* The next struct is unused, we can simply move it at little */
 756:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem_size_t next;
 997              		.loc 1 756 5 is_stmt 1 view .LVU307
 757:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("invalid next ptr", mem->next != MEM_SIZE_ALIGNED);
 998              		.loc 1 757 5 view .LVU308
 999              		.loc 1 757 5 view .LVU309
 1000 009c B7F5C86F 		cmp	r7, #1600
 1001 00a0 2DD0     		beq	.L79
 1002              	.LVL89:
 1003              	.L68:
 1004              		.loc 1 757 5 discriminator 3 view .LVU310
 1005              		.loc 1 757 5 discriminator 3 view .LVU311
 758:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* remember the old next pointer */
 759:Middlewares/Third_Party/LwIP/src/core/mem.c ****     next = mem2->next;
ARM GAS  /tmp/ccwyId6Y.s 			page 36


 1006              		.loc 1 759 5 view .LVU312
 1007              		.loc 1 759 10 is_stmt 0 view .LVU313
 1008 00a2 B9F80070 		ldrh	r7, [r9]
 1009              	.LVL90:
 760:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* create new struct mem which is moved directly after the shrinked mem */
 761:Middlewares/Third_Party/LwIP/src/core/mem.c ****     ptr2 = (mem_size_t)(ptr + SIZEOF_STRUCT_MEM + newsize);
 1010              		.loc 1 761 5 is_stmt 1 view .LVU314
 1011              		.loc 1 761 49 is_stmt 0 view .LVU315
 1012 00a6 3444     		add	r4, r4, r6
 1013              	.LVL91:
 1014              		.loc 1 761 49 view .LVU316
 1015 00a8 A4B2     		uxth	r4, r4
 1016              		.loc 1 761 10 view .LVU317
 1017 00aa 0834     		adds	r4, r4, #8
 1018 00ac A4B2     		uxth	r4, r4
 1019              	.LVL92:
 762:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (lfree == mem2) {
 1020              		.loc 1 762 5 is_stmt 1 view .LVU318
 1021              		.loc 1 762 15 is_stmt 0 view .LVU319
 1022 00ae 3A4B     		ldr	r3, .L82+24
 1023 00b0 1B68     		ldr	r3, [r3]
 1024              		.loc 1 762 8 view .LVU320
 1025 00b2 4B45     		cmp	r3, r9
 1026 00b4 2BD0     		beq	.L80
 1027              	.L69:
 763:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = ptr_to_mem(ptr2);
 764:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 765:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1028              		.loc 1 765 5 is_stmt 1 view .LVU321
 1029              		.loc 1 765 12 is_stmt 0 view .LVU322
 1030 00b6 2046     		mov	r0, r4
 1031 00b8 FFF7FEFF 		bl	ptr_to_mem
 1032              	.LVL93:
 766:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->used = 0;
 1033              		.loc 1 766 5 is_stmt 1 view .LVU323
 1034              		.loc 1 766 16 is_stmt 0 view .LVU324
 1035 00bc 0023     		movs	r3, #0
 1036 00be 0371     		strb	r3, [r0, #4]
 767:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* restore the next pointer */
 768:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->next = next;
 1037              		.loc 1 768 5 is_stmt 1 view .LVU325
 1038              		.loc 1 768 16 is_stmt 0 view .LVU326
 1039 00c0 0780     		strh	r7, [r0]	@ movhi
 769:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* link it back to mem */
 770:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->prev = ptr;
 1040              		.loc 1 770 5 is_stmt 1 view .LVU327
 1041              		.loc 1 770 16 is_stmt 0 view .LVU328
 1042 00c2 4680     		strh	r6, [r0, #2]	@ movhi
 771:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* link mem to it */
 772:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem->next = ptr2;
 1043              		.loc 1 772 5 is_stmt 1 view .LVU329
 1044              		.loc 1 772 15 is_stmt 0 view .LVU330
 1045 00c4 25F8084C 		strh	r4, [r5, #-8]	@ movhi
 773:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* last thing to restore linked list: as we have moved mem2,
 774:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * let 'mem2->next->prev' point to mem2 again. but only if mem2->next is not
 775:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * the end of the heap */
 776:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2->next != MEM_SIZE_ALIGNED) {
ARM GAS  /tmp/ccwyId6Y.s 			page 37


 1046              		.loc 1 776 5 is_stmt 1 view .LVU331
 1047              		.loc 1 776 13 is_stmt 0 view .LVU332
 1048 00c8 0088     		ldrh	r0, [r0]
 1049              	.LVL94:
 1050              		.loc 1 776 8 view .LVU333
 1051 00ca B0F5C86F 		cmp	r0, #1600
 1052 00ce 02D0     		beq	.L70
 777:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem2->next)->prev = ptr2;
 1053              		.loc 1 777 7 is_stmt 1 view .LVU334
 1054 00d0 FFF7FEFF 		bl	ptr_to_mem
 1055              	.LVL95:
 1056              		.loc 1 777 36 is_stmt 0 discriminator 1 view .LVU335
 1057 00d4 4480     		strh	r4, [r0, #2]	@ movhi
 1058              	.LVL96:
 1059              	.L70:
 1060              		.loc 1 777 36 discriminator 1 view .LVU336
 1061              	.LBE6:
 778:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 779:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_DEC_USED(used, (size - newsize));
 780:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* no need to plug holes, we've already done that */
 781:Middlewares/Third_Party/LwIP/src/core/mem.c ****   } else if (newsize + SIZEOF_STRUCT_MEM + MIN_SIZE_ALIGNED <= size) {
 782:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* Next struct is used but there's room for another struct mem with
 783:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * at least MIN_SIZE_ALIGNED of data.
 784:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * Old size ('size') must be big enough to contain at least 'newsize' plus a struct mem
 785:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * ('SIZEOF_STRUCT_MEM') with some data ('MIN_SIZE_ALIGNED').
 786:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * @todo we could leave out MIN_SIZE_ALIGNED. We would create an empty
 787:Middlewares/Third_Party/LwIP/src/core/mem.c ****      *       region that couldn't hold data, but when mem->next gets freed,
 788:Middlewares/Third_Party/LwIP/src/core/mem.c ****      *       the 2 regions would be combined, resulting in more free memory */
 789:Middlewares/Third_Party/LwIP/src/core/mem.c ****     ptr2 = (mem_size_t)(ptr + SIZEOF_STRUCT_MEM + newsize);
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("invalid next ptr", mem->next != MEM_SIZE_ALIGNED);
 791:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 792:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2 < lfree) {
 793:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem2;
 794:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 795:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->used = 0;
 796:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->next = mem->next;
 797:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->prev = ptr;
 798:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem->next = ptr2;
 799:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2->next != MEM_SIZE_ALIGNED) {
 800:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem2->next)->prev = ptr2;
 801:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 802:Middlewares/Third_Party/LwIP/src/core/mem.c ****     MEM_STATS_DEC_USED(used, (size - newsize));
 1062              		.loc 1 802 47 is_stmt 1 view .LVU337
 803:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* the original mem->next is used, so no need to plug holes! */
 804:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 805:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* else {
 806:Middlewares/Third_Party/LwIP/src/core/mem.c ****     next struct mem is used but size between mem and mem2 is not big enough
 807:Middlewares/Third_Party/LwIP/src/core/mem.c ****     to create another struct mem
 808:Middlewares/Third_Party/LwIP/src/core/mem.c ****     -> don't do anyhting.
 809:Middlewares/Third_Party/LwIP/src/core/mem.c ****     -> the remaining space stays unused since it is too small
 810:Middlewares/Third_Party/LwIP/src/core/mem.c ****   } */
 811:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 812:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_overflow_init_element(mem, new_size);
 813:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 814:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_SANITY();
 1063              		.loc 1 814 15 view .LVU338
 815:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
ARM GAS  /tmp/ccwyId6Y.s 			page 38


 816:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_free_count = 1;
 817:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 818:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_FREE_UNPROTECT();
 1064              		.loc 1 818 3 view .LVU339
 1065 00d6 2F48     		ldr	r0, .L82+20
 1066 00d8 FFF7FEFF 		bl	sys_mutex_unlock
 1067              	.LVL97:
 819:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return rmem;
 1068              		.loc 1 819 3 view .LVU340
 1069              		.loc 1 819 10 is_stmt 0 view .LVU341
 1070 00dc 2846     		mov	r0, r5
 1071 00de 04E0     		b	.L59
 1072              	.LVL98:
 1073              	.L64:
 725:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* protect mem stats from concurrent access */
 1074              		.loc 1 725 83 is_stmt 1 view .LVU342
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1075              		.loc 1 727 5 view .LVU343
 1076              	.LBB7:
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1077              		.loc 1 727 5 view .LVU344
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1078              		.loc 1 727 5 view .LVU345
 1079 00e0 FFF7FEFF 		bl	sys_arch_protect
 1080              	.LVL99:
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1081              		.loc 1 727 5 discriminator 1 view .LVU346
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1082              		.loc 1 727 5 discriminator 1 view .LVU347
 1083 00e4 FFF7FEFF 		bl	sys_arch_unprotect
 1084              	.LVL100:
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1085              		.loc 1 727 5 is_stmt 0 discriminator 1 view .LVU348
 1086              	.LBE7:
 727:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return rmem;
 1087              		.loc 1 727 5 is_stmt 1 discriminator 2 view .LVU349
 728:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1088              		.loc 1 728 5 view .LVU350
 728:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1089              		.loc 1 728 12 is_stmt 0 view .LVU351
 1090 00e8 2846     		mov	r0, r5
 1091              	.LVL101:
 1092              	.L59:
 820:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 1093              		.loc 1 820 1 view .LVU352
 1094 00ea BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 1095              	.LVL102:
 1096              	.L78:
 740:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (newsize > size) {
 1097              		.loc 1 740 3 is_stmt 1 discriminator 1 view .LVU353
 740:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (newsize > size) {
 1098              		.loc 1 740 3 discriminator 1 view .LVU354
 1099 00ee 264B     		ldr	r3, .L82+8
 1100 00f0 4FF43972 		mov	r2, #740
 1101 00f4 2949     		ldr	r1, .L82+28
 1102 00f6 2648     		ldr	r0, .L82+16
 1103              	.LVL103:
ARM GAS  /tmp/ccwyId6Y.s 			page 39


 740:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (newsize > size) {
 1104              		.loc 1 740 3 is_stmt 0 discriminator 1 view .LVU355
 1105 00f8 FFF7FEFF 		bl	printf
 1106              	.LVL104:
 1107 00fc BFE7     		b	.L66
 1108              	.LVL105:
 1109              	.L79:
 1110              	.LBB8:
 757:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* remember the old next pointer */
 1111              		.loc 1 757 5 is_stmt 1 discriminator 1 view .LVU356
 757:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* remember the old next pointer */
 1112              		.loc 1 757 5 discriminator 1 view .LVU357
 1113 00fe 224B     		ldr	r3, .L82+8
 1114 0100 40F2F522 		movw	r2, #757
 1115 0104 2649     		ldr	r1, .L82+32
 1116 0106 2248     		ldr	r0, .L82+16
 1117              	.LVL106:
 757:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* remember the old next pointer */
 1118              		.loc 1 757 5 is_stmt 0 discriminator 1 view .LVU358
 1119 0108 FFF7FEFF 		bl	printf
 1120              	.LVL107:
 1121 010c C9E7     		b	.L68
 1122              	.LVL108:
 1123              	.L80:
 763:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1124              		.loc 1 763 7 is_stmt 1 view .LVU359
 763:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1125              		.loc 1 763 15 is_stmt 0 view .LVU360
 1126 010e 2046     		mov	r0, r4
 1127 0110 FFF7FEFF 		bl	ptr_to_mem
 1128              	.LVL109:
 763:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1129              		.loc 1 763 13 discriminator 1 view .LVU361
 1130 0114 204B     		ldr	r3, .L82+24
 1131 0116 1860     		str	r0, [r3]
 1132 0118 CDE7     		b	.L69
 1133              	.LVL110:
 1134              	.L67:
 763:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1135              		.loc 1 763 13 discriminator 1 view .LVU362
 1136              	.LBE8:
 781:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* Next struct is used but there's room for another struct mem with
 1137              		.loc 1 781 10 is_stmt 1 view .LVU363
 781:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* Next struct is used but there's room for another struct mem with
 1138              		.loc 1 781 42 is_stmt 0 view .LVU364
 1139 011a 04F11403 		add	r3, r4, #20
 781:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* Next struct is used but there's room for another struct mem with
 1140              		.loc 1 781 13 view .LVU365
 1141 011e 4345     		cmp	r3, r8
 1142 0120 D9D8     		bhi	.L70
 789:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("invalid next ptr", mem->next != MEM_SIZE_ALIGNED);
 1143              		.loc 1 789 5 is_stmt 1 view .LVU366
 789:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("invalid next ptr", mem->next != MEM_SIZE_ALIGNED);
 1144              		.loc 1 789 49 is_stmt 0 view .LVU367
 1145 0122 3444     		add	r4, r4, r6
 1146              	.LVL111:
 789:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("invalid next ptr", mem->next != MEM_SIZE_ALIGNED);
ARM GAS  /tmp/ccwyId6Y.s 			page 40


 1147              		.loc 1 789 49 view .LVU368
 1148 0124 A4B2     		uxth	r4, r4
 789:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_ASSERT("invalid next ptr", mem->next != MEM_SIZE_ALIGNED);
 1149              		.loc 1 789 10 view .LVU369
 1150 0126 0834     		adds	r4, r4, #8
 1151 0128 A4B2     		uxth	r4, r4
 1152              	.LVL112:
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1153              		.loc 1 790 5 is_stmt 1 view .LVU370
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1154              		.loc 1 790 5 view .LVU371
 1155 012a B7F5C86F 		cmp	r7, #1600
 1156 012e 18D0     		beq	.L81
 1157              	.LVL113:
 1158              	.L71:
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1159              		.loc 1 790 5 discriminator 3 view .LVU372
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1160              		.loc 1 790 5 discriminator 3 view .LVU373
 791:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2 < lfree) {
 1161              		.loc 1 791 5 view .LVU374
 791:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2 < lfree) {
 1162              		.loc 1 791 12 is_stmt 0 view .LVU375
 1163 0130 2046     		mov	r0, r4
 1164 0132 FFF7FEFF 		bl	ptr_to_mem
 1165              	.LVL114:
 792:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem2;
 1166              		.loc 1 792 5 is_stmt 1 view .LVU376
 792:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem2;
 1167              		.loc 1 792 14 is_stmt 0 view .LVU377
 1168 0136 184B     		ldr	r3, .L82+24
 1169 0138 1B68     		ldr	r3, [r3]
 792:Middlewares/Third_Party/LwIP/src/core/mem.c ****       lfree = mem2;
 1170              		.loc 1 792 8 view .LVU378
 1171 013a 8342     		cmp	r3, r0
 1172 013c 01D9     		bls	.L72
 793:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1173              		.loc 1 793 7 is_stmt 1 view .LVU379
 793:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1174              		.loc 1 793 13 is_stmt 0 view .LVU380
 1175 013e 164B     		ldr	r3, .L82+24
 1176 0140 1860     		str	r0, [r3]
 1177              	.L72:
 795:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->next = mem->next;
 1178              		.loc 1 795 5 is_stmt 1 view .LVU381
 795:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->next = mem->next;
 1179              		.loc 1 795 16 is_stmt 0 view .LVU382
 1180 0142 0023     		movs	r3, #0
 1181 0144 0371     		strb	r3, [r0, #4]
 796:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->prev = ptr;
 1182              		.loc 1 796 5 is_stmt 1 view .LVU383
 796:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->prev = ptr;
 1183              		.loc 1 796 21 is_stmt 0 view .LVU384
 1184 0146 35F8083C 		ldrh	r3, [r5, #-8]
 796:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2->prev = ptr;
 1185              		.loc 1 796 16 view .LVU385
 1186 014a 0380     		strh	r3, [r0]	@ movhi
ARM GAS  /tmp/ccwyId6Y.s 			page 41


 797:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem->next = ptr2;
 1187              		.loc 1 797 5 is_stmt 1 view .LVU386
 797:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem->next = ptr2;
 1188              		.loc 1 797 16 is_stmt 0 view .LVU387
 1189 014c 4680     		strh	r6, [r0, #2]	@ movhi
 798:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2->next != MEM_SIZE_ALIGNED) {
 1190              		.loc 1 798 5 is_stmt 1 view .LVU388
 798:Middlewares/Third_Party/LwIP/src/core/mem.c ****     if (mem2->next != MEM_SIZE_ALIGNED) {
 1191              		.loc 1 798 15 is_stmt 0 view .LVU389
 1192 014e 25F8084C 		strh	r4, [r5, #-8]	@ movhi
 799:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem2->next)->prev = ptr2;
 1193              		.loc 1 799 5 is_stmt 1 view .LVU390
 799:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem2->next)->prev = ptr2;
 1194              		.loc 1 799 13 is_stmt 0 view .LVU391
 1195 0152 0088     		ldrh	r0, [r0]
 1196              	.LVL115:
 799:Middlewares/Third_Party/LwIP/src/core/mem.c ****       ptr_to_mem(mem2->next)->prev = ptr2;
 1197              		.loc 1 799 8 view .LVU392
 1198 0154 B0F5C86F 		cmp	r0, #1600
 1199 0158 BDD0     		beq	.L70
 800:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1200              		.loc 1 800 7 is_stmt 1 view .LVU393
 1201 015a FFF7FEFF 		bl	ptr_to_mem
 1202              	.LVL116:
 800:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 1203              		.loc 1 800 36 is_stmt 0 discriminator 1 view .LVU394
 1204 015e 4480     		strh	r4, [r0, #2]	@ movhi
 1205 0160 B9E7     		b	.L70
 1206              	.LVL117:
 1207              	.L81:
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1208              		.loc 1 790 5 is_stmt 1 discriminator 1 view .LVU395
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1209              		.loc 1 790 5 discriminator 1 view .LVU396
 1210 0162 094B     		ldr	r3, .L82+8
 1211              	.LVL118:
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1212              		.loc 1 790 5 is_stmt 0 discriminator 1 view .LVU397
 1213 0164 40F21632 		movw	r2, #790
 1214 0168 0D49     		ldr	r1, .L82+32
 1215 016a 0948     		ldr	r0, .L82+16
 1216              	.LVL119:
 790:Middlewares/Third_Party/LwIP/src/core/mem.c ****     mem2 = ptr_to_mem(ptr2);
 1217              		.loc 1 790 5 discriminator 1 view .LVU398
 1218 016c FFF7FEFF 		bl	printf
 1219              	.LVL120:
 1220 0170 DEE7     		b	.L71
 1221              	.LVL121:
 1222              	.L74:
 718:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1223              		.loc 1 718 12 view .LVU399
 1224 0172 0020     		movs	r0, #0
 1225              	.LVL122:
 718:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1226              		.loc 1 718 12 view .LVU400
 1227 0174 B9E7     		b	.L59
 1228              	.LVL123:
ARM GAS  /tmp/ccwyId6Y.s 			page 42


 1229              	.L75:
 743:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1230              		.loc 1 743 12 view .LVU401
 1231 0176 0020     		movs	r0, #0
 1232 0178 B7E7     		b	.L59
 1233              	.L76:
 747:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1234              		.loc 1 747 12 view .LVU402
 1235 017a 2846     		mov	r0, r5
 1236 017c B5E7     		b	.L59
 1237              	.L83:
 1238 017e 00BF     		.align	2
 1239              	.L82:
 1240 0180 00000000 		.word	ram
 1241 0184 00000000 		.word	ram_end
 1242 0188 00000000 		.word	.LC0
 1243 018c 00000000 		.word	.LC11
 1244 0190 44000000 		.word	.LC2
 1245 0194 00000000 		.word	mem_mutex
 1246 0198 00000000 		.word	lfree
 1247 019c 18000000 		.word	.LC12
 1248 01a0 38000000 		.word	.LC13
 1249              		.cfi_endproc
 1250              	.LFE180:
 1252              		.section	.rodata.mem_malloc.str1.4,"aMS",%progbits,1
 1253              		.align	2
 1254              	.LC14:
 1255 0000 6D656D5F 		.ascii	"mem_malloc: !lfree->used\000"
 1255      6D616C6C 
 1255      6F633A20 
 1255      216C6672 
 1255      65652D3E 
 1256 0019 000000   		.align	2
 1257              	.LC15:
 1258 001c 6D656D5F 		.ascii	"mem_malloc: allocated memory not above ram_end.\000"
 1258      6D616C6C 
 1258      6F633A20 
 1258      616C6C6F 
 1258      63617465 
 1259              		.align	2
 1260              	.LC16:
 1261 004c 6D656D5F 		.ascii	"mem_malloc: allocated memory properly aligned.\000"
 1261      6D616C6C 
 1261      6F633A20 
 1261      616C6C6F 
 1261      63617465 
 1262 007b 00       		.align	2
 1263              	.LC17:
 1264 007c 6D656D5F 		.ascii	"mem_malloc: sanity check alignment\000"
 1264      6D616C6C 
 1264      6F633A20 
 1264      73616E69 
 1264      74792063 
 1265              		.section	.text.mem_malloc,"ax",%progbits
 1266              		.align	1
 1267              		.global	mem_malloc
 1268              		.syntax unified
ARM GAS  /tmp/ccwyId6Y.s 			page 43


 1269              		.thumb
 1270              		.thumb_func
 1272              	mem_malloc:
 1273              	.LVL124:
 1274              	.LFB181:
 821:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 822:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 823:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Allocate a block of memory with a minimum of 'size' bytes.
 824:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 825:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param size_in is the minimum size of the requested block in bytes.
 826:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @return pointer to allocated memory or NULL if no free memory was found.
 827:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 828:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Note that the returned value will always be aligned (as defined by MEM_ALIGNMENT).
 829:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 830:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 831:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_malloc(mem_size_t size_in)
 832:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 1275              		.loc 1 832 1 is_stmt 1 view -0
 1276              		.cfi_startproc
 1277              		@ args = 0, pretend = 0, frame = 0
 1278              		@ frame_needed = 0, uses_anonymous_args = 0
 833:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t ptr, ptr2, size;
 1279              		.loc 1 833 3 view .LVU404
 834:Middlewares/Third_Party/LwIP/src/core/mem.c ****   struct mem *mem, *mem2;
 1280              		.loc 1 834 3 view .LVU405
 835:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 836:Middlewares/Third_Party/LwIP/src/core/mem.c ****   u8_t local_mem_free_count = 0;
 837:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 838:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_ALLOC_DECL_PROTECT();
 1281              		.loc 1 838 32 view .LVU406
 839:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 840:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (size_in == 0) {
 1282              		.loc 1 840 3 view .LVU407
 1283              		.loc 1 840 6 is_stmt 0 view .LVU408
 1284 0000 0028     		cmp	r0, #0
 1285 0002 00F0A780 		beq	.L100
 832:Middlewares/Third_Party/LwIP/src/core/mem.c ****   mem_size_t ptr, ptr2, size;
 1286              		.loc 1 832 1 view .LVU409
 1287 0006 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 1288              	.LCFI6:
 1289              		.cfi_def_cfa_offset 24
 1290              		.cfi_offset 4, -24
 1291              		.cfi_offset 5, -20
 1292              		.cfi_offset 6, -16
 1293              		.cfi_offset 7, -12
 1294              		.cfi_offset 8, -8
 1295              		.cfi_offset 14, -4
 1296 000a 0346     		mov	r3, r0
 841:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return NULL;
 842:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 843:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 844:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* Expand the size of the allocated memory region so that we can
 845:Middlewares/Third_Party/LwIP/src/core/mem.c ****      adjust for alignment. */
 846:Middlewares/Third_Party/LwIP/src/core/mem.c ****   size = (mem_size_t)LWIP_MEM_ALIGN_SIZE(size_in);
 1297              		.loc 1 846 3 is_stmt 1 view .LVU410
 1298              		.loc 1 846 22 is_stmt 0 view .LVU411
 1299 000c C61C     		adds	r6, r0, #3
ARM GAS  /tmp/ccwyId6Y.s 			page 44


 1300 000e B6B2     		uxth	r6, r6
 1301              		.loc 1 846 8 view .LVU412
 1302 0010 26F00306 		bic	r6, r6, #3
 1303 0014 B6B2     		uxth	r6, r6
 1304              	.LVL125:
 847:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (size < MIN_SIZE_ALIGNED) {
 1305              		.loc 1 847 3 is_stmt 1 view .LVU413
 1306              		.loc 1 847 6 is_stmt 0 view .LVU414
 1307 0016 0B2E     		cmp	r6, #11
 1308 0018 00D8     		bhi	.L86
 848:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* every data block must be at least MIN_SIZE_ALIGNED long */
 849:Middlewares/Third_Party/LwIP/src/core/mem.c ****     size = MIN_SIZE_ALIGNED;
 1309              		.loc 1 849 10 view .LVU415
 1310 001a 0C26     		movs	r6, #12
 1311              	.LVL126:
 1312              	.L86:
 850:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 851:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 852:Middlewares/Third_Party/LwIP/src/core/mem.c ****   size += MEM_SANITY_REGION_BEFORE_ALIGNED + MEM_SANITY_REGION_AFTER_ALIGNED;
 853:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 854:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((size > MEM_SIZE_ALIGNED) || (size < size_in)) {
 1313              		.loc 1 854 3 is_stmt 1 view .LVU416
 1314              		.loc 1 854 42 is_stmt 0 view .LVU417
 1315 001c 9E42     		cmp	r6, r3
 1316 001e 2CBF     		ite	cs
 1317 0020 0023     		movcs	r3, #0
 1318 0022 0123     		movcc	r3, #1
 1319              		.loc 1 854 33 view .LVU418
 1320 0024 B6F5C86F 		cmp	r6, #1600
 1321 0028 88BF     		it	hi
 1322 002a 43F00103 		orrhi	r3, r3, #1
 1323              		.loc 1 854 6 view .LVU419
 1324 002e 002B     		cmp	r3, #0
 1325 0030 40F09280 		bne	.L102
 855:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return NULL;
 856:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 857:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 858:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* protect the heap from concurrent access */
 859:Middlewares/Third_Party/LwIP/src/core/mem.c ****   sys_mutex_lock(&mem_mutex);
 1326              		.loc 1 859 3 is_stmt 1 view .LVU420
 1327 0034 4948     		ldr	r0, .L112
 1328              	.LVL127:
 1329              		.loc 1 859 3 is_stmt 0 view .LVU421
 1330 0036 FFF7FEFF 		bl	sys_mutex_lock
 1331              	.LVL128:
 860:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_ALLOC_PROTECT();
 1332              		.loc 1 860 27 is_stmt 1 view .LVU422
 861:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 862:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* run as long as a mem_free disturbed mem_malloc or mem_trim */
 863:Middlewares/Third_Party/LwIP/src/core/mem.c ****   do {
 864:Middlewares/Third_Party/LwIP/src/core/mem.c ****     local_mem_free_count = 0;
 865:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 866:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 867:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* Scan through the heap searching for a free block that is big enough,
 868:Middlewares/Third_Party/LwIP/src/core/mem.c ****      * beginning with the lowest free block.
 869:Middlewares/Third_Party/LwIP/src/core/mem.c ****      */
 870:Middlewares/Third_Party/LwIP/src/core/mem.c ****     for (ptr = mem_to_ptr(lfree); ptr < MEM_SIZE_ALIGNED - size;
ARM GAS  /tmp/ccwyId6Y.s 			page 45


 1333              		.loc 1 870 5 view .LVU423
 1334              		.loc 1 870 16 is_stmt 0 view .LVU424
 1335 003a 494B     		ldr	r3, .L112+4
 1336 003c 1868     		ldr	r0, [r3]
 1337 003e FFF7FEFF 		bl	mem_to_ptr
 1338              	.LVL129:
 1339 0042 0546     		mov	r5, r0
 1340              	.LVL130:
 1341              		.loc 1 870 5 view .LVU425
 1342 0044 4FE0     		b	.L87
 1343              	.LVL131:
 1344              	.L111:
 871:Middlewares/Third_Party/LwIP/src/core/mem.c ****          ptr = ptr_to_mem(ptr)->next) {
 872:Middlewares/Third_Party/LwIP/src/core/mem.c ****       mem = ptr_to_mem(ptr);
 873:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 874:Middlewares/Third_Party/LwIP/src/core/mem.c ****       mem_free_count = 0;
 875:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_MEM_ALLOC_UNPROTECT();
 876:Middlewares/Third_Party/LwIP/src/core/mem.c ****       /* allow mem_free or mem_trim to run */
 877:Middlewares/Third_Party/LwIP/src/core/mem.c ****       LWIP_MEM_ALLOC_PROTECT();
 878:Middlewares/Third_Party/LwIP/src/core/mem.c ****       if (mem_free_count != 0) {
 879:Middlewares/Third_Party/LwIP/src/core/mem.c ****         /* If mem_free or mem_trim have run, we have to restart since they
 880:Middlewares/Third_Party/LwIP/src/core/mem.c ****            could have altered our current struct mem. */
 881:Middlewares/Third_Party/LwIP/src/core/mem.c ****         local_mem_free_count = 1;
 882:Middlewares/Third_Party/LwIP/src/core/mem.c ****         break;
 883:Middlewares/Third_Party/LwIP/src/core/mem.c ****       }
 884:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 885:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 886:Middlewares/Third_Party/LwIP/src/core/mem.c ****       if ((!mem->used) &&
 887:Middlewares/Third_Party/LwIP/src/core/mem.c ****           (mem->next - (ptr + SIZEOF_STRUCT_MEM)) >= size) {
 888:Middlewares/Third_Party/LwIP/src/core/mem.c ****         /* mem is not used and at least perfect fit is possible:
 889:Middlewares/Third_Party/LwIP/src/core/mem.c ****          * mem->next - (ptr + SIZEOF_STRUCT_MEM) gives us the 'user data size' of mem */
 890:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 891:Middlewares/Third_Party/LwIP/src/core/mem.c ****         if (mem->next - (ptr + SIZEOF_STRUCT_MEM) >= (size + SIZEOF_STRUCT_MEM + MIN_SIZE_ALIGNED))
 892:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* (in addition to the above, we test if another struct mem (SIZEOF_STRUCT_MEM) containin
 893:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * at least MIN_SIZE_ALIGNED of data also fits in the 'user data space' of 'mem')
 894:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * -> split large block, create empty remainder,
 895:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * remainder must be large enough to contain MIN_SIZE_ALIGNED data: if
 896:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * mem->next - (ptr + (2*SIZEOF_STRUCT_MEM)) == size,
 897:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * struct mem would fit in but no data between mem2 and mem2->next
 898:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * @todo we could leave out MIN_SIZE_ALIGNED. We would create an empty
 899:Middlewares/Third_Party/LwIP/src/core/mem.c ****            *       region that couldn't hold data, but when mem->next gets freed,
 900:Middlewares/Third_Party/LwIP/src/core/mem.c ****            *       the 2 regions would be combined, resulting in more free memory
 901:Middlewares/Third_Party/LwIP/src/core/mem.c ****            */
 902:Middlewares/Third_Party/LwIP/src/core/mem.c ****           ptr2 = (mem_size_t)(ptr + SIZEOF_STRUCT_MEM + size);
 903:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("invalid next ptr",ptr2 != MEM_SIZE_ALIGNED);
 1345              		.loc 1 903 11 is_stmt 1 discriminator 1 view .LVU426
 1346              		.loc 1 903 11 discriminator 1 view .LVU427
 1347 0046 474B     		ldr	r3, .L112+8
 1348 0048 40F28732 		movw	r2, #903
 1349 004c 4649     		ldr	r1, .L112+12
 1350 004e 4748     		ldr	r0, .L112+16
 1351              	.LVL132:
 1352              		.loc 1 903 11 is_stmt 0 discriminator 1 view .LVU428
 1353 0050 FFF7FEFF 		bl	printf
 1354              	.LVL133:
 1355 0054 65E0     		b	.L90
 1356              	.LVL134:
ARM GAS  /tmp/ccwyId6Y.s 			page 46


 1357              	.L89:
 904:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* create mem2 struct */
 905:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2 = ptr_to_mem(ptr2);
 906:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->used = 0;
 907:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->next = mem->next;
 908:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->prev = ptr;
 909:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* and insert it between mem and mem->next */
 910:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem->next = ptr2;
 911:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem->used = 1;
 912:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 913:Middlewares/Third_Party/LwIP/src/core/mem.c ****           if (mem2->next != MEM_SIZE_ALIGNED) {
 914:Middlewares/Third_Party/LwIP/src/core/mem.c ****             ptr_to_mem(mem2->next)->prev = ptr2;
 915:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 916:Middlewares/Third_Party/LwIP/src/core/mem.c ****           MEM_STATS_INC_USED(used, (size + SIZEOF_STRUCT_MEM));
 917:Middlewares/Third_Party/LwIP/src/core/mem.c ****         } else {
 918:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* (a mem2 struct does no fit into the user data space of mem and mem->next will always
 919:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * be used at this point: if not we have 2 unused structs in a row, plug_holes should hav
 920:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * take care of this).
 921:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * -> near fit or exact fit: do not split, no mem2 creation
 922:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * also can't move mem->next directly behind mem, since mem->next
 923:Middlewares/Third_Party/LwIP/src/core/mem.c ****            * will always be used at this point!
 924:Middlewares/Third_Party/LwIP/src/core/mem.c ****            */
 925:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem->used = 1;
 1358              		.loc 1 925 11 is_stmt 1 view .LVU429
 1359              		.loc 1 925 21 is_stmt 0 view .LVU430
 1360 0056 0123     		movs	r3, #1
 1361 0058 0371     		strb	r3, [r0, #4]
 1362              	.LVL135:
 1363              	.L91:
 926:Middlewares/Third_Party/LwIP/src/core/mem.c ****           MEM_STATS_INC_USED(used, mem->next - mem_to_ptr(mem));
 1364              		.loc 1 926 64 is_stmt 1 view .LVU431
 927:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 928:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 929:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_malloc_adjust_lfree:
 930:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 931:Middlewares/Third_Party/LwIP/src/core/mem.c ****         if (mem == lfree) {
 1365              		.loc 1 931 9 view .LVU432
 1366              		.loc 1 931 17 is_stmt 0 view .LVU433
 1367 005a 414B     		ldr	r3, .L112+4
 1368 005c 1868     		ldr	r0, [r3]
 1369              		.loc 1 931 12 view .LVU434
 1370 005e A042     		cmp	r0, r4
 1371 0060 12D0     		beq	.L92
 1372              	.L93:
 1373              	.LBB9:
 932:Middlewares/Third_Party/LwIP/src/core/mem.c ****           struct mem *cur = lfree;
 933:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* Find next free block after mem and update lowest free pointer */
 934:Middlewares/Third_Party/LwIP/src/core/mem.c ****           while (cur->used && cur != ram_end) {
 935:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 936:Middlewares/Third_Party/LwIP/src/core/mem.c ****             mem_free_count = 0;
 937:Middlewares/Third_Party/LwIP/src/core/mem.c ****             LWIP_MEM_ALLOC_UNPROTECT();
 938:Middlewares/Third_Party/LwIP/src/core/mem.c ****             /* prevent high interrupt latency... */
 939:Middlewares/Third_Party/LwIP/src/core/mem.c ****             LWIP_MEM_ALLOC_PROTECT();
 940:Middlewares/Third_Party/LwIP/src/core/mem.c ****             if (mem_free_count != 0) {
 941:Middlewares/Third_Party/LwIP/src/core/mem.c ****               /* If mem_free or mem_trim have run, we have to restart since they
 942:Middlewares/Third_Party/LwIP/src/core/mem.c ****                  could have altered our current struct mem or lfree. */
 943:Middlewares/Third_Party/LwIP/src/core/mem.c ****               goto mem_malloc_adjust_lfree;
ARM GAS  /tmp/ccwyId6Y.s 			page 47


 944:Middlewares/Third_Party/LwIP/src/core/mem.c ****             }
 945:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 946:Middlewares/Third_Party/LwIP/src/core/mem.c ****             cur = ptr_to_mem(cur->next);
 947:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 948:Middlewares/Third_Party/LwIP/src/core/mem.c ****           lfree = cur;
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("mem_malloc: !lfree->used", ((lfree == ram_end) || (!lfree->used)));
 1374              		.loc 1 949 11 is_stmt 1 discriminator 5 view .LVU435
 1375              		.loc 1 949 11 discriminator 5 view .LVU436
 1376              	.LBE9:
 950:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 951:Middlewares/Third_Party/LwIP/src/core/mem.c ****         LWIP_MEM_ALLOC_UNPROTECT();
 1377              		.loc 1 951 35 view .LVU437
 952:Middlewares/Third_Party/LwIP/src/core/mem.c ****         sys_mutex_unlock(&mem_mutex);
 1378              		.loc 1 952 9 view .LVU438
 1379 0062 3E48     		ldr	r0, .L112
 1380 0064 FFF7FEFF 		bl	sys_mutex_unlock
 1381              	.LVL136:
 953:Middlewares/Third_Party/LwIP/src/core/mem.c ****         LWIP_ASSERT("mem_malloc: allocated memory not above ram_end.",
 1382              		.loc 1 953 9 view .LVU439
 1383              		.loc 1 953 9 view .LVU440
 1384 0068 2744     		add	r7, r7, r4
 1385              	.LVL137:
 1386              		.loc 1 953 9 is_stmt 0 view .LVU441
 1387 006a 0837     		adds	r7, r7, #8
 1388 006c 404B     		ldr	r3, .L112+20
 1389 006e 1B68     		ldr	r3, [r3]
 1390 0070 9F42     		cmp	r7, r3
 1391 0072 1FD8     		bhi	.L107
 1392              	.L96:
 1393              		.loc 1 953 9 is_stmt 1 discriminator 3 view .LVU442
 1394              		.loc 1 953 9 discriminator 3 view .LVU443
 954:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     (mem_ptr_t)mem + SIZEOF_STRUCT_MEM + size <= (mem_ptr_t)ram_end);
 955:Middlewares/Third_Party/LwIP/src/core/mem.c ****         LWIP_ASSERT("mem_malloc: allocated memory properly aligned.",
 1395              		.loc 1 955 9 view .LVU444
 1396              		.loc 1 955 9 view .LVU445
 1397 0074 14F00305 		ands	r5, r4, #3
 1398              	.LVL138:
 1399              		.loc 1 955 9 is_stmt 0 view .LVU446
 1400 0078 24D1     		bne	.L108
 1401              	.L97:
 1402              		.loc 1 955 9 is_stmt 1 discriminator 3 view .LVU447
 1403              		.loc 1 955 9 discriminator 3 view .LVU448
 956:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     ((mem_ptr_t)mem + SIZEOF_STRUCT_MEM) % MEM_ALIGNMENT == 0);
 957:Middlewares/Third_Party/LwIP/src/core/mem.c ****         LWIP_ASSERT("mem_malloc: sanity check alignment",
 1404              		.loc 1 957 9 view .LVU449
 1405              		.loc 1 957 9 view .LVU450
 1406 007a 5DBB     		cbnz	r5, .L109
 1407              	.L98:
 1408              		.loc 1 957 9 discriminator 3 view .LVU451
 1409              		.loc 1 957 9 discriminator 3 view .LVU452
 958:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     (((mem_ptr_t)mem) & (MEM_ALIGNMENT - 1)) == 0);
 959:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 960:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_OVERFLOW_CHECK
 961:Middlewares/Third_Party/LwIP/src/core/mem.c ****         mem_overflow_init_element(mem, size_in);
 962:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif
 963:Middlewares/Third_Party/LwIP/src/core/mem.c ****         MEM_SANITY();
 1410              		.loc 1 963 21 view .LVU453
ARM GAS  /tmp/ccwyId6Y.s 			page 48


 964:Middlewares/Third_Party/LwIP/src/core/mem.c ****         return (u8_t *)mem + SIZEOF_STRUCT_MEM + MEM_SANITY_OFFSET;
 1411              		.loc 1 964 9 view .LVU454
 1412              		.loc 1 964 48 is_stmt 0 view .LVU455
 1413 007c 04F10800 		add	r0, r4, #8
 1414 0080 66E0     		b	.L84
 1415              	.LVL139:
 1416              	.L95:
 1417              	.LBB10:
 946:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 1418              		.loc 1 946 13 is_stmt 1 view .LVU456
 946:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 1419              		.loc 1 946 19 is_stmt 0 view .LVU457
 1420 0082 0088     		ldrh	r0, [r0]
 1421              	.LVL140:
 946:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 1422              		.loc 1 946 19 view .LVU458
 1423 0084 FFF7FEFF 		bl	ptr_to_mem
 1424              	.LVL141:
 1425              	.L92:
 934:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1426              		.loc 1 934 28 is_stmt 1 view .LVU459
 934:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1427              		.loc 1 934 21 is_stmt 0 view .LVU460
 1428 0088 0379     		ldrb	r3, [r0, #4]	@ zero_extendqisi2
 934:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1429              		.loc 1 934 28 view .LVU461
 1430 008a 1BB1     		cbz	r3, .L94
 934:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1431              		.loc 1 934 35 discriminator 1 view .LVU462
 1432 008c 384A     		ldr	r2, .L112+20
 1433 008e 1268     		ldr	r2, [r2]
 934:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1434              		.loc 1 934 28 discriminator 1 view .LVU463
 1435 0090 8242     		cmp	r2, r0
 1436 0092 F6D1     		bne	.L95
 1437              	.L94:
 948:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("mem_malloc: !lfree->used", ((lfree == ram_end) || (!lfree->used)));
 1438              		.loc 1 948 11 is_stmt 1 view .LVU464
 948:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("mem_malloc: !lfree->used", ((lfree == ram_end) || (!lfree->used)));
 1439              		.loc 1 948 17 is_stmt 0 view .LVU465
 1440 0094 324A     		ldr	r2, .L112+4
 1441 0096 1060     		str	r0, [r2]
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1442              		.loc 1 949 11 is_stmt 1 view .LVU466
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1443              		.loc 1 949 11 view .LVU467
 1444 0098 354A     		ldr	r2, .L112+20
 1445 009a 1268     		ldr	r2, [r2]
 1446 009c 8242     		cmp	r2, r0
 1447 009e E0D0     		beq	.L93
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1448              		.loc 1 949 11 is_stmt 0 discriminator 1 view .LVU468
 1449 00a0 002B     		cmp	r3, #0
 1450 00a2 DED0     		beq	.L93
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1451              		.loc 1 949 11 is_stmt 1 discriminator 3 view .LVU469
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
ARM GAS  /tmp/ccwyId6Y.s 			page 49


 1452              		.loc 1 949 11 discriminator 3 view .LVU470
 1453 00a4 2F4B     		ldr	r3, .L112+8
 1454 00a6 40F2B532 		movw	r2, #949
 1455 00aa 3249     		ldr	r1, .L112+24
 1456 00ac 2F48     		ldr	r0, .L112+16
 1457              	.LVL142:
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1458              		.loc 1 949 11 is_stmt 0 discriminator 3 view .LVU471
 1459 00ae FFF7FEFF 		bl	printf
 1460              	.LVL143:
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1461              		.loc 1 949 11 discriminator 3 view .LVU472
 1462 00b2 D6E7     		b	.L93
 1463              	.LVL144:
 1464              	.L107:
 949:Middlewares/Third_Party/LwIP/src/core/mem.c ****         }
 1465              		.loc 1 949 11 discriminator 3 view .LVU473
 1466              	.LBE10:
 953:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     (mem_ptr_t)mem + SIZEOF_STRUCT_MEM + size <= (mem_ptr_t)ram_end);
 1467              		.loc 1 953 9 is_stmt 1 discriminator 1 view .LVU474
 953:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     (mem_ptr_t)mem + SIZEOF_STRUCT_MEM + size <= (mem_ptr_t)ram_end);
 1468              		.loc 1 953 9 discriminator 1 view .LVU475
 1469 00b4 2B4B     		ldr	r3, .L112+8
 1470 00b6 40F2B932 		movw	r2, #953
 1471 00ba 2F49     		ldr	r1, .L112+28
 1472 00bc 2B48     		ldr	r0, .L112+16
 1473 00be FFF7FEFF 		bl	printf
 1474              	.LVL145:
 1475 00c2 D7E7     		b	.L96
 1476              	.LVL146:
 1477              	.L108:
 955:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     ((mem_ptr_t)mem + SIZEOF_STRUCT_MEM) % MEM_ALIGNMENT == 0);
 1478              		.loc 1 955 9 discriminator 1 view .LVU476
 955:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     ((mem_ptr_t)mem + SIZEOF_STRUCT_MEM) % MEM_ALIGNMENT == 0);
 1479              		.loc 1 955 9 discriminator 1 view .LVU477
 1480 00c4 274B     		ldr	r3, .L112+8
 1481 00c6 40F2BB32 		movw	r2, #955
 1482 00ca 2C49     		ldr	r1, .L112+32
 1483 00cc 2748     		ldr	r0, .L112+16
 1484 00ce FFF7FEFF 		bl	printf
 1485              	.LVL147:
 1486 00d2 D2E7     		b	.L97
 1487              	.L109:
 957:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     (((mem_ptr_t)mem) & (MEM_ALIGNMENT - 1)) == 0);
 1488              		.loc 1 957 9 discriminator 1 view .LVU478
 957:Middlewares/Third_Party/LwIP/src/core/mem.c ****                     (((mem_ptr_t)mem) & (MEM_ALIGNMENT - 1)) == 0);
 1489              		.loc 1 957 9 discriminator 1 view .LVU479
 1490 00d4 234B     		ldr	r3, .L112+8
 1491 00d6 40F2BD32 		movw	r2, #957
 1492 00da 2949     		ldr	r1, .L112+36
 1493 00dc 2348     		ldr	r0, .L112+16
 1494 00de FFF7FEFF 		bl	printf
 1495              	.LVL148:
 1496 00e2 CBE7     		b	.L98
 1497              	.LVL149:
 1498              	.L88:
 871:Middlewares/Third_Party/LwIP/src/core/mem.c ****       mem = ptr_to_mem(ptr);
ARM GAS  /tmp/ccwyId6Y.s 			page 50


 1499              		.loc 1 871 14 view .LVU480
 871:Middlewares/Third_Party/LwIP/src/core/mem.c ****       mem = ptr_to_mem(ptr);
 1500              		.loc 1 871 14 is_stmt 0 discriminator 1 view .LVU481
 1501 00e4 2588     		ldrh	r5, [r4]
 1502              	.LVL150:
 1503              	.L87:
 870:Middlewares/Third_Party/LwIP/src/core/mem.c ****          ptr = ptr_to_mem(ptr)->next) {
 1504              		.loc 1 870 39 is_stmt 1 discriminator 1 view .LVU482
 1505 00e6 A846     		mov	r8, r5
 870:Middlewares/Third_Party/LwIP/src/core/mem.c ****          ptr = ptr_to_mem(ptr)->next) {
 1506              		.loc 1 870 58 is_stmt 0 discriminator 1 view .LVU483
 1507 00e8 3746     		mov	r7, r6
 1508 00ea C6F5C863 		rsb	r3, r6, #1600
 870:Middlewares/Third_Party/LwIP/src/core/mem.c ****          ptr = ptr_to_mem(ptr)->next) {
 1509              		.loc 1 870 39 discriminator 1 view .LVU484
 1510 00ee 9D42     		cmp	r5, r3
 1511 00f0 2AD2     		bcs	.L110
 872:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1512              		.loc 1 872 7 is_stmt 1 view .LVU485
 872:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 1513              		.loc 1 872 13 is_stmt 0 view .LVU486
 1514 00f2 2846     		mov	r0, r5
 1515 00f4 FFF7FEFF 		bl	ptr_to_mem
 1516              	.LVL151:
 1517 00f8 0446     		mov	r4, r0
 1518              	.LVL152:
 886:Middlewares/Third_Party/LwIP/src/core/mem.c ****           (mem->next - (ptr + SIZEOF_STRUCT_MEM)) >= size) {
 1519              		.loc 1 886 7 is_stmt 1 view .LVU487
 886:Middlewares/Third_Party/LwIP/src/core/mem.c ****           (mem->next - (ptr + SIZEOF_STRUCT_MEM)) >= size) {
 1520              		.loc 1 886 16 is_stmt 0 view .LVU488
 1521 00fa 0379     		ldrb	r3, [r0, #4]	@ zero_extendqisi2
 886:Middlewares/Third_Party/LwIP/src/core/mem.c ****           (mem->next - (ptr + SIZEOF_STRUCT_MEM)) >= size) {
 1522              		.loc 1 886 10 view .LVU489
 1523 00fc 002B     		cmp	r3, #0
 1524 00fe F1D1     		bne	.L88
 887:Middlewares/Third_Party/LwIP/src/core/mem.c ****         /* mem is not used and at least perfect fit is possible:
 1525              		.loc 1 887 15 view .LVU490
 1526 0100 0388     		ldrh	r3, [r0]
 887:Middlewares/Third_Party/LwIP/src/core/mem.c ****         /* mem is not used and at least perfect fit is possible:
 1527              		.loc 1 887 22 view .LVU491
 1528 0102 A3EB0803 		sub	r3, r3, r8
 1529 0106 083B     		subs	r3, r3, #8
 886:Middlewares/Third_Party/LwIP/src/core/mem.c ****           (mem->next - (ptr + SIZEOF_STRUCT_MEM)) >= size) {
 1530              		.loc 1 886 24 discriminator 1 view .LVU492
 1531 0108 BB42     		cmp	r3, r7
 1532 010a EBD3     		bcc	.L88
 891:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* (in addition to the above, we test if another struct mem (SIZEOF_STRUCT_MEM) containin
 1533              		.loc 1 891 9 is_stmt 1 view .LVU493
 891:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* (in addition to the above, we test if another struct mem (SIZEOF_STRUCT_MEM) containin
 1534              		.loc 1 891 80 is_stmt 0 view .LVU494
 1535 010c 07F11402 		add	r2, r7, #20
 891:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* (in addition to the above, we test if another struct mem (SIZEOF_STRUCT_MEM) containin
 1536              		.loc 1 891 12 view .LVU495
 1537 0110 9342     		cmp	r3, r2
 1538 0112 A0D3     		bcc	.L89
 902:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("invalid next ptr",ptr2 != MEM_SIZE_ALIGNED);
 1539              		.loc 1 902 11 is_stmt 1 view .LVU496
ARM GAS  /tmp/ccwyId6Y.s 			page 51


 902:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("invalid next ptr",ptr2 != MEM_SIZE_ALIGNED);
 1540              		.loc 1 902 55 is_stmt 0 view .LVU497
 1541 0114 2E44     		add	r6, r6, r5
 1542              	.LVL153:
 902:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("invalid next ptr",ptr2 != MEM_SIZE_ALIGNED);
 1543              		.loc 1 902 55 view .LVU498
 1544 0116 B6B2     		uxth	r6, r6
 902:Middlewares/Third_Party/LwIP/src/core/mem.c ****           LWIP_ASSERT("invalid next ptr",ptr2 != MEM_SIZE_ALIGNED);
 1545              		.loc 1 902 16 view .LVU499
 1546 0118 0836     		adds	r6, r6, #8
 1547 011a B6B2     		uxth	r6, r6
 1548              	.LVL154:
 903:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* create mem2 struct */
 1549              		.loc 1 903 11 is_stmt 1 view .LVU500
 903:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* create mem2 struct */
 1550              		.loc 1 903 11 view .LVU501
 1551 011c B6F5C86F 		cmp	r6, #1600
 1552 0120 91D0     		beq	.L111
 1553              	.LVL155:
 1554              	.L90:
 903:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* create mem2 struct */
 1555              		.loc 1 903 11 discriminator 3 view .LVU502
 903:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* create mem2 struct */
 1556              		.loc 1 903 11 discriminator 3 view .LVU503
 905:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->used = 0;
 1557              		.loc 1 905 11 view .LVU504
 905:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->used = 0;
 1558              		.loc 1 905 18 is_stmt 0 view .LVU505
 1559 0122 3046     		mov	r0, r6
 1560 0124 FFF7FEFF 		bl	ptr_to_mem
 1561              	.LVL156:
 906:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->next = mem->next;
 1562              		.loc 1 906 11 is_stmt 1 view .LVU506
 906:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->next = mem->next;
 1563              		.loc 1 906 22 is_stmt 0 view .LVU507
 1564 0128 0023     		movs	r3, #0
 1565 012a 0371     		strb	r3, [r0, #4]
 907:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->prev = ptr;
 1566              		.loc 1 907 11 is_stmt 1 view .LVU508
 907:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->prev = ptr;
 1567              		.loc 1 907 27 is_stmt 0 view .LVU509
 1568 012c 2388     		ldrh	r3, [r4]
 907:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem2->prev = ptr;
 1569              		.loc 1 907 22 view .LVU510
 1570 012e 0380     		strh	r3, [r0]	@ movhi
 908:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* and insert it between mem and mem->next */
 1571              		.loc 1 908 11 is_stmt 1 view .LVU511
 908:Middlewares/Third_Party/LwIP/src/core/mem.c ****           /* and insert it between mem and mem->next */
 1572              		.loc 1 908 22 is_stmt 0 view .LVU512
 1573 0130 4580     		strh	r5, [r0, #2]	@ movhi
 910:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem->used = 1;
 1574              		.loc 1 910 11 is_stmt 1 view .LVU513
 910:Middlewares/Third_Party/LwIP/src/core/mem.c ****           mem->used = 1;
 1575              		.loc 1 910 21 is_stmt 0 view .LVU514
 1576 0132 2680     		strh	r6, [r4]	@ movhi
 911:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 1577              		.loc 1 911 11 is_stmt 1 view .LVU515
ARM GAS  /tmp/ccwyId6Y.s 			page 52


 911:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 1578              		.loc 1 911 21 is_stmt 0 view .LVU516
 1579 0134 0123     		movs	r3, #1
 1580 0136 2371     		strb	r3, [r4, #4]
 913:Middlewares/Third_Party/LwIP/src/core/mem.c ****             ptr_to_mem(mem2->next)->prev = ptr2;
 1581              		.loc 1 913 11 is_stmt 1 view .LVU517
 913:Middlewares/Third_Party/LwIP/src/core/mem.c ****             ptr_to_mem(mem2->next)->prev = ptr2;
 1582              		.loc 1 913 19 is_stmt 0 view .LVU518
 1583 0138 0088     		ldrh	r0, [r0]
 1584              	.LVL157:
 913:Middlewares/Third_Party/LwIP/src/core/mem.c ****             ptr_to_mem(mem2->next)->prev = ptr2;
 1585              		.loc 1 913 14 view .LVU519
 1586 013a B0F5C86F 		cmp	r0, #1600
 1587 013e 8CD0     		beq	.L91
 914:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 1588              		.loc 1 914 13 is_stmt 1 view .LVU520
 1589 0140 FFF7FEFF 		bl	ptr_to_mem
 1590              	.LVL158:
 914:Middlewares/Third_Party/LwIP/src/core/mem.c ****           }
 1591              		.loc 1 914 42 is_stmt 0 discriminator 1 view .LVU521
 1592 0144 4680     		strh	r6, [r0, #2]	@ movhi
 1593 0146 88E7     		b	.L91
 1594              	.LVL159:
 1595              	.L110:
 965:Middlewares/Third_Party/LwIP/src/core/mem.c ****       }
 966:Middlewares/Third_Party/LwIP/src/core/mem.c ****     }
 967:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT
 968:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* if we got interrupted by a mem_free, try again */
 969:Middlewares/Third_Party/LwIP/src/core/mem.c ****   } while (local_mem_free_count != 0);
 970:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* LWIP_ALLOW_MEM_FREE_FROM_OTHER_CONTEXT */
 971:Middlewares/Third_Party/LwIP/src/core/mem.c ****   MEM_STATS_INC(err);
 1596              		.loc 1 971 21 is_stmt 1 view .LVU522
 972:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_MEM_ALLOC_UNPROTECT();
 1597              		.loc 1 972 29 view .LVU523
 973:Middlewares/Third_Party/LwIP/src/core/mem.c ****   sys_mutex_unlock(&mem_mutex);
 1598              		.loc 1 973 3 view .LVU524
 1599 0148 0448     		ldr	r0, .L112
 1600 014a FFF7FEFF 		bl	sys_mutex_unlock
 1601              	.LVL160:
 974:Middlewares/Third_Party/LwIP/src/core/mem.c ****   LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("mem_malloc: could not allocate %"S16_F" bytes\n
 1602              		.loc 1 974 116 view .LVU525
 975:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return NULL;
 1603              		.loc 1 975 3 view .LVU526
 1604              		.loc 1 975 10 is_stmt 0 view .LVU527
 1605 014e 0020     		movs	r0, #0
 1606              	.LVL161:
 1607              	.L84:
 976:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 1608              		.loc 1 976 1 view .LVU528
 1609 0150 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 1610              	.LVL162:
 1611              	.L100:
 1612              	.LCFI7:
 1613              		.cfi_def_cfa_offset 0
 1614              		.cfi_restore 4
 1615              		.cfi_restore 5
 1616              		.cfi_restore 6
ARM GAS  /tmp/ccwyId6Y.s 			page 53


 1617              		.cfi_restore 7
 1618              		.cfi_restore 8
 1619              		.cfi_restore 14
 841:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1620              		.loc 1 841 12 view .LVU529
 1621 0154 0020     		movs	r0, #0
 1622              	.LVL163:
 1623              		.loc 1 976 1 view .LVU530
 1624 0156 7047     		bx	lr
 1625              	.LVL164:
 1626              	.L102:
 1627              	.LCFI8:
 1628              		.cfi_def_cfa_offset 24
 1629              		.cfi_offset 4, -24
 1630              		.cfi_offset 5, -20
 1631              		.cfi_offset 6, -16
 1632              		.cfi_offset 7, -12
 1633              		.cfi_offset 8, -8
 1634              		.cfi_offset 14, -4
 855:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1635              		.loc 1 855 12 view .LVU531
 1636 0158 0020     		movs	r0, #0
 1637              	.LVL165:
 855:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1638              		.loc 1 855 12 view .LVU532
 1639 015a F9E7     		b	.L84
 1640              	.L113:
 1641              		.align	2
 1642              	.L112:
 1643 015c 00000000 		.word	mem_mutex
 1644 0160 00000000 		.word	lfree
 1645 0164 00000000 		.word	.LC0
 1646 0168 38000000 		.word	.LC13
 1647 016c 44000000 		.word	.LC2
 1648 0170 00000000 		.word	ram_end
 1649 0174 00000000 		.word	.LC14
 1650 0178 1C000000 		.word	.LC15
 1651 017c 4C000000 		.word	.LC16
 1652 0180 7C000000 		.word	.LC17
 1653              		.cfi_endproc
 1654              	.LFE181:
 1656              		.section	.text.mem_calloc,"ax",%progbits
 1657              		.align	1
 1658              		.global	mem_calloc
 1659              		.syntax unified
 1660              		.thumb
 1661              		.thumb_func
 1663              	mem_calloc:
 1664              	.LVL166:
 1665              	.LFB182:
 977:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 978:Middlewares/Third_Party/LwIP/src/core/mem.c **** #endif /* MEM_USE_POOLS */
 979:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 980:Middlewares/Third_Party/LwIP/src/core/mem.c **** #if MEM_LIBC_MALLOC && (!LWIP_STATS || !MEM_STATS)
 981:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 982:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_calloc(mem_size_t count, mem_size_t size)
 983:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
ARM GAS  /tmp/ccwyId6Y.s 			page 54


 984:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return mem_clib_calloc(count, size);
 985:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 986:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
 987:Middlewares/Third_Party/LwIP/src/core/mem.c **** #else /* MEM_LIBC_MALLOC && (!LWIP_STATS || !MEM_STATS) */
 988:Middlewares/Third_Party/LwIP/src/core/mem.c **** /**
 989:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * Contiguously allocates enough space for count objects that are size bytes
 990:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * of memory each and returns a pointer to the allocated memory.
 991:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 992:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * The allocated memory is filled with bytes of value zero.
 993:Middlewares/Third_Party/LwIP/src/core/mem.c ****  *
 994:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param count number of objects to allocate
 995:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @param size size of the objects to allocate
 996:Middlewares/Third_Party/LwIP/src/core/mem.c ****  * @return pointer to allocated memory / NULL pointer if there is an error
 997:Middlewares/Third_Party/LwIP/src/core/mem.c ****  */
 998:Middlewares/Third_Party/LwIP/src/core/mem.c **** void *
 999:Middlewares/Third_Party/LwIP/src/core/mem.c **** mem_calloc(mem_size_t count, mem_size_t size)
1000:Middlewares/Third_Party/LwIP/src/core/mem.c **** {
 1666              		.loc 1 1000 1 is_stmt 1 view -0
 1667              		.cfi_startproc
 1668              		@ args = 0, pretend = 0, frame = 0
 1669              		@ frame_needed = 0, uses_anonymous_args = 0
 1670              		.loc 1 1000 1 is_stmt 0 view .LVU534
 1671 0000 38B5     		push	{r3, r4, r5, lr}
 1672              	.LCFI9:
 1673              		.cfi_def_cfa_offset 16
 1674              		.cfi_offset 3, -16
 1675              		.cfi_offset 4, -12
 1676              		.cfi_offset 5, -8
 1677              		.cfi_offset 14, -4
1001:Middlewares/Third_Party/LwIP/src/core/mem.c ****   void *p;
 1678              		.loc 1 1001 3 is_stmt 1 view .LVU535
1002:Middlewares/Third_Party/LwIP/src/core/mem.c ****   size_t alloc_size = (size_t)count * (size_t)size;
 1679              		.loc 1 1002 3 view .LVU536
 1680              		.loc 1 1002 10 is_stmt 0 view .LVU537
 1681 0002 01FB00F4 		mul	r4, r1, r0
 1682              	.LVL167:
1003:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
1004:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if ((size_t)(mem_size_t)alloc_size != alloc_size) {
 1683              		.loc 1 1004 3 is_stmt 1 view .LVU538
 1684              		.loc 1 1004 6 is_stmt 0 view .LVU539
 1685 0006 B4F5803F 		cmp	r4, #65536
 1686 000a 0AD2     		bcs	.L116
1005:Middlewares/Third_Party/LwIP/src/core/mem.c ****     LWIP_DEBUGF(MEM_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("mem_calloc: could not allocate %"SZT_F" bytes
1006:Middlewares/Third_Party/LwIP/src/core/mem.c ****     return NULL;
1007:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
1008:Middlewares/Third_Party/LwIP/src/core/mem.c **** 
1009:Middlewares/Third_Party/LwIP/src/core/mem.c ****   /* allocate 'count' objects of size 'size' */
1010:Middlewares/Third_Party/LwIP/src/core/mem.c ****   p = mem_malloc((mem_size_t)alloc_size);
 1687              		.loc 1 1010 3 is_stmt 1 view .LVU540
 1688              		.loc 1 1010 7 is_stmt 0 view .LVU541
 1689 000c A0B2     		uxth	r0, r4
 1690              	.LVL168:
 1691              		.loc 1 1010 7 view .LVU542
 1692 000e FFF7FEFF 		bl	mem_malloc
 1693              	.LVL169:
1011:Middlewares/Third_Party/LwIP/src/core/mem.c ****   if (p) {
 1694              		.loc 1 1011 3 is_stmt 1 view .LVU543
ARM GAS  /tmp/ccwyId6Y.s 			page 55


 1695              		.loc 1 1011 6 is_stmt 0 view .LVU544
 1696 0012 0546     		mov	r5, r0
 1697 0014 18B1     		cbz	r0, .L114
1012:Middlewares/Third_Party/LwIP/src/core/mem.c ****     /* zero the memory */
1013:Middlewares/Third_Party/LwIP/src/core/mem.c ****     memset(p, 0, alloc_size);
 1698              		.loc 1 1013 5 is_stmt 1 view .LVU545
 1699 0016 2246     		mov	r2, r4
 1700 0018 0021     		movs	r1, #0
 1701 001a FFF7FEFF 		bl	memset
 1702              	.LVL170:
 1703              	.L114:
1014:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
1015:Middlewares/Third_Party/LwIP/src/core/mem.c ****   return p;
1016:Middlewares/Third_Party/LwIP/src/core/mem.c **** }
 1704              		.loc 1 1016 1 is_stmt 0 view .LVU546
 1705 001e 2846     		mov	r0, r5
 1706 0020 38BD     		pop	{r3, r4, r5, pc}
 1707              	.LVL171:
 1708              	.L116:
1006:Middlewares/Third_Party/LwIP/src/core/mem.c ****   }
 1709              		.loc 1 1006 12 view .LVU547
 1710 0022 0025     		movs	r5, #0
 1711 0024 FBE7     		b	.L114
 1712              		.cfi_endproc
 1713              	.LFE182:
 1715              		.section	.bss.lfree,"aw",%nobits
 1716              		.align	2
 1719              	lfree:
 1720 0000 00000000 		.space	4
 1721              		.section	.bss.mem_mutex,"aw",%nobits
 1722              		.align	2
 1725              	mem_mutex:
 1726 0000 00000000 		.space	4
 1727              		.section	.bss.ram_end,"aw",%nobits
 1728              		.align	2
 1731              	ram_end:
 1732 0000 00000000 		.space	4
 1733              		.section	.bss.ram,"aw",%nobits
 1734              		.align	2
 1737              	ram:
 1738 0000 00000000 		.space	4
 1739              		.text
 1740              	.Letext0:
 1741              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1742              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1743              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1744              		.file 5 "Middlewares/Third_Party/LwIP/system/arch/cc.h"
 1745              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 1746              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/mem.h"
 1747              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 1748              		.file 9 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 1749              		.file 10 "Middlewares/Third_Party/LwIP/system/arch/sys_arch.h"
 1750              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 1751              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
 1752              		.file 13 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 1753              		.file 14 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 1754              		.file 15 "<built-in>"
ARM GAS  /tmp/ccwyId6Y.s 			page 56


ARM GAS  /tmp/ccwyId6Y.s 			page 57


DEFINED SYMBOLS
                            *ABS*:00000000 mem.c
     /tmp/ccwyId6Y.s:20     .text.ptr_to_mem:00000000 $t
     /tmp/ccwyId6Y.s:25     .text.ptr_to_mem:00000000 ptr_to_mem
     /tmp/ccwyId6Y.s:45     .text.ptr_to_mem:00000008 $d
     /tmp/ccwyId6Y.s:1737   .bss.ram:00000000 ram
     /tmp/ccwyId6Y.s:50     .text.mem_to_ptr:00000000 $t
     /tmp/ccwyId6Y.s:55     .text.mem_to_ptr:00000000 mem_to_ptr
     /tmp/ccwyId6Y.s:75     .text.mem_to_ptr:0000000c $d
     /tmp/ccwyId6Y.s:80     .text.mem_link_valid:00000000 $t
     /tmp/ccwyId6Y.s:85     .text.mem_link_valid:00000000 mem_link_valid
     /tmp/ccwyId6Y.s:201    .text.mem_link_valid:00000058 $d
     /tmp/ccwyId6Y.s:1731   .bss.ram_end:00000000 ram_end
     /tmp/ccwyId6Y.s:206    .rodata.plug_holes.str1.4:00000000 $d
     /tmp/ccwyId6Y.s:225    .text.plug_holes:00000000 $t
     /tmp/ccwyId6Y.s:230    .text.plug_holes:00000000 plug_holes
     /tmp/ccwyId6Y.s:438    .text.plug_holes:000000d8 $d
     /tmp/ccwyId6Y.s:1719   .bss.lfree:00000000 lfree
     /tmp/ccwyId6Y.s:451    .rodata.mem_init.str1.4:00000000 $d
     /tmp/ccwyId6Y.s:455    .text.mem_init:00000000 $t
     /tmp/ccwyId6Y.s:461    .text.mem_init:00000000 mem_init
     /tmp/ccwyId6Y.s:550    .text.mem_init:00000048 $d
     /tmp/ccwyId6Y.s:1725   .bss.mem_mutex:00000000 mem_mutex
     /tmp/ccwyId6Y.s:562    .rodata.mem_free.str1.4:00000000 $d
     /tmp/ccwyId6Y.s:575    .text.mem_free:00000000 $t
     /tmp/ccwyId6Y.s:581    .text.mem_free:00000000 mem_free
     /tmp/ccwyId6Y.s:818    .text.mem_free:000000c8 $d
     /tmp/ccwyId6Y.s:832    .rodata.mem_trim.str1.4:00000000 $d
     /tmp/ccwyId6Y.s:842    .text.mem_trim:00000000 $t
     /tmp/ccwyId6Y.s:848    .text.mem_trim:00000000 mem_trim
     /tmp/ccwyId6Y.s:1240   .text.mem_trim:00000180 $d
     /tmp/ccwyId6Y.s:1253   .rodata.mem_malloc.str1.4:00000000 $d
     /tmp/ccwyId6Y.s:1266   .text.mem_malloc:00000000 $t
     /tmp/ccwyId6Y.s:1272   .text.mem_malloc:00000000 mem_malloc
     /tmp/ccwyId6Y.s:1643   .text.mem_malloc:0000015c $d
     /tmp/ccwyId6Y.s:1657   .text.mem_calloc:00000000 $t
     /tmp/ccwyId6Y.s:1663   .text.mem_calloc:00000000 mem_calloc
     /tmp/ccwyId6Y.s:1716   .bss.lfree:00000000 $d
     /tmp/ccwyId6Y.s:1722   .bss.mem_mutex:00000000 $d
     /tmp/ccwyId6Y.s:1728   .bss.ram_end:00000000 $d
     /tmp/ccwyId6Y.s:1734   .bss.ram:00000000 $d

UNDEFINED SYMBOLS
printf
sys_mutex_new
sys_arch_protect
sys_arch_unprotect
sys_mutex_lock
sys_mutex_unlock
memset
