ARM GAS  /tmp/cc6VmGwb.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"freertos.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/freertos.c"
  19              		.section	.rodata.StartDefaultTask.str1.4,"aMS",%progbits,1
  20              		.align	2
  21              	.LC0:
  22 0000 3139322E 		.ascii	"***********\000"
  22      3136382E 
  22      312E3100 
  23              		.align	2
  24              	.LC1:
  25 000c 4572726F 		.ascii	"Error on default allocators (line %d)\012\000"
  25      72206F6E 
  25      20646566 
  25      61756C74 
  25      20616C6C 
  26 0033 00       		.align	2
  27              	.LC2:
  28 0034 00       		.ascii	"\000"
  29 0035 000000   		.align	2
  30              	.LC3:
  31 0038 63756265 		.ascii	"cubemx_node\000"
  31      6D785F6E 
  31      6F646500 
  32              		.align	2
  33              	.LC4:
  34 0044 63756265 		.ascii	"cubemx_publisher\000"
  34      6D785F70 
  34      75626C69 
  34      73686572 
  34      00
  35 0055 000000   		.align	2
  36              	.LC5:
  37 0058 4572726F 		.ascii	"Error publishing (line %d)\012\000"
  37      72207075 
  37      626C6973 
  37      68696E67 
  37      20286C69 
  38              		.section	.text.StartDefaultTask,"ax",%progbits
  39              		.align	1
  40              		.global	StartDefaultTask
  41              		.syntax unified
ARM GAS  /tmp/cc6VmGwb.s 			page 2


  42              		.thumb
  43              		.thumb_func
  45              	StartDefaultTask:
  46              	.LVL0:
  47              	.LFB153:
   1:Core/Src/freertos.c **** /* USER CODE BEGIN Header */
   2:Core/Src/freertos.c **** /**
   3:Core/Src/freertos.c ****   ******************************************************************************
   4:Core/Src/freertos.c ****   * File Name          : freertos.c
   5:Core/Src/freertos.c ****   * Description        : Code for freertos applications
   6:Core/Src/freertos.c ****   ******************************************************************************
   7:Core/Src/freertos.c ****   * @attention
   8:Core/Src/freertos.c ****   *
   9:Core/Src/freertos.c ****   * Copyright (c) 2025 STMicroelectronics.
  10:Core/Src/freertos.c ****   * All rights reserved.
  11:Core/Src/freertos.c ****   *
  12:Core/Src/freertos.c ****   * This software is licensed under terms that can be found in the LICENSE file
  13:Core/Src/freertos.c ****   * in the root directory of this software component.
  14:Core/Src/freertos.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  15:Core/Src/freertos.c ****   *
  16:Core/Src/freertos.c ****   ******************************************************************************
  17:Core/Src/freertos.c ****   */
  18:Core/Src/freertos.c **** /* USER CODE END Header */
  19:Core/Src/freertos.c **** 
  20:Core/Src/freertos.c **** /* Includes ------------------------------------------------------------------*/
  21:Core/Src/freertos.c **** #include "FreeRTOS.h"
  22:Core/Src/freertos.c **** #include "task.h"
  23:Core/Src/freertos.c **** #include "main.h"
  24:Core/Src/freertos.c **** #include "cmsis_os.h"
  25:Core/Src/freertos.c **** 
  26:Core/Src/freertos.c **** /* Private includes ----------------------------------------------------------*/
  27:Core/Src/freertos.c **** /* USER CODE BEGIN Includes */
  28:Core/Src/freertos.c **** #include <rcl/rcl.h>
  29:Core/Src/freertos.c **** #include <rcl/error_handling.h>
  30:Core/Src/freertos.c **** #include <rclc/rclc.h>
  31:Core/Src/freertos.c **** #include <rclc/executor.h>
  32:Core/Src/freertos.c **** #include <uxr/client/transport.h>
  33:Core/Src/freertos.c **** #include <rmw_microxrcedds_c/config.h>
  34:Core/Src/freertos.c **** #include <rmw_microros/rmw_microros.h>
  35:Core/Src/freertos.c **** 
  36:Core/Src/freertos.c **** #include <std_msgs/msg/int32.h>
  37:Core/Src/freertos.c **** 
  38:Core/Src/freertos.c **** #include <stdbool.h>
  39:Core/Src/freertos.c **** /* USER CODE END Includes */
  40:Core/Src/freertos.c **** 
  41:Core/Src/freertos.c **** /* Private typedef -----------------------------------------------------------*/
  42:Core/Src/freertos.c **** /* USER CODE BEGIN PTD */
  43:Core/Src/freertos.c **** 
  44:Core/Src/freertos.c **** /* USER CODE END PTD */
  45:Core/Src/freertos.c **** 
  46:Core/Src/freertos.c **** /* Private define ------------------------------------------------------------*/
  47:Core/Src/freertos.c **** /* USER CODE BEGIN PD */
  48:Core/Src/freertos.c **** 
  49:Core/Src/freertos.c **** /* USER CODE END PD */
  50:Core/Src/freertos.c **** 
  51:Core/Src/freertos.c **** /* Private macro -------------------------------------------------------------*/
  52:Core/Src/freertos.c **** /* USER CODE BEGIN PM */
ARM GAS  /tmp/cc6VmGwb.s 			page 3


  53:Core/Src/freertos.c **** 
  54:Core/Src/freertos.c **** /* USER CODE END PM */
  55:Core/Src/freertos.c **** 
  56:Core/Src/freertos.c **** /* Private variables ---------------------------------------------------------*/
  57:Core/Src/freertos.c **** /* USER CODE BEGIN Variables */
  58:Core/Src/freertos.c **** 
  59:Core/Src/freertos.c **** /* USER CODE END Variables */
  60:Core/Src/freertos.c **** /* Definitions for defaultTask */
  61:Core/Src/freertos.c **** osThreadId_t defaultTaskHandle;
  62:Core/Src/freertos.c **** const osThreadAttr_t defaultTask_attributes = {
  63:Core/Src/freertos.c ****   .name = "defaultTask",
  64:Core/Src/freertos.c ****   .stack_size = 3000 * 4,
  65:Core/Src/freertos.c ****   .priority = (osPriority_t) osPriorityNormal,
  66:Core/Src/freertos.c **** };
  67:Core/Src/freertos.c **** 
  68:Core/Src/freertos.c **** /* Private function prototypes -----------------------------------------------*/
  69:Core/Src/freertos.c **** /* USER CODE BEGIN FunctionPrototypes */
  70:Core/Src/freertos.c **** bool cubemx_transport_open(struct uxrCustomTransport * transport);
  71:Core/Src/freertos.c **** bool cubemx_transport_close(struct uxrCustomTransport * transport);
  72:Core/Src/freertos.c **** size_t cubemx_transport_write(struct uxrCustomTransport* transport, const uint8_t * buf, size_t len
  73:Core/Src/freertos.c **** size_t cubemx_transport_read(struct uxrCustomTransport* transport, uint8_t* buf, size_t len, int ti
  74:Core/Src/freertos.c **** 
  75:Core/Src/freertos.c **** void * microros_allocate(size_t size, void * state);
  76:Core/Src/freertos.c **** void microros_deallocate(void * pointer, void * state);
  77:Core/Src/freertos.c **** void * microros_reallocate(void * pointer, size_t size, void * state);
  78:Core/Src/freertos.c **** void * microros_zero_allocate(size_t number_of_elements, size_t size_of_element, void * state);
  79:Core/Src/freertos.c **** /* USER CODE END FunctionPrototypes */
  80:Core/Src/freertos.c **** 
  81:Core/Src/freertos.c **** void StartDefaultTask(void *argument);
  82:Core/Src/freertos.c **** 
  83:Core/Src/freertos.c **** extern void MX_LWIP_Init(void);
  84:Core/Src/freertos.c **** void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */
  85:Core/Src/freertos.c **** 
  86:Core/Src/freertos.c **** /**
  87:Core/Src/freertos.c ****   * @brief  FreeRTOS initialization
  88:Core/Src/freertos.c ****   * @param  None
  89:Core/Src/freertos.c ****   * @retval None
  90:Core/Src/freertos.c ****   */
  91:Core/Src/freertos.c **** void MX_FREERTOS_Init(void) {
  92:Core/Src/freertos.c ****   /* USER CODE BEGIN Init */
  93:Core/Src/freertos.c **** 
  94:Core/Src/freertos.c ****   /* USER CODE END Init */
  95:Core/Src/freertos.c **** 
  96:Core/Src/freertos.c ****   /* USER CODE BEGIN RTOS_MUTEX */
  97:Core/Src/freertos.c ****   /* add mutexes, ... */
  98:Core/Src/freertos.c ****   /* USER CODE END RTOS_MUTEX */
  99:Core/Src/freertos.c **** 
 100:Core/Src/freertos.c ****   /* USER CODE BEGIN RTOS_SEMAPHORES */
 101:Core/Src/freertos.c ****   /* add semaphores, ... */
 102:Core/Src/freertos.c ****   /* USER CODE END RTOS_SEMAPHORES */
 103:Core/Src/freertos.c **** 
 104:Core/Src/freertos.c ****   /* USER CODE BEGIN RTOS_TIMERS */
 105:Core/Src/freertos.c ****   /* start timers, add new ones, ... */
 106:Core/Src/freertos.c ****   /* USER CODE END RTOS_TIMERS */
 107:Core/Src/freertos.c **** 
 108:Core/Src/freertos.c ****   /* USER CODE BEGIN RTOS_QUEUES */
 109:Core/Src/freertos.c ****   /* add queues, ... */
ARM GAS  /tmp/cc6VmGwb.s 			page 4


 110:Core/Src/freertos.c ****   /* USER CODE END RTOS_QUEUES */
 111:Core/Src/freertos.c **** 
 112:Core/Src/freertos.c ****   /* Create the thread(s) */
 113:Core/Src/freertos.c ****   /* creation of defaultTask */
 114:Core/Src/freertos.c ****   defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);
 115:Core/Src/freertos.c **** 
 116:Core/Src/freertos.c ****   /* USER CODE BEGIN RTOS_THREADS */
 117:Core/Src/freertos.c ****   /* add threads, ... */
 118:Core/Src/freertos.c ****   /* USER CODE END RTOS_THREADS */
 119:Core/Src/freertos.c **** 
 120:Core/Src/freertos.c ****   /* USER CODE BEGIN RTOS_EVENTS */
 121:Core/Src/freertos.c ****   /* add events, ... */
 122:Core/Src/freertos.c ****   /* USER CODE END RTOS_EVENTS */
 123:Core/Src/freertos.c **** 
 124:Core/Src/freertos.c **** }
 125:Core/Src/freertos.c **** 
 126:Core/Src/freertos.c **** /* USER CODE BEGIN Header_StartDefaultTask */
 127:Core/Src/freertos.c **** /**
 128:Core/Src/freertos.c ****   * @brief  Function implementing the defaultTask thread.
 129:Core/Src/freertos.c ****   * @param  argument: Not used
 130:Core/Src/freertos.c ****   * @retval None
 131:Core/Src/freertos.c ****   */
 132:Core/Src/freertos.c **** /* USER CODE END Header_StartDefaultTask */
 133:Core/Src/freertos.c **** void StartDefaultTask(void *argument)
 134:Core/Src/freertos.c **** {
  48              		.loc 1 134 1 view -0
  49              		.cfi_startproc
  50              		@ args = 0, pretend = 0, frame = 136
  51              		@ frame_needed = 0, uses_anonymous_args = 0
  52              		.loc 1 134 1 is_stmt 0 view .LVU1
  53 0000 00B5     		push	{lr}
  54              	.LCFI0:
  55              		.cfi_def_cfa_offset 4
  56              		.cfi_offset 14, -4
  57 0002 A5B0     		sub	sp, sp, #148
  58              	.LCFI1:
  59              		.cfi_def_cfa_offset 152
 135:Core/Src/freertos.c ****   /* init code for LWIP */
 136:Core/Src/freertos.c ****   MX_LWIP_Init();
  60              		.loc 1 136 3 is_stmt 1 view .LVU2
  61 0004 FFF7FEFF 		bl	MX_LWIP_Init
  62              	.LVL1:
 137:Core/Src/freertos.c ****   /* USER CODE BEGIN 5 */
 138:Core/Src/freertos.c **** 
 139:Core/Src/freertos.c ****   // micro-ROS configuration
 140:Core/Src/freertos.c **** 
 141:Core/Src/freertos.c ****   rmw_uros_set_custom_transport(
  63              		.loc 1 141 3 view .LVU3
  64 0008 2A4B     		ldr	r3, .L8
  65 000a 0193     		str	r3, [sp, #4]
  66 000c 2A4B     		ldr	r3, .L8+4
  67 000e 0093     		str	r3, [sp]
  68 0010 2A4B     		ldr	r3, .L8+8
  69 0012 2B4A     		ldr	r2, .L8+12
  70 0014 2B49     		ldr	r1, .L8+16
  71 0016 0020     		movs	r0, #0
  72 0018 FFF7FEFF 		bl	rmw_uros_set_custom_transport
ARM GAS  /tmp/cc6VmGwb.s 			page 5


  73              	.LVL2:
 142:Core/Src/freertos.c ****     false,              //Framing disable here. Udp should Use Packet-oriented mode.
 143:Core/Src/freertos.c ****     "***********",    //your Agent's ip address
 144:Core/Src/freertos.c ****     cubemx_transport_open,
 145:Core/Src/freertos.c ****     cubemx_transport_close,
 146:Core/Src/freertos.c ****     cubemx_transport_write,
 147:Core/Src/freertos.c ****     cubemx_transport_read);
 148:Core/Src/freertos.c **** 
 149:Core/Src/freertos.c ****   rcl_allocator_t freeRTOS_allocator = rcutils_get_zero_initialized_allocator();
  74              		.loc 1 149 3 view .LVU4
  75              		.loc 1 149 40 is_stmt 0 view .LVU5
  76 001c 1FA8     		add	r0, sp, #124
  77 001e FFF7FEFF 		bl	rcutils_get_zero_initialized_allocator
  78              	.LVL3:
 150:Core/Src/freertos.c ****   freeRTOS_allocator.allocate = microros_allocate;
  79              		.loc 1 150 3 is_stmt 1 view .LVU6
  80              		.loc 1 150 31 is_stmt 0 view .LVU7
  81 0022 294B     		ldr	r3, .L8+20
  82 0024 1F93     		str	r3, [sp, #124]
 151:Core/Src/freertos.c ****   freeRTOS_allocator.deallocate = microros_deallocate;
  83              		.loc 1 151 3 is_stmt 1 view .LVU8
  84              		.loc 1 151 33 is_stmt 0 view .LVU9
  85 0026 294B     		ldr	r3, .L8+24
  86 0028 2093     		str	r3, [sp, #128]
 152:Core/Src/freertos.c ****   freeRTOS_allocator.reallocate = microros_reallocate;
  87              		.loc 1 152 3 is_stmt 1 view .LVU10
  88              		.loc 1 152 33 is_stmt 0 view .LVU11
  89 002a 294B     		ldr	r3, .L8+28
  90 002c 2193     		str	r3, [sp, #132]
 153:Core/Src/freertos.c ****   freeRTOS_allocator.zero_allocate =  microros_zero_allocate;
  91              		.loc 1 153 3 is_stmt 1 view .LVU12
  92              		.loc 1 153 36 is_stmt 0 view .LVU13
  93 002e 294B     		ldr	r3, .L8+32
  94 0030 2293     		str	r3, [sp, #136]
 154:Core/Src/freertos.c **** 
 155:Core/Src/freertos.c ****   if (!rcutils_set_default_allocator(&freeRTOS_allocator)) {
  95              		.loc 1 155 3 is_stmt 1 view .LVU14
  96              		.loc 1 155 8 is_stmt 0 view .LVU15
  97 0032 1FA8     		add	r0, sp, #124
  98 0034 FFF7FEFF 		bl	rcutils_set_default_allocator
  99              	.LVL4:
 100              		.loc 1 155 6 discriminator 1 view .LVU16
 101 0038 28B3     		cbz	r0, .L6
 102              	.L2:
 156:Core/Src/freertos.c ****       printf("Error on default allocators (line %d)\n", __LINE__); 
 157:Core/Src/freertos.c ****   }
 158:Core/Src/freertos.c **** 
 159:Core/Src/freertos.c ****   // micro-ROS app
 160:Core/Src/freertos.c **** 
 161:Core/Src/freertos.c ****   rcl_publisher_t publisher;
 103              		.loc 1 161 3 is_stmt 1 view .LVU17
 162:Core/Src/freertos.c ****   std_msgs__msg__Int32 msg;
 104              		.loc 1 162 3 view .LVU18
 163:Core/Src/freertos.c ****   rclc_support_t support;
 105              		.loc 1 163 3 view .LVU19
 164:Core/Src/freertos.c ****   rcl_allocator_t allocator;
 106              		.loc 1 164 3 view .LVU20
ARM GAS  /tmp/cc6VmGwb.s 			page 6


 165:Core/Src/freertos.c ****   rcl_node_t node;
 107              		.loc 1 165 3 view .LVU21
 166:Core/Src/freertos.c **** 
 167:Core/Src/freertos.c ****   allocator = rcl_get_default_allocator();
 108              		.loc 1 167 3 view .LVU22
 109              		.loc 1 167 15 is_stmt 0 view .LVU23
 110 003a 02A8     		add	r0, sp, #8
 111 003c FFF7FEFF 		bl	rcutils_get_default_allocator
 112              	.LVL5:
 113 0040 0DF12C0E 		add	lr, sp, #44
 114 0044 0DF1080C 		add	ip, sp, #8
 115 0048 BCE80F00 		ldmia	ip!, {r0, r1, r2, r3}
 116 004c AEE80F00 		stmia	lr!, {r0, r1, r2, r3}
 117 0050 DCF80030 		ldr	r3, [ip]
 118 0054 CEF80030 		str	r3, [lr]
 168:Core/Src/freertos.c **** 
 169:Core/Src/freertos.c ****   //create init_options
 170:Core/Src/freertos.c ****   rclc_support_init(&support, 0, NULL, &allocator);
 119              		.loc 1 170 3 is_stmt 1 view .LVU24
 120 0058 0BAB     		add	r3, sp, #44
 121 005a 0022     		movs	r2, #0
 122 005c 1146     		mov	r1, r2
 123 005e 10A8     		add	r0, sp, #64
 124 0060 FFF7FEFF 		bl	rclc_support_init
 125              	.LVL6:
 171:Core/Src/freertos.c **** 
 172:Core/Src/freertos.c ****   // create node
 173:Core/Src/freertos.c ****   rclc_node_init_default(&node, "cubemx_node", "", &support);
 126              		.loc 1 173 3 view .LVU25
 127 0064 10AB     		add	r3, sp, #64
 128 0066 1C4A     		ldr	r2, .L8+36
 129 0068 1C49     		ldr	r1, .L8+40
 130 006a 09A8     		add	r0, sp, #36
 131 006c FFF7FEFF 		bl	rclc_node_init_default
 132              	.LVL7:
 174:Core/Src/freertos.c **** 
 175:Core/Src/freertos.c ****   // create publisher
 176:Core/Src/freertos.c ****   rclc_publisher_init_default(
 133              		.loc 1 176 3 view .LVU26
 134 0070 FFF7FEFF 		bl	rosidl_typesupport_c__get_message_type_support_handle__std_msgs__msg__Int32
 135              	.LVL8:
 136 0074 0246     		mov	r2, r0
 137              		.loc 1 176 3 is_stmt 0 discriminator 1 view .LVU27
 138 0076 1A4B     		ldr	r3, .L8+44
 139 0078 09A9     		add	r1, sp, #36
 140 007a 1EA8     		add	r0, sp, #120
 141 007c FFF7FEFF 		bl	rclc_publisher_init_default
 142              	.LVL9:
 177:Core/Src/freertos.c ****     &publisher,
 178:Core/Src/freertos.c ****     &node,
 179:Core/Src/freertos.c ****     ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Int32),
 180:Core/Src/freertos.c ****     "cubemx_publisher");
 181:Core/Src/freertos.c **** 
 182:Core/Src/freertos.c ****   msg.data = 0;
 143              		.loc 1 182 3 is_stmt 1 view .LVU28
 144              		.loc 1 182 12 is_stmt 0 view .LVU29
 145 0080 0023     		movs	r3, #0
ARM GAS  /tmp/cc6VmGwb.s 			page 7


 146 0082 1D93     		str	r3, [sp, #116]
 147 0084 0EE0     		b	.L4
 148              	.L6:
 156:Core/Src/freertos.c ****   }
 149              		.loc 1 156 7 is_stmt 1 view .LVU30
 150 0086 9C21     		movs	r1, #156
 151 0088 1648     		ldr	r0, .L8+48
 152 008a FFF7FEFF 		bl	printf
 153              	.LVL10:
 154 008e D4E7     		b	.L2
 155              	.LVL11:
 156              	.L7:
 157              	.LBB2:
 183:Core/Src/freertos.c **** 
 184:Core/Src/freertos.c ****   for(;;)
 185:Core/Src/freertos.c ****   {
 186:Core/Src/freertos.c ****     rcl_ret_t ret = rcl_publish(&publisher, &msg, NULL);
 187:Core/Src/freertos.c ****     if (ret != RCL_RET_OK)
 188:Core/Src/freertos.c ****     {
 189:Core/Src/freertos.c ****       printf("Error publishing (line %d)\n", __LINE__); 
 158              		.loc 1 189 7 view .LVU31
 159 0090 BD21     		movs	r1, #189
 160 0092 1548     		ldr	r0, .L8+52
 161              	.LVL12:
 162              		.loc 1 189 7 is_stmt 0 view .LVU32
 163 0094 FFF7FEFF 		bl	printf
 164              	.LVL13:
 165              	.L3:
 190:Core/Src/freertos.c ****     }
 191:Core/Src/freertos.c ****     
 192:Core/Src/freertos.c ****     msg.data++;
 166              		.loc 1 192 5 is_stmt 1 view .LVU33
 167              		.loc 1 192 8 is_stmt 0 view .LVU34
 168 0098 1D9B     		ldr	r3, [sp, #116]
 169              		.loc 1 192 13 view .LVU35
 170 009a 0133     		adds	r3, r3, #1
 171 009c 1D93     		str	r3, [sp, #116]
 193:Core/Src/freertos.c ****     osDelay(10);
 172              		.loc 1 193 5 is_stmt 1 view .LVU36
 173 009e 0A20     		movs	r0, #10
 174 00a0 FFF7FEFF 		bl	osDelay
 175              	.LVL14:
 176              	.LBE2:
 184:Core/Src/freertos.c ****   {
 177              		.loc 1 184 3 view .LVU37
 178              	.L4:
 184:Core/Src/freertos.c ****   {
 179              		.loc 1 184 3 view .LVU38
 180              	.LBB3:
 186:Core/Src/freertos.c ****     if (ret != RCL_RET_OK)
 181              		.loc 1 186 5 view .LVU39
 186:Core/Src/freertos.c ****     if (ret != RCL_RET_OK)
 182              		.loc 1 186 21 is_stmt 0 view .LVU40
 183 00a4 0022     		movs	r2, #0
 184 00a6 1DA9     		add	r1, sp, #116
 185 00a8 1EA8     		add	r0, sp, #120
 186 00aa FFF7FEFF 		bl	rcl_publish
ARM GAS  /tmp/cc6VmGwb.s 			page 8


 187              	.LVL15:
 187:Core/Src/freertos.c ****     {
 188              		.loc 1 187 5 is_stmt 1 view .LVU41
 187:Core/Src/freertos.c ****     {
 189              		.loc 1 187 8 is_stmt 0 view .LVU42
 190 00ae 0028     		cmp	r0, #0
 191 00b0 EED1     		bne	.L7
 192 00b2 F1E7     		b	.L3
 193              	.L9:
 194              		.align	2
 195              	.L8:
 196 00b4 00000000 		.word	cubemx_transport_read
 197 00b8 00000000 		.word	cubemx_transport_write
 198 00bc 00000000 		.word	cubemx_transport_close
 199 00c0 00000000 		.word	cubemx_transport_open
 200 00c4 00000000 		.word	.LC0
 201 00c8 00000000 		.word	microros_allocate
 202 00cc 00000000 		.word	microros_deallocate
 203 00d0 00000000 		.word	microros_reallocate
 204 00d4 00000000 		.word	microros_zero_allocate
 205 00d8 34000000 		.word	.LC2
 206 00dc 38000000 		.word	.LC3
 207 00e0 44000000 		.word	.LC4
 208 00e4 0C000000 		.word	.LC1
 209 00e8 58000000 		.word	.LC5
 210              	.LBE3:
 211              		.cfi_endproc
 212              	.LFE153:
 214              		.section	.text.MX_FREERTOS_Init,"ax",%progbits
 215              		.align	1
 216              		.global	MX_FREERTOS_Init
 217              		.syntax unified
 218              		.thumb
 219              		.thumb_func
 221              	MX_FREERTOS_Init:
 222              	.LFB152:
  91:Core/Src/freertos.c ****   /* USER CODE BEGIN Init */
 223              		.loc 1 91 29 is_stmt 1 view -0
 224              		.cfi_startproc
 225              		@ args = 0, pretend = 0, frame = 0
 226              		@ frame_needed = 0, uses_anonymous_args = 0
 227 0000 08B5     		push	{r3, lr}
 228              	.LCFI2:
 229              		.cfi_def_cfa_offset 8
 230              		.cfi_offset 3, -8
 231              		.cfi_offset 14, -4
 114:Core/Src/freertos.c **** 
 232              		.loc 1 114 3 view .LVU44
 114:Core/Src/freertos.c **** 
 233              		.loc 1 114 23 is_stmt 0 view .LVU45
 234 0002 044A     		ldr	r2, .L12
 235 0004 0021     		movs	r1, #0
 236 0006 0448     		ldr	r0, .L12+4
 237 0008 FFF7FEFF 		bl	osThreadNew
 238              	.LVL16:
 114:Core/Src/freertos.c **** 
 239              		.loc 1 114 21 discriminator 1 view .LVU46
ARM GAS  /tmp/cc6VmGwb.s 			page 9


 240 000c 034B     		ldr	r3, .L12+8
 241 000e 1860     		str	r0, [r3]
 124:Core/Src/freertos.c **** 
 242              		.loc 1 124 1 view .LVU47
 243 0010 08BD     		pop	{r3, pc}
 244              	.L13:
 245 0012 00BF     		.align	2
 246              	.L12:
 247 0014 00000000 		.word	defaultTask_attributes
 248 0018 00000000 		.word	StartDefaultTask
 249 001c 00000000 		.word	defaultTaskHandle
 250              		.cfi_endproc
 251              	.LFE152:
 253              		.global	defaultTask_attributes
 254              		.section	.rodata.str1.4,"aMS",%progbits,1
 255              		.align	2
 256              	.LC6:
 257 0000 64656661 		.ascii	"defaultTask\000"
 257      756C7454 
 257      61736B00 
 258              		.section	.rodata.defaultTask_attributes,"a"
 259              		.align	2
 262              	defaultTask_attributes:
 263 0000 00000000 		.word	.LC6
 264 0004 00000000 		.space	16
 264      00000000 
 264      00000000 
 264      00000000 
 265 0014 E02E0000 		.word	12000
 266 0018 18000000 		.word	24
 267 001c 00000000 		.space	8
 267      00000000 
 268              		.global	defaultTaskHandle
 269              		.section	.bss.defaultTaskHandle,"aw",%nobits
 270              		.align	2
 273              	defaultTaskHandle:
 274 0000 00000000 		.space	4
 275              		.text
 276              	.Letext0:
 277              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 278              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 279              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 280              		.file 5 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 281              		.file 6 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcutils/
 282              		.file 7 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/allo
 283              		.file 8 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/ret_
 284              		.file 9 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcutils/
 285              		.file 10 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/tim
 286              		.file 11 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/typ
 287              		.file 12 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/typ
 288              		.file 13 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/con
 289              		.file 14 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/nod
 290              		.file 15 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rosidl_
 291              		.file 16 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/tim
 292              		.file 17 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl/pub
 293              		.file 18 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw/qos
 294              		.file 19 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/ty
ARM GAS  /tmp/cc6VmGwb.s 			page 10


 295              		.file 20 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rcl_act
 296              		.file 21 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/cli
 297              		.file 22 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/cli
 298              		.file 23 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/uxr/cli
 299              		.file 24 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/std_msg
 300              		.file 25 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/pu
 301              		.file 26 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/no
 302              		.file 27 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rclc/in
 303              		.file 28 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 304              		.file 29 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/std_msg
 305              		.file 30 "micro_ros_stm32cubemx_utils/microros_static_library/libmicroros/microros_include/rmw_mic
ARM GAS  /tmp/cc6VmGwb.s 			page 11


DEFINED SYMBOLS
                            *ABS*:00000000 freertos.c
     /tmp/cc6VmGwb.s:20     .rodata.StartDefaultTask.str1.4:00000000 $d
     /tmp/cc6VmGwb.s:39     .text.StartDefaultTask:00000000 $t
     /tmp/cc6VmGwb.s:45     .text.StartDefaultTask:00000000 StartDefaultTask
     /tmp/cc6VmGwb.s:196    .text.StartDefaultTask:000000b4 $d
     /tmp/cc6VmGwb.s:215    .text.MX_FREERTOS_Init:00000000 $t
     /tmp/cc6VmGwb.s:221    .text.MX_FREERTOS_Init:00000000 MX_FREERTOS_Init
     /tmp/cc6VmGwb.s:247    .text.MX_FREERTOS_Init:00000014 $d
     /tmp/cc6VmGwb.s:262    .rodata.defaultTask_attributes:00000000 defaultTask_attributes
     /tmp/cc6VmGwb.s:273    .bss.defaultTaskHandle:00000000 defaultTaskHandle
     /tmp/cc6VmGwb.s:255    .rodata.str1.4:00000000 $d
     /tmp/cc6VmGwb.s:259    .rodata.defaultTask_attributes:00000000 $d
     /tmp/cc6VmGwb.s:270    .bss.defaultTaskHandle:00000000 $d

UNDEFINED SYMBOLS
MX_LWIP_Init
rmw_uros_set_custom_transport
rcutils_get_zero_initialized_allocator
rcutils_set_default_allocator
rcutils_get_default_allocator
rclc_support_init
rclc_node_init_default
rosidl_typesupport_c__get_message_type_support_handle__std_msgs__msg__Int32
rclc_publisher_init_default
printf
osDelay
rcl_publish
cubemx_transport_read
cubemx_transport_write
cubemx_transport_close
cubemx_transport_open
microros_allocate
microros_deallocate
microros_reallocate
microros_zero_allocate
osThreadNew
