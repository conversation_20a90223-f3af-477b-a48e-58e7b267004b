ARM GAS  /tmp/ccqkcEof.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"memp.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/memp.c"
  19              		.section	.rodata.do_memp_malloc_pool.str1.4,"aMS",%progbits,1
  20              		.align	2
  21              	.LC0:
  22 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/memp.c\000"
  22      6C657761 
  22      7265732F 
  22      54686972 
  22      645F5061 
  23 002d 000000   		.align	2
  24              	.LC1:
  25 0030 6D656D70 		.ascii	"memp_malloc: memp properly aligned\000"
  25      5F6D616C 
  25      6C6F633A 
  25      206D656D 
  25      70207072 
  26 0053 00       		.align	2
  27              	.LC2:
  28 0054 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  28      7274696F 
  28      6E202225 
  28      73222066 
  28      61696C65 
  29              		.section	.text.do_memp_malloc_pool,"ax",%progbits
  30              		.align	1
  31              		.syntax unified
  32              		.thumb
  33              		.thumb_func
  35              	do_memp_malloc_pool:
  36              	.LVL0:
  37              	.LFB176:
   1:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Dynamic pool memory manager
   4:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * lwIP has dedicated pools for many structures (netconn, protocol control blocks,
   6:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * packet buffers, ...). All these pools are managed here.
   7:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
   8:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @defgroup mempool Memory pools
   9:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @ingroup infrastructure
ARM GAS  /tmp/ccqkcEof.s 			page 2


  10:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Custom memory pools
  11:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  12:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
  13:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  14:Middlewares/Third_Party/LwIP/src/core/memp.c **** /*
  15:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
  16:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * All rights reserved.
  17:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
  18:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Redistribution and use in source and binary forms, with or without modification,
  19:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * are permitted provided that the following conditions are met:
  20:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
  21:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  22:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *    this list of conditions and the following disclaimer.
  23:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  24:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *    this list of conditions and the following disclaimer in the documentation
  25:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *    and/or other materials provided with the distribution.
  26:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * 3. The name of the author may not be used to endorse or promote products
  27:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *    derived from this software without specific prior written permission.
  28:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
  29:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  30:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  31:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  32:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  33:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  34:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  35:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  36:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  37:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  38:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * OF SUCH DAMAGE.
  39:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
  40:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * This file is part of the lwIP TCP/IP stack.
  41:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
  42:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Author: Adam Dunkels <<EMAIL>>
  43:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
  44:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
  45:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  46:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/opt.h"
  47:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  48:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/memp.h"
  49:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/sys.h"
  50:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/stats.h"
  51:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  52:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include <string.h>
  53:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  54:Middlewares/Third_Party/LwIP/src/core/memp.c **** /* Make sure we include everything we need for size calculation required by memp_std.h */
  55:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/pbuf.h"
  56:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/raw.h"
  57:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/udp.h"
  58:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/tcp.h"
  59:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/tcp_priv.h"
  60:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/altcp.h"
  61:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/ip4_frag.h"
  62:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/netbuf.h"
  63:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/api.h"
  64:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/tcpip_priv.h"
  65:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/api_msg.h"
  66:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/sockets_priv.h"
ARM GAS  /tmp/ccqkcEof.s 			page 3


  67:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/etharp.h"
  68:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/igmp.h"
  69:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/timeouts.h"
  70:Middlewares/Third_Party/LwIP/src/core/memp.c **** /* needed by default MEMP_NUM_SYS_TIMEOUT */
  71:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "netif/ppp/ppp_opts.h"
  72:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/netdb.h"
  73:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/dns.h"
  74:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/nd6_priv.h"
  75:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/ip6_frag.h"
  76:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/mld6.h"
  77:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  78:Middlewares/Third_Party/LwIP/src/core/memp.c **** #define LWIP_MEMPOOL(name,num,size,desc) LWIP_MEMPOOL_DECLARE(name,num,size,desc)
  79:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/memp_std.h"
  80:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  81:Middlewares/Third_Party/LwIP/src/core/memp.c **** const struct memp_desc *const memp_pools[MEMP_MAX] = {
  82:Middlewares/Third_Party/LwIP/src/core/memp.c **** #define LWIP_MEMPOOL(name,num,size,desc) &memp_ ## name,
  83:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include "lwip/priv/memp_std.h"
  84:Middlewares/Third_Party/LwIP/src/core/memp.c **** };
  85:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  86:Middlewares/Third_Party/LwIP/src/core/memp.c **** #ifdef LWIP_HOOK_FILENAME
  87:Middlewares/Third_Party/LwIP/src/core/memp.c **** #include LWIP_HOOK_FILENAME
  88:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
  89:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  90:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_MALLOC && MEMP_OVERFLOW_CHECK >= 2
  91:Middlewares/Third_Party/LwIP/src/core/memp.c **** #undef MEMP_OVERFLOW_CHECK
  92:Middlewares/Third_Party/LwIP/src/core/memp.c **** /* MEMP_OVERFLOW_CHECK >= 2 does not work with MEMP_MEM_MALLOC, use 1 instead */
  93:Middlewares/Third_Party/LwIP/src/core/memp.c **** #define MEMP_OVERFLOW_CHECK 1
  94:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
  95:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
  96:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_SANITY_CHECK && !MEMP_MEM_MALLOC
  97:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
  98:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Check that memp-lists don't form a circle, using "Floyd's cycle-finding algorithm".
  99:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 100:Middlewares/Third_Party/LwIP/src/core/memp.c **** static int
 101:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_sanity(const struct memp_desc *desc)
 102:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 103:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *t, *h;
 104:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 105:Middlewares/Third_Party/LwIP/src/core/memp.c ****   t = *desc->tab;
 106:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (t != NULL) {
 107:Middlewares/Third_Party/LwIP/src/core/memp.c ****     for (h = t->next; (t != NULL) && (h != NULL); t = t->next,
 108:Middlewares/Third_Party/LwIP/src/core/memp.c ****          h = ((h->next != NULL) ? h->next->next : NULL)) {
 109:Middlewares/Third_Party/LwIP/src/core/memp.c ****       if (t == h) {
 110:Middlewares/Third_Party/LwIP/src/core/memp.c ****         return 0;
 111:Middlewares/Third_Party/LwIP/src/core/memp.c ****       }
 112:Middlewares/Third_Party/LwIP/src/core/memp.c ****     }
 113:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 114:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 115:Middlewares/Third_Party/LwIP/src/core/memp.c ****   return 1;
 116:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 117:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_SANITY_CHECK && !MEMP_MEM_MALLOC */
 118:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 119:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 120:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 121:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Check if a memp element was victim of an overflow or underflow
 122:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * (e.g. the restricted area after/before it has been altered)
 123:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
ARM GAS  /tmp/ccqkcEof.s 			page 4


 124:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param p the memp element to check
 125:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param desc the pool p comes from
 126:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 127:Middlewares/Third_Party/LwIP/src/core/memp.c **** static void
 128:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_overflow_check_element(struct memp *p, const struct memp_desc *desc)
 129:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 130:Middlewares/Third_Party/LwIP/src/core/memp.c ****   mem_overflow_check_raw((u8_t *)p + MEMP_SIZE, desc->size, "pool ", desc->desc);
 131:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 132:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 133:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 134:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Initialize the restricted area of on memp element.
 135:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 136:Middlewares/Third_Party/LwIP/src/core/memp.c **** static void
 137:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_overflow_init_element(struct memp *p, const struct memp_desc *desc)
 138:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 139:Middlewares/Third_Party/LwIP/src/core/memp.c ****   mem_overflow_init_raw((u8_t *)p + MEMP_SIZE, desc->size);
 140:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 141:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 142:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK >= 2
 143:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 144:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Do an overflow check for all elements in every pool.
 145:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 146:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @see memp_overflow_check_element for a description of the check
 147:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 148:Middlewares/Third_Party/LwIP/src/core/memp.c **** static void
 149:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_overflow_check_all(void)
 150:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 151:Middlewares/Third_Party/LwIP/src/core/memp.c ****   u16_t i, j;
 152:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *p;
 153:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_DECL_PROTECT(old_level);
 154:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_PROTECT(old_level);
 155:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 156:Middlewares/Third_Party/LwIP/src/core/memp.c ****   for (i = 0; i < MEMP_MAX; ++i) {
 157:Middlewares/Third_Party/LwIP/src/core/memp.c ****     p = (struct memp *)LWIP_MEM_ALIGN(memp_pools[i]->base);
 158:Middlewares/Third_Party/LwIP/src/core/memp.c ****     for (j = 0; j < memp_pools[i]->num; ++j) {
 159:Middlewares/Third_Party/LwIP/src/core/memp.c ****       memp_overflow_check_element(p, memp_pools[i]);
 160:Middlewares/Third_Party/LwIP/src/core/memp.c ****       p = LWIP_ALIGNMENT_CAST(struct memp *, ((u8_t *)p + MEMP_SIZE + memp_pools[i]->size + MEM_SAN
 161:Middlewares/Third_Party/LwIP/src/core/memp.c ****     }
 162:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 163:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_UNPROTECT(old_level);
 164:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 165:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK >= 2 */
 166:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK */
 167:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 168:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 169:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Initialize custom memory pool.
 170:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Related functions: memp_malloc_pool, memp_free_pool
 171:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 172:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param desc pool to initialize
 173:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 174:Middlewares/Third_Party/LwIP/src/core/memp.c **** void
 175:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_init_pool(const struct memp_desc *desc)
 176:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 177:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_MALLOC
 178:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_UNUSED_ARG(desc);
 179:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 180:Middlewares/Third_Party/LwIP/src/core/memp.c ****   int i;
ARM GAS  /tmp/ccqkcEof.s 			page 5


 181:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *memp;
 182:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 183:Middlewares/Third_Party/LwIP/src/core/memp.c ****   *desc->tab = NULL;
 184:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = (struct memp *)LWIP_MEM_ALIGN(desc->base);
 185:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_INIT
 186:Middlewares/Third_Party/LwIP/src/core/memp.c ****   /* force memset on pool memory */
 187:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memset(memp, 0, (size_t)desc->num * (MEMP_SIZE + desc->size
 188:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 189:Middlewares/Third_Party/LwIP/src/core/memp.c ****                                        + MEM_SANITY_REGION_AFTER_ALIGNED
 190:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 191:Middlewares/Third_Party/LwIP/src/core/memp.c ****                                       ));
 192:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 193:Middlewares/Third_Party/LwIP/src/core/memp.c ****   /* create a linked list of memp elements */
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****   for (i = 0; i < desc->num; ++i) {
 195:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 196:Middlewares/Third_Party/LwIP/src/core/memp.c ****     *desc->tab = memp;
 197:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 198:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_overflow_init_element(memp, desc);
 199:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK */
 200:Middlewares/Third_Party/LwIP/src/core/memp.c ****     /* cast through void* to get rid of alignment warnings */
 201:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp = (struct memp *)(void *)((u8_t *)memp + MEMP_SIZE + desc->size
 202:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 203:Middlewares/Third_Party/LwIP/src/core/memp.c ****                                    + MEM_SANITY_REGION_AFTER_ALIGNED
 204:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 205:Middlewares/Third_Party/LwIP/src/core/memp.c ****                                   );
 206:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 207:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_STATS
 208:Middlewares/Third_Party/LwIP/src/core/memp.c ****   desc->stats->avail = desc->num;
 209:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_STATS */
 210:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* !MEMP_MEM_MALLOC */
 211:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 212:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_STATS && (defined(LWIP_DEBUG) || LWIP_STATS_DISPLAY)
 213:Middlewares/Third_Party/LwIP/src/core/memp.c ****   desc->stats->name  = desc->desc;
 214:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_STATS && (defined(LWIP_DEBUG) || LWIP_STATS_DISPLAY) */
 215:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 216:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 217:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 218:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Initializes lwIP built-in pools.
 219:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Related functions: memp_malloc, memp_free
 220:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 221:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Carves out memp_memory into linked lists for each pool-type.
 222:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 223:Middlewares/Third_Party/LwIP/src/core/memp.c **** void
 224:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_init(void)
 225:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 226:Middlewares/Third_Party/LwIP/src/core/memp.c ****   u16_t i;
 227:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 228:Middlewares/Third_Party/LwIP/src/core/memp.c ****   /* for every pool: */
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****   for (i = 0; i < LWIP_ARRAYSIZE(memp_pools); i++) {
 230:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 231:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 232:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if LWIP_STATS && MEMP_STATS
 233:Middlewares/Third_Party/LwIP/src/core/memp.c ****     lwip_stats.memp[i] = memp_pools[i]->stats;
 234:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 235:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 236:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 237:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK >= 2
ARM GAS  /tmp/ccqkcEof.s 			page 6


 238:Middlewares/Third_Party/LwIP/src/core/memp.c ****   /* check everything a first time to see if it worked */
 239:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp_overflow_check_all();
 240:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK >= 2 */
 241:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 242:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/memp.c **** static void *
 244:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if !MEMP_OVERFLOW_CHECK
 245:Middlewares/Third_Party/LwIP/src/core/memp.c **** do_memp_malloc_pool(const struct memp_desc *desc)
 246:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 247:Middlewares/Third_Party/LwIP/src/core/memp.c **** do_memp_malloc_pool_fn(const struct memp_desc *desc, const char *file, const int line)
 248:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 249:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
  38              		.loc 1 249 1 view -0
  39              		.cfi_startproc
  40              		@ args = 0, pretend = 0, frame = 0
  41              		@ frame_needed = 0, uses_anonymous_args = 0
  42              		.loc 1 249 1 is_stmt 0 view .LVU1
  43 0000 38B5     		push	{r3, r4, r5, lr}
  44              	.LCFI0:
  45              		.cfi_def_cfa_offset 16
  46              		.cfi_offset 3, -16
  47              		.cfi_offset 4, -12
  48              		.cfi_offset 5, -8
  49              		.cfi_offset 14, -4
  50 0002 0446     		mov	r4, r0
 250:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *memp;
  51              		.loc 1 250 3 is_stmt 1 view .LVU2
 251:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_DECL_PROTECT(old_level);
  52              		.loc 1 251 3 view .LVU3
 252:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 253:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_MALLOC
 254:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = (struct memp *)mem_malloc(MEMP_SIZE + MEMP_ALIGN_SIZE(desc->size));
 255:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_PROTECT(old_level);
 256:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else /* MEMP_MEM_MALLOC */
 257:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_PROTECT(old_level);
  53              		.loc 1 257 3 view .LVU4
  54 0004 FFF7FEFF 		bl	sys_arch_protect
  55              	.LVL1:
  56              		.loc 1 257 3 is_stmt 0 view .LVU5
  57 0008 0546     		mov	r5, r0
  58              	.LVL2:
 258:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 259:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = *desc->tab;
  59              		.loc 1 259 3 is_stmt 1 view .LVU6
  60              		.loc 1 259 15 is_stmt 0 view .LVU7
  61 000a A368     		ldr	r3, [r4, #8]
  62              		.loc 1 259 8 view .LVU8
  63 000c 1C68     		ldr	r4, [r3]
  64              	.LVL3:
 260:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_MEM_MALLOC */
 261:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 262:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (memp != NULL) {
  65              		.loc 1 262 3 is_stmt 1 view .LVU9
  66              		.loc 1 262 6 is_stmt 0 view .LVU10
  67 000e 8CB1     		cbz	r4, .L2
 263:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if !MEMP_MEM_MALLOC
 264:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK == 1
ARM GAS  /tmp/ccqkcEof.s 			page 7


 265:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_overflow_check_element(memp, desc);
 266:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK */
 267:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 268:Middlewares/Third_Party/LwIP/src/core/memp.c ****     *desc->tab = memp->next;
  68              		.loc 1 268 5 is_stmt 1 view .LVU11
  69              		.loc 1 268 22 is_stmt 0 view .LVU12
  70 0010 2268     		ldr	r2, [r4]
  71              		.loc 1 268 16 view .LVU13
  72 0012 1A60     		str	r2, [r3]
 269:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 270:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = NULL;
 271:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK */
 272:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* !MEMP_MEM_MALLOC */
 273:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 274:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->file = file;
 275:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->line = line;
 276:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_MALLOC
 277:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_overflow_init_element(memp, desc);
 278:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_MEM_MALLOC */
 279:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK */
 280:Middlewares/Third_Party/LwIP/src/core/memp.c ****     LWIP_ASSERT("memp_malloc: memp properly aligned",
  73              		.loc 1 280 5 is_stmt 1 view .LVU14
  74              		.loc 1 280 5 view .LVU15
  75 0014 14F0030F 		tst	r4, #3
  76 0018 04D1     		bne	.L6
  77              	.LVL4:
  78              	.L3:
  79              		.loc 1 280 5 discriminator 3 view .LVU16
  80              		.loc 1 280 5 discriminator 3 view .LVU17
 281:Middlewares/Third_Party/LwIP/src/core/memp.c ****                 ((mem_ptr_t)memp % MEM_ALIGNMENT) == 0);
 282:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_STATS
 283:Middlewares/Third_Party/LwIP/src/core/memp.c ****     desc->stats->used++;
 284:Middlewares/Third_Party/LwIP/src/core/memp.c ****     if (desc->stats->used > desc->stats->max) {
 285:Middlewares/Third_Party/LwIP/src/core/memp.c ****       desc->stats->max = desc->stats->used;
 286:Middlewares/Third_Party/LwIP/src/core/memp.c ****     }
 287:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 288:Middlewares/Third_Party/LwIP/src/core/memp.c ****     SYS_ARCH_UNPROTECT(old_level);
  81              		.loc 1 288 5 view .LVU18
  82 001a 2846     		mov	r0, r5
  83 001c FFF7FEFF 		bl	sys_arch_unprotect
  84              	.LVL5:
 289:Middlewares/Third_Party/LwIP/src/core/memp.c ****     /* cast through u8_t* to get rid of alignment warnings */
 290:Middlewares/Third_Party/LwIP/src/core/memp.c ****     return ((u8_t *)memp + MEMP_SIZE);
  85              		.loc 1 290 5 view .LVU19
  86              	.L1:
 291:Middlewares/Third_Party/LwIP/src/core/memp.c ****   } else {
 292:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_STATS
 293:Middlewares/Third_Party/LwIP/src/core/memp.c ****     desc->stats->err++;
 294:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 295:Middlewares/Third_Party/LwIP/src/core/memp.c ****     SYS_ARCH_UNPROTECT(old_level);
 296:Middlewares/Third_Party/LwIP/src/core/memp.c ****     LWIP_DEBUGF(MEMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("memp_malloc: out of memory in pool %s\n", de
 297:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 298:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 299:Middlewares/Third_Party/LwIP/src/core/memp.c ****   return NULL;
 300:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
  87              		.loc 1 300 1 is_stmt 0 view .LVU20
  88 0020 2046     		mov	r0, r4
ARM GAS  /tmp/ccqkcEof.s 			page 8


  89 0022 38BD     		pop	{r3, r4, r5, pc}
  90              	.LVL6:
  91              	.L6:
 280:Middlewares/Third_Party/LwIP/src/core/memp.c ****                 ((mem_ptr_t)memp % MEM_ALIGNMENT) == 0);
  92              		.loc 1 280 5 is_stmt 1 discriminator 1 view .LVU21
 280:Middlewares/Third_Party/LwIP/src/core/memp.c ****                 ((mem_ptr_t)memp % MEM_ALIGNMENT) == 0);
  93              		.loc 1 280 5 discriminator 1 view .LVU22
  94 0024 054B     		ldr	r3, .L7
  95 0026 4FF48C72 		mov	r2, #280
  96 002a 0549     		ldr	r1, .L7+4
  97 002c 0548     		ldr	r0, .L7+8
  98              	.LVL7:
 280:Middlewares/Third_Party/LwIP/src/core/memp.c ****                 ((mem_ptr_t)memp % MEM_ALIGNMENT) == 0);
  99              		.loc 1 280 5 is_stmt 0 discriminator 1 view .LVU23
 100 002e FFF7FEFF 		bl	printf
 101              	.LVL8:
 102 0032 F2E7     		b	.L3
 103              	.LVL9:
 104              	.L2:
 295:Middlewares/Third_Party/LwIP/src/core/memp.c ****     LWIP_DEBUGF(MEMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("memp_malloc: out of memory in pool %s\n", de
 105              		.loc 1 295 5 is_stmt 1 view .LVU24
 106 0034 FFF7FEFF 		bl	sys_arch_unprotect
 107              	.LVL10:
 296:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 108              		.loc 1 296 110 view .LVU25
 299:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 109              		.loc 1 299 3 view .LVU26
 299:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 110              		.loc 1 299 10 is_stmt 0 view .LVU27
 111 0038 F2E7     		b	.L1
 112              	.L8:
 113 003a 00BF     		.align	2
 114              	.L7:
 115 003c 00000000 		.word	.LC0
 116 0040 30000000 		.word	.LC1
 117 0044 54000000 		.word	.LC2
 118              		.cfi_endproc
 119              	.LFE176:
 121              		.section	.rodata.do_memp_free_pool.str1.4,"aMS",%progbits,1
 122              		.align	2
 123              	.LC3:
 124 0000 6D656D70 		.ascii	"memp_free: mem properly aligned\000"
 124      5F667265 
 124      653A206D 
 124      656D2070 
 124      726F7065 
 125              		.section	.text.do_memp_free_pool,"ax",%progbits
 126              		.align	1
 127              		.syntax unified
 128              		.thumb
 129              		.thumb_func
 131              	do_memp_free_pool:
 132              	.LVL11:
 133              	.LFB179:
 301:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 302:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 303:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Get an element from a custom pool.
ARM GAS  /tmp/ccqkcEof.s 			page 9


 304:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 305:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param desc the pool to get an element from
 306:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 307:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @return a pointer to the allocated memory or a NULL pointer on error
 308:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 309:Middlewares/Third_Party/LwIP/src/core/memp.c **** void *
 310:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if !MEMP_OVERFLOW_CHECK
 311:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_malloc_pool(const struct memp_desc *desc)
 312:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 313:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_malloc_pool_fn(const struct memp_desc *desc, const char *file, const int line)
 314:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 315:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ASSERT("invalid pool desc", desc != NULL);
 317:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 318:Middlewares/Third_Party/LwIP/src/core/memp.c ****     return NULL;
 319:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 320:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 321:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if !MEMP_OVERFLOW_CHECK
 322:Middlewares/Third_Party/LwIP/src/core/memp.c ****   return do_memp_malloc_pool(desc);
 323:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 324:Middlewares/Third_Party/LwIP/src/core/memp.c ****   return do_memp_malloc_pool_fn(desc, file, line);
 325:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 326:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 327:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 328:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 329:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Get an element from a specific pool.
 330:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 331:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param type the pool to get an element from
 332:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 333:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @return a pointer to the allocated memory or a NULL pointer on error
 334:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 335:Middlewares/Third_Party/LwIP/src/core/memp.c **** void *
 336:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if !MEMP_OVERFLOW_CHECK
 337:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_malloc(memp_t type)
 338:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 339:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_malloc_fn(memp_t type, const char *file, const int line)
 340:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 341:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 342:Middlewares/Third_Party/LwIP/src/core/memp.c ****   void *memp;
 343:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ERROR("memp_malloc: type < MEMP_MAX", (type < MEMP_MAX), return NULL;);
 344:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 345:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK >= 2
 346:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp_overflow_check_all();
 347:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK >= 2 */
 348:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 349:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if !MEMP_OVERFLOW_CHECK
 350:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = do_memp_malloc_pool(memp_pools[type]);
 351:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 352:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = do_memp_malloc_pool_fn(memp_pools[type], file, line);
 353:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 354:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 355:Middlewares/Third_Party/LwIP/src/core/memp.c ****   return memp;
 356:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 357:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 358:Middlewares/Third_Party/LwIP/src/core/memp.c **** static void
 359:Middlewares/Third_Party/LwIP/src/core/memp.c **** do_memp_free_pool(const struct memp_desc *desc, void *mem)
 360:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
ARM GAS  /tmp/ccqkcEof.s 			page 10


 134              		.loc 1 360 1 is_stmt 1 view -0
 135              		.cfi_startproc
 136              		@ args = 0, pretend = 0, frame = 0
 137              		@ frame_needed = 0, uses_anonymous_args = 0
 138              		.loc 1 360 1 is_stmt 0 view .LVU29
 139 0000 38B5     		push	{r3, r4, r5, lr}
 140              	.LCFI1:
 141              		.cfi_def_cfa_offset 16
 142              		.cfi_offset 3, -16
 143              		.cfi_offset 4, -12
 144              		.cfi_offset 5, -8
 145              		.cfi_offset 14, -4
 146 0002 0546     		mov	r5, r0
 147 0004 0C46     		mov	r4, r1
 361:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *memp;
 148              		.loc 1 361 3 is_stmt 1 view .LVU30
 362:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_DECL_PROTECT(old_level);
 149              		.loc 1 362 3 view .LVU31
 363:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 364:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ASSERT("memp_free: mem properly aligned",
 150              		.loc 1 364 3 view .LVU32
 151              		.loc 1 364 3 view .LVU33
 152 0006 11F0030F 		tst	r1, #3
 153 000a 09D1     		bne	.L12
 154              	.LVL12:
 155              	.L10:
 156              		.loc 1 364 3 discriminator 3 view .LVU34
 157              		.loc 1 364 3 discriminator 3 view .LVU35
 365:Middlewares/Third_Party/LwIP/src/core/memp.c ****               ((mem_ptr_t)mem % MEM_ALIGNMENT) == 0);
 366:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 367:Middlewares/Third_Party/LwIP/src/core/memp.c ****   /* cast through void* to get rid of alignment warnings */
 368:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = (struct memp *)(void *)((u8_t *)mem - MEMP_SIZE);
 158              		.loc 1 368 3 view .LVU36
 369:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 370:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_PROTECT(old_level);
 159              		.loc 1 370 3 view .LVU37
 160 000c FFF7FEFF 		bl	sys_arch_protect
 161              	.LVL13:
 371:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 372:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK == 1
 373:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp_overflow_check_element(memp, desc);
 374:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK */
 375:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 376:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_STATS
 377:Middlewares/Third_Party/LwIP/src/core/memp.c ****   desc->stats->used--;
 378:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 379:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 380:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_MALLOC
 381:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_UNUSED_ARG(desc);
 382:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_UNPROTECT(old_level);
 383:Middlewares/Third_Party/LwIP/src/core/memp.c ****   mem_free(memp);
 384:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else /* MEMP_MEM_MALLOC */
 385:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp->next = *desc->tab;
 162              		.loc 1 385 3 view .LVU38
 163              		.loc 1 385 21 is_stmt 0 view .LVU39
 164 0010 AB68     		ldr	r3, [r5, #8]
 165              		.loc 1 385 16 view .LVU40
ARM GAS  /tmp/ccqkcEof.s 			page 11


 166 0012 1B68     		ldr	r3, [r3]
 167              		.loc 1 385 14 view .LVU41
 168 0014 2360     		str	r3, [r4]
 386:Middlewares/Third_Party/LwIP/src/core/memp.c ****   *desc->tab = memp;
 169              		.loc 1 386 3 is_stmt 1 view .LVU42
 170              		.loc 1 386 8 is_stmt 0 view .LVU43
 171 0016 AB68     		ldr	r3, [r5, #8]
 172              		.loc 1 386 14 view .LVU44
 173 0018 1C60     		str	r4, [r3]
 387:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 388:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_SANITY_CHECK
 389:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ASSERT("memp sanity", memp_sanity(desc));
 390:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_SANITY_CHECK */
 391:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 392:Middlewares/Third_Party/LwIP/src/core/memp.c ****   SYS_ARCH_UNPROTECT(old_level);
 174              		.loc 1 392 3 is_stmt 1 view .LVU45
 175 001a FFF7FEFF 		bl	sys_arch_unprotect
 176              	.LVL14:
 393:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* !MEMP_MEM_MALLOC */
 394:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 177              		.loc 1 394 1 is_stmt 0 view .LVU46
 178 001e 38BD     		pop	{r3, r4, r5, pc}
 179              	.LVL15:
 180              	.L12:
 364:Middlewares/Third_Party/LwIP/src/core/memp.c ****               ((mem_ptr_t)mem % MEM_ALIGNMENT) == 0);
 181              		.loc 1 364 3 is_stmt 1 discriminator 1 view .LVU47
 364:Middlewares/Third_Party/LwIP/src/core/memp.c ****               ((mem_ptr_t)mem % MEM_ALIGNMENT) == 0);
 182              		.loc 1 364 3 discriminator 1 view .LVU48
 183 0020 034B     		ldr	r3, .L13
 184 0022 4FF4B672 		mov	r2, #364
 185 0026 0349     		ldr	r1, .L13+4
 186              	.LVL16:
 364:Middlewares/Third_Party/LwIP/src/core/memp.c ****               ((mem_ptr_t)mem % MEM_ALIGNMENT) == 0);
 187              		.loc 1 364 3 is_stmt 0 discriminator 1 view .LVU49
 188 0028 0348     		ldr	r0, .L13+8
 189              	.LVL17:
 364:Middlewares/Third_Party/LwIP/src/core/memp.c ****               ((mem_ptr_t)mem % MEM_ALIGNMENT) == 0);
 190              		.loc 1 364 3 discriminator 1 view .LVU50
 191 002a FFF7FEFF 		bl	printf
 192              	.LVL18:
 193 002e EDE7     		b	.L10
 194              	.L14:
 195              		.align	2
 196              	.L13:
 197 0030 00000000 		.word	.LC0
 198 0034 00000000 		.word	.LC3
 199 0038 54000000 		.word	.LC2
 200              		.cfi_endproc
 201              	.LFE179:
 203              		.section	.text.memp_init_pool,"ax",%progbits
 204              		.align	1
 205              		.global	memp_init_pool
 206              		.syntax unified
 207              		.thumb
 208              		.thumb_func
 210              	memp_init_pool:
 211              	.LVL19:
ARM GAS  /tmp/ccqkcEof.s 			page 12


 212              	.LFB174:
 176:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_MALLOC
 213              		.loc 1 176 1 is_stmt 1 view -0
 214              		.cfi_startproc
 215              		@ args = 0, pretend = 0, frame = 0
 216              		@ frame_needed = 0, uses_anonymous_args = 0
 217              		@ link register save eliminated.
 180:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *memp;
 218              		.loc 1 180 3 view .LVU52
 181:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 219              		.loc 1 181 3 view .LVU53
 183:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = (struct memp *)LWIP_MEM_ALIGN(desc->base);
 220              		.loc 1 183 3 view .LVU54
 183:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = (struct memp *)LWIP_MEM_ALIGN(desc->base);
 221              		.loc 1 183 8 is_stmt 0 view .LVU55
 222 0000 8368     		ldr	r3, [r0, #8]
 183:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp = (struct memp *)LWIP_MEM_ALIGN(desc->base);
 223              		.loc 1 183 14 view .LVU56
 224 0002 0022     		movs	r2, #0
 225 0004 1A60     		str	r2, [r3]
 184:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_INIT
 226              		.loc 1 184 3 is_stmt 1 view .LVU57
 184:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_MEM_INIT
 227              		.loc 1 184 25 is_stmt 0 view .LVU58
 228 0006 4368     		ldr	r3, [r0, #4]
 229 0008 0333     		adds	r3, r3, #3
 230 000a 23F00303 		bic	r3, r3, #3
 231              	.LVL20:
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 232              		.loc 1 194 3 is_stmt 1 view .LVU59
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 233              		.loc 1 194 3 is_stmt 0 view .LVU60
 234 000e 07E0     		b	.L16
 235              	.LVL21:
 236              	.L17:
 195:Middlewares/Third_Party/LwIP/src/core/memp.c ****     *desc->tab = memp;
 237              		.loc 1 195 5 is_stmt 1 view .LVU61
 195:Middlewares/Third_Party/LwIP/src/core/memp.c ****     *desc->tab = memp;
 238              		.loc 1 195 23 is_stmt 0 view .LVU62
 239 0010 8168     		ldr	r1, [r0, #8]
 195:Middlewares/Third_Party/LwIP/src/core/memp.c ****     *desc->tab = memp;
 240              		.loc 1 195 18 view .LVU63
 241 0012 0968     		ldr	r1, [r1]
 195:Middlewares/Third_Party/LwIP/src/core/memp.c ****     *desc->tab = memp;
 242              		.loc 1 195 16 view .LVU64
 243 0014 1960     		str	r1, [r3]
 196:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 244              		.loc 1 196 5 is_stmt 1 view .LVU65
 196:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 245              		.loc 1 196 10 is_stmt 0 view .LVU66
 246 0016 8168     		ldr	r1, [r0, #8]
 196:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 247              		.loc 1 196 16 view .LVU67
 248 0018 0B60     		str	r3, [r1]
 201:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 249              		.loc 1 201 5 is_stmt 1 view .LVU68
 201:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
ARM GAS  /tmp/ccqkcEof.s 			page 13


 250              		.loc 1 201 67 is_stmt 0 view .LVU69
 251 001a 0188     		ldrh	r1, [r0]
 201:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK
 252              		.loc 1 201 10 view .LVU70
 253 001c 0B44     		add	r3, r3, r1
 254              	.LVL22:
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 255              		.loc 1 194 30 is_stmt 1 discriminator 3 view .LVU71
 256 001e 0132     		adds	r2, r2, #1
 257              	.LVL23:
 258              	.L16:
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 259              		.loc 1 194 17 discriminator 1 view .LVU72
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 260              		.loc 1 194 23 is_stmt 0 discriminator 1 view .LVU73
 261 0020 4188     		ldrh	r1, [r0, #2]
 194:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp->next = *desc->tab;
 262              		.loc 1 194 17 discriminator 1 view .LVU74
 263 0022 9142     		cmp	r1, r2
 264 0024 F4DC     		bgt	.L17
 215:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 265              		.loc 1 215 1 view .LVU75
 266 0026 7047     		bx	lr
 267              		.cfi_endproc
 268              	.LFE174:
 270              		.section	.text.memp_init,"ax",%progbits
 271              		.align	1
 272              		.global	memp_init
 273              		.syntax unified
 274              		.thumb
 275              		.thumb_func
 277              	memp_init:
 278              	.LFB175:
 225:Middlewares/Third_Party/LwIP/src/core/memp.c ****   u16_t i;
 279              		.loc 1 225 1 is_stmt 1 view -0
 280              		.cfi_startproc
 281              		@ args = 0, pretend = 0, frame = 0
 282              		@ frame_needed = 0, uses_anonymous_args = 0
 283 0000 10B5     		push	{r4, lr}
 284              	.LCFI2:
 285              		.cfi_def_cfa_offset 8
 286              		.cfi_offset 4, -8
 287              		.cfi_offset 14, -4
 226:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 288              		.loc 1 226 3 view .LVU77
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 289              		.loc 1 229 3 view .LVU78
 290              	.LVL24:
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 291              		.loc 1 229 10 is_stmt 0 view .LVU79
 292 0002 0024     		movs	r4, #0
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 293              		.loc 1 229 3 view .LVU80
 294 0004 06E0     		b	.L19
 295              	.LVL25:
 296              	.L20:
 230:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
ARM GAS  /tmp/ccqkcEof.s 			page 14


 297              		.loc 1 230 5 is_stmt 1 view .LVU81
 298 0006 054B     		ldr	r3, .L22
 299 0008 53F82400 		ldr	r0, [r3, r4, lsl #2]
 300 000c FFF7FEFF 		bl	memp_init_pool
 301              	.LVL26:
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 302              		.loc 1 229 48 discriminator 3 view .LVU82
 303 0010 0134     		adds	r4, r4, #1
 304              	.LVL27:
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 305              		.loc 1 229 48 is_stmt 0 discriminator 3 view .LVU83
 306 0012 A4B2     		uxth	r4, r4
 307              	.LVL28:
 308              	.L19:
 229:Middlewares/Third_Party/LwIP/src/core/memp.c ****     memp_init_pool(memp_pools[i]);
 309              		.loc 1 229 17 is_stmt 1 discriminator 1 view .LVU84
 310 0014 0C2C     		cmp	r4, #12
 311 0016 F6D9     		bls	.L20
 241:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 312              		.loc 1 241 1 is_stmt 0 view .LVU85
 313 0018 10BD     		pop	{r4, pc}
 314              	.LVL29:
 315              	.L23:
 241:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 316              		.loc 1 241 1 view .LVU86
 317 001a 00BF     		.align	2
 318              	.L22:
 319 001c 00000000 		.word	memp_pools
 320              		.cfi_endproc
 321              	.LFE175:
 323              		.section	.rodata.memp_malloc_pool.str1.4,"aMS",%progbits,1
 324              		.align	2
 325              	.LC4:
 326 0000 696E7661 		.ascii	"invalid pool desc\000"
 326      6C696420 
 326      706F6F6C 
 326      20646573 
 326      6300
 327              		.section	.text.memp_malloc_pool,"ax",%progbits
 328              		.align	1
 329              		.global	memp_malloc_pool
 330              		.syntax unified
 331              		.thumb
 332              		.thumb_func
 334              	memp_malloc_pool:
 335              	.LVL30:
 336              	.LFB177:
 315:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ASSERT("invalid pool desc", desc != NULL);
 337              		.loc 1 315 1 is_stmt 1 view -0
 338              		.cfi_startproc
 339              		@ args = 0, pretend = 0, frame = 0
 340              		@ frame_needed = 0, uses_anonymous_args = 0
 315:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ASSERT("invalid pool desc", desc != NULL);
 341              		.loc 1 315 1 is_stmt 0 view .LVU88
 342 0000 10B5     		push	{r4, lr}
 343              	.LCFI3:
 344              		.cfi_def_cfa_offset 8
ARM GAS  /tmp/ccqkcEof.s 			page 15


 345              		.cfi_offset 4, -8
 346              		.cfi_offset 14, -4
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 347              		.loc 1 316 3 is_stmt 1 view .LVU89
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 348              		.loc 1 316 3 view .LVU90
 349 0002 0446     		mov	r4, r0
 350 0004 10B1     		cbz	r0, .L25
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 351              		.loc 1 316 3 discriminator 3 view .LVU91
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 352              		.loc 1 316 3 discriminator 3 view .LVU92
 317:Middlewares/Third_Party/LwIP/src/core/memp.c ****     return NULL;
 353              		.loc 1 317 3 view .LVU93
 322:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 354              		.loc 1 322 3 view .LVU94
 322:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 355              		.loc 1 322 10 is_stmt 0 view .LVU95
 356 0006 FFF7FEFF 		bl	do_memp_malloc_pool
 357              	.LVL31:
 358              	.L24:
 326:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 359              		.loc 1 326 1 view .LVU96
 360 000a 10BD     		pop	{r4, pc}
 361              	.LVL32:
 362              	.L25:
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 363              		.loc 1 316 3 is_stmt 1 discriminator 1 view .LVU97
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 364              		.loc 1 316 3 discriminator 1 view .LVU98
 365 000c 044B     		ldr	r3, .L28
 366 000e 4FF49E72 		mov	r2, #316
 367 0012 0449     		ldr	r1, .L28+4
 368 0014 0448     		ldr	r0, .L28+8
 369              	.LVL33:
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 370              		.loc 1 316 3 is_stmt 0 discriminator 1 view .LVU99
 371 0016 FFF7FEFF 		bl	printf
 372              	.LVL34:
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 373              		.loc 1 316 3 is_stmt 1 discriminator 3 view .LVU100
 316:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (desc == NULL) {
 374              		.loc 1 316 3 discriminator 3 view .LVU101
 317:Middlewares/Third_Party/LwIP/src/core/memp.c ****     return NULL;
 375              		.loc 1 317 3 view .LVU102
 318:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 376              		.loc 1 318 5 view .LVU103
 318:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 377              		.loc 1 318 12 is_stmt 0 view .LVU104
 378 001a 2046     		mov	r0, r4
 379 001c F5E7     		b	.L24
 380              	.L29:
 381 001e 00BF     		.align	2
 382              	.L28:
 383 0020 00000000 		.word	.LC0
 384 0024 00000000 		.word	.LC4
 385 0028 54000000 		.word	.LC2
ARM GAS  /tmp/ccqkcEof.s 			page 16


 386              		.cfi_endproc
 387              	.LFE177:
 389              		.section	.rodata.memp_malloc.str1.4,"aMS",%progbits,1
 390              		.align	2
 391              	.LC5:
 392 0000 6D656D70 		.ascii	"memp_malloc: type < MEMP_MAX\000"
 392      5F6D616C 
 392      6C6F633A 
 392      20747970 
 392      65203C20 
 393              		.section	.text.memp_malloc,"ax",%progbits
 394              		.align	1
 395              		.global	memp_malloc
 396              		.syntax unified
 397              		.thumb
 398              		.thumb_func
 400              	memp_malloc:
 401              	.LVL35:
 402              	.LFB178:
 341:Middlewares/Third_Party/LwIP/src/core/memp.c ****   void *memp;
 403              		.loc 1 341 1 is_stmt 1 view -0
 404              		.cfi_startproc
 405              		@ args = 0, pretend = 0, frame = 0
 406              		@ frame_needed = 0, uses_anonymous_args = 0
 341:Middlewares/Third_Party/LwIP/src/core/memp.c ****   void *memp;
 407              		.loc 1 341 1 is_stmt 0 view .LVU106
 408 0000 08B5     		push	{r3, lr}
 409              	.LCFI4:
 410              		.cfi_def_cfa_offset 8
 411              		.cfi_offset 3, -8
 412              		.cfi_offset 14, -4
 342:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ERROR("memp_malloc: type < MEMP_MAX", (type < MEMP_MAX), return NULL;);
 413              		.loc 1 342 3 is_stmt 1 view .LVU107
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 414              		.loc 1 343 3 view .LVU108
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 415              		.loc 1 343 3 view .LVU109
 416 0002 0C28     		cmp	r0, #12
 417 0004 05D8     		bhi	.L34
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 418              		.loc 1 343 3 discriminator 2 view .LVU110
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 419              		.loc 1 343 3 discriminator 2 view .LVU111
 350:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 420              		.loc 1 350 3 view .LVU112
 350:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 421              		.loc 1 350 10 is_stmt 0 view .LVU113
 422 0006 074B     		ldr	r3, .L35
 423 0008 53F82000 		ldr	r0, [r3, r0, lsl #2]
 424              	.LVL36:
 350:Middlewares/Third_Party/LwIP/src/core/memp.c **** #else
 425              		.loc 1 350 10 view .LVU114
 426 000c FFF7FEFF 		bl	do_memp_malloc_pool
 427              	.LVL37:
 355:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 428              		.loc 1 355 3 is_stmt 1 view .LVU115
 429              	.L30:
ARM GAS  /tmp/ccqkcEof.s 			page 17


 356:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 430              		.loc 1 356 1 is_stmt 0 view .LVU116
 431 0010 08BD     		pop	{r3, pc}
 432              	.LVL38:
 433              	.L34:
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 434              		.loc 1 343 3 is_stmt 1 discriminator 1 view .LVU117
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 435              		.loc 1 343 3 discriminator 1 view .LVU118
 436 0012 054B     		ldr	r3, .L35+4
 437 0014 40F25712 		movw	r2, #343
 438 0018 0449     		ldr	r1, .L35+8
 439 001a 0548     		ldr	r0, .L35+12
 440              	.LVL39:
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 441              		.loc 1 343 3 is_stmt 0 discriminator 1 view .LVU119
 442 001c FFF7FEFF 		bl	printf
 443              	.LVL40:
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 444              		.loc 1 343 3 is_stmt 1 discriminator 1 view .LVU120
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 445              		.loc 1 343 3 discriminator 1 view .LVU121
 446 0020 0020     		movs	r0, #0
 343:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 447              		.loc 1 343 3 is_stmt 0 view .LVU122
 448 0022 F5E7     		b	.L30
 449              	.L36:
 450              		.align	2
 451              	.L35:
 452 0024 00000000 		.word	memp_pools
 453 0028 00000000 		.word	.LC0
 454 002c 00000000 		.word	.LC5
 455 0030 54000000 		.word	.LC2
 456              		.cfi_endproc
 457              	.LFE178:
 459              		.section	.text.memp_free_pool,"ax",%progbits
 460              		.align	1
 461              		.global	memp_free_pool
 462              		.syntax unified
 463              		.thumb
 464              		.thumb_func
 466              	memp_free_pool:
 467              	.LVL41:
 468              	.LFB180:
 395:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 396:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 397:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Put a custom pool element back into its pool.
 398:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 399:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param desc the pool where to put mem
 400:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param mem the memp element to free
 401:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 402:Middlewares/Third_Party/LwIP/src/core/memp.c **** void
 403:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_free_pool(const struct memp_desc *desc, void *mem)
 404:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 469              		.loc 1 404 1 is_stmt 1 view -0
 470              		.cfi_startproc
 471              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/ccqkcEof.s 			page 18


 472              		@ frame_needed = 0, uses_anonymous_args = 0
 473              		.loc 1 404 1 is_stmt 0 view .LVU124
 474 0000 38B5     		push	{r3, r4, r5, lr}
 475              	.LCFI5:
 476              		.cfi_def_cfa_offset 16
 477              		.cfi_offset 3, -16
 478              		.cfi_offset 4, -12
 479              		.cfi_offset 5, -8
 480              		.cfi_offset 14, -4
 481 0002 0C46     		mov	r4, r1
 405:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ASSERT("invalid pool desc", desc != NULL);
 482              		.loc 1 405 3 is_stmt 1 view .LVU125
 483              		.loc 1 405 3 view .LVU126
 484 0004 0546     		mov	r5, r0
 485 0006 20B1     		cbz	r0, .L41
 486              	.LVL42:
 487              	.L38:
 488              		.loc 1 405 3 discriminator 3 view .LVU127
 489              		.loc 1 405 3 discriminator 3 view .LVU128
 406:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if ((desc == NULL) || (mem == NULL)) {
 490              		.loc 1 406 3 view .LVU129
 491              		.loc 1 406 6 is_stmt 0 view .LVU130
 492 0008 002C     		cmp	r4, #0
 493 000a 18BF     		it	ne
 494 000c 002D     		cmpne	r5, #0
 495 000e 08D1     		bne	.L42
 496              	.L37:
 407:Middlewares/Third_Party/LwIP/src/core/memp.c ****     return;
 408:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 409:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 410:Middlewares/Third_Party/LwIP/src/core/memp.c ****   do_memp_free_pool(desc, mem);
 411:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 497              		.loc 1 411 1 view .LVU131
 498 0010 38BD     		pop	{r3, r4, r5, pc}
 499              	.LVL43:
 500              	.L41:
 405:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if ((desc == NULL) || (mem == NULL)) {
 501              		.loc 1 405 3 is_stmt 1 discriminator 1 view .LVU132
 405:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if ((desc == NULL) || (mem == NULL)) {
 502              		.loc 1 405 3 discriminator 1 view .LVU133
 503 0012 064B     		ldr	r3, .L43
 504 0014 40F29512 		movw	r2, #405
 505 0018 0549     		ldr	r1, .L43+4
 506              	.LVL44:
 405:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if ((desc == NULL) || (mem == NULL)) {
 507              		.loc 1 405 3 is_stmt 0 discriminator 1 view .LVU134
 508 001a 0648     		ldr	r0, .L43+8
 509              	.LVL45:
 405:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if ((desc == NULL) || (mem == NULL)) {
 510              		.loc 1 405 3 discriminator 1 view .LVU135
 511 001c FFF7FEFF 		bl	printf
 512              	.LVL46:
 513 0020 F2E7     		b	.L38
 514              	.L42:
 410:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 515              		.loc 1 410 3 is_stmt 1 view .LVU136
 516 0022 2146     		mov	r1, r4
ARM GAS  /tmp/ccqkcEof.s 			page 19


 517 0024 2846     		mov	r0, r5
 518 0026 FFF7FEFF 		bl	do_memp_free_pool
 519              	.LVL47:
 520 002a F1E7     		b	.L37
 521              	.L44:
 522              		.align	2
 523              	.L43:
 524 002c 00000000 		.word	.LC0
 525 0030 00000000 		.word	.LC4
 526 0034 54000000 		.word	.LC2
 527              		.cfi_endproc
 528              	.LFE180:
 530              		.section	.rodata.memp_free.str1.4,"aMS",%progbits,1
 531              		.align	2
 532              	.LC6:
 533 0000 6D656D70 		.ascii	"memp_free: type < MEMP_MAX\000"
 533      5F667265 
 533      653A2074 
 533      79706520 
 533      3C204D45 
 534              		.section	.text.memp_free,"ax",%progbits
 535              		.align	1
 536              		.global	memp_free
 537              		.syntax unified
 538              		.thumb
 539              		.thumb_func
 541              	memp_free:
 542              	.LVL48:
 543              	.LFB181:
 412:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 413:Middlewares/Third_Party/LwIP/src/core/memp.c **** /**
 414:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * Put an element back into its pool.
 415:Middlewares/Third_Party/LwIP/src/core/memp.c ****  *
 416:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param type the pool where to put mem
 417:Middlewares/Third_Party/LwIP/src/core/memp.c ****  * @param mem the memp element to free
 418:Middlewares/Third_Party/LwIP/src/core/memp.c ****  */
 419:Middlewares/Third_Party/LwIP/src/core/memp.c **** void
 420:Middlewares/Third_Party/LwIP/src/core/memp.c **** memp_free(memp_t type, void *mem)
 421:Middlewares/Third_Party/LwIP/src/core/memp.c **** {
 544              		.loc 1 421 1 view -0
 545              		.cfi_startproc
 546              		@ args = 0, pretend = 0, frame = 0
 547              		@ frame_needed = 0, uses_anonymous_args = 0
 548              		.loc 1 421 1 is_stmt 0 view .LVU138
 549 0000 08B5     		push	{r3, lr}
 550              	.LCFI6:
 551              		.cfi_def_cfa_offset 8
 552              		.cfi_offset 3, -8
 553              		.cfi_offset 14, -4
 422:Middlewares/Third_Party/LwIP/src/core/memp.c **** #ifdef LWIP_HOOK_MEMP_AVAILABLE
 423:Middlewares/Third_Party/LwIP/src/core/memp.c ****   struct memp *old_first;
 424:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 425:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 426:Middlewares/Third_Party/LwIP/src/core/memp.c ****   LWIP_ERROR("memp_free: type < MEMP_MAX", (type < MEMP_MAX), return;);
 554              		.loc 1 426 3 is_stmt 1 view .LVU139
 555              		.loc 1 426 3 view .LVU140
 556 0002 0C28     		cmp	r0, #12
ARM GAS  /tmp/ccqkcEof.s 			page 20


 557 0004 06D8     		bhi	.L49
 558              		.loc 1 426 3 discriminator 2 view .LVU141
 559              		.loc 1 426 3 discriminator 2 view .LVU142
 427:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 428:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (mem == NULL) {
 560              		.loc 1 428 3 view .LVU143
 561              		.loc 1 428 6 is_stmt 0 view .LVU144
 562 0006 21B1     		cbz	r1, .L45
 429:Middlewares/Third_Party/LwIP/src/core/memp.c ****     return;
 430:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 431:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 432:Middlewares/Third_Party/LwIP/src/core/memp.c **** #if MEMP_OVERFLOW_CHECK >= 2
 433:Middlewares/Third_Party/LwIP/src/core/memp.c ****   memp_overflow_check_all();
 434:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif /* MEMP_OVERFLOW_CHECK >= 2 */
 435:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 436:Middlewares/Third_Party/LwIP/src/core/memp.c **** #ifdef LWIP_HOOK_MEMP_AVAILABLE
 437:Middlewares/Third_Party/LwIP/src/core/memp.c ****   old_first = *memp_pools[type]->tab;
 438:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 439:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 440:Middlewares/Third_Party/LwIP/src/core/memp.c ****   do_memp_free_pool(memp_pools[type], mem);
 563              		.loc 1 440 3 is_stmt 1 view .LVU145
 564 0008 064B     		ldr	r3, .L50
 565 000a 53F82000 		ldr	r0, [r3, r0, lsl #2]
 566              	.LVL49:
 567              		.loc 1 440 3 is_stmt 0 view .LVU146
 568 000e FFF7FEFF 		bl	do_memp_free_pool
 569              	.LVL50:
 570              	.L45:
 441:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 442:Middlewares/Third_Party/LwIP/src/core/memp.c **** #ifdef LWIP_HOOK_MEMP_AVAILABLE
 443:Middlewares/Third_Party/LwIP/src/core/memp.c ****   if (old_first == NULL) {
 444:Middlewares/Third_Party/LwIP/src/core/memp.c ****     LWIP_HOOK_MEMP_AVAILABLE(type);
 445:Middlewares/Third_Party/LwIP/src/core/memp.c ****   }
 446:Middlewares/Third_Party/LwIP/src/core/memp.c **** #endif
 447:Middlewares/Third_Party/LwIP/src/core/memp.c **** }
 571              		.loc 1 447 1 view .LVU147
 572 0012 08BD     		pop	{r3, pc}
 573              	.LVL51:
 574              	.L49:
 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 575              		.loc 1 426 3 is_stmt 1 discriminator 1 view .LVU148
 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 576              		.loc 1 426 3 discriminator 1 view .LVU149
 577 0014 044B     		ldr	r3, .L50+4
 578 0016 4FF4D572 		mov	r2, #426
 579 001a 0449     		ldr	r1, .L50+8
 580              	.LVL52:
 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 581              		.loc 1 426 3 is_stmt 0 discriminator 1 view .LVU150
 582 001c 0448     		ldr	r0, .L50+12
 583              	.LVL53:
 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 584              		.loc 1 426 3 discriminator 1 view .LVU151
 585 001e FFF7FEFF 		bl	printf
 586              	.LVL54:
 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 587              		.loc 1 426 3 is_stmt 1 discriminator 1 view .LVU152
ARM GAS  /tmp/ccqkcEof.s 			page 21


 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 588              		.loc 1 426 3 discriminator 1 view .LVU153
 426:Middlewares/Third_Party/LwIP/src/core/memp.c **** 
 589              		.loc 1 426 3 is_stmt 0 view .LVU154
 590 0022 F6E7     		b	.L45
 591              	.L51:
 592              		.align	2
 593              	.L50:
 594 0024 00000000 		.word	memp_pools
 595 0028 00000000 		.word	.LC0
 596 002c 00000000 		.word	.LC6
 597 0030 54000000 		.word	.LC2
 598              		.cfi_endproc
 599              	.LFE181:
 601              		.global	memp_pools
 602              		.section	.rodata.memp_pools,"a"
 603              		.align	2
 606              	memp_pools:
 607 0000 00000000 		.word	memp_UDP_PCB
 608 0004 00000000 		.word	memp_TCP_PCB
 609 0008 00000000 		.word	memp_TCP_PCB_LISTEN
 610 000c 00000000 		.word	memp_TCP_SEG
 611 0010 00000000 		.word	memp_REASSDATA
 612 0014 00000000 		.word	memp_FRAG_PBUF
 613 0018 00000000 		.word	memp_NETBUF
 614 001c 00000000 		.word	memp_NETCONN
 615 0020 00000000 		.word	memp_TCPIP_MSG_API
 616 0024 00000000 		.word	memp_TCPIP_MSG_INPKT
 617 0028 00000000 		.word	memp_SYS_TIMEOUT
 618 002c 00000000 		.word	memp_PBUF
 619 0030 00000000 		.word	memp_PBUF_POOL
 620              		.global	memp_PBUF_POOL
 621              		.section	.rodata.memp_PBUF_POOL,"a"
 622              		.align	2
 625              	memp_PBUF_POOL:
 626 0000 6002     		.short	608
 627 0002 1000     		.short	16
 628 0004 00000000 		.word	memp_memory_PBUF_POOL_base
 629 0008 00000000 		.word	memp_tab_PBUF_POOL
 630              		.section	.bss.memp_tab_PBUF_POOL,"aw",%nobits
 631              		.align	2
 634              	memp_tab_PBUF_POOL:
 635 0000 00000000 		.space	4
 636              		.global	memp_memory_PBUF_POOL_base
 637              		.section	.bss.memp_memory_PBUF_POOL_base,"aw",%nobits
 638              		.align	2
 641              	memp_memory_PBUF_POOL_base:
 642 0000 00000000 		.space	9731
 642      00000000 
 642      00000000 
 642      00000000 
 642      00000000 
 643              		.global	memp_PBUF
 644              		.section	.rodata.memp_PBUF,"a"
 645              		.align	2
 648              	memp_PBUF:
 649 0000 1000     		.short	16
ARM GAS  /tmp/ccqkcEof.s 			page 22


 650 0002 1000     		.short	16
 651 0004 00000000 		.word	memp_memory_PBUF_base
 652 0008 00000000 		.word	memp_tab_PBUF
 653              		.section	.bss.memp_tab_PBUF,"aw",%nobits
 654              		.align	2
 657              	memp_tab_PBUF:
 658 0000 00000000 		.space	4
 659              		.global	memp_memory_PBUF_base
 660              		.section	.bss.memp_memory_PBUF_base,"aw",%nobits
 661              		.align	2
 664              	memp_memory_PBUF_base:
 665 0000 00000000 		.space	259
 665      00000000 
 665      00000000 
 665      00000000 
 665      00000000 
 666              		.global	memp_SYS_TIMEOUT
 667              		.section	.rodata.memp_SYS_TIMEOUT,"a"
 668              		.align	2
 671              	memp_SYS_TIMEOUT:
 672 0000 1000     		.short	16
 673 0002 0300     		.short	3
 674 0004 00000000 		.word	memp_memory_SYS_TIMEOUT_base
 675 0008 00000000 		.word	memp_tab_SYS_TIMEOUT
 676              		.section	.bss.memp_tab_SYS_TIMEOUT,"aw",%nobits
 677              		.align	2
 680              	memp_tab_SYS_TIMEOUT:
 681 0000 00000000 		.space	4
 682              		.global	memp_memory_SYS_TIMEOUT_base
 683              		.section	.bss.memp_memory_SYS_TIMEOUT_base,"aw",%nobits
 684              		.align	2
 687              	memp_memory_SYS_TIMEOUT_base:
 688 0000 00000000 		.space	51
 688      00000000 
 688      00000000 
 688      00000000 
 688      00000000 
 689              		.global	memp_TCPIP_MSG_INPKT
 690              		.section	.rodata.memp_TCPIP_MSG_INPKT,"a"
 691              		.align	2
 694              	memp_TCPIP_MSG_INPKT:
 695 0000 1000     		.short	16
 696 0002 0800     		.short	8
 697 0004 00000000 		.word	memp_memory_TCPIP_MSG_INPKT_base
 698 0008 00000000 		.word	memp_tab_TCPIP_MSG_INPKT
 699              		.section	.bss.memp_tab_TCPIP_MSG_INPKT,"aw",%nobits
 700              		.align	2
 703              	memp_tab_TCPIP_MSG_INPKT:
 704 0000 00000000 		.space	4
 705              		.global	memp_memory_TCPIP_MSG_INPKT_base
 706              		.section	.bss.memp_memory_TCPIP_MSG_INPKT_base,"aw",%nobits
 707              		.align	2
 710              	memp_memory_TCPIP_MSG_INPKT_base:
 711 0000 00000000 		.space	131
 711      00000000 
 711      00000000 
 711      00000000 
ARM GAS  /tmp/ccqkcEof.s 			page 23


 711      00000000 
 712              		.global	memp_TCPIP_MSG_API
 713              		.section	.rodata.memp_TCPIP_MSG_API,"a"
 714              		.align	2
 717              	memp_TCPIP_MSG_API:
 718 0000 1000     		.short	16
 719 0002 0800     		.short	8
 720 0004 00000000 		.word	memp_memory_TCPIP_MSG_API_base
 721 0008 00000000 		.word	memp_tab_TCPIP_MSG_API
 722              		.section	.bss.memp_tab_TCPIP_MSG_API,"aw",%nobits
 723              		.align	2
 726              	memp_tab_TCPIP_MSG_API:
 727 0000 00000000 		.space	4
 728              		.global	memp_memory_TCPIP_MSG_API_base
 729              		.section	.bss.memp_memory_TCPIP_MSG_API_base,"aw",%nobits
 730              		.align	2
 733              	memp_memory_TCPIP_MSG_API_base:
 734 0000 00000000 		.space	131
 734      00000000 
 734      00000000 
 734      00000000 
 734      00000000 
 735              		.global	memp_NETCONN
 736              		.section	.rodata.memp_NETCONN,"a"
 737              		.align	2
 740              	memp_NETCONN:
 741 0000 2C00     		.short	44
 742 0002 0400     		.short	4
 743 0004 00000000 		.word	memp_memory_NETCONN_base
 744 0008 00000000 		.word	memp_tab_NETCONN
 745              		.section	.bss.memp_tab_NETCONN,"aw",%nobits
 746              		.align	2
 749              	memp_tab_NETCONN:
 750 0000 00000000 		.space	4
 751              		.global	memp_memory_NETCONN_base
 752              		.section	.bss.memp_memory_NETCONN_base,"aw",%nobits
 753              		.align	2
 756              	memp_memory_NETCONN_base:
 757 0000 00000000 		.space	179
 757      00000000 
 757      00000000 
 757      00000000 
 757      00000000 
 758              		.global	memp_NETBUF
 759              		.section	.rodata.memp_NETBUF,"a"
 760              		.align	2
 763              	memp_NETBUF:
 764 0000 1000     		.short	16
 765 0002 0200     		.short	2
 766 0004 00000000 		.word	memp_memory_NETBUF_base
 767 0008 00000000 		.word	memp_tab_NETBUF
 768              		.section	.bss.memp_tab_NETBUF,"aw",%nobits
 769              		.align	2
 772              	memp_tab_NETBUF:
 773 0000 00000000 		.space	4
 774              		.global	memp_memory_NETBUF_base
 775              		.section	.bss.memp_memory_NETBUF_base,"aw",%nobits
ARM GAS  /tmp/ccqkcEof.s 			page 24


 776              		.align	2
 779              	memp_memory_NETBUF_base:
 780 0000 00000000 		.space	35
 780      00000000 
 780      00000000 
 780      00000000 
 780      00000000 
 781              		.global	memp_FRAG_PBUF
 782              		.section	.rodata.memp_FRAG_PBUF,"a"
 783              		.align	2
 786              	memp_FRAG_PBUF:
 787 0000 1800     		.short	24
 788 0002 0F00     		.short	15
 789 0004 00000000 		.word	memp_memory_FRAG_PBUF_base
 790 0008 00000000 		.word	memp_tab_FRAG_PBUF
 791              		.section	.bss.memp_tab_FRAG_PBUF,"aw",%nobits
 792              		.align	2
 795              	memp_tab_FRAG_PBUF:
 796 0000 00000000 		.space	4
 797              		.global	memp_memory_FRAG_PBUF_base
 798              		.section	.bss.memp_memory_FRAG_PBUF_base,"aw",%nobits
 799              		.align	2
 802              	memp_memory_FRAG_PBUF_base:
 803 0000 00000000 		.space	363
 803      00000000 
 803      00000000 
 803      00000000 
 803      00000000 
 804              		.global	memp_REASSDATA
 805              		.section	.rodata.memp_REASSDATA,"a"
 806              		.align	2
 809              	memp_REASSDATA:
 810 0000 2000     		.short	32
 811 0002 0500     		.short	5
 812 0004 00000000 		.word	memp_memory_REASSDATA_base
 813 0008 00000000 		.word	memp_tab_REASSDATA
 814              		.section	.bss.memp_tab_REASSDATA,"aw",%nobits
 815              		.align	2
 818              	memp_tab_REASSDATA:
 819 0000 00000000 		.space	4
 820              		.global	memp_memory_REASSDATA_base
 821              		.section	.bss.memp_memory_REASSDATA_base,"aw",%nobits
 822              		.align	2
 825              	memp_memory_REASSDATA_base:
 826 0000 00000000 		.space	163
 826      00000000 
 826      00000000 
 826      00000000 
 826      00000000 
 827              		.global	memp_TCP_SEG
 828              		.section	.rodata.memp_TCP_SEG,"a"
 829              		.align	2
 832              	memp_TCP_SEG:
 833 0000 1000     		.short	16
 834 0002 1000     		.short	16
 835 0004 00000000 		.word	memp_memory_TCP_SEG_base
 836 0008 00000000 		.word	memp_tab_TCP_SEG
ARM GAS  /tmp/ccqkcEof.s 			page 25


 837              		.section	.bss.memp_tab_TCP_SEG,"aw",%nobits
 838              		.align	2
 841              	memp_tab_TCP_SEG:
 842 0000 00000000 		.space	4
 843              		.global	memp_memory_TCP_SEG_base
 844              		.section	.bss.memp_memory_TCP_SEG_base,"aw",%nobits
 845              		.align	2
 848              	memp_memory_TCP_SEG_base:
 849 0000 00000000 		.space	259
 849      00000000 
 849      00000000 
 849      00000000 
 849      00000000 
 850              		.global	memp_TCP_PCB_LISTEN
 851              		.section	.rodata.memp_TCP_PCB_LISTEN,"a"
 852              		.align	2
 855              	memp_TCP_PCB_LISTEN:
 856 0000 1C00     		.short	28
 857 0002 0800     		.short	8
 858 0004 00000000 		.word	memp_memory_TCP_PCB_LISTEN_base
 859 0008 00000000 		.word	memp_tab_TCP_PCB_LISTEN
 860              		.section	.bss.memp_tab_TCP_PCB_LISTEN,"aw",%nobits
 861              		.align	2
 864              	memp_tab_TCP_PCB_LISTEN:
 865 0000 00000000 		.space	4
 866              		.global	memp_memory_TCP_PCB_LISTEN_base
 867              		.section	.bss.memp_memory_TCP_PCB_LISTEN_base,"aw",%nobits
 868              		.align	2
 871              	memp_memory_TCP_PCB_LISTEN_base:
 872 0000 00000000 		.space	227
 872      00000000 
 872      00000000 
 872      00000000 
 872      00000000 
 873              		.global	memp_TCP_PCB
 874              		.section	.rodata.memp_TCP_PCB,"a"
 875              		.align	2
 878              	memp_TCP_PCB:
 879 0000 9C00     		.short	156
 880 0002 0500     		.short	5
 881 0004 00000000 		.word	memp_memory_TCP_PCB_base
 882 0008 00000000 		.word	memp_tab_TCP_PCB
 883              		.section	.bss.memp_tab_TCP_PCB,"aw",%nobits
 884              		.align	2
 887              	memp_tab_TCP_PCB:
 888 0000 00000000 		.space	4
 889              		.global	memp_memory_TCP_PCB_base
 890              		.section	.bss.memp_memory_TCP_PCB_base,"aw",%nobits
 891              		.align	2
 894              	memp_memory_TCP_PCB_base:
 895 0000 00000000 		.space	783
 895      00000000 
 895      00000000 
 895      00000000 
 895      00000000 
 896              		.global	memp_UDP_PCB
 897              		.section	.rodata.memp_UDP_PCB,"a"
ARM GAS  /tmp/ccqkcEof.s 			page 26


 898              		.align	2
 901              	memp_UDP_PCB:
 902 0000 2000     		.short	32
 903 0002 0F00     		.short	15
 904 0004 00000000 		.word	memp_memory_UDP_PCB_base
 905 0008 00000000 		.word	memp_tab_UDP_PCB
 906              		.section	.bss.memp_tab_UDP_PCB,"aw",%nobits
 907              		.align	2
 910              	memp_tab_UDP_PCB:
 911 0000 00000000 		.space	4
 912              		.global	memp_memory_UDP_PCB_base
 913              		.section	.bss.memp_memory_UDP_PCB_base,"aw",%nobits
 914              		.align	2
 917              	memp_memory_UDP_PCB_base:
 918 0000 00000000 		.space	483
 918      00000000 
 918      00000000 
 918      00000000 
 918      00000000 
 919              		.text
 920              	.Letext0:
 921              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 922              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 923              		.file 4 "Middlewares/Third_Party/LwIP/system/arch/cc.h"
 924              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 925              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 926              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/memp_priv.h"
 927              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/memp_std.h"
 928              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
 929              		.file 10 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
ARM GAS  /tmp/ccqkcEof.s 			page 27


DEFINED SYMBOLS
                            *ABS*:00000000 memp.c
     /tmp/ccqkcEof.s:20     .rodata.do_memp_malloc_pool.str1.4:00000000 $d
     /tmp/ccqkcEof.s:30     .text.do_memp_malloc_pool:00000000 $t
     /tmp/ccqkcEof.s:35     .text.do_memp_malloc_pool:00000000 do_memp_malloc_pool
     /tmp/ccqkcEof.s:115    .text.do_memp_malloc_pool:0000003c $d
     /tmp/ccqkcEof.s:122    .rodata.do_memp_free_pool.str1.4:00000000 $d
     /tmp/ccqkcEof.s:126    .text.do_memp_free_pool:00000000 $t
     /tmp/ccqkcEof.s:131    .text.do_memp_free_pool:00000000 do_memp_free_pool
     /tmp/ccqkcEof.s:197    .text.do_memp_free_pool:00000030 $d
     /tmp/ccqkcEof.s:204    .text.memp_init_pool:00000000 $t
     /tmp/ccqkcEof.s:210    .text.memp_init_pool:00000000 memp_init_pool
     /tmp/ccqkcEof.s:271    .text.memp_init:00000000 $t
     /tmp/ccqkcEof.s:277    .text.memp_init:00000000 memp_init
     /tmp/ccqkcEof.s:319    .text.memp_init:0000001c $d
     /tmp/ccqkcEof.s:606    .rodata.memp_pools:00000000 memp_pools
     /tmp/ccqkcEof.s:324    .rodata.memp_malloc_pool.str1.4:00000000 $d
     /tmp/ccqkcEof.s:328    .text.memp_malloc_pool:00000000 $t
     /tmp/ccqkcEof.s:334    .text.memp_malloc_pool:00000000 memp_malloc_pool
     /tmp/ccqkcEof.s:383    .text.memp_malloc_pool:00000020 $d
     /tmp/ccqkcEof.s:390    .rodata.memp_malloc.str1.4:00000000 $d
     /tmp/ccqkcEof.s:394    .text.memp_malloc:00000000 $t
     /tmp/ccqkcEof.s:400    .text.memp_malloc:00000000 memp_malloc
     /tmp/ccqkcEof.s:452    .text.memp_malloc:00000024 $d
     /tmp/ccqkcEof.s:460    .text.memp_free_pool:00000000 $t
     /tmp/ccqkcEof.s:466    .text.memp_free_pool:00000000 memp_free_pool
     /tmp/ccqkcEof.s:524    .text.memp_free_pool:0000002c $d
     /tmp/ccqkcEof.s:531    .rodata.memp_free.str1.4:00000000 $d
     /tmp/ccqkcEof.s:535    .text.memp_free:00000000 $t
     /tmp/ccqkcEof.s:541    .text.memp_free:00000000 memp_free
     /tmp/ccqkcEof.s:594    .text.memp_free:00000024 $d
     /tmp/ccqkcEof.s:603    .rodata.memp_pools:00000000 $d
     /tmp/ccqkcEof.s:901    .rodata.memp_UDP_PCB:00000000 memp_UDP_PCB
     /tmp/ccqkcEof.s:878    .rodata.memp_TCP_PCB:00000000 memp_TCP_PCB
     /tmp/ccqkcEof.s:855    .rodata.memp_TCP_PCB_LISTEN:00000000 memp_TCP_PCB_LISTEN
     /tmp/ccqkcEof.s:832    .rodata.memp_TCP_SEG:00000000 memp_TCP_SEG
     /tmp/ccqkcEof.s:809    .rodata.memp_REASSDATA:00000000 memp_REASSDATA
     /tmp/ccqkcEof.s:786    .rodata.memp_FRAG_PBUF:00000000 memp_FRAG_PBUF
     /tmp/ccqkcEof.s:763    .rodata.memp_NETBUF:00000000 memp_NETBUF
     /tmp/ccqkcEof.s:740    .rodata.memp_NETCONN:00000000 memp_NETCONN
     /tmp/ccqkcEof.s:717    .rodata.memp_TCPIP_MSG_API:00000000 memp_TCPIP_MSG_API
     /tmp/ccqkcEof.s:694    .rodata.memp_TCPIP_MSG_INPKT:00000000 memp_TCPIP_MSG_INPKT
     /tmp/ccqkcEof.s:671    .rodata.memp_SYS_TIMEOUT:00000000 memp_SYS_TIMEOUT
     /tmp/ccqkcEof.s:648    .rodata.memp_PBUF:00000000 memp_PBUF
     /tmp/ccqkcEof.s:625    .rodata.memp_PBUF_POOL:00000000 memp_PBUF_POOL
     /tmp/ccqkcEof.s:622    .rodata.memp_PBUF_POOL:00000000 $d
     /tmp/ccqkcEof.s:641    .bss.memp_memory_PBUF_POOL_base:00000000 memp_memory_PBUF_POOL_base
     /tmp/ccqkcEof.s:634    .bss.memp_tab_PBUF_POOL:00000000 memp_tab_PBUF_POOL
     /tmp/ccqkcEof.s:631    .bss.memp_tab_PBUF_POOL:00000000 $d
     /tmp/ccqkcEof.s:638    .bss.memp_memory_PBUF_POOL_base:00000000 $d
     /tmp/ccqkcEof.s:645    .rodata.memp_PBUF:00000000 $d
     /tmp/ccqkcEof.s:664    .bss.memp_memory_PBUF_base:00000000 memp_memory_PBUF_base
     /tmp/ccqkcEof.s:657    .bss.memp_tab_PBUF:00000000 memp_tab_PBUF
     /tmp/ccqkcEof.s:654    .bss.memp_tab_PBUF:00000000 $d
     /tmp/ccqkcEof.s:661    .bss.memp_memory_PBUF_base:00000000 $d
     /tmp/ccqkcEof.s:668    .rodata.memp_SYS_TIMEOUT:00000000 $d
     /tmp/ccqkcEof.s:687    .bss.memp_memory_SYS_TIMEOUT_base:00000000 memp_memory_SYS_TIMEOUT_base
ARM GAS  /tmp/ccqkcEof.s 			page 28


     /tmp/ccqkcEof.s:680    .bss.memp_tab_SYS_TIMEOUT:00000000 memp_tab_SYS_TIMEOUT
     /tmp/ccqkcEof.s:677    .bss.memp_tab_SYS_TIMEOUT:00000000 $d
     /tmp/ccqkcEof.s:684    .bss.memp_memory_SYS_TIMEOUT_base:00000000 $d
     /tmp/ccqkcEof.s:691    .rodata.memp_TCPIP_MSG_INPKT:00000000 $d
     /tmp/ccqkcEof.s:710    .bss.memp_memory_TCPIP_MSG_INPKT_base:00000000 memp_memory_TCPIP_MSG_INPKT_base
     /tmp/ccqkcEof.s:703    .bss.memp_tab_TCPIP_MSG_INPKT:00000000 memp_tab_TCPIP_MSG_INPKT
     /tmp/ccqkcEof.s:700    .bss.memp_tab_TCPIP_MSG_INPKT:00000000 $d
     /tmp/ccqkcEof.s:707    .bss.memp_memory_TCPIP_MSG_INPKT_base:00000000 $d
     /tmp/ccqkcEof.s:714    .rodata.memp_TCPIP_MSG_API:00000000 $d
     /tmp/ccqkcEof.s:733    .bss.memp_memory_TCPIP_MSG_API_base:00000000 memp_memory_TCPIP_MSG_API_base
     /tmp/ccqkcEof.s:726    .bss.memp_tab_TCPIP_MSG_API:00000000 memp_tab_TCPIP_MSG_API
     /tmp/ccqkcEof.s:723    .bss.memp_tab_TCPIP_MSG_API:00000000 $d
     /tmp/ccqkcEof.s:730    .bss.memp_memory_TCPIP_MSG_API_base:00000000 $d
     /tmp/ccqkcEof.s:737    .rodata.memp_NETCONN:00000000 $d
     /tmp/ccqkcEof.s:756    .bss.memp_memory_NETCONN_base:00000000 memp_memory_NETCONN_base
     /tmp/ccqkcEof.s:749    .bss.memp_tab_NETCONN:00000000 memp_tab_NETCONN
     /tmp/ccqkcEof.s:746    .bss.memp_tab_NETCONN:00000000 $d
     /tmp/ccqkcEof.s:753    .bss.memp_memory_NETCONN_base:00000000 $d
     /tmp/ccqkcEof.s:760    .rodata.memp_NETBUF:00000000 $d
     /tmp/ccqkcEof.s:779    .bss.memp_memory_NETBUF_base:00000000 memp_memory_NETBUF_base
     /tmp/ccqkcEof.s:772    .bss.memp_tab_NETBUF:00000000 memp_tab_NETBUF
     /tmp/ccqkcEof.s:769    .bss.memp_tab_NETBUF:00000000 $d
     /tmp/ccqkcEof.s:776    .bss.memp_memory_NETBUF_base:00000000 $d
     /tmp/ccqkcEof.s:783    .rodata.memp_FRAG_PBUF:00000000 $d
     /tmp/ccqkcEof.s:802    .bss.memp_memory_FRAG_PBUF_base:00000000 memp_memory_FRAG_PBUF_base
     /tmp/ccqkcEof.s:795    .bss.memp_tab_FRAG_PBUF:00000000 memp_tab_FRAG_PBUF
     /tmp/ccqkcEof.s:792    .bss.memp_tab_FRAG_PBUF:00000000 $d
     /tmp/ccqkcEof.s:799    .bss.memp_memory_FRAG_PBUF_base:00000000 $d
     /tmp/ccqkcEof.s:806    .rodata.memp_REASSDATA:00000000 $d
     /tmp/ccqkcEof.s:825    .bss.memp_memory_REASSDATA_base:00000000 memp_memory_REASSDATA_base
     /tmp/ccqkcEof.s:818    .bss.memp_tab_REASSDATA:00000000 memp_tab_REASSDATA
     /tmp/ccqkcEof.s:815    .bss.memp_tab_REASSDATA:00000000 $d
     /tmp/ccqkcEof.s:822    .bss.memp_memory_REASSDATA_base:00000000 $d
     /tmp/ccqkcEof.s:829    .rodata.memp_TCP_SEG:00000000 $d
     /tmp/ccqkcEof.s:848    .bss.memp_memory_TCP_SEG_base:00000000 memp_memory_TCP_SEG_base
     /tmp/ccqkcEof.s:841    .bss.memp_tab_TCP_SEG:00000000 memp_tab_TCP_SEG
     /tmp/ccqkcEof.s:838    .bss.memp_tab_TCP_SEG:00000000 $d
     /tmp/ccqkcEof.s:845    .bss.memp_memory_TCP_SEG_base:00000000 $d
     /tmp/ccqkcEof.s:852    .rodata.memp_TCP_PCB_LISTEN:00000000 $d
     /tmp/ccqkcEof.s:871    .bss.memp_memory_TCP_PCB_LISTEN_base:00000000 memp_memory_TCP_PCB_LISTEN_base
     /tmp/ccqkcEof.s:864    .bss.memp_tab_TCP_PCB_LISTEN:00000000 memp_tab_TCP_PCB_LISTEN
     /tmp/ccqkcEof.s:861    .bss.memp_tab_TCP_PCB_LISTEN:00000000 $d
     /tmp/ccqkcEof.s:868    .bss.memp_memory_TCP_PCB_LISTEN_base:00000000 $d
     /tmp/ccqkcEof.s:875    .rodata.memp_TCP_PCB:00000000 $d
     /tmp/ccqkcEof.s:894    .bss.memp_memory_TCP_PCB_base:00000000 memp_memory_TCP_PCB_base
     /tmp/ccqkcEof.s:887    .bss.memp_tab_TCP_PCB:00000000 memp_tab_TCP_PCB
     /tmp/ccqkcEof.s:884    .bss.memp_tab_TCP_PCB:00000000 $d
     /tmp/ccqkcEof.s:891    .bss.memp_memory_TCP_PCB_base:00000000 $d
     /tmp/ccqkcEof.s:898    .rodata.memp_UDP_PCB:00000000 $d
     /tmp/ccqkcEof.s:917    .bss.memp_memory_UDP_PCB_base:00000000 memp_memory_UDP_PCB_base
     /tmp/ccqkcEof.s:910    .bss.memp_tab_UDP_PCB:00000000 memp_tab_UDP_PCB
     /tmp/ccqkcEof.s:907    .bss.memp_tab_UDP_PCB:00000000 $d
     /tmp/ccqkcEof.s:914    .bss.memp_memory_UDP_PCB_base:00000000 $d

UNDEFINED SYMBOLS
sys_arch_protect
sys_arch_unprotect
ARM GAS  /tmp/ccqkcEof.s 			page 29


printf
