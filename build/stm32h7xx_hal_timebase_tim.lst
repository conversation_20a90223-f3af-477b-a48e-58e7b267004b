ARM GAS  /tmp/cccedySp.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_timebase_tim.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/stm32h7xx_hal_timebase_tim.c"
  19              		.section	.text.HAL_InitTick,"ax",%progbits
  20              		.align	1
  21              		.global	HAL_InitTick
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	HAL_InitTick:
  27              	.LVL0:
  28              	.LFB144:
   1:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* USER CODE BEGIN Header */
   2:Core/Src/stm32h7xx_hal_timebase_tim.c **** /**
   3:Core/Src/stm32h7xx_hal_timebase_tim.c ****   ******************************************************************************
   4:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @file    stm32h7xx_hal_timebase_tim.c
   5:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @brief   HAL time base based on the hardware TIM.
   6:Core/Src/stm32h7xx_hal_timebase_tim.c ****   ******************************************************************************
   7:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @attention
   8:Core/Src/stm32h7xx_hal_timebase_tim.c ****   *
   9:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * Copyright (c) 2025 STMicroelectronics.
  10:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * All rights reserved.
  11:Core/Src/stm32h7xx_hal_timebase_tim.c ****   *
  12:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * This software is licensed under terms that can be found in the LICENSE file
  13:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * in the root directory of this software component.
  14:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  15:Core/Src/stm32h7xx_hal_timebase_tim.c ****   *
  16:Core/Src/stm32h7xx_hal_timebase_tim.c ****   ******************************************************************************
  17:Core/Src/stm32h7xx_hal_timebase_tim.c ****   */
  18:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* USER CODE END Header */
  19:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  20:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Includes ------------------------------------------------------------------*/
  21:Core/Src/stm32h7xx_hal_timebase_tim.c **** #include "stm32h7xx_hal.h"
  22:Core/Src/stm32h7xx_hal_timebase_tim.c **** #include "stm32h7xx_hal_tim.h"
  23:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  24:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Private typedef -----------------------------------------------------------*/
  25:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Private define ------------------------------------------------------------*/
  26:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Private macro -------------------------------------------------------------*/
  27:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Private variables ---------------------------------------------------------*/
  28:Core/Src/stm32h7xx_hal_timebase_tim.c **** TIM_HandleTypeDef        htim1;
  29:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Private function prototypes -----------------------------------------------*/
  30:Core/Src/stm32h7xx_hal_timebase_tim.c **** /* Private functions ---------------------------------------------------------*/
ARM GAS  /tmp/cccedySp.s 			page 2


  31:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  32:Core/Src/stm32h7xx_hal_timebase_tim.c **** /**
  33:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @brief  This function configures the TIM1 as a time base source.
  34:Core/Src/stm32h7xx_hal_timebase_tim.c ****   *         The time source is configured  to have 1ms time base with a dedicated
  35:Core/Src/stm32h7xx_hal_timebase_tim.c ****   *         Tick interrupt priority.
  36:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @note   This function is called  automatically at the beginning of program after
  37:Core/Src/stm32h7xx_hal_timebase_tim.c ****   *         reset by HAL_Init() or at any time when clock is configured, by HAL_RCC_ClockConfig().
  38:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @param  TickPriority: Tick interrupt priority.
  39:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @retval HAL status
  40:Core/Src/stm32h7xx_hal_timebase_tim.c ****   */
  41:Core/Src/stm32h7xx_hal_timebase_tim.c **** HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
  42:Core/Src/stm32h7xx_hal_timebase_tim.c **** {
  29              		.loc 1 42 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 40
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  43:Core/Src/stm32h7xx_hal_timebase_tim.c ****   RCC_ClkInitTypeDef    clkconfig;
  33              		.loc 1 43 3 view .LVU1
  44:Core/Src/stm32h7xx_hal_timebase_tim.c ****   uint32_t              uwTimclock;
  34              		.loc 1 44 3 view .LVU2
  45:Core/Src/stm32h7xx_hal_timebase_tim.c ****   uint32_t              uwPrescalerValue;
  35              		.loc 1 45 3 view .LVU3
  46:Core/Src/stm32h7xx_hal_timebase_tim.c ****   uint32_t              pFLatency;
  36              		.loc 1 46 3 view .LVU4
  47:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  48:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /*Configure the TIM1 IRQ priority */
  49:Core/Src/stm32h7xx_hal_timebase_tim.c ****   if (TickPriority < (1UL << __NVIC_PRIO_BITS))
  37              		.loc 1 49 3 view .LVU5
  38              		.loc 1 49 6 is_stmt 0 view .LVU6
  39 0000 0F28     		cmp	r0, #15
  40 0002 01D9     		bls	.L9
  50:Core/Src/stm32h7xx_hal_timebase_tim.c ****    {
  51:Core/Src/stm32h7xx_hal_timebase_tim.c ****      HAL_NVIC_SetPriority(TIM1_UP_IRQn, TickPriority ,0);
  52:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  53:Core/Src/stm32h7xx_hal_timebase_tim.c ****      /* Enable the TIM1 global Interrupt */
  54:Core/Src/stm32h7xx_hal_timebase_tim.c ****      HAL_NVIC_EnableIRQ(TIM1_UP_IRQn);
  55:Core/Src/stm32h7xx_hal_timebase_tim.c ****      uwTickPrio = TickPriority;
  56:Core/Src/stm32h7xx_hal_timebase_tim.c ****     }
  57:Core/Src/stm32h7xx_hal_timebase_tim.c ****   else
  58:Core/Src/stm32h7xx_hal_timebase_tim.c ****   {
  59:Core/Src/stm32h7xx_hal_timebase_tim.c ****     return HAL_ERROR;
  41              		.loc 1 59 12 view .LVU7
  42 0004 0120     		movs	r0, #1
  43              	.LVL1:
  60:Core/Src/stm32h7xx_hal_timebase_tim.c ****   }
  61:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  62:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Enable TIM1 clock */
  63:Core/Src/stm32h7xx_hal_timebase_tim.c ****   __HAL_RCC_TIM1_CLK_ENABLE();
  64:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  65:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Get clock configuration */
  66:Core/Src/stm32h7xx_hal_timebase_tim.c ****   HAL_RCC_GetClockConfig(&clkconfig, &pFLatency);
  67:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  68:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Compute TIM1 clock */
  69:Core/Src/stm32h7xx_hal_timebase_tim.c ****       uwTimclock = 2*HAL_RCC_GetPCLK2Freq();
  70:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  71:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Compute the prescaler value to have TIM1 counter clock equal to 1MHz */
  72:Core/Src/stm32h7xx_hal_timebase_tim.c ****   uwPrescalerValue = (uint32_t) ((uwTimclock / 1000000U) - 1U);
ARM GAS  /tmp/cccedySp.s 			page 3


  73:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  74:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Initialize TIM1 */
  75:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Instance = TIM1;
  76:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  77:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Initialize TIMx peripheral as follow:
  78:Core/Src/stm32h7xx_hal_timebase_tim.c ****    * Period = [(TIM1CLK/1000) - 1]. to have a (1/1000) s time base.
  79:Core/Src/stm32h7xx_hal_timebase_tim.c ****    * Prescaler = (uwTimclock/1000000 - 1) to have a 1MHz counter clock.
  80:Core/Src/stm32h7xx_hal_timebase_tim.c ****    * ClockDivision = 0
  81:Core/Src/stm32h7xx_hal_timebase_tim.c ****    * Counter direction = Up
  82:Core/Src/stm32h7xx_hal_timebase_tim.c ****    */
  83:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.Period = (1000000U / 1000U) - 1U;
  84:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.Prescaler = uwPrescalerValue;
  85:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.ClockDivision = 0;
  86:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
  87:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  88:Core/Src/stm32h7xx_hal_timebase_tim.c ****   if(HAL_TIM_Base_Init(&htim1) == HAL_OK)
  89:Core/Src/stm32h7xx_hal_timebase_tim.c ****   {
  90:Core/Src/stm32h7xx_hal_timebase_tim.c ****     /* Start the TIM time Base generation in interrupt mode */
  91:Core/Src/stm32h7xx_hal_timebase_tim.c ****     return HAL_TIM_Base_Start_IT(&htim1);
  92:Core/Src/stm32h7xx_hal_timebase_tim.c ****   }
  93:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  94:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Return function status */
  95:Core/Src/stm32h7xx_hal_timebase_tim.c ****   return HAL_ERROR;
  96:Core/Src/stm32h7xx_hal_timebase_tim.c **** }
  44              		.loc 1 96 1 view .LVU8
  45 0006 7047     		bx	lr
  46              	.LVL2:
  47              	.L9:
  42:Core/Src/stm32h7xx_hal_timebase_tim.c ****   RCC_ClkInitTypeDef    clkconfig;
  48              		.loc 1 42 1 view .LVU9
  49 0008 10B5     		push	{r4, lr}
  50              	.LCFI0:
  51              		.cfi_def_cfa_offset 8
  52              		.cfi_offset 4, -8
  53              		.cfi_offset 14, -4
  54 000a 8AB0     		sub	sp, sp, #40
  55              	.LCFI1:
  56              		.cfi_def_cfa_offset 48
  57 000c 0446     		mov	r4, r0
  51:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  58              		.loc 1 51 6 is_stmt 1 view .LVU10
  59 000e 0022     		movs	r2, #0
  60 0010 0146     		mov	r1, r0
  61 0012 1920     		movs	r0, #25
  62              	.LVL3:
  51:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  63              		.loc 1 51 6 is_stmt 0 view .LVU11
  64 0014 FFF7FEFF 		bl	HAL_NVIC_SetPriority
  65              	.LVL4:
  54:Core/Src/stm32h7xx_hal_timebase_tim.c ****      uwTickPrio = TickPriority;
  66              		.loc 1 54 6 is_stmt 1 view .LVU12
  67 0018 1920     		movs	r0, #25
  68 001a FFF7FEFF 		bl	HAL_NVIC_EnableIRQ
  69              	.LVL5:
  55:Core/Src/stm32h7xx_hal_timebase_tim.c ****     }
  70              		.loc 1 55 6 view .LVU13
  55:Core/Src/stm32h7xx_hal_timebase_tim.c ****     }
ARM GAS  /tmp/cccedySp.s 			page 4


  71              		.loc 1 55 17 is_stmt 0 view .LVU14
  72 001e 174B     		ldr	r3, .L11
  73 0020 1C60     		str	r4, [r3]
  63:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  74              		.loc 1 63 3 is_stmt 1 view .LVU15
  75              	.LBB2:
  63:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  76              		.loc 1 63 3 view .LVU16
  63:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  77              		.loc 1 63 3 view .LVU17
  78 0022 174B     		ldr	r3, .L11+4
  79 0024 D3F8F020 		ldr	r2, [r3, #240]
  80 0028 42F00102 		orr	r2, r2, #1
  81 002c C3F8F020 		str	r2, [r3, #240]
  63:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  82              		.loc 1 63 3 view .LVU18
  83 0030 D3F8F030 		ldr	r3, [r3, #240]
  84 0034 03F00103 		and	r3, r3, #1
  85 0038 0093     		str	r3, [sp]
  63:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  86              		.loc 1 63 3 view .LVU19
  87 003a 009B     		ldr	r3, [sp]
  88              	.LBE2:
  63:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  89              		.loc 1 63 3 view .LVU20
  66:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  90              		.loc 1 66 3 view .LVU21
  91 003c 01A9     		add	r1, sp, #4
  92 003e 02A8     		add	r0, sp, #8
  93 0040 FFF7FEFF 		bl	HAL_RCC_GetClockConfig
  94              	.LVL6:
  69:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  95              		.loc 1 69 7 view .LVU22
  69:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  96              		.loc 1 69 22 is_stmt 0 view .LVU23
  97 0044 FFF7FEFF 		bl	HAL_RCC_GetPCLK2Freq
  98              	.LVL7:
  69:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  99              		.loc 1 69 18 discriminator 1 view .LVU24
 100 0048 4300     		lsls	r3, r0, #1
 101              	.LVL8:
  72:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 102              		.loc 1 72 3 is_stmt 1 view .LVU25
  72:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 103              		.loc 1 72 46 is_stmt 0 view .LVU26
 104 004a 0E4A     		ldr	r2, .L11+8
 105 004c A2FB0323 		umull	r2, r3, r2, r3
 106              	.LVL9:
  72:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 107              		.loc 1 72 46 view .LVU27
 108 0050 9B0C     		lsrs	r3, r3, #18
  72:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 109              		.loc 1 72 20 view .LVU28
 110 0052 013B     		subs	r3, r3, #1
 111              	.LVL10:
  75:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 112              		.loc 1 75 3 is_stmt 1 view .LVU29
ARM GAS  /tmp/cccedySp.s 			page 5


  75:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 113              		.loc 1 75 18 is_stmt 0 view .LVU30
 114 0054 0C48     		ldr	r0, .L11+12
 115              	.LVL11:
  75:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 116              		.loc 1 75 18 view .LVU31
 117 0056 0D4A     		ldr	r2, .L11+16
 118 0058 0260     		str	r2, [r0]
  83:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.Prescaler = uwPrescalerValue;
 119              		.loc 1 83 3 is_stmt 1 view .LVU32
  83:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.Prescaler = uwPrescalerValue;
 120              		.loc 1 83 21 is_stmt 0 view .LVU33
 121 005a 40F2E732 		movw	r2, #999
 122 005e C260     		str	r2, [r0, #12]
  84:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.ClockDivision = 0;
 123              		.loc 1 84 3 is_stmt 1 view .LVU34
  84:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.ClockDivision = 0;
 124              		.loc 1 84 24 is_stmt 0 view .LVU35
 125 0060 4360     		str	r3, [r0, #4]
  85:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
 126              		.loc 1 85 3 is_stmt 1 view .LVU36
  85:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
 127              		.loc 1 85 28 is_stmt 0 view .LVU37
 128 0062 0023     		movs	r3, #0
 129              	.LVL12:
  85:Core/Src/stm32h7xx_hal_timebase_tim.c ****   htim1.Init.CounterMode = TIM_COUNTERMODE_UP;
 130              		.loc 1 85 28 view .LVU38
 131 0064 0361     		str	r3, [r0, #16]
  86:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 132              		.loc 1 86 3 is_stmt 1 view .LVU39
  86:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 133              		.loc 1 86 26 is_stmt 0 view .LVU40
 134 0066 8360     		str	r3, [r0, #8]
  88:Core/Src/stm32h7xx_hal_timebase_tim.c ****   {
 135              		.loc 1 88 3 is_stmt 1 view .LVU41
  88:Core/Src/stm32h7xx_hal_timebase_tim.c ****   {
 136              		.loc 1 88 6 is_stmt 0 view .LVU42
 137 0068 FFF7FEFF 		bl	HAL_TIM_Base_Init
 138              	.LVL13:
  88:Core/Src/stm32h7xx_hal_timebase_tim.c ****   {
 139              		.loc 1 88 5 discriminator 1 view .LVU43
 140 006c 10B1     		cbz	r0, .L10
  95:Core/Src/stm32h7xx_hal_timebase_tim.c **** }
 141              		.loc 1 95 10 view .LVU44
 142 006e 0120     		movs	r0, #1
 143              	.L2:
 144              		.loc 1 96 1 view .LVU45
 145 0070 0AB0     		add	sp, sp, #40
 146              	.LCFI2:
 147              		.cfi_remember_state
 148              		.cfi_def_cfa_offset 8
 149              		@ sp needed
 150 0072 10BD     		pop	{r4, pc}
 151              	.LVL14:
 152              	.L10:
 153              	.LCFI3:
 154              		.cfi_restore_state
ARM GAS  /tmp/cccedySp.s 			page 6


  91:Core/Src/stm32h7xx_hal_timebase_tim.c ****   }
 155              		.loc 1 91 5 is_stmt 1 view .LVU46
  91:Core/Src/stm32h7xx_hal_timebase_tim.c ****   }
 156              		.loc 1 91 12 is_stmt 0 view .LVU47
 157 0074 0448     		ldr	r0, .L11+12
 158 0076 FFF7FEFF 		bl	HAL_TIM_Base_Start_IT
 159              	.LVL15:
 160 007a F9E7     		b	.L2
 161              	.L12:
 162              		.align	2
 163              	.L11:
 164 007c 00000000 		.word	uwTickPrio
 165 0080 00440258 		.word	1476543488
 166 0084 83DE1B43 		.word	1125899907
 167 0088 00000000 		.word	htim1
 168 008c 00000140 		.word	1073807360
 169              		.cfi_endproc
 170              	.LFE144:
 172              		.section	.text.HAL_SuspendTick,"ax",%progbits
 173              		.align	1
 174              		.global	HAL_SuspendTick
 175              		.syntax unified
 176              		.thumb
 177              		.thumb_func
 179              	HAL_SuspendTick:
 180              	.LFB145:
  97:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
  98:Core/Src/stm32h7xx_hal_timebase_tim.c **** /**
  99:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @brief  Suspend Tick increment.
 100:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @note   Disable the tick increment by disabling TIM1 update interrupt.
 101:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @param  None
 102:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @retval None
 103:Core/Src/stm32h7xx_hal_timebase_tim.c ****   */
 104:Core/Src/stm32h7xx_hal_timebase_tim.c **** void HAL_SuspendTick(void)
 105:Core/Src/stm32h7xx_hal_timebase_tim.c **** {
 181              		.loc 1 105 1 is_stmt 1 view -0
 182              		.cfi_startproc
 183              		@ args = 0, pretend = 0, frame = 0
 184              		@ frame_needed = 0, uses_anonymous_args = 0
 185              		@ link register save eliminated.
 106:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Disable TIM1 update Interrupt */
 107:Core/Src/stm32h7xx_hal_timebase_tim.c ****   __HAL_TIM_DISABLE_IT(&htim1, TIM_IT_UPDATE);
 186              		.loc 1 107 3 view .LVU49
 187 0000 034B     		ldr	r3, .L14
 188 0002 1A68     		ldr	r2, [r3]
 189 0004 D368     		ldr	r3, [r2, #12]
 190 0006 23F00103 		bic	r3, r3, #1
 191 000a D360     		str	r3, [r2, #12]
 108:Core/Src/stm32h7xx_hal_timebase_tim.c **** }
 192              		.loc 1 108 1 is_stmt 0 view .LVU50
 193 000c 7047     		bx	lr
 194              	.L15:
 195 000e 00BF     		.align	2
 196              	.L14:
 197 0010 00000000 		.word	htim1
 198              		.cfi_endproc
 199              	.LFE145:
ARM GAS  /tmp/cccedySp.s 			page 7


 201              		.section	.text.HAL_ResumeTick,"ax",%progbits
 202              		.align	1
 203              		.global	HAL_ResumeTick
 204              		.syntax unified
 205              		.thumb
 206              		.thumb_func
 208              	HAL_ResumeTick:
 209              	.LFB146:
 109:Core/Src/stm32h7xx_hal_timebase_tim.c **** 
 110:Core/Src/stm32h7xx_hal_timebase_tim.c **** /**
 111:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @brief  Resume Tick increment.
 112:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @note   Enable the tick increment by Enabling TIM1 update interrupt.
 113:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @param  None
 114:Core/Src/stm32h7xx_hal_timebase_tim.c ****   * @retval None
 115:Core/Src/stm32h7xx_hal_timebase_tim.c ****   */
 116:Core/Src/stm32h7xx_hal_timebase_tim.c **** void HAL_ResumeTick(void)
 117:Core/Src/stm32h7xx_hal_timebase_tim.c **** {
 210              		.loc 1 117 1 is_stmt 1 view -0
 211              		.cfi_startproc
 212              		@ args = 0, pretend = 0, frame = 0
 213              		@ frame_needed = 0, uses_anonymous_args = 0
 214              		@ link register save eliminated.
 118:Core/Src/stm32h7xx_hal_timebase_tim.c ****   /* Enable TIM1 Update interrupt */
 119:Core/Src/stm32h7xx_hal_timebase_tim.c ****   __HAL_TIM_ENABLE_IT(&htim1, TIM_IT_UPDATE);
 215              		.loc 1 119 3 view .LVU52
 216 0000 034B     		ldr	r3, .L17
 217 0002 1A68     		ldr	r2, [r3]
 218 0004 D368     		ldr	r3, [r2, #12]
 219 0006 43F00103 		orr	r3, r3, #1
 220 000a D360     		str	r3, [r2, #12]
 120:Core/Src/stm32h7xx_hal_timebase_tim.c **** }
 221              		.loc 1 120 1 is_stmt 0 view .LVU53
 222 000c 7047     		bx	lr
 223              	.L18:
 224 000e 00BF     		.align	2
 225              	.L17:
 226 0010 00000000 		.word	htim1
 227              		.cfi_endproc
 228              	.LFE146:
 230              		.global	htim1
 231              		.section	.bss.htim1,"aw",%nobits
 232              		.align	2
 235              	htim1:
 236 0000 00000000 		.space	76
 236      00000000 
 236      00000000 
 236      00000000 
 236      00000000 
 237              		.text
 238              	.Letext0:
 239              		.file 2 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 240              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 241              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 242              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 243              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_rcc.h"
 244              		.file 7 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_dma.h"
 245              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_tim.h"
ARM GAS  /tmp/cccedySp.s 			page 8


 246              		.file 9 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
 247              		.file 10 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_cortex.h"
ARM GAS  /tmp/cccedySp.s 			page 9


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_timebase_tim.c
     /tmp/cccedySp.s:20     .text.HAL_InitTick:00000000 $t
     /tmp/cccedySp.s:26     .text.HAL_InitTick:00000000 HAL_InitTick
     /tmp/cccedySp.s:164    .text.HAL_InitTick:0000007c $d
     /tmp/cccedySp.s:235    .bss.htim1:00000000 htim1
     /tmp/cccedySp.s:173    .text.HAL_SuspendTick:00000000 $t
     /tmp/cccedySp.s:179    .text.HAL_SuspendTick:00000000 HAL_SuspendTick
     /tmp/cccedySp.s:197    .text.HAL_SuspendTick:00000010 $d
     /tmp/cccedySp.s:202    .text.HAL_ResumeTick:00000000 $t
     /tmp/cccedySp.s:208    .text.HAL_ResumeTick:00000000 HAL_ResumeTick
     /tmp/cccedySp.s:226    .text.HAL_ResumeTick:00000010 $d
     /tmp/cccedySp.s:232    .bss.htim1:00000000 $d

UNDEFINED SYMBOLS
HAL_NVIC_SetPriority
HAL_NVIC_EnableIRQ
HAL_RCC_GetClockConfig
HAL_RCC_GetPCLK2Freq
HAL_TIM_Base_Init
HAL_TIM_Base_Start_IT
uwTickPrio
