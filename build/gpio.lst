ARM GAS  /tmp/ccmp7bw7.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"gpio.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Core/Src/gpio.c"
  19              		.section	.text.MX_GPIO_Init,"ax",%progbits
  20              		.align	1
  21              		.global	MX_GPIO_Init
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	MX_GPIO_Init:
  27              	.LFB144:
   1:Core/Src/gpio.c **** /* USER CODE BEGIN Header */
   2:Core/Src/gpio.c **** /**
   3:Core/Src/gpio.c ****   ******************************************************************************
   4:Core/Src/gpio.c ****   * @file    gpio.c
   5:Core/Src/gpio.c ****   * @brief   This file provides code for the configuration
   6:Core/Src/gpio.c ****   *          of all used GPIO pins.
   7:Core/Src/gpio.c ****   ******************************************************************************
   8:Core/Src/gpio.c ****   * @attention
   9:Core/Src/gpio.c ****   *
  10:Core/Src/gpio.c ****   * Copyright (c) 2025 STMicroelectronics.
  11:Core/Src/gpio.c ****   * All rights reserved.
  12:Core/Src/gpio.c ****   *
  13:Core/Src/gpio.c ****   * This software is licensed under terms that can be found in the LICENSE file
  14:Core/Src/gpio.c ****   * in the root directory of this software component.
  15:Core/Src/gpio.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  16:Core/Src/gpio.c ****   *
  17:Core/Src/gpio.c ****   ******************************************************************************
  18:Core/Src/gpio.c ****   */
  19:Core/Src/gpio.c **** /* USER CODE END Header */
  20:Core/Src/gpio.c **** 
  21:Core/Src/gpio.c **** /* Includes ------------------------------------------------------------------*/
  22:Core/Src/gpio.c **** #include "gpio.h"
  23:Core/Src/gpio.c **** 
  24:Core/Src/gpio.c **** /* USER CODE BEGIN 0 */
  25:Core/Src/gpio.c **** 
  26:Core/Src/gpio.c **** /* USER CODE END 0 */
  27:Core/Src/gpio.c **** 
  28:Core/Src/gpio.c **** /*----------------------------------------------------------------------------*/
  29:Core/Src/gpio.c **** /* Configure GPIO                                                             */
  30:Core/Src/gpio.c **** /*----------------------------------------------------------------------------*/
  31:Core/Src/gpio.c **** /* USER CODE BEGIN 1 */
ARM GAS  /tmp/ccmp7bw7.s 			page 2


  32:Core/Src/gpio.c **** 
  33:Core/Src/gpio.c **** /* USER CODE END 1 */
  34:Core/Src/gpio.c **** 
  35:Core/Src/gpio.c **** /** Configure pins as
  36:Core/Src/gpio.c ****         * Analog
  37:Core/Src/gpio.c ****         * Input
  38:Core/Src/gpio.c ****         * Output
  39:Core/Src/gpio.c ****         * EVENT_OUT
  40:Core/Src/gpio.c ****         * EXTI
  41:Core/Src/gpio.c ****      PD8   ------> USART3_TX
  42:Core/Src/gpio.c ****      PD9   ------> USART3_RX
  43:Core/Src/gpio.c **** */
  44:Core/Src/gpio.c **** void MX_GPIO_Init(void)
  45:Core/Src/gpio.c **** {
  28              		.loc 1 45 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 48
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32 0000 F0B5     		push	{r4, r5, r6, r7, lr}
  33              	.LCFI0:
  34              		.cfi_def_cfa_offset 20
  35              		.cfi_offset 4, -20
  36              		.cfi_offset 5, -16
  37              		.cfi_offset 6, -12
  38              		.cfi_offset 7, -8
  39              		.cfi_offset 14, -4
  40 0002 8DB0     		sub	sp, sp, #52
  41              	.LCFI1:
  42              		.cfi_def_cfa_offset 72
  46:Core/Src/gpio.c **** 
  47:Core/Src/gpio.c ****   GPIO_InitTypeDef GPIO_InitStruct = {0};
  43              		.loc 1 47 3 view .LVU1
  44              		.loc 1 47 20 is_stmt 0 view .LVU2
  45 0004 0024     		movs	r4, #0
  46 0006 0794     		str	r4, [sp, #28]
  47 0008 0894     		str	r4, [sp, #32]
  48 000a 0994     		str	r4, [sp, #36]
  49 000c 0A94     		str	r4, [sp, #40]
  50 000e 0B94     		str	r4, [sp, #44]
  48:Core/Src/gpio.c **** 
  49:Core/Src/gpio.c ****   /* GPIO Ports Clock Enable */
  50:Core/Src/gpio.c ****   __HAL_RCC_GPIOC_CLK_ENABLE();
  51              		.loc 1 50 3 is_stmt 1 view .LVU3
  52              	.LBB2:
  53              		.loc 1 50 3 view .LVU4
  54              		.loc 1 50 3 view .LVU5
  55 0010 404B     		ldr	r3, .L3
  56 0012 D3F8E020 		ldr	r2, [r3, #224]
  57 0016 42F00402 		orr	r2, r2, #4
  58 001a C3F8E020 		str	r2, [r3, #224]
  59              		.loc 1 50 3 view .LVU6
  60 001e D3F8E020 		ldr	r2, [r3, #224]
  61 0022 02F00402 		and	r2, r2, #4
  62 0026 0192     		str	r2, [sp, #4]
  63              		.loc 1 50 3 view .LVU7
  64 0028 019A     		ldr	r2, [sp, #4]
  65              	.LBE2:
ARM GAS  /tmp/ccmp7bw7.s 			page 3


  66              		.loc 1 50 3 view .LVU8
  51:Core/Src/gpio.c ****   __HAL_RCC_GPIOH_CLK_ENABLE();
  67              		.loc 1 51 3 view .LVU9
  68              	.LBB3:
  69              		.loc 1 51 3 view .LVU10
  70              		.loc 1 51 3 view .LVU11
  71 002a D3F8E020 		ldr	r2, [r3, #224]
  72 002e 42F08002 		orr	r2, r2, #128
  73 0032 C3F8E020 		str	r2, [r3, #224]
  74              		.loc 1 51 3 view .LVU12
  75 0036 D3F8E020 		ldr	r2, [r3, #224]
  76 003a 02F08002 		and	r2, r2, #128
  77 003e 0292     		str	r2, [sp, #8]
  78              		.loc 1 51 3 view .LVU13
  79 0040 029A     		ldr	r2, [sp, #8]
  80              	.LBE3:
  81              		.loc 1 51 3 view .LVU14
  52:Core/Src/gpio.c ****   __HAL_RCC_GPIOA_CLK_ENABLE();
  82              		.loc 1 52 3 view .LVU15
  83              	.LBB4:
  84              		.loc 1 52 3 view .LVU16
  85              		.loc 1 52 3 view .LVU17
  86 0042 D3F8E020 		ldr	r2, [r3, #224]
  87 0046 42F00102 		orr	r2, r2, #1
  88 004a C3F8E020 		str	r2, [r3, #224]
  89              		.loc 1 52 3 view .LVU18
  90 004e D3F8E020 		ldr	r2, [r3, #224]
  91 0052 02F00102 		and	r2, r2, #1
  92 0056 0392     		str	r2, [sp, #12]
  93              		.loc 1 52 3 view .LVU19
  94 0058 039A     		ldr	r2, [sp, #12]
  95              	.LBE4:
  96              		.loc 1 52 3 view .LVU20
  53:Core/Src/gpio.c ****   __HAL_RCC_GPIOB_CLK_ENABLE();
  97              		.loc 1 53 3 view .LVU21
  98              	.LBB5:
  99              		.loc 1 53 3 view .LVU22
 100              		.loc 1 53 3 view .LVU23
 101 005a D3F8E020 		ldr	r2, [r3, #224]
 102 005e 42F00202 		orr	r2, r2, #2
 103 0062 C3F8E020 		str	r2, [r3, #224]
 104              		.loc 1 53 3 view .LVU24
 105 0066 D3F8E020 		ldr	r2, [r3, #224]
 106 006a 02F00202 		and	r2, r2, #2
 107 006e 0492     		str	r2, [sp, #16]
 108              		.loc 1 53 3 view .LVU25
 109 0070 049A     		ldr	r2, [sp, #16]
 110              	.LBE5:
 111              		.loc 1 53 3 view .LVU26
  54:Core/Src/gpio.c ****   __HAL_RCC_GPIOD_CLK_ENABLE();
 112              		.loc 1 54 3 view .LVU27
 113              	.LBB6:
 114              		.loc 1 54 3 view .LVU28
 115              		.loc 1 54 3 view .LVU29
 116 0072 D3F8E020 		ldr	r2, [r3, #224]
 117 0076 42F00802 		orr	r2, r2, #8
 118 007a C3F8E020 		str	r2, [r3, #224]
ARM GAS  /tmp/ccmp7bw7.s 			page 4


 119              		.loc 1 54 3 view .LVU30
 120 007e D3F8E020 		ldr	r2, [r3, #224]
 121 0082 02F00802 		and	r2, r2, #8
 122 0086 0592     		str	r2, [sp, #20]
 123              		.loc 1 54 3 view .LVU31
 124 0088 059A     		ldr	r2, [sp, #20]
 125              	.LBE6:
 126              		.loc 1 54 3 view .LVU32
  55:Core/Src/gpio.c ****   __HAL_RCC_GPIOE_CLK_ENABLE();
 127              		.loc 1 55 3 view .LVU33
 128              	.LBB7:
 129              		.loc 1 55 3 view .LVU34
 130              		.loc 1 55 3 view .LVU35
 131 008a D3F8E020 		ldr	r2, [r3, #224]
 132 008e 42F01002 		orr	r2, r2, #16
 133 0092 C3F8E020 		str	r2, [r3, #224]
 134              		.loc 1 55 3 view .LVU36
 135 0096 D3F8E030 		ldr	r3, [r3, #224]
 136 009a 03F01003 		and	r3, r3, #16
 137 009e 0693     		str	r3, [sp, #24]
 138              		.loc 1 55 3 view .LVU37
 139 00a0 069B     		ldr	r3, [sp, #24]
 140              	.LBE7:
 141              		.loc 1 55 3 view .LVU38
  56:Core/Src/gpio.c **** 
  57:Core/Src/gpio.c ****   /*Configure GPIO pin Output Level */
  58:Core/Src/gpio.c ****   HAL_GPIO_WritePin(LED_RED_GPIO_Port, LED_RED_Pin, GPIO_PIN_RESET);
 142              		.loc 1 58 3 view .LVU39
 143 00a2 1D4F     		ldr	r7, .L3+4
 144 00a4 2246     		mov	r2, r4
 145 00a6 4FF48041 		mov	r1, #16384
 146 00aa 3846     		mov	r0, r7
 147 00ac FFF7FEFF 		bl	HAL_GPIO_WritePin
 148              	.LVL0:
  59:Core/Src/gpio.c **** 
  60:Core/Src/gpio.c ****   /*Configure GPIO pin Output Level */
  61:Core/Src/gpio.c ****   HAL_GPIO_WritePin(LED_YELLOW_GPIO_Port, LED_YELLOW_Pin, GPIO_PIN_RESET);
 149              		.loc 1 61 3 view .LVU40
 150 00b0 1A4D     		ldr	r5, .L3+8
 151 00b2 2246     		mov	r2, r4
 152 00b4 0221     		movs	r1, #2
 153 00b6 2846     		mov	r0, r5
 154 00b8 FFF7FEFF 		bl	HAL_GPIO_WritePin
 155              	.LVL1:
  62:Core/Src/gpio.c **** 
  63:Core/Src/gpio.c ****   /*Configure GPIO pin : B1_Pin */
  64:Core/Src/gpio.c ****   GPIO_InitStruct.Pin = B1_Pin;
 156              		.loc 1 64 3 view .LVU41
 157              		.loc 1 64 23 is_stmt 0 view .LVU42
 158 00bc 4FF40053 		mov	r3, #8192
 159 00c0 0793     		str	r3, [sp, #28]
  65:Core/Src/gpio.c ****   GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
 160              		.loc 1 65 3 is_stmt 1 view .LVU43
 161              		.loc 1 65 24 is_stmt 0 view .LVU44
 162 00c2 0894     		str	r4, [sp, #32]
  66:Core/Src/gpio.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 163              		.loc 1 66 3 is_stmt 1 view .LVU45
ARM GAS  /tmp/ccmp7bw7.s 			page 5


 164              		.loc 1 66 24 is_stmt 0 view .LVU46
 165 00c4 0994     		str	r4, [sp, #36]
  67:Core/Src/gpio.c ****   HAL_GPIO_Init(B1_GPIO_Port, &GPIO_InitStruct);
 166              		.loc 1 67 3 is_stmt 1 view .LVU47
 167 00c6 07A9     		add	r1, sp, #28
 168 00c8 1548     		ldr	r0, .L3+12
 169 00ca FFF7FEFF 		bl	HAL_GPIO_Init
 170              	.LVL2:
  68:Core/Src/gpio.c **** 
  69:Core/Src/gpio.c ****   /*Configure GPIO pin : LED_RED_Pin */
  70:Core/Src/gpio.c ****   GPIO_InitStruct.Pin = LED_RED_Pin;
 171              		.loc 1 70 3 view .LVU48
 172              		.loc 1 70 23 is_stmt 0 view .LVU49
 173 00ce 4FF48043 		mov	r3, #16384
 174 00d2 0793     		str	r3, [sp, #28]
  71:Core/Src/gpio.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 175              		.loc 1 71 3 is_stmt 1 view .LVU50
 176              		.loc 1 71 24 is_stmt 0 view .LVU51
 177 00d4 0126     		movs	r6, #1
 178 00d6 0896     		str	r6, [sp, #32]
  72:Core/Src/gpio.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 179              		.loc 1 72 3 is_stmt 1 view .LVU52
 180              		.loc 1 72 24 is_stmt 0 view .LVU53
 181 00d8 0994     		str	r4, [sp, #36]
  73:Core/Src/gpio.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 182              		.loc 1 73 3 is_stmt 1 view .LVU54
 183              		.loc 1 73 25 is_stmt 0 view .LVU55
 184 00da 0A94     		str	r4, [sp, #40]
  74:Core/Src/gpio.c ****   HAL_GPIO_Init(LED_RED_GPIO_Port, &GPIO_InitStruct);
 185              		.loc 1 74 3 is_stmt 1 view .LVU56
 186 00dc 07A9     		add	r1, sp, #28
 187 00de 3846     		mov	r0, r7
 188 00e0 FFF7FEFF 		bl	HAL_GPIO_Init
 189              	.LVL3:
  75:Core/Src/gpio.c **** 
  76:Core/Src/gpio.c ****   /*Configure GPIO pins : STLK_VCP_RX_Pin STLK_VCP_TX_Pin */
  77:Core/Src/gpio.c ****   GPIO_InitStruct.Pin = STLK_VCP_RX_Pin|STLK_VCP_TX_Pin;
 190              		.loc 1 77 3 view .LVU57
 191              		.loc 1 77 23 is_stmt 0 view .LVU58
 192 00e4 4FF44073 		mov	r3, #768
 193 00e8 0793     		str	r3, [sp, #28]
  78:Core/Src/gpio.c ****   GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
 194              		.loc 1 78 3 is_stmt 1 view .LVU59
 195              		.loc 1 78 24 is_stmt 0 view .LVU60
 196 00ea 0227     		movs	r7, #2
 197 00ec 0897     		str	r7, [sp, #32]
  79:Core/Src/gpio.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 198              		.loc 1 79 3 is_stmt 1 view .LVU61
 199              		.loc 1 79 24 is_stmt 0 view .LVU62
 200 00ee 0994     		str	r4, [sp, #36]
  80:Core/Src/gpio.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 201              		.loc 1 80 3 is_stmt 1 view .LVU63
 202              		.loc 1 80 25 is_stmt 0 view .LVU64
 203 00f0 0A94     		str	r4, [sp, #40]
  81:Core/Src/gpio.c ****   GPIO_InitStruct.Alternate = GPIO_AF7_USART3;
 204              		.loc 1 81 3 is_stmt 1 view .LVU65
 205              		.loc 1 81 29 is_stmt 0 view .LVU66
ARM GAS  /tmp/ccmp7bw7.s 			page 6


 206 00f2 0723     		movs	r3, #7
 207 00f4 0B93     		str	r3, [sp, #44]
  82:Core/Src/gpio.c ****   HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 208              		.loc 1 82 3 is_stmt 1 view .LVU67
 209 00f6 07A9     		add	r1, sp, #28
 210 00f8 0A48     		ldr	r0, .L3+16
 211 00fa FFF7FEFF 		bl	HAL_GPIO_Init
 212              	.LVL4:
  83:Core/Src/gpio.c **** 
  84:Core/Src/gpio.c ****   /*Configure GPIO pin : LED_YELLOW_Pin */
  85:Core/Src/gpio.c ****   GPIO_InitStruct.Pin = LED_YELLOW_Pin;
 213              		.loc 1 85 3 view .LVU68
 214              		.loc 1 85 23 is_stmt 0 view .LVU69
 215 00fe 0797     		str	r7, [sp, #28]
  86:Core/Src/gpio.c ****   GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 216              		.loc 1 86 3 is_stmt 1 view .LVU70
 217              		.loc 1 86 24 is_stmt 0 view .LVU71
 218 0100 0896     		str	r6, [sp, #32]
  87:Core/Src/gpio.c ****   GPIO_InitStruct.Pull = GPIO_NOPULL;
 219              		.loc 1 87 3 is_stmt 1 view .LVU72
 220              		.loc 1 87 24 is_stmt 0 view .LVU73
 221 0102 0994     		str	r4, [sp, #36]
  88:Core/Src/gpio.c ****   GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 222              		.loc 1 88 3 is_stmt 1 view .LVU74
 223              		.loc 1 88 25 is_stmt 0 view .LVU75
 224 0104 0A94     		str	r4, [sp, #40]
  89:Core/Src/gpio.c ****   HAL_GPIO_Init(LED_YELLOW_GPIO_Port, &GPIO_InitStruct);
 225              		.loc 1 89 3 is_stmt 1 view .LVU76
 226 0106 07A9     		add	r1, sp, #28
 227 0108 2846     		mov	r0, r5
 228 010a FFF7FEFF 		bl	HAL_GPIO_Init
 229              	.LVL5:
  90:Core/Src/gpio.c **** 
  91:Core/Src/gpio.c **** }
 230              		.loc 1 91 1 is_stmt 0 view .LVU77
 231 010e 0DB0     		add	sp, sp, #52
 232              	.LCFI2:
 233              		.cfi_def_cfa_offset 20
 234              		@ sp needed
 235 0110 F0BD     		pop	{r4, r5, r6, r7, pc}
 236              	.L4:
 237 0112 00BF     		.align	2
 238              	.L3:
 239 0114 00440258 		.word	1476543488
 240 0118 00040258 		.word	1476527104
 241 011c 00100258 		.word	1476530176
 242 0120 00080258 		.word	1476528128
 243 0124 000C0258 		.word	1476529152
 244              		.cfi_endproc
 245              	.LFE144:
 247              		.text
 248              	.Letext0:
 249              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 250              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 251              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 252              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_gpio.h"
ARM GAS  /tmp/ccmp7bw7.s 			page 7


DEFINED SYMBOLS
                            *ABS*:00000000 gpio.c
     /tmp/ccmp7bw7.s:20     .text.MX_GPIO_Init:00000000 $t
     /tmp/ccmp7bw7.s:26     .text.MX_GPIO_Init:00000000 MX_GPIO_Init
     /tmp/ccmp7bw7.s:239    .text.MX_GPIO_Init:00000114 $d

UNDEFINED SYMBOLS
HAL_GPIO_WritePin
HAL_GPIO_Init
