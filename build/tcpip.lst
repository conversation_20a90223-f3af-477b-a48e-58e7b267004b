ARM GAS  /tmp/ccRmksMf.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"tcpip.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/api/tcpip.c"
  19              		.section	.text.tcpip_timeouts_mbox_fetch,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	tcpip_timeouts_mbox_fetch:
  26              	.LVL0:
  27              	.LFB174:
   1:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
   2:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Sequential API Main thread module
   4:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
   5:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
   6:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
   7:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /*
   8:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
   9:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * All rights reserved.
  10:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  11:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Redistribution and use in source and binary forms, with or without modification,
  12:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * are permitted provided that the following conditions are met:
  13:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  14:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  15:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *    this list of conditions and the following disclaimer.
  16:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *    this list of conditions and the following disclaimer in the documentation
  18:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *    and/or other materials provided with the distribution.
  19:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * 3. The name of the author may not be used to endorse or promote products
  20:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *    derived from this software without specific prior written permission.
  21:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  22:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  23:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  24:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  25:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  26:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  27:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  28:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  29:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  30:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  31:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * OF SUCH DAMAGE.
ARM GAS  /tmp/ccRmksMf.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  33:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * This file is part of the lwIP TCP/IP stack.
  34:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  35:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Author: Adam Dunkels <<EMAIL>>
  36:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  37:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
  38:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  39:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/opt.h"
  40:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  41:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if !NO_SYS /* don't build if not configured for use in lwipopts.h */
  42:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  43:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/priv/tcpip_priv.h"
  44:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/sys.h"
  45:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/memp.h"
  46:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/mem.h"
  47:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/init.h"
  48:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/ip.h"
  49:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/pbuf.h"
  50:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "lwip/etharp.h"
  51:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #include "netif/ethernet.h"
  52:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  53:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #define TCPIP_MSG_VAR_REF(name)     API_VAR_REF(name)
  54:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #define TCPIP_MSG_VAR_DECLARE(name) API_VAR_DECLARE(struct tcpip_msg, name)
  55:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #define TCPIP_MSG_VAR_ALLOC(name)   API_VAR_ALLOC(struct tcpip_msg, MEMP_TCPIP_MSG_API, name, ERR_M
  56:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #define TCPIP_MSG_VAR_FREE(name)    API_VAR_FREE(MEMP_TCPIP_MSG_API, name)
  57:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  58:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /* global variables */
  59:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static tcpip_init_done_fn tcpip_init_done;
  60:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static void *tcpip_init_done_arg;
  61:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static sys_mbox_t tcpip_mbox;
  62:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  63:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
  64:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /** The global semaphore to lock the stack. */
  65:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** sys_mutex_t lock_tcpip_core;
  66:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_CORE_LOCKING */
  67:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  68:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static void tcpip_thread_handle_msg(struct tcpip_msg *msg);
  69:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  70:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if !LWIP_TIMERS
  71:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /* wait for a message with timers disabled (e.g. pass a timer-check trigger into tcpip_thread) */
  72:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #define TCPIP_MBOX_FETCH(mbox, msg) sys_mbox_fetch(mbox, msg)
  73:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* !LWIP_TIMERS */
  74:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /* wait for a message, timeouts are processed while waiting */
  75:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #define TCPIP_MBOX_FETCH(mbox, msg) tcpip_timeouts_mbox_fetch(mbox, msg)
  76:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
  77:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Wait (forever) for a message to arrive in an mbox.
  78:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * While waiting, timeouts are processed.
  79:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
  80:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param mbox the mbox to fetch the message from
  81:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param msg the place to store the message
  82:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
  83:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static void
  84:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_timeouts_mbox_fetch(sys_mbox_t *mbox, void **msg)
  85:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
  28              		.loc 1 85 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/ccRmksMf.s 			page 3


  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		.loc 1 85 1 is_stmt 0 view .LVU1
  33 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
  34              	.LCFI0:
  35              		.cfi_def_cfa_offset 24
  36              		.cfi_offset 3, -24
  37              		.cfi_offset 4, -20
  38              		.cfi_offset 5, -16
  39              		.cfi_offset 6, -12
  40              		.cfi_offset 7, -8
  41              		.cfi_offset 14, -4
  42 0002 0646     		mov	r6, r0
  43 0004 0F46     		mov	r7, r1
  44 0006 0EE0     		b	.L2
  45              	.LVL1:
  46              	.L8:
  86:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   u32_t sleeptime, res;
  87:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  88:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** again:
  89:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT_CORE_LOCKED();
  90:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  91:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sleeptime = sys_timeouts_sleeptime();
  92:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sleeptime == SYS_TIMEOUTS_SLEEPTIME_INFINITE) {
  93:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     UNLOCK_TCPIP_CORE();
  47              		.loc 1 93 5 is_stmt 1 view .LVU2
  48 0008 144C     		ldr	r4, .L10
  49 000a 2046     		mov	r0, r4
  50              	.LVL2:
  51              		.loc 1 93 5 is_stmt 0 view .LVU3
  52 000c FFF7FEFF 		bl	sys_mutex_unlock
  53              	.LVL3:
  94:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     sys_arch_mbox_fetch(mbox, msg, 0);
  54              		.loc 1 94 5 is_stmt 1 view .LVU4
  55 0010 0022     		movs	r2, #0
  56 0012 3946     		mov	r1, r7
  57 0014 3046     		mov	r0, r6
  58 0016 FFF7FEFF 		bl	sys_arch_mbox_fetch
  59              	.LVL4:
  95:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LOCK_TCPIP_CORE();
  60              		.loc 1 95 5 view .LVU5
  61 001a 2046     		mov	r0, r4
  62 001c FFF7FEFF 		bl	sys_mutex_lock
  63              	.LVL5:
  96:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return;
  64              		.loc 1 96 5 view .LVU6
  65              	.L1:
  97:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   } else if (sleeptime == 0) {
  98:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     sys_check_timeouts();
  99:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* We try again to fetch a message from the mbox. */
 100:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     goto again;
 101:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 102:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 103:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
 104:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   res = sys_arch_mbox_fetch(mbox, msg, sleeptime);
 105:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 106:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (res == SYS_ARCH_TIMEOUT) {
 107:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* If a SYS_ARCH_TIMEOUT value is returned, a timeout occurred
ARM GAS  /tmp/ccRmksMf.s 			page 4


 108:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****        before a message could be fetched. */
 109:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     sys_check_timeouts();
 110:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* We try again to fetch a message from the mbox. */
 111:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     goto again;
 112:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 113:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
  66              		.loc 1 113 1 is_stmt 0 view .LVU7
  67 0020 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
  68              	.LVL6:
  69              	.L9:
  98:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* We try again to fetch a message from the mbox. */
  70              		.loc 1 98 5 is_stmt 1 view .LVU8
  71 0022 FFF7FEFF 		bl	sys_check_timeouts
  72              	.LVL7:
 100:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
  73              		.loc 1 100 5 view .LVU9
  74              	.L2:
  86:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  75              		.loc 1 86 3 view .LVU10
  89:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
  76              		.loc 1 89 28 view .LVU11
  91:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sleeptime == SYS_TIMEOUTS_SLEEPTIME_INFINITE) {
  77              		.loc 1 91 3 view .LVU12
  91:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sleeptime == SYS_TIMEOUTS_SLEEPTIME_INFINITE) {
  78              		.loc 1 91 15 is_stmt 0 view .LVU13
  79 0026 FFF7FEFF 		bl	sys_timeouts_sleeptime
  80              	.LVL8:
  81 002a 0446     		mov	r4, r0
  82              	.LVL9:
  92:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     UNLOCK_TCPIP_CORE();
  83              		.loc 1 92 3 is_stmt 1 view .LVU14
  92:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     UNLOCK_TCPIP_CORE();
  84              		.loc 1 92 6 is_stmt 0 view .LVU15
  85 002c B0F1FF3F 		cmp	r0, #-1
  86 0030 EAD0     		beq	.L8
  97:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     sys_check_timeouts();
  87              		.loc 1 97 10 is_stmt 1 view .LVU16
  97:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     sys_check_timeouts();
  88              		.loc 1 97 13 is_stmt 0 view .LVU17
  89 0032 0028     		cmp	r0, #0
  90 0034 F5D0     		beq	.L9
 103:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   res = sys_arch_mbox_fetch(mbox, msg, sleeptime);
  91              		.loc 1 103 3 is_stmt 1 view .LVU18
  92 0036 094D     		ldr	r5, .L10
  93 0038 2846     		mov	r0, r5
  94              	.LVL10:
 103:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   res = sys_arch_mbox_fetch(mbox, msg, sleeptime);
  95              		.loc 1 103 3 is_stmt 0 view .LVU19
  96 003a FFF7FEFF 		bl	sys_mutex_unlock
  97              	.LVL11:
 104:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
  98              		.loc 1 104 3 is_stmt 1 view .LVU20
 104:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
  99              		.loc 1 104 9 is_stmt 0 view .LVU21
 100 003e 2246     		mov	r2, r4
 101 0040 3946     		mov	r1, r7
 102 0042 3046     		mov	r0, r6
ARM GAS  /tmp/ccRmksMf.s 			page 5


 103 0044 FFF7FEFF 		bl	sys_arch_mbox_fetch
 104              	.LVL12:
 105 0048 0446     		mov	r4, r0
 106              	.LVL13:
 105:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (res == SYS_ARCH_TIMEOUT) {
 107              		.loc 1 105 3 is_stmt 1 view .LVU22
 108 004a 2846     		mov	r0, r5
 109              	.LVL14:
 105:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (res == SYS_ARCH_TIMEOUT) {
 110              		.loc 1 105 3 is_stmt 0 view .LVU23
 111 004c FFF7FEFF 		bl	sys_mutex_lock
 112              	.LVL15:
 106:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* If a SYS_ARCH_TIMEOUT value is returned, a timeout occurred
 113              		.loc 1 106 3 is_stmt 1 view .LVU24
 106:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* If a SYS_ARCH_TIMEOUT value is returned, a timeout occurred
 114              		.loc 1 106 6 is_stmt 0 view .LVU25
 115 0050 B4F1FF3F 		cmp	r4, #-1
 116 0054 E4D1     		bne	.L1
 109:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* We try again to fetch a message from the mbox. */
 117              		.loc 1 109 5 is_stmt 1 view .LVU26
 118 0056 FFF7FEFF 		bl	sys_check_timeouts
 119              	.LVL16:
 111:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 120              		.loc 1 111 5 view .LVU27
 121              	.L3:
 122 005a E4E7     		b	.L2
 123              	.L11:
 124              		.align	2
 125              	.L10:
 126 005c 00000000 		.word	lock_tcpip_core
 127              		.cfi_endproc
 128              	.LFE174:
 130              		.section	.rodata.tcpip_thread_handle_msg.str1.4,"aMS",%progbits,1
 131              		.align	2
 132              	.LC0:
 133 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/api/tcpip.c\000"
 133      6C657761 
 133      7265732F 
 133      54686972 
 133      645F5061 
 134 002d 000000   		.align	2
 135              	.LC1:
 136 0030 74637069 		.ascii	"tcpip_thread: invalid message\000"
 136      705F7468 
 136      72656164 
 136      3A20696E 
 136      76616C69 
 137 004e 0000     		.align	2
 138              	.LC2:
 139 0050 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 139      7274696F 
 139      6E202225 
 139      73222066 
 139      61696C65 
 140              		.section	.text.tcpip_thread_handle_msg,"ax",%progbits
 141              		.align	1
 142              		.syntax unified
ARM GAS  /tmp/ccRmksMf.s 			page 6


 143              		.thumb
 144              		.thumb_func
 146              	tcpip_thread_handle_msg:
 147              	.LVL17:
 148              	.LFB176:
 114:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* !LWIP_TIMERS */
 115:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 116:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 117:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * The main lwIP thread. This thread has exclusive access to lwIP core functions
 118:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * (unless access to them is not locked). Other threads communicate with this
 119:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * thread using message boxes.
 120:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 121:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * It also starts all the timers to make sure they are running in the right
 122:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * thread context.
 123:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 124:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param arg unused argument
 125:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 126:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static void
 127:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_thread(void *arg)
 128:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 129:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 130:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_UNUSED_ARG(arg);
 131:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 132:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_MARK_TCPIP_THREAD();
 133:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 134:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 135:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (tcpip_init_done != NULL) {
 136:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     tcpip_init_done(tcpip_init_done_arg);
 137:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 138:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 139:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   while (1) {                          /* MAIN Loop */
 140:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_TCPIP_THREAD_ALIVE();
 141:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* wait for a message, timeouts are processed while waiting */
 142:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     TCPIP_MBOX_FETCH(&tcpip_mbox, (void **)&msg);
 143:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     if (msg == NULL) {
 144:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: invalid message: NULL\n"));
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_ASSERT("tcpip_thread: invalid message", 0);
 146:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 147:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     }
 148:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     tcpip_thread_handle_msg(msg);
 149:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 150:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 151:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 152:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /* Handle a single tcpip_msg
 153:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * This is in its own function for access by tests only.
 154:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 155:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static void
 156:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_thread_handle_msg(struct tcpip_msg *msg)
 157:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 149              		.loc 1 157 1 view -0
 150              		.cfi_startproc
 151              		@ args = 0, pretend = 0, frame = 0
 152              		@ frame_needed = 0, uses_anonymous_args = 0
 153              		.loc 1 157 1 is_stmt 0 view .LVU29
 154 0000 10B5     		push	{r4, lr}
 155              	.LCFI1:
 156              		.cfi_def_cfa_offset 8
ARM GAS  /tmp/ccRmksMf.s 			page 7


 157              		.cfi_offset 4, -8
 158              		.cfi_offset 14, -4
 159 0002 0446     		mov	r4, r0
 158:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   switch (msg->type) {
 160              		.loc 1 158 3 is_stmt 1 view .LVU30
 161              		.loc 1 158 14 is_stmt 0 view .LVU31
 162 0004 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
 163              		.loc 1 158 3 view .LVU32
 164 0006 012B     		cmp	r3, #1
 165 0008 10D0     		beq	.L13
 166 000a 022B     		cmp	r3, #2
 167 000c 16D0     		beq	.L14
 168 000e CBB9     		cbnz	r3, .L15
 159:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if !LWIP_TCPIP_CORE_LOCKING
 160:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_API:
 161:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: API message %p\n", (void *)msg));
 162:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       msg->msg.api_msg.function(msg->msg.api_msg.msg);
 163:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 164:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_API_CALL:
 165:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: API CALL message %p\n", (void *)msg));
 166:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       msg->msg.api_call.arg->err = msg->msg.api_call.function(msg->msg.api_call.arg);
 167:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       sys_sem_signal(msg->msg.api_call.sem);
 168:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 169:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* !LWIP_TCPIP_CORE_LOCKING */
 170:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 171:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if !LWIP_TCPIP_CORE_LOCKING_INPUT
 172:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_INPKT:
 173:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: PACKET %p\n", (void *)msg));
 169              		.loc 1 173 75 is_stmt 1 view .LVU33
 174:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       if (msg->msg.inp.input_fn(msg->msg.inp.p, msg->msg.inp.netif) != ERR_OK) {
 170              		.loc 1 174 7 view .LVU34
 171              		.loc 1 174 23 is_stmt 0 view .LVU35
 172 0010 C368     		ldr	r3, [r0, #12]
 173              		.loc 1 174 11 view .LVU36
 174 0012 8168     		ldr	r1, [r0, #8]
 175 0014 4068     		ldr	r0, [r0, #4]
 176              	.LVL18:
 177              		.loc 1 174 11 view .LVU37
 178 0016 9847     		blx	r3
 179              	.LVL19:
 180              		.loc 1 174 10 discriminator 1 view .LVU38
 181 0018 20B9     		cbnz	r0, .L19
 182              	.L16:
 175:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****         pbuf_free(msg->msg.inp.p);
 176:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       }
 177:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_INPKT, msg);
 183              		.loc 1 177 7 is_stmt 1 view .LVU39
 184 001a 2146     		mov	r1, r4
 185 001c 0920     		movs	r0, #9
 186 001e FFF7FEFF 		bl	memp_free
 187              	.LVL20:
 178:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 188              		.loc 1 178 7 view .LVU40
 189              	.L12:
 179:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* !LWIP_TCPIP_CORE_LOCKING_INPUT */
 180:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 181:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_TIMEOUT && LWIP_TIMERS
ARM GAS  /tmp/ccRmksMf.s 			page 8


 182:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_TIMEOUT:
 183:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: TIMEOUT %p\n", (void *)msg));
 184:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       sys_timeout(msg->msg.tmo.msecs, msg->msg.tmo.h, msg->msg.tmo.arg);
 185:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 186:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 187:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_UNTIMEOUT:
 188:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: UNTIMEOUT %p\n", (void *)msg));
 189:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       sys_untimeout(msg->msg.tmo.h, msg->msg.tmo.arg);
 190:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 191:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 192:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_TIMEOUT && LWIP_TIMERS */
 193:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 194:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_CALLBACK:
 195:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: CALLBACK %p\n", (void *)msg));
 196:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       msg->msg.cb.function(msg->msg.cb.ctx);
 197:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 198:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 199:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 200:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     case TCPIP_MSG_CALLBACK_STATIC:
 201:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: CALLBACK_STATIC %p\n", (void *)msg));
 202:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       msg->msg.cb.function(msg->msg.cb.ctx);
 203:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 204:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 205:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     default:
 206:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: invalid message: %d\n", msg->type));
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_ASSERT("tcpip_thread: invalid message", 0);
 208:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 209:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 210:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 190              		.loc 1 210 1 is_stmt 0 view .LVU41
 191 0022 10BD     		pop	{r4, pc}
 192              	.LVL21:
 193              	.L19:
 175:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       }
 194              		.loc 1 175 9 is_stmt 1 view .LVU42
 195 0024 6068     		ldr	r0, [r4, #4]
 196 0026 FFF7FEFF 		bl	pbuf_free
 197              	.LVL22:
 198 002a F6E7     		b	.L16
 199              	.LVL23:
 200              	.L13:
 195:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       msg->msg.cb.function(msg->msg.cb.ctx);
 201              		.loc 1 195 77 view .LVU43
 196:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 202              		.loc 1 196 7 view .LVU44
 196:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 203              		.loc 1 196 18 is_stmt 0 view .LVU45
 204 002c 4368     		ldr	r3, [r0, #4]
 196:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 205              		.loc 1 196 7 view .LVU46
 206 002e 8068     		ldr	r0, [r0, #8]
 207              	.LVL24:
 196:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       memp_free(MEMP_TCPIP_MSG_API, msg);
 208              		.loc 1 196 7 view .LVU47
 209 0030 9847     		blx	r3
 210              	.LVL25:
 197:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
ARM GAS  /tmp/ccRmksMf.s 			page 9


 211              		.loc 1 197 7 is_stmt 1 view .LVU48
 212 0032 2146     		mov	r1, r4
 213 0034 0820     		movs	r0, #8
 214 0036 FFF7FEFF 		bl	memp_free
 215              	.LVL26:
 198:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 216              		.loc 1 198 7 view .LVU49
 217 003a F2E7     		b	.L12
 218              	.LVL27:
 219              	.L14:
 201:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       msg->msg.cb.function(msg->msg.cb.ctx);
 220              		.loc 1 201 84 view .LVU50
 202:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 221              		.loc 1 202 7 view .LVU51
 202:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 222              		.loc 1 202 18 is_stmt 0 view .LVU52
 223 003c 4368     		ldr	r3, [r0, #4]
 202:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 224              		.loc 1 202 7 view .LVU53
 225 003e 8068     		ldr	r0, [r0, #8]
 226              	.LVL28:
 202:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 227              		.loc 1 202 7 view .LVU54
 228 0040 9847     		blx	r3
 229              	.LVL29:
 203:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 230              		.loc 1 203 7 is_stmt 1 view .LVU55
 231 0042 EEE7     		b	.L12
 232              	.LVL30:
 233              	.L15:
 206:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_ASSERT("tcpip_thread: invalid message", 0);
 234              		.loc 1 206 83 view .LVU56
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 235              		.loc 1 207 7 view .LVU57
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 236              		.loc 1 207 7 view .LVU58
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 237              		.loc 1 207 7 discriminator 1 view .LVU59
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 238              		.loc 1 207 7 discriminator 1 view .LVU60
 239 0044 034B     		ldr	r3, .L20
 240 0046 CF22     		movs	r2, #207
 241 0048 0349     		ldr	r1, .L20+4
 242 004a 0448     		ldr	r0, .L20+8
 243              	.LVL31:
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 244              		.loc 1 207 7 is_stmt 0 discriminator 1 view .LVU61
 245 004c FFF7FEFF 		bl	printf
 246              	.LVL32:
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 247              		.loc 1 207 7 is_stmt 1 discriminator 3 view .LVU62
 207:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       break;
 248              		.loc 1 207 7 discriminator 3 view .LVU63
 208:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 249              		.loc 1 208 7 view .LVU64
 250              		.loc 1 210 1 is_stmt 0 view .LVU65
 251 0050 E7E7     		b	.L12
ARM GAS  /tmp/ccRmksMf.s 			page 10


 252              	.L21:
 253 0052 00BF     		.align	2
 254              	.L20:
 255 0054 00000000 		.word	.LC0
 256 0058 30000000 		.word	.LC1
 257 005c 50000000 		.word	.LC2
 258              		.cfi_endproc
 259              	.LFE176:
 261              		.section	.text.tcpip_thread,"ax",%progbits
 262              		.align	1
 263              		.syntax unified
 264              		.thumb
 265              		.thumb_func
 267              	tcpip_thread:
 268              	.LVL33:
 269              	.LFB175:
 128:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 270              		.loc 1 128 1 is_stmt 1 view -0
 271              		.cfi_startproc
 272              		@ Volatile: function does not return.
 273              		@ args = 0, pretend = 0, frame = 8
 274              		@ frame_needed = 0, uses_anonymous_args = 0
 128:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 275              		.loc 1 128 1 is_stmt 0 view .LVU67
 276 0000 00B5     		push	{lr}
 277              	.LCFI2:
 278              		.cfi_def_cfa_offset 4
 279              		.cfi_offset 14, -4
 280 0002 83B0     		sub	sp, sp, #12
 281              	.LCFI3:
 282              		.cfi_def_cfa_offset 16
 129:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_UNUSED_ARG(arg);
 283              		.loc 1 129 3 is_stmt 1 view .LVU68
 130:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 284              		.loc 1 130 3 view .LVU69
 132:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 285              		.loc 1 132 27 view .LVU70
 134:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (tcpip_init_done != NULL) {
 286              		.loc 1 134 3 view .LVU71
 287 0004 0C48     		ldr	r0, .L28
 288              	.LVL34:
 134:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (tcpip_init_done != NULL) {
 289              		.loc 1 134 3 is_stmt 0 view .LVU72
 290 0006 FFF7FEFF 		bl	sys_mutex_lock
 291              	.LVL35:
 135:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     tcpip_init_done(tcpip_init_done_arg);
 292              		.loc 1 135 3 is_stmt 1 view .LVU73
 135:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     tcpip_init_done(tcpip_init_done_arg);
 293              		.loc 1 135 23 is_stmt 0 view .LVU74
 294 000a 0C4B     		ldr	r3, .L28+4
 295 000c 1B68     		ldr	r3, [r3]
 135:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     tcpip_init_done(tcpip_init_done_arg);
 296              		.loc 1 135 6 view .LVU75
 297 000e 2BB1     		cbz	r3, .L24
 136:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 298              		.loc 1 136 5 is_stmt 1 view .LVU76
 299 0010 0B4A     		ldr	r2, .L28+8
ARM GAS  /tmp/ccRmksMf.s 			page 11


 300 0012 1068     		ldr	r0, [r2]
 301 0014 9847     		blx	r3
 302              	.LVL36:
 303 0016 01E0     		b	.L24
 304              	.L25:
 148:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 305              		.loc 1 148 5 view .LVU77
 306 0018 FFF7FEFF 		bl	tcpip_thread_handle_msg
 307              	.LVL37:
 308              	.L24:
 139:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_TCPIP_THREAD_ALIVE();
 309              		.loc 1 139 3 view .LVU78
 140:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     /* wait for a message, timeouts are processed while waiting */
 310              		.loc 1 140 30 view .LVU79
 142:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     if (msg == NULL) {
 311              		.loc 1 142 5 view .LVU80
 312 001c 01A9     		add	r1, sp, #4
 313 001e 0948     		ldr	r0, .L28+12
 314 0020 FFF7FEFF 		bl	tcpip_timeouts_mbox_fetch
 315              	.LVL38:
 143:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: invalid message: NULL\n"));
 316              		.loc 1 143 5 view .LVU81
 143:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: invalid message: NULL\n"));
 317              		.loc 1 143 13 is_stmt 0 view .LVU82
 318 0024 0198     		ldr	r0, [sp, #4]
 143:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_thread: invalid message: NULL\n"));
 319              		.loc 1 143 8 view .LVU83
 320 0026 0028     		cmp	r0, #0
 321 0028 F6D1     		bne	.L25
 144:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       LWIP_ASSERT("tcpip_thread: invalid message", 0);
 322              		.loc 1 144 74 is_stmt 1 view .LVU84
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 323              		.loc 1 145 7 view .LVU85
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 324              		.loc 1 145 7 view .LVU86
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 325              		.loc 1 145 7 discriminator 1 view .LVU87
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 326              		.loc 1 145 7 discriminator 1 view .LVU88
 327 002a 074B     		ldr	r3, .L28+16
 328 002c 9122     		movs	r2, #145
 329 002e 0749     		ldr	r1, .L28+20
 330 0030 0748     		ldr	r0, .L28+24
 331 0032 FFF7FEFF 		bl	printf
 332              	.LVL39:
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 333              		.loc 1 145 7 discriminator 3 view .LVU89
 145:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       continue;
 334              		.loc 1 145 7 discriminator 3 view .LVU90
 146:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     }
 335              		.loc 1 146 7 view .LVU91
 336 0036 F1E7     		b	.L24
 337              	.L29:
 338              		.align	2
 339              	.L28:
 340 0038 00000000 		.word	lock_tcpip_core
 341 003c 00000000 		.word	tcpip_init_done
ARM GAS  /tmp/ccRmksMf.s 			page 12


 342 0040 00000000 		.word	tcpip_init_done_arg
 343 0044 00000000 		.word	tcpip_mbox
 344 0048 00000000 		.word	.LC0
 345 004c 30000000 		.word	.LC1
 346 0050 50000000 		.word	.LC2
 347              		.cfi_endproc
 348              	.LFE175:
 350              		.section	.text.pbuf_free_int,"ax",%progbits
 351              		.align	1
 352              		.syntax unified
 353              		.thumb
 354              		.thumb_func
 356              	pbuf_free_int:
 357              	.LVL40:
 358              	.LFB188:
 211:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 212:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #ifdef TCPIP_THREAD_TEST
 213:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /** Work on queued items in single-threaded test mode */
 214:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** int
 215:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_thread_poll_one(void)
 216:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 217:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   int ret = 0;
 218:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 219:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 220:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_arch_mbox_tryfetch(&tcpip_mbox, (void **)&msg) != SYS_ARCH_TIMEOUT) {
 221:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LOCK_TCPIP_CORE();
 222:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     if (msg != NULL) {
 223:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       tcpip_thread_handle_msg(msg);
 224:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****       ret = 1;
 225:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     }
 226:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     UNLOCK_TCPIP_CORE();
 227:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 228:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ret;
 229:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 230:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif
 231:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 232:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 233:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Pass a received packet to tcpip_thread for input processing
 234:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 235:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param p the received packet
 236:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param inp the network interface on which the packet was received
 237:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param input_fn input function to call
 238:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 239:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 240:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_inpkt(struct pbuf *p, struct netif *inp, netif_input_fn input_fn)
 241:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 242:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING_INPUT
 243:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   err_t ret;
 244:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_DEBUGF(TCPIP_DEBUG, ("tcpip_inpkt: PACKET %p/%p\n", (void *)p, (void *)inp));
 245:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 246:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   ret = input_fn(p, inp);
 247:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
 248:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ret;
 249:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* LWIP_TCPIP_CORE_LOCKING_INPUT */
 250:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 251:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
ARM GAS  /tmp/ccRmksMf.s 			page 13


 253:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 254:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_INPKT);
 255:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 256:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 257:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 258:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 259:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->type = TCPIP_MSG_INPKT;
 260:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.p = p;
 261:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.netif = inp;
 262:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.input_fn = input_fn;
 263:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_trypost(&tcpip_mbox, msg) != ERR_OK) {
 264:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_INPKT, msg);
 265:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 266:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 267:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 268:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_CORE_LOCKING_INPUT */
 269:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 270:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 271:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 272:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 273:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Pass a received packet to tcpip_thread for input processing with
 274:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * ethernet_input or ip_input. Don't call directly, pass to netif_add()
 275:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * and call netif->input().
 276:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 277:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param p the received packet, p->payload pointing to the Ethernet header or
 278:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *          to an IP header (if inp doesn't have NETIF_FLAG_ETHARP or
 279:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *          NETIF_FLAG_ETHERNET flags)
 280:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param inp the network interface on which the packet was received
 281:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 282:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 283:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_input(struct pbuf *p, struct netif *inp)
 284:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 285:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_ETHERNET
 286:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (inp->flags & (NETIF_FLAG_ETHARP | NETIF_FLAG_ETHERNET)) {
 287:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return tcpip_inpkt(p, inp, ethernet_input);
 288:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   } else
 289:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_ETHERNET */
 290:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return tcpip_inpkt(p, inp, ip_input);
 291:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 292:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 293:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 294:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 295:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Call a specific function in the thread context of
 296:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * tcpip_thread for easy access synchronization.
 297:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * A function called in that way may access lwIP core code
 298:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * without fearing concurrent access.
 299:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Blocks until the request is posted.
 300:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Must not be called from interrupt context!
 301:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 302:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param function the function to call
 303:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param ctx parameter passed to f
 304:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_OK if the function was called, another err_t if not
 305:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 306:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_try_callback
 307:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 308:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 309:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_callback(tcpip_callback_fn function, void *ctx)
ARM GAS  /tmp/ccRmksMf.s 			page 14


 310:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 311:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 312:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 314:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 315:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 316:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 317:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 318:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 319:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 320:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->type = TCPIP_MSG_CALLBACK;
 321:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 322:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 323:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 324:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_mbox_post(&tcpip_mbox, msg);
 325:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 326:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 327:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 328:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 329:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 330:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Call a specific function in the thread context of
 331:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * tcpip_thread for easy access synchronization.
 332:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * A function called in that way may access lwIP core code
 333:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * without fearing concurrent access.
 334:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Does NOT block when the request cannot be posted because the
 335:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * tcpip_mbox is full, but returns ERR_MEM instead.
 336:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Can be called from interrupt context.
 337:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 338:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param function the function to call
 339:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param ctx parameter passed to f
 340:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_OK if the function was called, another err_t if not
 341:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 342:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_callback
 343:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 344:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 345:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_try_callback(tcpip_callback_fn function, void *ctx)
 346:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 347:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 348:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 350:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 351:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 352:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 353:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 354:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 355:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 356:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->type = TCPIP_MSG_CALLBACK;
 357:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 358:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 359:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 360:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_trypost(&tcpip_mbox, msg) != ERR_OK) {
 361:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_API, msg);
 362:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 363:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 364:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 365:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 366:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
ARM GAS  /tmp/ccRmksMf.s 			page 15


 367:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_TIMEOUT && LWIP_TIMERS
 368:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 369:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * call sys_timeout in tcpip_thread
 370:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 371:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param msecs time in milliseconds for timeout
 372:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param h function to be called on timeout
 373:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param arg argument to pass to timeout function h
 374:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_MEM on memory error, ERR_OK otherwise
 375:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 376:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 377:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_timeout(u32_t msecs, sys_timeout_handler h, void *arg)
 378:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 379:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 380:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 381:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 382:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 383:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 384:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 385:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 386:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 387:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 388:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->type = TCPIP_MSG_TIMEOUT;
 389:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.tmo.msecs = msecs;
 390:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.tmo.h = h;
 391:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.tmo.arg = arg;
 392:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_mbox_post(&tcpip_mbox, msg);
 393:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 394:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 395:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 396:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 397:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * call sys_untimeout in tcpip_thread
 398:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 399:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param h function to be called on timeout
 400:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param arg argument to pass to timeout function h
 401:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_MEM on memory error, ERR_OK otherwise
 402:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 403:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 404:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_untimeout(sys_timeout_handler h, void *arg)
 405:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 406:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 407:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 408:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 409:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 410:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 411:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 412:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 413:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 414:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 415:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->type = TCPIP_MSG_UNTIMEOUT;
 416:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.tmo.h = h;
 417:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.tmo.arg = arg;
 418:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_mbox_post(&tcpip_mbox, msg);
 419:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 420:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 421:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_TIMEOUT && LWIP_TIMERS */
 422:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 423:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
ARM GAS  /tmp/ccRmksMf.s 			page 16


 424:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 425:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Sends a message to TCPIP thread to call a function. Caller thread blocks on
 426:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * on a provided semaphore, which ist NOT automatically signalled by TCPIP thread,
 427:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * this has to be done by the user.
 428:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * It is recommended to use LWIP_TCPIP_CORE_LOCKING since this is the way
 429:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * with least runtime overhead.
 430:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 431:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param fn function to be called from TCPIP thread
 432:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param apimsg argument to API function
 433:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param sem semaphore to wait on
 434:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_OK if the function was called, another err_t if not
 435:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 436:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 437:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_send_msg_wait_sem(tcpip_callback_fn fn, void *apimsg, sys_sem_t *sem)
 438:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 439:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 440:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_UNUSED_ARG(sem);
 441:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 442:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   fn(apimsg);
 443:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
 444:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 445:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* LWIP_TCPIP_CORE_LOCKING */
 446:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_DECLARE(msg);
 447:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 448:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("semaphore not initialized", sys_sem_valid(sem));
 449:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 450:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 451:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_ALLOC(msg);
 452:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).type = TCPIP_MSG_API;
 453:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).msg.api_msg.function = fn;
 454:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).msg.api_msg.msg = apimsg;
 455:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_mbox_post(&tcpip_mbox, &TCPIP_MSG_VAR_REF(msg));
 456:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_arch_sem_wait(sem, 0);
 457:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_FREE(msg);
 458:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 459:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_CORE_LOCKING */
 460:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 461:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 462:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 463:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Synchronously calls function in TCPIP thread and waits for its completion.
 464:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * It is recommended to use LWIP_TCPIP_CORE_LOCKING (preferred) or
 465:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * LWIP_NETCONN_SEM_PER_THREAD.
 466:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * If not, a semaphore is created and destroyed on every call which is usually
 467:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * an expensive/slow operation.
 468:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param fn Function to call
 469:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param call Call parameters
 470:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return Return value from tcpip_api_call_fn
 471:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 472:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 473:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_api_call(tcpip_api_call_fn fn, struct tcpip_api_call_data *call)
 474:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 475:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 476:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   err_t err;
 477:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 478:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   err = fn(call);
 479:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
 480:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return err;
ARM GAS  /tmp/ccRmksMf.s 			page 17


 481:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* LWIP_TCPIP_CORE_LOCKING */
 482:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_DECLARE(msg);
 483:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 484:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if !LWIP_NETCONN_SEM_PER_THREAD
 485:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   err_t err = sys_sem_new(&call->sem, 0);
 486:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (err != ERR_OK) {
 487:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return err;
 488:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 489:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_NETCONN_SEM_PER_THREAD */
 490:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 491:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 492:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 493:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_ALLOC(msg);
 494:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).type = TCPIP_MSG_API_CALL;
 495:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).msg.api_call.arg = call;
 496:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).msg.api_call.function = fn;
 497:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_NETCONN_SEM_PER_THREAD
 498:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).msg.api_call.sem = LWIP_NETCONN_THREAD_SEM_GET();
 499:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* LWIP_NETCONN_SEM_PER_THREAD */
 500:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_REF(msg).msg.api_call.sem = &call->sem;
 501:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_NETCONN_SEM_PER_THREAD */
 502:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_mbox_post(&tcpip_mbox, &TCPIP_MSG_VAR_REF(msg));
 503:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_arch_sem_wait(TCPIP_MSG_VAR_REF(msg).msg.api_call.sem, 0);
 504:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   TCPIP_MSG_VAR_FREE(msg);
 505:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 506:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if !LWIP_NETCONN_SEM_PER_THREAD
 507:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_sem_free(&call->sem);
 508:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_NETCONN_SEM_PER_THREAD */
 509:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 510:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return call->err;
 511:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_CORE_LOCKING */
 512:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 513:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 514:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 515:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 516:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Allocate a structure for a static callback message and initialize it.
 517:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * The message has a special type such that lwIP never frees it.
 518:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * This is intended to be used to send "static" messages from interrupt context,
 519:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * e.g. the message is allocated once and posted several times from an IRQ
 520:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * using tcpip_callbackmsg_trycallback().
 521:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Example usage: Trigger execution of an ethernet IRQ DPC routine in lwIP thread context.
 522:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * 
 523:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param function the function to call
 524:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param ctx parameter passed to function
 525:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return a struct pointer to pass to tcpip_callbackmsg_trycallback().
 526:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 527:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_callbackmsg_trycallback()
 528:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_callbackmsg_delete()
 529:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 530:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** struct tcpip_callback_msg *
 531:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_callbackmsg_new(tcpip_callback_fn function, void *ctx)
 532:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 533:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 534:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 535:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return NULL;
 536:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 537:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->type = TCPIP_MSG_CALLBACK_STATIC;
ARM GAS  /tmp/ccRmksMf.s 			page 18


 538:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 539:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 540:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return (struct tcpip_callback_msg *)msg;
 541:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 542:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 543:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 544:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 545:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Free a callback message allocated by tcpip_callbackmsg_new().
 546:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 547:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param msg the message to free
 548:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 549:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_callbackmsg_new()
 550:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 551:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** void
 552:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_callbackmsg_delete(struct tcpip_callback_msg *msg)
 553:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 554:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   memp_free(MEMP_TCPIP_MSG_API, msg);
 555:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 556:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 557:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 558:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 559:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Try to post a callback-message to the tcpip_thread tcpip_mbox.
 560:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 561:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param msg pointer to the message to post
 562:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return sys_mbox_trypost() return code
 563:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 564:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_callbackmsg_new()
 565:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 566:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 567:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_callbackmsg_trycallback(struct tcpip_callback_msg *msg)
 568:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 570:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 571:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 572:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 573:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 574:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 575:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Try to post a callback-message to the tcpip_thread mbox.
 576:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Same as @ref tcpip_callbackmsg_trycallback but calls sys_mbox_trypost_fromisr(),
 577:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * mainly to help FreeRTOS, where calls differ between task level and ISR level.
 578:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 579:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param msg pointer to the message to post
 580:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return sys_mbox_trypost_fromisr() return code (without change, so this
 581:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *         knowledge can be used to e.g. propagate "bool needs_scheduling")
 582:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 583:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @see tcpip_callbackmsg_new()
 584:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 585:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 586:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_callbackmsg_trycallback_fromisr(struct tcpip_callback_msg *msg)
 587:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 589:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 590:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 591:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 592:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 593:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @ingroup lwip_os
 594:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Initialize this module:
ARM GAS  /tmp/ccRmksMf.s 			page 19


 595:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * - initialize all sub modules
 596:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * - start the tcpip_thread
 597:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 598:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param initfunc a function to call when tcpip_thread is running and finished initializing
 599:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param arg argument to pass to initfunc
 600:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 601:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** void
 602:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** tcpip_init(tcpip_init_done_fn initfunc, void *arg)
 603:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 604:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   lwip_init();
 605:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 606:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   tcpip_init_done = initfunc;
 607:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   tcpip_init_done_arg = arg;
 608:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_new(&tcpip_mbox, TCPIP_MBOX_SIZE) != ERR_OK) {
 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create tcpip_thread mbox", 0);
 610:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 611:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 612:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mutex_new(&lock_tcpip_core) != ERR_OK) {
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create lock_tcpip_core", 0);
 614:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 615:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #endif /* LWIP_TCPIP_CORE_LOCKING */
 616:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 617:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   sys_thread_new(TCPIP_THREAD_NAME, tcpip_thread, NULL, TCPIP_THREAD_STACKSIZE, TCPIP_THREAD_PRIO);
 618:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 619:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 620:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 621:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * Simple callback function used with tcpip_callback to free a pbuf
 622:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * (pbuf_free has a wrong signature for tcpip_callback)
 623:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 624:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param p The pbuf (chain) to be dereferenced.
 625:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 626:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** static void
 627:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** pbuf_free_int(void *p)
 628:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 359              		.loc 1 628 1 view -0
 360              		.cfi_startproc
 361              		@ args = 0, pretend = 0, frame = 0
 362              		@ frame_needed = 0, uses_anonymous_args = 0
 363              		.loc 1 628 1 is_stmt 0 view .LVU93
 364 0000 08B5     		push	{r3, lr}
 365              	.LCFI4:
 366              		.cfi_def_cfa_offset 8
 367              		.cfi_offset 3, -8
 368              		.cfi_offset 14, -4
 629:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct pbuf *q = (struct pbuf *)p;
 369              		.loc 1 629 3 is_stmt 1 view .LVU94
 370              	.LVL41:
 630:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   pbuf_free(q);
 371              		.loc 1 630 3 view .LVU95
 372 0002 FFF7FEFF 		bl	pbuf_free
 373              	.LVL42:
 631:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 374              		.loc 1 631 1 is_stmt 0 view .LVU96
 375 0006 08BD     		pop	{r3, pc}
 376              		.cfi_endproc
 377              	.LFE188:
 379              		.section	.rodata.tcpip_inpkt.str1.4,"aMS",%progbits,1
ARM GAS  /tmp/ccRmksMf.s 			page 20


 380              		.align	2
 381              	.LC3:
 382 0000 496E7661 		.ascii	"Invalid mbox\000"
 382      6C696420 
 382      6D626F78 
 382      00
 383              		.section	.text.tcpip_inpkt,"ax",%progbits
 384              		.align	1
 385              		.global	tcpip_inpkt
 386              		.syntax unified
 387              		.thumb
 388              		.thumb_func
 390              	tcpip_inpkt:
 391              	.LVL43:
 392              	.LFB177:
 241:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING_INPUT
 393              		.loc 1 241 1 is_stmt 1 view -0
 394              		.cfi_startproc
 395              		@ args = 0, pretend = 0, frame = 0
 396              		@ frame_needed = 0, uses_anonymous_args = 0
 241:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING_INPUT
 397              		.loc 1 241 1 is_stmt 0 view .LVU98
 398 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 399              	.LCFI5:
 400              		.cfi_def_cfa_offset 24
 401              		.cfi_offset 3, -24
 402              		.cfi_offset 4, -20
 403              		.cfi_offset 5, -16
 404              		.cfi_offset 6, -12
 405              		.cfi_offset 7, -8
 406              		.cfi_offset 14, -4
 407 0002 0746     		mov	r7, r0
 408 0004 0E46     		mov	r6, r1
 409 0006 1546     		mov	r5, r2
 250:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 410              		.loc 1 250 3 is_stmt 1 view .LVU99
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 411              		.loc 1 252 3 view .LVU100
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 412              		.loc 1 252 3 view .LVU101
 413 0008 1248     		ldr	r0, .L39
 414              	.LVL44:
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 415              		.loc 1 252 3 is_stmt 0 view .LVU102
 416 000a FFF7FEFF 		bl	sys_mbox_valid
 417              	.LVL45:
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 418              		.loc 1 252 3 discriminator 1 view .LVU103
 419 000e 78B1     		cbz	r0, .L37
 420              	.L33:
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 421              		.loc 1 252 3 is_stmt 1 discriminator 3 view .LVU104
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 422              		.loc 1 252 3 discriminator 3 view .LVU105
 254:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 423              		.loc 1 254 3 view .LVU106
 254:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
ARM GAS  /tmp/ccRmksMf.s 			page 21


 424              		.loc 1 254 29 is_stmt 0 view .LVU107
 425 0010 0920     		movs	r0, #9
 426 0012 FFF7FEFF 		bl	memp_malloc
 427              	.LVL46:
 255:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 428              		.loc 1 255 3 is_stmt 1 view .LVU108
 255:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 429              		.loc 1 255 6 is_stmt 0 view .LVU109
 430 0016 0446     		mov	r4, r0
 431 0018 C0B1     		cbz	r0, .L35
 259:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.p = p;
 432              		.loc 1 259 3 is_stmt 1 view .LVU110
 259:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.p = p;
 433              		.loc 1 259 13 is_stmt 0 view .LVU111
 434 001a 0023     		movs	r3, #0
 435 001c 0370     		strb	r3, [r0]
 260:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.netif = inp;
 436              		.loc 1 260 3 is_stmt 1 view .LVU112
 260:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.netif = inp;
 437              		.loc 1 260 18 is_stmt 0 view .LVU113
 438 001e 4760     		str	r7, [r0, #4]
 261:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.input_fn = input_fn;
 439              		.loc 1 261 3 is_stmt 1 view .LVU114
 261:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.inp.input_fn = input_fn;
 440              		.loc 1 261 22 is_stmt 0 view .LVU115
 441 0020 8660     		str	r6, [r0, #8]
 262:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_trypost(&tcpip_mbox, msg) != ERR_OK) {
 442              		.loc 1 262 3 is_stmt 1 view .LVU116
 262:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_trypost(&tcpip_mbox, msg) != ERR_OK) {
 443              		.loc 1 262 25 is_stmt 0 view .LVU117
 444 0022 C560     		str	r5, [r0, #12]
 263:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_INPKT, msg);
 445              		.loc 1 263 3 is_stmt 1 view .LVU118
 263:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_INPKT, msg);
 446              		.loc 1 263 7 is_stmt 0 view .LVU119
 447 0024 0146     		mov	r1, r0
 448 0026 0B48     		ldr	r0, .L39
 449              	.LVL47:
 263:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_INPKT, msg);
 450              		.loc 1 263 7 view .LVU120
 451 0028 FFF7FEFF 		bl	sys_mbox_trypost
 452              	.LVL48:
 263:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_INPKT, msg);
 453              		.loc 1 263 6 discriminator 1 view .LVU121
 454 002c 38B9     		cbnz	r0, .L38
 455              	.L34:
 269:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 456              		.loc 1 269 1 view .LVU122
 457 002e F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 458              	.LVL49:
 459              	.L37:
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 460              		.loc 1 252 3 is_stmt 1 discriminator 1 view .LVU123
 252:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 461              		.loc 1 252 3 discriminator 1 view .LVU124
 462 0030 094B     		ldr	r3, .L39+4
 463 0032 FC22     		movs	r2, #252
ARM GAS  /tmp/ccRmksMf.s 			page 22


 464 0034 0949     		ldr	r1, .L39+8
 465 0036 0A48     		ldr	r0, .L39+12
 466 0038 FFF7FEFF 		bl	printf
 467              	.LVL50:
 468 003c E8E7     		b	.L33
 469              	.LVL51:
 470              	.L38:
 264:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 471              		.loc 1 264 5 view .LVU125
 472 003e 2146     		mov	r1, r4
 473 0040 0920     		movs	r0, #9
 474 0042 FFF7FEFF 		bl	memp_free
 475              	.LVL52:
 265:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 476              		.loc 1 265 5 view .LVU126
 265:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 477              		.loc 1 265 12 is_stmt 0 view .LVU127
 478 0046 4FF0FF30 		mov	r0, #-1
 479 004a F0E7     		b	.L34
 480              	.LVL53:
 481              	.L35:
 256:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 482              		.loc 1 256 12 view .LVU128
 483 004c 4FF0FF30 		mov	r0, #-1
 484              	.LVL54:
 256:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 485              		.loc 1 256 12 view .LVU129
 486 0050 EDE7     		b	.L34
 487              	.L40:
 488 0052 00BF     		.align	2
 489              	.L39:
 490 0054 00000000 		.word	tcpip_mbox
 491 0058 00000000 		.word	.LC0
 492 005c 00000000 		.word	.LC3
 493 0060 50000000 		.word	.LC2
 494              		.cfi_endproc
 495              	.LFE177:
 497              		.section	.text.tcpip_input,"ax",%progbits
 498              		.align	1
 499              		.global	tcpip_input
 500              		.syntax unified
 501              		.thumb
 502              		.thumb_func
 504              	tcpip_input:
 505              	.LVL55:
 506              	.LFB178:
 284:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_ETHERNET
 507              		.loc 1 284 1 is_stmt 1 view -0
 508              		.cfi_startproc
 509              		@ args = 0, pretend = 0, frame = 0
 510              		@ frame_needed = 0, uses_anonymous_args = 0
 284:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_ETHERNET
 511              		.loc 1 284 1 is_stmt 0 view .LVU131
 512 0000 08B5     		push	{r3, lr}
 513              	.LCFI6:
 514              		.cfi_def_cfa_offset 8
 515              		.cfi_offset 3, -8
ARM GAS  /tmp/ccRmksMf.s 			page 23


 516              		.cfi_offset 14, -4
 286:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return tcpip_inpkt(p, inp, ethernet_input);
 517              		.loc 1 286 3 is_stmt 1 view .LVU132
 286:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return tcpip_inpkt(p, inp, ethernet_input);
 518              		.loc 1 286 10 is_stmt 0 view .LVU133
 519 0002 91F82D30 		ldrb	r3, [r1, #45]	@ zero_extendqisi2
 286:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return tcpip_inpkt(p, inp, ethernet_input);
 520              		.loc 1 286 6 view .LVU134
 521 0006 13F0180F 		tst	r3, #24
 522 000a 03D0     		beq	.L42
 287:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   } else
 523              		.loc 1 287 5 is_stmt 1 view .LVU135
 287:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   } else
 524              		.loc 1 287 12 is_stmt 0 view .LVU136
 525 000c 034A     		ldr	r2, .L45
 526 000e FFF7FEFF 		bl	tcpip_inpkt
 527              	.LVL56:
 528              	.L43:
 291:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 529              		.loc 1 291 1 view .LVU137
 530 0012 08BD     		pop	{r3, pc}
 531              	.LVL57:
 532              	.L42:
 290:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 533              		.loc 1 290 5 is_stmt 1 view .LVU138
 290:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 534              		.loc 1 290 12 is_stmt 0 view .LVU139
 535 0014 024A     		ldr	r2, .L45+4
 536 0016 FFF7FEFF 		bl	tcpip_inpkt
 537              	.LVL58:
 290:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 538              		.loc 1 290 12 view .LVU140
 539 001a FAE7     		b	.L43
 540              	.L46:
 541              		.align	2
 542              	.L45:
 543 001c 00000000 		.word	ethernet_input
 544 0020 00000000 		.word	ip4_input
 545              		.cfi_endproc
 546              	.LFE178:
 548              		.section	.text.tcpip_callback,"ax",%progbits
 549              		.align	1
 550              		.global	tcpip_callback
 551              		.syntax unified
 552              		.thumb
 553              		.thumb_func
 555              	tcpip_callback:
 556              	.LVL59:
 557              	.LFB179:
 310:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 558              		.loc 1 310 1 is_stmt 1 view -0
 559              		.cfi_startproc
 560              		@ args = 0, pretend = 0, frame = 0
 561              		@ frame_needed = 0, uses_anonymous_args = 0
 310:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 562              		.loc 1 310 1 is_stmt 0 view .LVU142
 563 0000 38B5     		push	{r3, r4, r5, lr}
ARM GAS  /tmp/ccRmksMf.s 			page 24


 564              	.LCFI7:
 565              		.cfi_def_cfa_offset 16
 566              		.cfi_offset 3, -16
 567              		.cfi_offset 4, -12
 568              		.cfi_offset 5, -8
 569              		.cfi_offset 14, -4
 570 0002 0546     		mov	r5, r0
 571 0004 0C46     		mov	r4, r1
 311:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 572              		.loc 1 311 3 is_stmt 1 view .LVU143
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 573              		.loc 1 313 3 view .LVU144
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 574              		.loc 1 313 3 view .LVU145
 575 0006 0E48     		ldr	r0, .L53
 576              	.LVL60:
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 577              		.loc 1 313 3 is_stmt 0 view .LVU146
 578 0008 FFF7FEFF 		bl	sys_mbox_valid
 579              	.LVL61:
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 580              		.loc 1 313 3 discriminator 1 view .LVU147
 581 000c 68B1     		cbz	r0, .L52
 582              	.L48:
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 583              		.loc 1 313 3 is_stmt 1 discriminator 3 view .LVU148
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 584              		.loc 1 313 3 discriminator 3 view .LVU149
 315:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 585              		.loc 1 315 3 view .LVU150
 315:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 586              		.loc 1 315 29 is_stmt 0 view .LVU151
 587 000e 0820     		movs	r0, #8
 588 0010 FFF7FEFF 		bl	memp_malloc
 589              	.LVL62:
 316:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 590              		.loc 1 316 3 is_stmt 1 view .LVU152
 316:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 591              		.loc 1 316 6 is_stmt 0 view .LVU153
 592 0014 0146     		mov	r1, r0
 593 0016 80B1     		cbz	r0, .L50
 320:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 594              		.loc 1 320 3 is_stmt 1 view .LVU154
 320:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 595              		.loc 1 320 13 is_stmt 0 view .LVU155
 596 0018 0123     		movs	r3, #1
 597 001a 0370     		strb	r3, [r0]
 321:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 598              		.loc 1 321 3 is_stmt 1 view .LVU156
 321:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 599              		.loc 1 321 24 is_stmt 0 view .LVU157
 600 001c 4560     		str	r5, [r0, #4]
 322:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 601              		.loc 1 322 3 is_stmt 1 view .LVU158
 322:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 602              		.loc 1 322 19 is_stmt 0 view .LVU159
 603 001e 8460     		str	r4, [r0, #8]
ARM GAS  /tmp/ccRmksMf.s 			page 25


 324:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 604              		.loc 1 324 3 is_stmt 1 view .LVU160
 605 0020 0748     		ldr	r0, .L53
 606              	.LVL63:
 324:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 607              		.loc 1 324 3 is_stmt 0 view .LVU161
 608 0022 FFF7FEFF 		bl	sys_mbox_post
 609              	.LVL64:
 325:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 610              		.loc 1 325 3 is_stmt 1 view .LVU162
 325:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 611              		.loc 1 325 10 is_stmt 0 view .LVU163
 612 0026 0020     		movs	r0, #0
 613              	.L49:
 326:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 614              		.loc 1 326 1 view .LVU164
 615 0028 38BD     		pop	{r3, r4, r5, pc}
 616              	.LVL65:
 617              	.L52:
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 618              		.loc 1 313 3 is_stmt 1 discriminator 1 view .LVU165
 313:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 619              		.loc 1 313 3 discriminator 1 view .LVU166
 620 002a 064B     		ldr	r3, .L53+4
 621 002c 40F23912 		movw	r2, #313
 622 0030 0549     		ldr	r1, .L53+8
 623 0032 0648     		ldr	r0, .L53+12
 624 0034 FFF7FEFF 		bl	printf
 625              	.LVL66:
 626 0038 E9E7     		b	.L48
 627              	.LVL67:
 628              	.L50:
 317:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 629              		.loc 1 317 12 is_stmt 0 view .LVU167
 630 003a 4FF0FF30 		mov	r0, #-1
 631              	.LVL68:
 317:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 632              		.loc 1 317 12 view .LVU168
 633 003e F3E7     		b	.L49
 634              	.L54:
 635              		.align	2
 636              	.L53:
 637 0040 00000000 		.word	tcpip_mbox
 638 0044 00000000 		.word	.LC0
 639 0048 00000000 		.word	.LC3
 640 004c 50000000 		.word	.LC2
 641              		.cfi_endproc
 642              	.LFE179:
 644              		.section	.text.tcpip_try_callback,"ax",%progbits
 645              		.align	1
 646              		.global	tcpip_try_callback
 647              		.syntax unified
 648              		.thumb
 649              		.thumb_func
 651              	tcpip_try_callback:
 652              	.LVL69:
 653              	.LFB180:
ARM GAS  /tmp/ccRmksMf.s 			page 26


 346:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 654              		.loc 1 346 1 is_stmt 1 view -0
 655              		.cfi_startproc
 656              		@ args = 0, pretend = 0, frame = 0
 657              		@ frame_needed = 0, uses_anonymous_args = 0
 346:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg;
 658              		.loc 1 346 1 is_stmt 0 view .LVU170
 659 0000 70B5     		push	{r4, r5, r6, lr}
 660              	.LCFI8:
 661              		.cfi_def_cfa_offset 16
 662              		.cfi_offset 4, -16
 663              		.cfi_offset 5, -12
 664              		.cfi_offset 6, -8
 665              		.cfi_offset 14, -4
 666 0002 0646     		mov	r6, r0
 667 0004 0D46     		mov	r5, r1
 347:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 668              		.loc 1 347 3 is_stmt 1 view .LVU171
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 669              		.loc 1 349 3 view .LVU172
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 670              		.loc 1 349 3 view .LVU173
 671 0006 1248     		ldr	r0, .L62
 672              	.LVL70:
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 673              		.loc 1 349 3 is_stmt 0 view .LVU174
 674 0008 FFF7FEFF 		bl	sys_mbox_valid
 675              	.LVL71:
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 676              		.loc 1 349 3 discriminator 1 view .LVU175
 677 000c 70B1     		cbz	r0, .L60
 678              	.L56:
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 679              		.loc 1 349 3 is_stmt 1 discriminator 3 view .LVU176
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 680              		.loc 1 349 3 discriminator 3 view .LVU177
 351:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 681              		.loc 1 351 3 view .LVU178
 351:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 682              		.loc 1 351 29 is_stmt 0 view .LVU179
 683 000e 0820     		movs	r0, #8
 684 0010 FFF7FEFF 		bl	memp_malloc
 685              	.LVL72:
 352:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 686              		.loc 1 352 3 is_stmt 1 view .LVU180
 352:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 687              		.loc 1 352 6 is_stmt 0 view .LVU181
 688 0014 0446     		mov	r4, r0
 689 0016 C0B1     		cbz	r0, .L58
 356:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 690              		.loc 1 356 3 is_stmt 1 view .LVU182
 356:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 691              		.loc 1 356 13 is_stmt 0 view .LVU183
 692 0018 0123     		movs	r3, #1
 693 001a 0370     		strb	r3, [r0]
 357:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 694              		.loc 1 357 3 is_stmt 1 view .LVU184
ARM GAS  /tmp/ccRmksMf.s 			page 27


 357:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 695              		.loc 1 357 24 is_stmt 0 view .LVU185
 696 001c 4660     		str	r6, [r0, #4]
 358:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 697              		.loc 1 358 3 is_stmt 1 view .LVU186
 358:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 698              		.loc 1 358 19 is_stmt 0 view .LVU187
 699 001e 8560     		str	r5, [r0, #8]
 360:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_API, msg);
 700              		.loc 1 360 3 is_stmt 1 view .LVU188
 360:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_API, msg);
 701              		.loc 1 360 7 is_stmt 0 view .LVU189
 702 0020 0146     		mov	r1, r0
 703 0022 0B48     		ldr	r0, .L62
 704              	.LVL73:
 360:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_API, msg);
 705              		.loc 1 360 7 view .LVU190
 706 0024 FFF7FEFF 		bl	sys_mbox_trypost
 707              	.LVL74:
 360:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     memp_free(MEMP_TCPIP_MSG_API, msg);
 708              		.loc 1 360 6 discriminator 1 view .LVU191
 709 0028 40B9     		cbnz	r0, .L61
 710              	.L57:
 365:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 711              		.loc 1 365 1 view .LVU192
 712 002a 70BD     		pop	{r4, r5, r6, pc}
 713              	.LVL75:
 714              	.L60:
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 715              		.loc 1 349 3 is_stmt 1 discriminator 1 view .LVU193
 349:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 716              		.loc 1 349 3 discriminator 1 view .LVU194
 717 002c 094B     		ldr	r3, .L62+4
 718 002e 40F25D12 		movw	r2, #349
 719 0032 0949     		ldr	r1, .L62+8
 720 0034 0948     		ldr	r0, .L62+12
 721 0036 FFF7FEFF 		bl	printf
 722              	.LVL76:
 723 003a E8E7     		b	.L56
 724              	.LVL77:
 725              	.L61:
 361:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return ERR_MEM;
 726              		.loc 1 361 5 view .LVU195
 727 003c 2146     		mov	r1, r4
 728 003e 0820     		movs	r0, #8
 729 0040 FFF7FEFF 		bl	memp_free
 730              	.LVL78:
 362:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 731              		.loc 1 362 5 view .LVU196
 362:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 732              		.loc 1 362 12 is_stmt 0 view .LVU197
 733 0044 4FF0FF30 		mov	r0, #-1
 734 0048 EFE7     		b	.L57
 735              	.LVL79:
 736              	.L58:
 353:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 737              		.loc 1 353 12 view .LVU198
ARM GAS  /tmp/ccRmksMf.s 			page 28


 738 004a 4FF0FF30 		mov	r0, #-1
 739              	.LVL80:
 353:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 740              		.loc 1 353 12 view .LVU199
 741 004e ECE7     		b	.L57
 742              	.L63:
 743              		.align	2
 744              	.L62:
 745 0050 00000000 		.word	tcpip_mbox
 746 0054 00000000 		.word	.LC0
 747 0058 00000000 		.word	.LC3
 748 005c 50000000 		.word	.LC2
 749              		.cfi_endproc
 750              	.LFE180:
 752              		.section	.text.tcpip_send_msg_wait_sem,"ax",%progbits
 753              		.align	1
 754              		.global	tcpip_send_msg_wait_sem
 755              		.syntax unified
 756              		.thumb
 757              		.thumb_func
 759              	tcpip_send_msg_wait_sem:
 760              	.LVL81:
 761              	.LFB181:
 438:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 762              		.loc 1 438 1 is_stmt 1 view -0
 763              		.cfi_startproc
 764              		@ args = 0, pretend = 0, frame = 0
 765              		@ frame_needed = 0, uses_anonymous_args = 0
 438:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 766              		.loc 1 438 1 is_stmt 0 view .LVU201
 767 0000 70B5     		push	{r4, r5, r6, lr}
 768              	.LCFI9:
 769              		.cfi_def_cfa_offset 16
 770              		.cfi_offset 4, -16
 771              		.cfi_offset 5, -12
 772              		.cfi_offset 6, -8
 773              		.cfi_offset 14, -4
 774 0002 0446     		mov	r4, r0
 775 0004 0D46     		mov	r5, r1
 440:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 776              		.loc 1 440 3 is_stmt 1 view .LVU202
 441:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   fn(apimsg);
 777              		.loc 1 441 3 view .LVU203
 778 0006 054E     		ldr	r6, .L66
 779 0008 3046     		mov	r0, r6
 780              	.LVL82:
 441:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   fn(apimsg);
 781              		.loc 1 441 3 is_stmt 0 view .LVU204
 782 000a FFF7FEFF 		bl	sys_mutex_lock
 783              	.LVL83:
 442:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
 784              		.loc 1 442 3 is_stmt 1 view .LVU205
 785 000e 2846     		mov	r0, r5
 786 0010 A047     		blx	r4
 787              	.LVL84:
 443:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return ERR_OK;
 788              		.loc 1 443 3 view .LVU206
ARM GAS  /tmp/ccRmksMf.s 			page 29


 789 0012 3046     		mov	r0, r6
 790 0014 FFF7FEFF 		bl	sys_mutex_unlock
 791              	.LVL85:
 444:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* LWIP_TCPIP_CORE_LOCKING */
 792              		.loc 1 444 3 view .LVU207
 460:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 793              		.loc 1 460 1 is_stmt 0 view .LVU208
 794 0018 0020     		movs	r0, #0
 795 001a 70BD     		pop	{r4, r5, r6, pc}
 796              	.LVL86:
 797              	.L67:
 460:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 798              		.loc 1 460 1 view .LVU209
 799              		.align	2
 800              	.L66:
 801 001c 00000000 		.word	lock_tcpip_core
 802              		.cfi_endproc
 803              	.LFE181:
 805              		.section	.text.tcpip_api_call,"ax",%progbits
 806              		.align	1
 807              		.global	tcpip_api_call
 808              		.syntax unified
 809              		.thumb
 810              		.thumb_func
 812              	tcpip_api_call:
 813              	.LVL87:
 814              	.LFB182:
 474:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 815              		.loc 1 474 1 is_stmt 1 view -0
 816              		.cfi_startproc
 817              		@ args = 0, pretend = 0, frame = 0
 818              		@ frame_needed = 0, uses_anonymous_args = 0
 474:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #if LWIP_TCPIP_CORE_LOCKING
 819              		.loc 1 474 1 is_stmt 0 view .LVU211
 820 0000 70B5     		push	{r4, r5, r6, lr}
 821              	.LCFI10:
 822              		.cfi_def_cfa_offset 16
 823              		.cfi_offset 4, -16
 824              		.cfi_offset 5, -12
 825              		.cfi_offset 6, -8
 826              		.cfi_offset 14, -4
 827 0002 0446     		mov	r4, r0
 828 0004 0D46     		mov	r5, r1
 476:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LOCK_TCPIP_CORE();
 829              		.loc 1 476 3 is_stmt 1 view .LVU212
 477:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   err = fn(call);
 830              		.loc 1 477 3 view .LVU213
 831 0006 064E     		ldr	r6, .L70
 832 0008 3046     		mov	r0, r6
 833              	.LVL88:
 477:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   err = fn(call);
 834              		.loc 1 477 3 is_stmt 0 view .LVU214
 835 000a FFF7FEFF 		bl	sys_mutex_lock
 836              	.LVL89:
 478:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
 837              		.loc 1 478 3 is_stmt 1 view .LVU215
 478:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   UNLOCK_TCPIP_CORE();
ARM GAS  /tmp/ccRmksMf.s 			page 30


 838              		.loc 1 478 9 is_stmt 0 view .LVU216
 839 000e 2846     		mov	r0, r5
 840 0010 A047     		blx	r4
 841              	.LVL90:
 842 0012 0446     		mov	r4, r0
 843              	.LVL91:
 479:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return err;
 844              		.loc 1 479 3 is_stmt 1 view .LVU217
 845 0014 3046     		mov	r0, r6
 846 0016 FFF7FEFF 		bl	sys_mutex_unlock
 847              	.LVL92:
 480:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** #else /* LWIP_TCPIP_CORE_LOCKING */
 848              		.loc 1 480 3 view .LVU218
 512:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 849              		.loc 1 512 1 is_stmt 0 view .LVU219
 850 001a 2046     		mov	r0, r4
 851 001c 70BD     		pop	{r4, r5, r6, pc}
 852              	.LVL93:
 853              	.L71:
 512:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 854              		.loc 1 512 1 view .LVU220
 855 001e 00BF     		.align	2
 856              	.L70:
 857 0020 00000000 		.word	lock_tcpip_core
 858              		.cfi_endproc
 859              	.LFE182:
 861              		.section	.text.tcpip_callbackmsg_new,"ax",%progbits
 862              		.align	1
 863              		.global	tcpip_callbackmsg_new
 864              		.syntax unified
 865              		.thumb
 866              		.thumb_func
 868              	tcpip_callbackmsg_new:
 869              	.LVL94:
 870              	.LFB183:
 532:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 871              		.loc 1 532 1 is_stmt 1 view -0
 872              		.cfi_startproc
 873              		@ args = 0, pretend = 0, frame = 0
 874              		@ frame_needed = 0, uses_anonymous_args = 0
 532:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   struct tcpip_msg *msg = (struct tcpip_msg *)memp_malloc(MEMP_TCPIP_MSG_API);
 875              		.loc 1 532 1 is_stmt 0 view .LVU222
 876 0000 38B5     		push	{r3, r4, r5, lr}
 877              	.LCFI11:
 878              		.cfi_def_cfa_offset 16
 879              		.cfi_offset 3, -16
 880              		.cfi_offset 4, -12
 881              		.cfi_offset 5, -8
 882              		.cfi_offset 14, -4
 883 0002 0546     		mov	r5, r0
 884 0004 0C46     		mov	r4, r1
 533:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 885              		.loc 1 533 3 is_stmt 1 view .LVU223
 533:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 886              		.loc 1 533 47 is_stmt 0 view .LVU224
 887 0006 0820     		movs	r0, #8
 888              	.LVL95:
ARM GAS  /tmp/ccRmksMf.s 			page 31


 533:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (msg == NULL) {
 889              		.loc 1 533 47 view .LVU225
 890 0008 FFF7FEFF 		bl	memp_malloc
 891              	.LVL96:
 534:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return NULL;
 892              		.loc 1 534 3 is_stmt 1 view .LVU226
 534:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     return NULL;
 893              		.loc 1 534 6 is_stmt 0 view .LVU227
 894 000c 0346     		mov	r3, r0
 895 000e 18B1     		cbz	r0, .L72
 537:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 896              		.loc 1 537 3 is_stmt 1 view .LVU228
 537:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.function = function;
 897              		.loc 1 537 13 is_stmt 0 view .LVU229
 898 0010 0222     		movs	r2, #2
 899 0012 0270     		strb	r2, [r0]
 538:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 900              		.loc 1 538 3 is_stmt 1 view .LVU230
 538:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   msg->msg.cb.ctx = ctx;
 901              		.loc 1 538 24 is_stmt 0 view .LVU231
 902 0014 4560     		str	r5, [r0, #4]
 539:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return (struct tcpip_callback_msg *)msg;
 903              		.loc 1 539 3 is_stmt 1 view .LVU232
 539:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return (struct tcpip_callback_msg *)msg;
 904              		.loc 1 539 19 is_stmt 0 view .LVU233
 905 0016 8460     		str	r4, [r0, #8]
 540:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 906              		.loc 1 540 3 is_stmt 1 view .LVU234
 907              	.L72:
 541:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 908              		.loc 1 541 1 is_stmt 0 view .LVU235
 909 0018 1846     		mov	r0, r3
 910              	.LVL97:
 541:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 911              		.loc 1 541 1 view .LVU236
 912 001a 38BD     		pop	{r3, r4, r5, pc}
 541:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 913              		.loc 1 541 1 view .LVU237
 914              		.cfi_endproc
 915              	.LFE183:
 917              		.section	.text.tcpip_callbackmsg_delete,"ax",%progbits
 918              		.align	1
 919              		.global	tcpip_callbackmsg_delete
 920              		.syntax unified
 921              		.thumb
 922              		.thumb_func
 924              	tcpip_callbackmsg_delete:
 925              	.LVL98:
 926              	.LFB184:
 553:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   memp_free(MEMP_TCPIP_MSG_API, msg);
 927              		.loc 1 553 1 is_stmt 1 view -0
 928              		.cfi_startproc
 929              		@ args = 0, pretend = 0, frame = 0
 930              		@ frame_needed = 0, uses_anonymous_args = 0
 553:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   memp_free(MEMP_TCPIP_MSG_API, msg);
 931              		.loc 1 553 1 is_stmt 0 view .LVU239
 932 0000 08B5     		push	{r3, lr}
ARM GAS  /tmp/ccRmksMf.s 			page 32


 933              	.LCFI12:
 934              		.cfi_def_cfa_offset 8
 935              		.cfi_offset 3, -8
 936              		.cfi_offset 14, -4
 937 0002 0146     		mov	r1, r0
 554:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 938              		.loc 1 554 3 is_stmt 1 view .LVU240
 939 0004 0820     		movs	r0, #8
 940              	.LVL99:
 554:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 941              		.loc 1 554 3 is_stmt 0 view .LVU241
 942 0006 FFF7FEFF 		bl	memp_free
 943              	.LVL100:
 555:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 944              		.loc 1 555 1 view .LVU242
 945 000a 08BD     		pop	{r3, pc}
 946              		.cfi_endproc
 947              	.LFE184:
 949              		.section	.text.tcpip_callbackmsg_trycallback,"ax",%progbits
 950              		.align	1
 951              		.global	tcpip_callbackmsg_trycallback
 952              		.syntax unified
 953              		.thumb
 954              		.thumb_func
 956              	tcpip_callbackmsg_trycallback:
 957              	.LVL101:
 958              	.LFB185:
 568:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 959              		.loc 1 568 1 is_stmt 1 view -0
 960              		.cfi_startproc
 961              		@ args = 0, pretend = 0, frame = 0
 962              		@ frame_needed = 0, uses_anonymous_args = 0
 568:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 963              		.loc 1 568 1 is_stmt 0 view .LVU244
 964 0000 10B5     		push	{r4, lr}
 965              	.LCFI13:
 966              		.cfi_def_cfa_offset 8
 967              		.cfi_offset 4, -8
 968              		.cfi_offset 14, -4
 969 0002 0446     		mov	r4, r0
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 970              		.loc 1 569 3 is_stmt 1 view .LVU245
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 971              		.loc 1 569 3 view .LVU246
 972 0004 0848     		ldr	r0, .L81
 973              	.LVL102:
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 974              		.loc 1 569 3 is_stmt 0 view .LVU247
 975 0006 FFF7FEFF 		bl	sys_mbox_valid
 976              	.LVL103:
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 977              		.loc 1 569 3 discriminator 1 view .LVU248
 978 000a 20B1     		cbz	r0, .L80
 979              	.L78:
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 980              		.loc 1 569 3 is_stmt 1 discriminator 3 view .LVU249
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
ARM GAS  /tmp/ccRmksMf.s 			page 33


 981              		.loc 1 569 3 discriminator 3 view .LVU250
 570:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 982              		.loc 1 570 3 view .LVU251
 570:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 983              		.loc 1 570 10 is_stmt 0 view .LVU252
 984 000c 2146     		mov	r1, r4
 985 000e 0648     		ldr	r0, .L81
 986 0010 FFF7FEFF 		bl	sys_mbox_trypost
 987              	.LVL104:
 571:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 988              		.loc 1 571 1 view .LVU253
 989 0014 10BD     		pop	{r4, pc}
 990              	.LVL105:
 991              	.L80:
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 992              		.loc 1 569 3 is_stmt 1 discriminator 1 view .LVU254
 569:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost(&tcpip_mbox, msg);
 993              		.loc 1 569 3 discriminator 1 view .LVU255
 994 0016 054B     		ldr	r3, .L81+4
 995 0018 40F23922 		movw	r2, #569
 996 001c 0449     		ldr	r1, .L81+8
 997 001e 0548     		ldr	r0, .L81+12
 998 0020 FFF7FEFF 		bl	printf
 999              	.LVL106:
 1000 0024 F2E7     		b	.L78
 1001              	.L82:
 1002 0026 00BF     		.align	2
 1003              	.L81:
 1004 0028 00000000 		.word	tcpip_mbox
 1005 002c 00000000 		.word	.LC0
 1006 0030 00000000 		.word	.LC3
 1007 0034 50000000 		.word	.LC2
 1008              		.cfi_endproc
 1009              	.LFE185:
 1011              		.section	.text.tcpip_callbackmsg_trycallback_fromisr,"ax",%progbits
 1012              		.align	1
 1013              		.global	tcpip_callbackmsg_trycallback_fromisr
 1014              		.syntax unified
 1015              		.thumb
 1016              		.thumb_func
 1018              	tcpip_callbackmsg_trycallback_fromisr:
 1019              	.LVL107:
 1020              	.LFB186:
 587:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 1021              		.loc 1 587 1 view -0
 1022              		.cfi_startproc
 1023              		@ args = 0, pretend = 0, frame = 0
 1024              		@ frame_needed = 0, uses_anonymous_args = 0
 587:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   LWIP_ASSERT("Invalid mbox", sys_mbox_valid_val(tcpip_mbox));
 1025              		.loc 1 587 1 is_stmt 0 view .LVU257
 1026 0000 10B5     		push	{r4, lr}
 1027              	.LCFI14:
 1028              		.cfi_def_cfa_offset 8
 1029              		.cfi_offset 4, -8
 1030              		.cfi_offset 14, -4
 1031 0002 0446     		mov	r4, r0
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
ARM GAS  /tmp/ccRmksMf.s 			page 34


 1032              		.loc 1 588 3 is_stmt 1 view .LVU258
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1033              		.loc 1 588 3 view .LVU259
 1034 0004 0848     		ldr	r0, .L87
 1035              	.LVL108:
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1036              		.loc 1 588 3 is_stmt 0 view .LVU260
 1037 0006 FFF7FEFF 		bl	sys_mbox_valid
 1038              	.LVL109:
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1039              		.loc 1 588 3 discriminator 1 view .LVU261
 1040 000a 20B1     		cbz	r0, .L86
 1041              	.L84:
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1042              		.loc 1 588 3 is_stmt 1 discriminator 3 view .LVU262
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1043              		.loc 1 588 3 discriminator 3 view .LVU263
 589:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 1044              		.loc 1 589 3 view .LVU264
 589:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 1045              		.loc 1 589 10 is_stmt 0 view .LVU265
 1046 000c 2146     		mov	r1, r4
 1047 000e 0648     		ldr	r0, .L87
 1048 0010 FFF7FEFF 		bl	sys_mbox_trypost_fromisr
 1049              	.LVL110:
 590:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 1050              		.loc 1 590 1 view .LVU266
 1051 0014 10BD     		pop	{r4, pc}
 1052              	.LVL111:
 1053              	.L86:
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1054              		.loc 1 588 3 is_stmt 1 discriminator 1 view .LVU267
 588:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return sys_mbox_trypost_fromisr(&tcpip_mbox, msg);
 1055              		.loc 1 588 3 discriminator 1 view .LVU268
 1056 0016 054B     		ldr	r3, .L87+4
 1057 0018 4FF41372 		mov	r2, #588
 1058 001c 0449     		ldr	r1, .L87+8
 1059 001e 0548     		ldr	r0, .L87+12
 1060 0020 FFF7FEFF 		bl	printf
 1061              	.LVL112:
 1062 0024 F2E7     		b	.L84
 1063              	.L88:
 1064 0026 00BF     		.align	2
 1065              	.L87:
 1066 0028 00000000 		.word	tcpip_mbox
 1067 002c 00000000 		.word	.LC0
 1068 0030 00000000 		.word	.LC3
 1069 0034 50000000 		.word	.LC2
 1070              		.cfi_endproc
 1071              	.LFE186:
 1073              		.section	.rodata.tcpip_init.str1.4,"aMS",%progbits,1
 1074              		.align	2
 1075              	.LC4:
 1076 0000 6661696C 		.ascii	"failed to create tcpip_thread mbox\000"
 1076      65642074 
 1076      6F206372 
 1076      65617465 
ARM GAS  /tmp/ccRmksMf.s 			page 35


 1076      20746370 
 1077 0023 00       		.align	2
 1078              	.LC5:
 1079 0024 6661696C 		.ascii	"failed to create lock_tcpip_core\000"
 1079      65642074 
 1079      6F206372 
 1079      65617465 
 1079      206C6F63 
 1080 0045 000000   		.align	2
 1081              	.LC6:
 1082 0048 74637069 		.ascii	"tcpip_thread\000"
 1082      705F7468 
 1082      72656164 
 1082      00
 1083              		.section	.text.tcpip_init,"ax",%progbits
 1084              		.align	1
 1085              		.global	tcpip_init
 1086              		.syntax unified
 1087              		.thumb
 1088              		.thumb_func
 1090              	tcpip_init:
 1091              	.LVL113:
 1092              	.LFB187:
 603:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   lwip_init();
 1093              		.loc 1 603 1 view -0
 1094              		.cfi_startproc
 1095              		@ args = 0, pretend = 0, frame = 0
 1096              		@ frame_needed = 0, uses_anonymous_args = 0
 603:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   lwip_init();
 1097              		.loc 1 603 1 is_stmt 0 view .LVU270
 1098 0000 30B5     		push	{r4, r5, lr}
 1099              	.LCFI15:
 1100              		.cfi_def_cfa_offset 12
 1101              		.cfi_offset 4, -12
 1102              		.cfi_offset 5, -8
 1103              		.cfi_offset 14, -4
 1104 0002 83B0     		sub	sp, sp, #12
 1105              	.LCFI16:
 1106              		.cfi_def_cfa_offset 24
 1107 0004 0546     		mov	r5, r0
 1108 0006 0C46     		mov	r4, r1
 604:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 1109              		.loc 1 604 3 is_stmt 1 view .LVU271
 1110 0008 FFF7FEFF 		bl	lwip_init
 1111              	.LVL114:
 606:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   tcpip_init_done_arg = arg;
 1112              		.loc 1 606 3 view .LVU272
 606:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   tcpip_init_done_arg = arg;
 1113              		.loc 1 606 19 is_stmt 0 view .LVU273
 1114 000c 134B     		ldr	r3, .L95
 1115 000e 1D60     		str	r5, [r3]
 607:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_new(&tcpip_mbox, TCPIP_MBOX_SIZE) != ERR_OK) {
 1116              		.loc 1 607 3 is_stmt 1 view .LVU274
 607:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   if (sys_mbox_new(&tcpip_mbox, TCPIP_MBOX_SIZE) != ERR_OK) {
 1117              		.loc 1 607 23 is_stmt 0 view .LVU275
 1118 0010 134B     		ldr	r3, .L95+4
 1119 0012 1C60     		str	r4, [r3]
ARM GAS  /tmp/ccRmksMf.s 			page 36


 608:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create tcpip_thread mbox", 0);
 1120              		.loc 1 608 3 is_stmt 1 view .LVU276
 608:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create tcpip_thread mbox", 0);
 1121              		.loc 1 608 7 is_stmt 0 view .LVU277
 1122 0014 0621     		movs	r1, #6
 1123 0016 1348     		ldr	r0, .L95+8
 1124 0018 FFF7FEFF 		bl	sys_mbox_new
 1125              	.LVL115:
 608:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create tcpip_thread mbox", 0);
 1126              		.loc 1 608 6 discriminator 1 view .LVU278
 1127 001c 70B9     		cbnz	r0, .L93
 1128              	.L90:
 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1129              		.loc 1 609 5 is_stmt 1 discriminator 3 view .LVU279
 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1130              		.loc 1 609 5 discriminator 3 view .LVU280
 612:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create lock_tcpip_core", 0);
 1131              		.loc 1 612 3 view .LVU281
 612:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create lock_tcpip_core", 0);
 1132              		.loc 1 612 7 is_stmt 0 view .LVU282
 1133 001e 1248     		ldr	r0, .L95+12
 1134 0020 FFF7FEFF 		bl	sys_mutex_new
 1135              	.LVL116:
 612:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****     LWIP_ASSERT("failed to create lock_tcpip_core", 0);
 1136              		.loc 1 612 6 discriminator 1 view .LVU283
 1137 0024 90B9     		cbnz	r0, .L94
 1138              	.L91:
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1139              		.loc 1 613 5 is_stmt 1 discriminator 3 view .LVU284
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1140              		.loc 1 613 5 discriminator 3 view .LVU285
 617:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 1141              		.loc 1 617 3 view .LVU286
 1142 0026 1823     		movs	r3, #24
 1143 0028 0093     		str	r3, [sp]
 1144 002a 4FF48063 		mov	r3, #1024
 1145 002e 0022     		movs	r2, #0
 1146 0030 0E49     		ldr	r1, .L95+16
 1147 0032 0F48     		ldr	r0, .L95+20
 1148 0034 FFF7FEFF 		bl	sys_thread_new
 1149              	.LVL117:
 618:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 1150              		.loc 1 618 1 is_stmt 0 view .LVU287
 1151 0038 03B0     		add	sp, sp, #12
 1152              	.LCFI17:
 1153              		.cfi_remember_state
 1154              		.cfi_def_cfa_offset 12
 1155              		@ sp needed
 1156 003a 30BD     		pop	{r4, r5, pc}
 1157              	.LVL118:
 1158              	.L93:
 1159              	.LCFI18:
 1160              		.cfi_restore_state
 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1161              		.loc 1 609 5 is_stmt 1 view .LVU288
 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1162              		.loc 1 609 5 view .LVU289
ARM GAS  /tmp/ccRmksMf.s 			page 37


 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1163              		.loc 1 609 5 discriminator 1 view .LVU290
 609:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1164              		.loc 1 609 5 discriminator 1 view .LVU291
 1165 003c 0D4B     		ldr	r3, .L95+24
 1166 003e 40F26122 		movw	r2, #609
 1167 0042 0D49     		ldr	r1, .L95+28
 1168 0044 0D48     		ldr	r0, .L95+32
 1169 0046 FFF7FEFF 		bl	printf
 1170              	.LVL119:
 1171 004a E8E7     		b	.L90
 1172              	.L94:
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1173              		.loc 1 613 5 view .LVU292
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1174              		.loc 1 613 5 view .LVU293
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1175              		.loc 1 613 5 discriminator 1 view .LVU294
 613:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   }
 1176              		.loc 1 613 5 discriminator 1 view .LVU295
 1177 004c 094B     		ldr	r3, .L95+24
 1178 004e 40F26522 		movw	r2, #613
 1179 0052 0B49     		ldr	r1, .L95+36
 1180 0054 0948     		ldr	r0, .L95+32
 1181 0056 FFF7FEFF 		bl	printf
 1182              	.LVL120:
 1183 005a E4E7     		b	.L91
 1184              	.L96:
 1185              		.align	2
 1186              	.L95:
 1187 005c 00000000 		.word	tcpip_init_done
 1188 0060 00000000 		.word	tcpip_init_done_arg
 1189 0064 00000000 		.word	tcpip_mbox
 1190 0068 00000000 		.word	lock_tcpip_core
 1191 006c 00000000 		.word	tcpip_thread
 1192 0070 48000000 		.word	.LC6
 1193 0074 00000000 		.word	.LC0
 1194 0078 00000000 		.word	.LC4
 1195 007c 50000000 		.word	.LC2
 1196 0080 24000000 		.word	.LC5
 1197              		.cfi_endproc
 1198              	.LFE187:
 1200              		.section	.text.pbuf_free_callback,"ax",%progbits
 1201              		.align	1
 1202              		.global	pbuf_free_callback
 1203              		.syntax unified
 1204              		.thumb
 1205              		.thumb_func
 1207              	pbuf_free_callback:
 1208              	.LVL121:
 1209              	.LFB189:
 632:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 633:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 634:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * A simple wrapper function that allows you to free a pbuf from interrupt context.
 635:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 636:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param p The pbuf (chain) to be dereferenced.
 637:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_OK if callback could be enqueued, an err_t if not
ARM GAS  /tmp/ccRmksMf.s 			page 38


 638:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 639:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 640:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** pbuf_free_callback(struct pbuf *p)
 641:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 1210              		.loc 1 641 1 view -0
 1211              		.cfi_startproc
 1212              		@ args = 0, pretend = 0, frame = 0
 1213              		@ frame_needed = 0, uses_anonymous_args = 0
 1214              		.loc 1 641 1 is_stmt 0 view .LVU297
 1215 0000 08B5     		push	{r3, lr}
 1216              	.LCFI19:
 1217              		.cfi_def_cfa_offset 8
 1218              		.cfi_offset 3, -8
 1219              		.cfi_offset 14, -4
 1220 0002 0146     		mov	r1, r0
 642:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return tcpip_try_callback(pbuf_free_int, p);
 1221              		.loc 1 642 3 is_stmt 1 view .LVU298
 1222              		.loc 1 642 10 is_stmt 0 view .LVU299
 1223 0004 0148     		ldr	r0, .L99
 1224              	.LVL122:
 1225              		.loc 1 642 10 view .LVU300
 1226 0006 FFF7FEFF 		bl	tcpip_try_callback
 1227              	.LVL123:
 643:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 1228              		.loc 1 643 1 view .LVU301
 1229 000a 08BD     		pop	{r3, pc}
 1230              	.L100:
 1231              		.align	2
 1232              	.L99:
 1233 000c 00000000 		.word	pbuf_free_int
 1234              		.cfi_endproc
 1235              	.LFE189:
 1237              		.section	.text.mem_free_callback,"ax",%progbits
 1238              		.align	1
 1239              		.global	mem_free_callback
 1240              		.syntax unified
 1241              		.thumb
 1242              		.thumb_func
 1244              	mem_free_callback:
 1245              	.LVL124:
 1246              	.LFB190:
 644:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** 
 645:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** /**
 646:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * A simple wrapper function that allows you to free heap memory from
 647:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * interrupt context.
 648:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  *
 649:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @param m the heap memory to free
 650:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  * @return ERR_OK if callback could be enqueued, an err_t if not
 651:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****  */
 652:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** err_t
 653:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** mem_free_callback(void *m)
 654:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** {
 1247              		.loc 1 654 1 is_stmt 1 view -0
 1248              		.cfi_startproc
 1249              		@ args = 0, pretend = 0, frame = 0
 1250              		@ frame_needed = 0, uses_anonymous_args = 0
 1251              		.loc 1 654 1 is_stmt 0 view .LVU303
ARM GAS  /tmp/ccRmksMf.s 			page 39


 1252 0000 08B5     		push	{r3, lr}
 1253              	.LCFI20:
 1254              		.cfi_def_cfa_offset 8
 1255              		.cfi_offset 3, -8
 1256              		.cfi_offset 14, -4
 1257 0002 0146     		mov	r1, r0
 655:Middlewares/Third_Party/LwIP/src/api/tcpip.c ****   return tcpip_try_callback(mem_free, m);
 1258              		.loc 1 655 3 is_stmt 1 view .LVU304
 1259              		.loc 1 655 10 is_stmt 0 view .LVU305
 1260 0004 0148     		ldr	r0, .L103
 1261              	.LVL125:
 1262              		.loc 1 655 10 view .LVU306
 1263 0006 FFF7FEFF 		bl	tcpip_try_callback
 1264              	.LVL126:
 656:Middlewares/Third_Party/LwIP/src/api/tcpip.c **** }
 1265              		.loc 1 656 1 view .LVU307
 1266 000a 08BD     		pop	{r3, pc}
 1267              	.L104:
 1268              		.align	2
 1269              	.L103:
 1270 000c 00000000 		.word	mem_free
 1271              		.cfi_endproc
 1272              	.LFE190:
 1274              		.global	lock_tcpip_core
 1275              		.section	.bss.lock_tcpip_core,"aw",%nobits
 1276              		.align	2
 1279              	lock_tcpip_core:
 1280 0000 00000000 		.space	4
 1281              		.section	.bss.tcpip_mbox,"aw",%nobits
 1282              		.align	2
 1285              	tcpip_mbox:
 1286 0000 00000000 		.space	4
 1287              		.section	.bss.tcpip_init_done_arg,"aw",%nobits
 1288              		.align	2
 1291              	tcpip_init_done_arg:
 1292 0000 00000000 		.space	4
 1293              		.section	.bss.tcpip_init_done,"aw",%nobits
 1294              		.align	2
 1297              	tcpip_init_done:
 1298 0000 00000000 		.space	4
 1299              		.text
 1300              	.Letext0:
 1301              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1302              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 1303              		.file 4 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 1304              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 1305              		.file 6 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 1306              		.file 7 "Middlewares/Third_Party/LwIP/system/arch/sys_arch.h"
 1307              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
 1308              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 1309              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 1310              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 1311              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 1312              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 1313              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/tcpip.h"
 1314              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/priv/tcpip_priv.h"
 1315              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/timeouts.h"
ARM GAS  /tmp/ccRmksMf.s 			page 40


 1316              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/init.h"
 1317              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/mem.h"
 1318              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4.h"
 1319              		.file 20 "Middlewares/Third_Party/LwIP/src/include/netif/ethernet.h"
 1320              		.file 21 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
ARM GAS  /tmp/ccRmksMf.s 			page 41


DEFINED SYMBOLS
                            *ABS*:00000000 tcpip.c
     /tmp/ccRmksMf.s:20     .text.tcpip_timeouts_mbox_fetch:00000000 $t
     /tmp/ccRmksMf.s:25     .text.tcpip_timeouts_mbox_fetch:00000000 tcpip_timeouts_mbox_fetch
     /tmp/ccRmksMf.s:126    .text.tcpip_timeouts_mbox_fetch:0000005c $d
     /tmp/ccRmksMf.s:1279   .bss.lock_tcpip_core:00000000 lock_tcpip_core
     /tmp/ccRmksMf.s:131    .rodata.tcpip_thread_handle_msg.str1.4:00000000 $d
     /tmp/ccRmksMf.s:141    .text.tcpip_thread_handle_msg:00000000 $t
     /tmp/ccRmksMf.s:146    .text.tcpip_thread_handle_msg:00000000 tcpip_thread_handle_msg
     /tmp/ccRmksMf.s:255    .text.tcpip_thread_handle_msg:00000054 $d
     /tmp/ccRmksMf.s:262    .text.tcpip_thread:00000000 $t
     /tmp/ccRmksMf.s:267    .text.tcpip_thread:00000000 tcpip_thread
     /tmp/ccRmksMf.s:340    .text.tcpip_thread:00000038 $d
     /tmp/ccRmksMf.s:1297   .bss.tcpip_init_done:00000000 tcpip_init_done
     /tmp/ccRmksMf.s:1291   .bss.tcpip_init_done_arg:00000000 tcpip_init_done_arg
     /tmp/ccRmksMf.s:1285   .bss.tcpip_mbox:00000000 tcpip_mbox
     /tmp/ccRmksMf.s:351    .text.pbuf_free_int:00000000 $t
     /tmp/ccRmksMf.s:356    .text.pbuf_free_int:00000000 pbuf_free_int
     /tmp/ccRmksMf.s:380    .rodata.tcpip_inpkt.str1.4:00000000 $d
     /tmp/ccRmksMf.s:384    .text.tcpip_inpkt:00000000 $t
     /tmp/ccRmksMf.s:390    .text.tcpip_inpkt:00000000 tcpip_inpkt
     /tmp/ccRmksMf.s:490    .text.tcpip_inpkt:00000054 $d
     /tmp/ccRmksMf.s:498    .text.tcpip_input:00000000 $t
     /tmp/ccRmksMf.s:504    .text.tcpip_input:00000000 tcpip_input
     /tmp/ccRmksMf.s:543    .text.tcpip_input:0000001c $d
     /tmp/ccRmksMf.s:549    .text.tcpip_callback:00000000 $t
     /tmp/ccRmksMf.s:555    .text.tcpip_callback:00000000 tcpip_callback
     /tmp/ccRmksMf.s:637    .text.tcpip_callback:00000040 $d
     /tmp/ccRmksMf.s:645    .text.tcpip_try_callback:00000000 $t
     /tmp/ccRmksMf.s:651    .text.tcpip_try_callback:00000000 tcpip_try_callback
     /tmp/ccRmksMf.s:745    .text.tcpip_try_callback:00000050 $d
     /tmp/ccRmksMf.s:753    .text.tcpip_send_msg_wait_sem:00000000 $t
     /tmp/ccRmksMf.s:759    .text.tcpip_send_msg_wait_sem:00000000 tcpip_send_msg_wait_sem
     /tmp/ccRmksMf.s:801    .text.tcpip_send_msg_wait_sem:0000001c $d
     /tmp/ccRmksMf.s:806    .text.tcpip_api_call:00000000 $t
     /tmp/ccRmksMf.s:812    .text.tcpip_api_call:00000000 tcpip_api_call
     /tmp/ccRmksMf.s:857    .text.tcpip_api_call:00000020 $d
     /tmp/ccRmksMf.s:862    .text.tcpip_callbackmsg_new:00000000 $t
     /tmp/ccRmksMf.s:868    .text.tcpip_callbackmsg_new:00000000 tcpip_callbackmsg_new
     /tmp/ccRmksMf.s:918    .text.tcpip_callbackmsg_delete:00000000 $t
     /tmp/ccRmksMf.s:924    .text.tcpip_callbackmsg_delete:00000000 tcpip_callbackmsg_delete
     /tmp/ccRmksMf.s:950    .text.tcpip_callbackmsg_trycallback:00000000 $t
     /tmp/ccRmksMf.s:956    .text.tcpip_callbackmsg_trycallback:00000000 tcpip_callbackmsg_trycallback
     /tmp/ccRmksMf.s:1004   .text.tcpip_callbackmsg_trycallback:00000028 $d
     /tmp/ccRmksMf.s:1012   .text.tcpip_callbackmsg_trycallback_fromisr:00000000 $t
     /tmp/ccRmksMf.s:1018   .text.tcpip_callbackmsg_trycallback_fromisr:00000000 tcpip_callbackmsg_trycallback_fromisr
     /tmp/ccRmksMf.s:1066   .text.tcpip_callbackmsg_trycallback_fromisr:00000028 $d
     /tmp/ccRmksMf.s:1074   .rodata.tcpip_init.str1.4:00000000 $d
     /tmp/ccRmksMf.s:1084   .text.tcpip_init:00000000 $t
     /tmp/ccRmksMf.s:1090   .text.tcpip_init:00000000 tcpip_init
     /tmp/ccRmksMf.s:1187   .text.tcpip_init:0000005c $d
     /tmp/ccRmksMf.s:1201   .text.pbuf_free_callback:00000000 $t
     /tmp/ccRmksMf.s:1207   .text.pbuf_free_callback:00000000 pbuf_free_callback
     /tmp/ccRmksMf.s:1233   .text.pbuf_free_callback:0000000c $d
     /tmp/ccRmksMf.s:1238   .text.mem_free_callback:00000000 $t
     /tmp/ccRmksMf.s:1244   .text.mem_free_callback:00000000 mem_free_callback
     /tmp/ccRmksMf.s:1270   .text.mem_free_callback:0000000c $d
ARM GAS  /tmp/ccRmksMf.s 			page 42


     /tmp/ccRmksMf.s:1276   .bss.lock_tcpip_core:00000000 $d
     /tmp/ccRmksMf.s:1282   .bss.tcpip_mbox:00000000 $d
     /tmp/ccRmksMf.s:1288   .bss.tcpip_init_done_arg:00000000 $d
     /tmp/ccRmksMf.s:1294   .bss.tcpip_init_done:00000000 $d

UNDEFINED SYMBOLS
sys_mutex_unlock
sys_arch_mbox_fetch
sys_mutex_lock
sys_check_timeouts
sys_timeouts_sleeptime
memp_free
pbuf_free
printf
sys_mbox_valid
memp_malloc
sys_mbox_trypost
ethernet_input
ip4_input
sys_mbox_post
sys_mbox_trypost_fromisr
lwip_init
sys_mbox_new
sys_mutex_new
sys_thread_new
mem_free
