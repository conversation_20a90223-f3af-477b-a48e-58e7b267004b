ARM GAS  /tmp/cciabMxY.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"ethernet.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/netif/ethernet.c"
  19              		.section	.text.ethernet_input,"ax",%progbits
  20              		.align	1
  21              		.global	ethernet_input
  22              		.syntax unified
  23              		.thumb
  24              		.thumb_func
  26              	ethernet_input:
  27              	.LVL0:
  28              	.LFB170:
   1:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** /**
   2:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Ethernet common functions
   4:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
   5:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @defgroup ethernet Ethernet
   6:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @ingroup callbackstyle_api
   7:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  */
   8:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
   9:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** /*
  10:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Copyright (c) 2001-2003 Swedish Institute of Computer Science.
  11:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Copyright (c) 2003-2004 Leon Woestenberg <<EMAIL>>
  12:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Copyright (c) 2003-2004 Axon Digital Design B.V., The Netherlands.
  13:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * All rights reserved.
  14:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  15:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Redistribution and use in source and binary forms, with or without modification,
  16:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * are permitted provided that the following conditions are met:
  17:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  18:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  19:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *    this list of conditions and the following disclaimer.
  20:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  21:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *    this list of conditions and the following disclaimer in the documentation
  22:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *    and/or other materials provided with the distribution.
  23:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * 3. The name of the author may not be used to endorse or promote products
  24:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *    derived from this software without specific prior written permission.
  25:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  26:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  27:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  28:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  29:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  30:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
ARM GAS  /tmp/cciabMxY.s 			page 2


  31:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  32:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  33:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  34:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  35:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * OF SUCH DAMAGE.
  36:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  37:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * This file is part of the lwIP TCP/IP stack.
  38:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  39:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  */
  40:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  41:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "lwip/opt.h"
  42:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  43:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_ARP || LWIP_ETHERNET
  44:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  45:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "netif/ethernet.h"
  46:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "lwip/def.h"
  47:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "lwip/stats.h"
  48:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "lwip/etharp.h"
  49:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "lwip/ip.h"
  50:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "lwip/snmp.h"
  51:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  52:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include <string.h>
  53:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  54:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "netif/ppp/ppp_opts.h"
  55:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if PPPOE_SUPPORT
  56:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include "netif/ppp/pppoe.h"
  57:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* PPPOE_SUPPORT */
  58:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  59:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #ifdef LWIP_HOOK_FILENAME
  60:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #include LWIP_HOOK_FILENAME
  61:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif
  62:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  63:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** const struct eth_addr ethbroadcast = {{0xff, 0xff, 0xff, 0xff, 0xff, 0xff}};
  64:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** const struct eth_addr ethzero = {{0, 0, 0, 0, 0, 0}};
  65:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  66:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** /**
  67:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @ingroup lwip_nosys
  68:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Process received ethernet frames. Using this function instead of directly
  69:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * calling ip_input and passing ARP frames through etharp in ethernetif_input,
  70:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * the ARP cache is protected from concurrent access.\n
  71:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Don't call directly, pass to netif_add() and call netif->input().
  72:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  73:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param p the received packet, p->payload pointing to the ethernet header
  74:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param netif the network interface on which the packet was received
  75:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
  76:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @see LWIP_HOOK_UNKNOWN_ETH_PROTOCOL
  77:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @see ETHARP_SUPPORT_VLAN
  78:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @see LWIP_HOOK_VLAN_CHECK
  79:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  */
  80:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** err_t
  81:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** ethernet_input(struct pbuf *p, struct netif *netif)
  82:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** {
  29              		.loc 1 82 1 view -0
  30              		.cfi_startproc
  31              		@ args = 0, pretend = 0, frame = 0
  32              		@ frame_needed = 0, uses_anonymous_args = 0
  33              		.loc 1 82 1 is_stmt 0 view .LVU1
ARM GAS  /tmp/cciabMxY.s 			page 3


  34 0000 70B5     		push	{r4, r5, r6, lr}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 16
  37              		.cfi_offset 4, -16
  38              		.cfi_offset 5, -12
  39              		.cfi_offset 6, -8
  40              		.cfi_offset 14, -4
  41 0002 0446     		mov	r4, r0
  83:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   struct eth_hdr *ethhdr;
  42              		.loc 1 83 3 is_stmt 1 view .LVU2
  84:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   u16_t type;
  43              		.loc 1 84 3 view .LVU3
  85:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_ARP || ETHARP_SUPPORT_VLAN || LWIP_IPV6
  86:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   u16_t next_hdr_offset = SIZEOF_ETH_HDR;
  44              		.loc 1 86 3 view .LVU4
  45              	.LVL1:
  87:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_ARP || ETHARP_SUPPORT_VLAN */
  88:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  89:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LWIP_ASSERT_CORE_LOCKED();
  46              		.loc 1 89 28 view .LVU5
  90:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  91:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   if (p->len <= SIZEOF_ETH_HDR) {
  47              		.loc 1 91 3 view .LVU6
  48              		.loc 1 91 8 is_stmt 0 view .LVU7
  49 0004 4389     		ldrh	r3, [r0, #10]
  50              		.loc 1 91 6 view .LVU8
  51 0006 0E2B     		cmp	r3, #14
  52 0008 1CD9     		bls	.L2
  53 000a 0D46     		mov	r5, r1
  92:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     /* a packet with only an ethernet header (or less) is not valid for us */
  93:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     ETHARP_STATS_INC(etharp.proterr);
  94:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     ETHARP_STATS_INC(etharp.drop);
  95:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     MIB2_STATS_NETIF_INC(netif, ifinerrors);
  96:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     goto free_and_return;
  97:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   }
  98:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
  99:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   if (p->if_idx == NETIF_NO_INDEX) {
  54              		.loc 1 99 3 is_stmt 1 view .LVU9
  55              		.loc 1 99 8 is_stmt 0 view .LVU10
  56 000c C37B     		ldrb	r3, [r0, #15]	@ zero_extendqisi2
  57              		.loc 1 99 6 view .LVU11
  58 000e 1BB9     		cbnz	r3, .L3
 100:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     p->if_idx = netif_get_index(netif);
  59              		.loc 1 100 5 is_stmt 1 view .LVU12
  60              		.loc 1 100 17 is_stmt 0 view .LVU13
  61 0010 91F83030 		ldrb	r3, [r1, #48]	@ zero_extendqisi2
  62 0014 0133     		adds	r3, r3, #1
  63              		.loc 1 100 15 view .LVU14
  64 0016 C373     		strb	r3, [r0, #15]
  65              	.L3:
 101:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   }
 102:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 103:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   /* points to packet payload, which starts with an Ethernet header */
 104:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   ethhdr = (struct eth_hdr *)p->payload;
  66              		.loc 1 104 3 is_stmt 1 view .LVU15
  67              		.loc 1 104 10 is_stmt 0 view .LVU16
  68 0018 6068     		ldr	r0, [r4, #4]
ARM GAS  /tmp/cciabMxY.s 			page 4


  69              	.LVL2:
 105:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE,
 106:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               ("ethernet_input: dest:%"X8_F":%"X8_F":%"X8_F":%"X8_F":%"X8_F":%"X8_F", src:%"X8_F":%
 107:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                (unsigned char)ethhdr->dest.addr[0], (unsigned char)ethhdr->dest.addr[1], (unsigned 
 108:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                (unsigned char)ethhdr->dest.addr[3], (unsigned char)ethhdr->dest.addr[4], (unsigned 
 109:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                (unsigned char)ethhdr->src.addr[0],  (unsigned char)ethhdr->src.addr[1],  (unsigned 
 110:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                (unsigned char)ethhdr->src.addr[3],  (unsigned char)ethhdr->src.addr[4],  (unsigned 
 111:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                lwip_htons(ethhdr->type)));
  70              		.loc 1 111 42 is_stmt 1 view .LVU17
 112:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 113:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   type = ethhdr->type;
  71              		.loc 1 113 3 view .LVU18
  72              		.loc 1 113 8 is_stmt 0 view .LVU19
  73 001a 8689     		ldrh	r6, [r0, #12]	@ unaligned
  74              	.LVL3:
 114:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if ETHARP_SUPPORT_VLAN
 115:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   if (type == PP_HTONS(ETHTYPE_VLAN)) {
 116:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     struct eth_vlan_hdr *vlan = (struct eth_vlan_hdr *)(((char *)ethhdr) + SIZEOF_ETH_HDR);
 117:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     next_hdr_offset = SIZEOF_ETH_HDR + SIZEOF_VLAN_HDR;
 118:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (p->len <= SIZEOF_ETH_HDR + SIZEOF_VLAN_HDR) {
 119:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* a packet with only an ethernet/vlan header (or less) is not valid for us */
 120:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       ETHARP_STATS_INC(etharp.proterr);
 121:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       ETHARP_STATS_INC(etharp.drop);
 122:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       MIB2_STATS_NETIF_INC(netif, ifinerrors);
 123:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       goto free_and_return;
 124:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 125:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if defined(LWIP_HOOK_VLAN_CHECK) || defined(ETHARP_VLAN_CHECK) || defined(ETHARP_VLAN_CHECK_FN) /*
 126:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #ifdef LWIP_HOOK_VLAN_CHECK
 127:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (!LWIP_HOOK_VLAN_CHECK(netif, ethhdr, vlan)) {
 128:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #elif defined(ETHARP_VLAN_CHECK_FN)
 129:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (!ETHARP_VLAN_CHECK_FN(ethhdr, vlan)) {
 130:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #elif defined(ETHARP_VLAN_CHECK)
 131:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (VLAN_ID(vlan) != ETHARP_VLAN_CHECK) {
 132:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif
 133:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* silently ignore this packet: not for our VLAN */
 134:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       pbuf_free(p);
 135:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       return ERR_OK;
 136:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 137:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* defined(LWIP_HOOK_VLAN_CHECK) || defined(ETHARP_VLAN_CHECK) || defined(ETHARP_VLAN_CHECK_
 138:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     type = vlan->tpid;
 139:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   }
 140:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* ETHARP_SUPPORT_VLAN */
 141:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 142:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_ARP_FILTER_NETIF
 143:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   netif = LWIP_ARP_FILTER_NETIF_FN(p, netif, lwip_htons(type));
 144:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_ARP_FILTER_NETIF*/
 145:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 146:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   if (ethhdr->dest.addr[0] & 1) {
  75              		.loc 1 146 3 is_stmt 1 view .LVU20
  76              		.loc 1 146 24 is_stmt 0 view .LVU21
  77 001c 0378     		ldrb	r3, [r0]	@ zero_extendqisi2
  78              		.loc 1 146 6 view .LVU22
  79 001e 13F0010F 		tst	r3, #1
  80 0022 0AD0     		beq	.L4
 147:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     /* this might be a multicast or broadcast packet */
 148:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (ethhdr->dest.addr[0] == LL_IP4_MULTICAST_ADDR_0) {
  81              		.loc 1 148 5 is_stmt 1 view .LVU23
ARM GAS  /tmp/cciabMxY.s 			page 5


  82              		.loc 1 148 8 is_stmt 0 view .LVU24
  83 0024 012B     		cmp	r3, #1
  84 0026 12D0     		beq	.L10
 149:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_IPV4
 150:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if ((ethhdr->dest.addr[1] == LL_IP4_MULTICAST_ADDR_1) &&
 151:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****           (ethhdr->dest.addr[2] == LL_IP4_MULTICAST_ADDR_2)) {
 152:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         /* mark the pbuf as link-layer multicast */
 153:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         p->flags |= PBUF_FLAG_LLMCAST;
 154:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 155:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_IPV4 */
 156:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 157:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_IPV6
 158:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     else if ((ethhdr->dest.addr[0] == LL_IP6_MULTICAST_ADDR_0) &&
 159:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****              (ethhdr->dest.addr[1] == LL_IP6_MULTICAST_ADDR_1)) {
 160:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* mark the pbuf as link-layer multicast */
 161:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       p->flags |= PBUF_FLAG_LLMCAST;
 162:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 163:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_IPV6 */
 164:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     else if (eth_addr_cmp(&ethhdr->dest, &ethbroadcast)) {
  85              		.loc 1 164 10 is_stmt 1 view .LVU25
  86              		.loc 1 164 14 is_stmt 0 view .LVU26
  87 0028 0622     		movs	r2, #6
  88 002a 1E49     		ldr	r1, .L11
  89              	.LVL4:
  90              		.loc 1 164 14 view .LVU27
  91 002c FFF7FEFF 		bl	memcmp
  92              	.LVL5:
  93              		.loc 1 164 13 discriminator 1 view .LVU28
  94 0030 18B9     		cbnz	r0, .L4
 165:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* mark the pbuf as link-layer broadcast */
 166:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       p->flags |= PBUF_FLAG_LLBCAST;
  95              		.loc 1 166 7 is_stmt 1 view .LVU29
  96              		.loc 1 166 8 is_stmt 0 view .LVU30
  97 0032 637B     		ldrb	r3, [r4, #13]	@ zero_extendqisi2
  98              		.loc 1 166 16 view .LVU31
  99 0034 43F00803 		orr	r3, r3, #8
 100 0038 6373     		strb	r3, [r4, #13]
 101              	.L4:
 167:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 168:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   }
 169:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 170:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   switch (type) {
 102              		.loc 1 170 3 is_stmt 1 view .LVU32
 103 003a 082E     		cmp	r6, #8
 104 003c 12D0     		beq	.L6
 105 003e B6F5C16F 		cmp	r6, #1544
 106 0042 1FD0     		beq	.L7
 107              	.LVL6:
 108              	.L2:
 171:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_IPV4 && LWIP_ARP
 172:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     /* IP packet? */
 173:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     case PP_HTONS(ETHTYPE_IP):
 174:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if (!(netif->flags & NETIF_FLAG_ETHARP)) {
 175:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 176:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 177:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* skip Ethernet header (min. size checked above) */
 178:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if (pbuf_remove_header(p, next_hdr_offset)) {
ARM GAS  /tmp/cciabMxY.s 			page 6


 179:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 180:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                     ("ethernet_input: IPv4 packet dropped, too short (%"U16_F"/%"U16_F")\n",
 181:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                      p->tot_len, next_hdr_offset));
 182:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("Can't move over header in packet"));
 183:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 184:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       } else {
 185:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         /* pass to IP layer */
 186:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         ip4_input(p, netif);
 187:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 188:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       break;
 189:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 190:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     case PP_HTONS(ETHTYPE_ARP):
 191:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if (!(netif->flags & NETIF_FLAG_ETHARP)) {
 192:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 193:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 194:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* skip Ethernet header (min. size checked above) */
 195:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if (pbuf_remove_header(p, next_hdr_offset)) {
 196:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 197:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                     ("ethernet_input: ARP response packet dropped, too short (%"U16_F"/%"U16_F")\n"
 198:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                      p->tot_len, next_hdr_offset));
 199:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("Can't move over header in packet"));
 200:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         ETHARP_STATS_INC(etharp.lenerr);
 201:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         ETHARP_STATS_INC(etharp.drop);
 202:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 203:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       } else {
 204:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         /* pass p to ARP module */
 205:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         etharp_input(p, netif);
 206:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 207:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       break;
 208:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_IPV4 && LWIP_ARP */
 209:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if PPPOE_SUPPORT
 210:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     case PP_HTONS(ETHTYPE_PPPOEDISC): /* PPP Over Ethernet Discovery Stage */
 211:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       pppoe_disc_input(netif, p);
 212:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       break;
 213:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 214:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     case PP_HTONS(ETHTYPE_PPPOE): /* PPP Over Ethernet Session Stage */
 215:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       pppoe_data_input(netif, p);
 216:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       break;
 217:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* PPPOE_SUPPORT */
 218:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 219:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if LWIP_IPV6
 220:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     case PP_HTONS(ETHTYPE_IPV6): /* IPv6 */
 221:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       /* skip Ethernet header */
 222:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if ((p->len < next_hdr_offset) || pbuf_remove_header(p, next_hdr_offset)) {
 223:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 224:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                     ("ethernet_input: IPv6 packet dropped, too short (%"U16_F"/%"U16_F")\n",
 225:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                      p->tot_len, next_hdr_offset));
 226:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 227:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       } else {
 228:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         /* pass to IPv6 layer */
 229:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         ip6_input(p, netif);
 230:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 231:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       break;
 232:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_IPV6 */
 233:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 234:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     default:
 235:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #ifdef LWIP_HOOK_UNKNOWN_ETH_PROTOCOL
ARM GAS  /tmp/cciabMxY.s 			page 7


 236:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       if (LWIP_HOOK_UNKNOWN_ETH_PROTOCOL(p, netif) == ERR_OK) {
 237:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         break;
 238:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 239:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif
 240:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       ETHARP_STATS_INC(etharp.proterr);
 241:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       ETHARP_STATS_INC(etharp.drop);
 242:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       MIB2_STATS_NETIF_INC(netif, ifinunknownprotos);
 243:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       goto free_and_return;
 244:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   }
 245:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 246:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   /* This means the pbuf is freed or consumed,
 247:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****      so the caller doesn't have to free it again */
 248:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   return ERR_OK;
 249:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 250:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** free_and_return:
 251:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   pbuf_free(p);
 109              		.loc 1 251 3 view .LVU33
 110 0044 2046     		mov	r0, r4
 111 0046 FFF7FEFF 		bl	pbuf_free
 112              	.LVL7:
 252:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   return ERR_OK;
 113              		.loc 1 252 3 view .LVU34
 114              	.L8:
 253:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** }
 115              		.loc 1 253 1 is_stmt 0 view .LVU35
 116 004a 0020     		movs	r0, #0
 117 004c 70BD     		pop	{r4, r5, r6, pc}
 118              	.LVL8:
 119              	.L10:
 150:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****           (ethhdr->dest.addr[2] == LL_IP4_MULTICAST_ADDR_2)) {
 120              		.loc 1 150 7 is_stmt 1 view .LVU36
 150:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****           (ethhdr->dest.addr[2] == LL_IP4_MULTICAST_ADDR_2)) {
 121              		.loc 1 150 29 is_stmt 0 view .LVU37
 122 004e 4378     		ldrb	r3, [r0, #1]	@ zero_extendqisi2
 150:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****           (ethhdr->dest.addr[2] == LL_IP4_MULTICAST_ADDR_2)) {
 123              		.loc 1 150 10 view .LVU38
 124 0050 002B     		cmp	r3, #0
 125 0052 F2D1     		bne	.L4
 151:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         /* mark the pbuf as link-layer multicast */
 126              		.loc 1 151 29 view .LVU39
 127 0054 8378     		ldrb	r3, [r0, #2]	@ zero_extendqisi2
 150:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****           (ethhdr->dest.addr[2] == LL_IP4_MULTICAST_ADDR_2)) {
 128              		.loc 1 150 61 discriminator 1 view .LVU40
 129 0056 5E2B     		cmp	r3, #94
 130 0058 EFD1     		bne	.L4
 153:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 131              		.loc 1 153 9 is_stmt 1 view .LVU41
 153:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 132              		.loc 1 153 10 is_stmt 0 view .LVU42
 133 005a 637B     		ldrb	r3, [r4, #13]	@ zero_extendqisi2
 153:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 134              		.loc 1 153 18 view .LVU43
 135 005c 43F01003 		orr	r3, r3, #16
 136 0060 6373     		strb	r3, [r4, #13]
 137              	.LVL9:
 153:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 138              		.loc 1 153 18 view .LVU44
ARM GAS  /tmp/cciabMxY.s 			page 8


 139 0062 EAE7     		b	.L4
 140              	.LVL10:
 141              	.L6:
 174:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 142              		.loc 1 174 7 is_stmt 1 view .LVU45
 174:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 143              		.loc 1 174 18 is_stmt 0 view .LVU46
 144 0064 95F82D30 		ldrb	r3, [r5, #45]	@ zero_extendqisi2
 174:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 145              		.loc 1 174 10 view .LVU47
 146 0068 13F0080F 		tst	r3, #8
 147 006c EAD0     		beq	.L2
 178:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 148              		.loc 1 178 7 is_stmt 1 view .LVU48
 178:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 149              		.loc 1 178 11 is_stmt 0 view .LVU49
 150 006e 0E21     		movs	r1, #14
 151 0070 2046     		mov	r0, r4
 152 0072 FFF7FEFF 		bl	pbuf_remove_header
 153              	.LVL11:
 178:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 154              		.loc 1 178 10 discriminator 1 view .LVU50
 155 0076 0028     		cmp	r0, #0
 156 0078 E4D1     		bne	.L2
 186:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 157              		.loc 1 186 9 is_stmt 1 view .LVU51
 158 007a 2946     		mov	r1, r5
 159 007c 2046     		mov	r0, r4
 160 007e FFF7FEFF 		bl	ip4_input
 161              	.LVL12:
 188:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 162              		.loc 1 188 7 view .LVU52
 163 0082 E2E7     		b	.L8
 164              	.L7:
 191:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 165              		.loc 1 191 7 view .LVU53
 191:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 166              		.loc 1 191 18 is_stmt 0 view .LVU54
 167 0084 95F82D30 		ldrb	r3, [r5, #45]	@ zero_extendqisi2
 191:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         goto free_and_return;
 168              		.loc 1 191 10 view .LVU55
 169 0088 13F0080F 		tst	r3, #8
 170 008c DAD0     		beq	.L2
 195:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 171              		.loc 1 195 7 is_stmt 1 view .LVU56
 195:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 172              		.loc 1 195 11 is_stmt 0 view .LVU57
 173 008e 0E21     		movs	r1, #14
 174 0090 2046     		mov	r0, r4
 175 0092 FFF7FEFF 		bl	pbuf_remove_header
 176              	.LVL13:
 195:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 177              		.loc 1 195 10 discriminator 1 view .LVU58
 178 0096 0028     		cmp	r0, #0
 179 0098 D4D1     		bne	.L2
 205:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       }
 180              		.loc 1 205 9 is_stmt 1 view .LVU59
ARM GAS  /tmp/cciabMxY.s 			page 9


 181 009a 2946     		mov	r1, r5
 182 009c 2046     		mov	r0, r4
 183 009e FFF7FEFF 		bl	etharp_input
 184              	.LVL14:
 207:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* LWIP_IPV4 && LWIP_ARP */
 185              		.loc 1 207 7 view .LVU60
 186 00a2 D2E7     		b	.L8
 187              	.L12:
 188              		.align	2
 189              	.L11:
 190 00a4 00000000 		.word	ethbroadcast
 191              		.cfi_endproc
 192              	.LFE170:
 194              		.section	.rodata.ethernet_output.str1.4,"aMS",%progbits,1
 195              		.align	2
 196              	.LC0:
 197 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/netif/ethernet.c\000"
 197      6C657761 
 197      7265732F 
 197      54686972 
 197      645F5061 
 198 0032 0000     		.align	2
 199              	.LC1:
 200 0034 6E657469 		.ascii	"netif->hwaddr_len must be 6 for ethernet_output!\000"
 200      662D3E68 
 200      77616464 
 200      725F6C65 
 200      6E206D75 
 201 0065 000000   		.align	2
 202              	.LC2:
 203 0068 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
 203      7274696F 
 203      6E202225 
 203      73222066 
 203      61696C65 
 204              		.section	.text.ethernet_output,"ax",%progbits
 205              		.align	1
 206              		.global	ethernet_output
 207              		.syntax unified
 208              		.thumb
 209              		.thumb_func
 211              	ethernet_output:
 212              	.LVL15:
 213              	.LFB171:
 254:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 255:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** /**
 256:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @ingroup ethernet
 257:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * Send an ethernet packet on the network using netif->linkoutput().
 258:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * The ethernet header is filled in before sending.
 259:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
 260:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @see LWIP_HOOK_VLAN_SET
 261:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  *
 262:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param netif the lwIP network interface on which to send the packet
 263:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param p the packet to send. pbuf layer must be @ref PBUF_LINK.
 264:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param src the source MAC address to be copied into the ethernet header
 265:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param dst the destination MAC address to be copied into the ethernet header
 266:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @param eth_type ethernet type (@ref lwip_ieee_eth_type)
ARM GAS  /tmp/cciabMxY.s 			page 10


 267:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  * @return ERR_OK if the packet was sent, any other err_t on failure
 268:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****  */
 269:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** err_t
 270:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** ethernet_output(struct netif * netif, struct pbuf * p,
 271:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                 const struct eth_addr * src, const struct eth_addr * dst,
 272:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****                 u16_t eth_type) {
 214              		.loc 1 272 33 view -0
 215              		.cfi_startproc
 216              		@ args = 4, pretend = 0, frame = 0
 217              		@ frame_needed = 0, uses_anonymous_args = 0
 218              		.loc 1 272 33 is_stmt 0 view .LVU62
 219 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 220              	.LCFI1:
 221              		.cfi_def_cfa_offset 24
 222              		.cfi_offset 4, -24
 223              		.cfi_offset 5, -20
 224              		.cfi_offset 6, -16
 225              		.cfi_offset 7, -12
 226              		.cfi_offset 8, -8
 227              		.cfi_offset 14, -4
 228 0004 0546     		mov	r5, r0
 229 0006 0C46     		mov	r4, r1
 230 0008 1646     		mov	r6, r2
 231 000a 1F46     		mov	r7, r3
 273:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   struct eth_hdr *ethhdr;
 232              		.loc 1 273 3 is_stmt 1 view .LVU63
 274:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   u16_t eth_type_be = lwip_htons(eth_type);
 233              		.loc 1 274 3 view .LVU64
 234              		.loc 1 274 23 is_stmt 0 view .LVU65
 235 000c BDF81800 		ldrh	r0, [sp, #24]
 236              	.LVL16:
 237              		.loc 1 274 23 view .LVU66
 238 0010 FFF7FEFF 		bl	lwip_htons
 239              	.LVL17:
 240              		.loc 1 274 23 view .LVU67
 241 0014 8046     		mov	r8, r0
 242              	.LVL18:
 275:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 276:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #if ETHARP_SUPPORT_VLAN && defined(LWIP_HOOK_VLAN_SET)
 277:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   s32_t vlan_prio_vid = LWIP_HOOK_VLAN_SET(netif, p, src, dst, eth_type);
 278:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   if (vlan_prio_vid >= 0) {
 279:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     struct eth_vlan_hdr *vlanhdr;
 280:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 281:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     LWIP_ASSERT("prio_vid must be <= 0xFFFF", vlan_prio_vid <= 0xFFFF);
 282:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 283:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (pbuf_add_header(p, SIZEOF_ETH_HDR + SIZEOF_VLAN_HDR) != 0) {
 284:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       goto pbuf_header_failed;
 285:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 286:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     vlanhdr = (struct eth_vlan_hdr *)(((u8_t *)p->payload) + SIZEOF_ETH_HDR);
 287:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     vlanhdr->tpid     = eth_type_be;
 288:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     vlanhdr->prio_vid = lwip_htons((u16_t)vlan_prio_vid);
 289:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 290:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     eth_type_be = PP_HTONS(ETHTYPE_VLAN);
 291:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   } else
 292:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** #endif /* ETHARP_SUPPORT_VLAN && defined(LWIP_HOOK_VLAN_SET) */
 293:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   {
 294:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     if (pbuf_add_header(p, SIZEOF_ETH_HDR) != 0) {
ARM GAS  /tmp/cciabMxY.s 			page 11


 243              		.loc 1 294 5 is_stmt 1 view .LVU68
 244              		.loc 1 294 9 is_stmt 0 view .LVU69
 245 0016 0E21     		movs	r1, #14
 246 0018 2046     		mov	r0, r4
 247 001a FFF7FEFF 		bl	pbuf_add_header
 248              	.LVL19:
 249              		.loc 1 294 8 discriminator 1 view .LVU70
 250 001e E8B9     		cbnz	r0, .L16
 295:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****       goto pbuf_header_failed;
 296:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****     }
 297:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   }
 298:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 299:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LWIP_ASSERT_CORE_LOCKED();
 251              		.loc 1 299 28 is_stmt 1 view .LVU71
 300:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 301:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   ethhdr = (struct eth_hdr *)p->payload;
 252              		.loc 1 301 3 view .LVU72
 253              		.loc 1 301 10 is_stmt 0 view .LVU73
 254 0020 6168     		ldr	r1, [r4, #4]
 255              	.LVL20:
 302:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   ethhdr->type = eth_type_be;
 256              		.loc 1 302 3 is_stmt 1 view .LVU74
 257              		.loc 1 302 16 is_stmt 0 view .LVU75
 258 0022 A1F80C80 		strh	r8, [r1, #12]	@ unaligned
 303:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   SMEMCPY(&ethhdr->dest, dst, ETH_HWADDR_LEN);
 259              		.loc 1 303 3 is_stmt 1 view .LVU76
 260 0026 3B68     		ldr	r3, [r7]	@ unaligned
 261 0028 0B60     		str	r3, [r1]	@ unaligned
 262 002a BB88     		ldrh	r3, [r7, #4]	@ unaligned
 263 002c 8B80     		strh	r3, [r1, #4]	@ unaligned
 304:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   SMEMCPY(&ethhdr->src,  src, ETH_HWADDR_LEN);
 264              		.loc 1 304 3 view .LVU77
 265 002e 3368     		ldr	r3, [r6]	@ unaligned
 266 0030 C1F80630 		str	r3, [r1, #6]	@ unaligned
 267 0034 B388     		ldrh	r3, [r6, #4]	@ unaligned
 268 0036 4B81     		strh	r3, [r1, #10]	@ unaligned
 305:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 306:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LWIP_ASSERT("netif->hwaddr_len must be 6 for ethernet_output!",
 269              		.loc 1 306 3 view .LVU78
 270              		.loc 1 306 3 view .LVU79
 271 0038 95F82C30 		ldrb	r3, [r5, #44]	@ zero_extendqisi2
 272 003c 062B     		cmp	r3, #6
 273 003e 05D1     		bne	.L18
 274              	.LVL21:
 275              	.L15:
 276              		.loc 1 306 3 discriminator 3 view .LVU80
 277              		.loc 1 306 3 discriminator 3 view .LVU81
 307:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
 308:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE,
 309:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               ("ethernet_output: sending packet %p\n", (void *)p));
 278              		.loc 1 309 67 view .LVU82
 310:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 311:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   /* send the packet */
 312:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   return netif->linkoutput(netif, p);
 279              		.loc 1 312 3 view .LVU83
 280              		.loc 1 312 15 is_stmt 0 view .LVU84
 281 0040 AB69     		ldr	r3, [r5, #24]
ARM GAS  /tmp/cciabMxY.s 			page 12


 282              		.loc 1 312 10 view .LVU85
 283 0042 2146     		mov	r1, r4
 284 0044 2846     		mov	r0, r5
 285 0046 9847     		blx	r3
 286              	.LVL22:
 287              	.L14:
 288              	.LDL1:
 313:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** 
 314:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** pbuf_header_failed:
 315:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS,
 316:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               ("ethernet_output: could not allocate room for header.\n"));
 317:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   LINK_STATS_INC(link.lenerr);
 318:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****   return ERR_BUF;
 319:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** }
 289              		.loc 1 319 1 view .LVU86
 290 0048 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 291              	.LVL23:
 292              	.L18:
 306:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
 293              		.loc 1 306 3 is_stmt 1 discriminator 1 view .LVU87
 306:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
 294              		.loc 1 306 3 discriminator 1 view .LVU88
 295 004c 054B     		ldr	r3, .L19
 296 004e 4FF49972 		mov	r2, #306
 297 0052 0549     		ldr	r1, .L19+4
 298              	.LVL24:
 306:Middlewares/Third_Party/LwIP/src/netif/ethernet.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
 299              		.loc 1 306 3 is_stmt 0 discriminator 1 view .LVU89
 300 0054 0548     		ldr	r0, .L19+8
 301 0056 FFF7FEFF 		bl	printf
 302              	.LVL25:
 303 005a F1E7     		b	.L15
 304              	.LVL26:
 305              	.L16:
 318:Middlewares/Third_Party/LwIP/src/netif/ethernet.c **** }
 306              		.loc 1 318 10 view .LVU90
 307 005c 6FF00100 		mvn	r0, #1
 308 0060 F2E7     		b	.L14
 309              	.L20:
 310 0062 00BF     		.align	2
 311              	.L19:
 312 0064 00000000 		.word	.LC0
 313 0068 34000000 		.word	.LC1
 314 006c 68000000 		.word	.LC2
 315              		.cfi_endproc
 316              	.LFE171:
 318              		.global	ethzero
 319              		.section	.rodata.ethzero,"a"
 320              		.align	2
 323              	ethzero:
 324 0000 00000000 		.space	6
 324      0000
 325              		.global	ethbroadcast
 326              		.section	.rodata.ethbroadcast,"a"
 327              		.align	2
 330              	ethbroadcast:
 331 0000 FFFFFFFF 		.ascii	"\377\377\377\377\377\377"
ARM GAS  /tmp/cciabMxY.s 			page 13


 331      FFFF
 332              		.text
 333              	.Letext0:
 334              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 335              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 336              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 337              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 338              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 339              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 340              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 341              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 342              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 343              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 344              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ethernet.h"
 345              		.file 13 "Middlewares/Third_Party/LwIP/src/include/netif/ethernet.h"
 346              		.file 14 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 347              		.file 15 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 348              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
 349              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4.h"
 350              		.file 18 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ieee.h"
 351              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/etharp.h"
ARM GAS  /tmp/cciabMxY.s 			page 14


DEFINED SYMBOLS
                            *ABS*:00000000 ethernet.c
     /tmp/cciabMxY.s:20     .text.ethernet_input:00000000 $t
     /tmp/cciabMxY.s:26     .text.ethernet_input:00000000 ethernet_input
     /tmp/cciabMxY.s:190    .text.ethernet_input:000000a4 $d
     /tmp/cciabMxY.s:330    .rodata.ethbroadcast:00000000 ethbroadcast
     /tmp/cciabMxY.s:195    .rodata.ethernet_output.str1.4:00000000 $d
     /tmp/cciabMxY.s:205    .text.ethernet_output:00000000 $t
     /tmp/cciabMxY.s:211    .text.ethernet_output:00000000 ethernet_output
     /tmp/cciabMxY.s:312    .text.ethernet_output:00000064 $d
     /tmp/cciabMxY.s:323    .rodata.ethzero:00000000 ethzero
     /tmp/cciabMxY.s:320    .rodata.ethzero:00000000 $d
     /tmp/cciabMxY.s:327    .rodata.ethbroadcast:00000000 $d

UNDEFINED SYMBOLS
memcmp
pbuf_free
pbuf_remove_header
ip4_input
etharp_input
lwip_htons
pbuf_add_header
printf
