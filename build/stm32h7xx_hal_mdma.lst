ARM GAS  /tmp/ccsqcIPI.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"stm32h7xx_hal_mdma.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c"
  19              		.section	.text.MDMA_SetConfig,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	MDMA_SetConfig:
  26              	.LVL0:
  27              	.LFB163:
   1:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
   2:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   ******************************************************************************
   3:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @file    stm32h7xx_hal_mdma.c
   4:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * <AUTHOR> Application Team
   5:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  This file provides firmware functions to manage the following
   6:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *         functionalities of the Master Direct Memory Access (MDMA) peripheral:
   7:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *           + Initialization/de-initialization functions
   8:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *           + I/O operation functions
   9:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *           + Peripheral State and errors functions
  10:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   ******************************************************************************
  11:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @attention
  12:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
  13:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * Copyright (c) 2017 STMicroelectronics.
  14:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * All rights reserved.
  15:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
  16:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * This software is licensed under terms that can be found in the LICENSE file
  17:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * in the root directory of this software component.
  18:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * If no LICENSE file comes with this software, it is provided AS-IS.
  19:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
  20:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   ******************************************************************************
  21:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   @verbatim
  22:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   ==============================================================================
  23:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                         ##### How to use this driver #####
  24:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   ==============================================================================
  25:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   [..]
  26:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****    (#) Enable and configure the peripheral to be connected to the MDMA Channel
  27:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        (except for internal SRAM/FLASH memories: no initialization is
  28:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        necessary) please refer to Reference manual for connection between peripherals
  29:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        and MDMA requests.
  30:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  31:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****    (#)
ARM GAS  /tmp/ccsqcIPI.s 			page 2


  32:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        For a given Channel use HAL_MDMA_Init function to program the required configuration through
  33:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        transfer request , channel priority, data endianness, Source increment, destination incremen
  34:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        source data size, destination data size, data alignment, source Burst, destination Burst ,
  35:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        buffer Transfer Length, Transfer Trigger Mode (buffer transfer, block transfer, repeated blo
  36:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        or full transfer) source and destination block address offset, mask address and data.
  37:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  38:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        If using the MDMA in linked list mode then use function HAL_MDMA_LinkedList_CreateNode to fi
  39:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        Note that parameters given to the function HAL_MDMA_Init corresponds always to the node zero
  40:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        Use function HAL_MDMA_LinkedList_AddNode to connect the created node to the linked list at a
  41:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        User can make a linked list circular using function HAL_MDMA_LinkedList_EnableCircularMode ,
  42:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        last node of the list to the first one in order to make the list circular.
  43:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        In this case the linked list will loop on node 1 : first node connected after the initial tr
  44:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  45:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       -@-   The initial transfer itself (node 0 corresponding to the Init).
  46:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             User can disable the circular mode using function HAL_MDMA_LinkedList_DisableCircularMo
  47:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             the connection between last node and first one.
  48:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  49:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        Function HAL_MDMA_LinkedList_RemoveNode can be used to remove (disconnect) a node from the t
  50:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        When a linked list is circular (last node connected to first one), if removing node1  (node 
  51:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        the linked list remains circular and node 2 becomes the first one.
  52:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        Note that if the linked list is made circular the transfer will loop infinitely (or until ab
  53:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  54:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
  55:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        (+) User can select the transfer trigger mode (parameter TransferTriggerMode) to define the 
  56:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****            transfer upon a request :
  57:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****              (++) MDMA_BUFFER_TRANSFER : each request triggers a transfer of BufferTransferLength d
  58:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                with BufferTransferLength defined within the HAL_MDMA_Init.
  59:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****              (++) MDMA_BLOCK_TRANSFER : each request triggers a transfer of a block
  60:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                with block size defined within the function HAL_MDMA_Start/HAL_MDMA_Start_IT
  61:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                or within the current linked list node parameters.
  62:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****              (++) MDMA_REPEAT_BLOCK_TRANSFER : each request triggers a transfer of a number of bloc
  63:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                with block size and number of blocks defined within the function HAL_MDMA_Start/HAL_
  64:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                or within the current linked list node parameters.
  65:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****              (++) MDMA_FULL_TRANSFER : each request triggers a full transfer
  66:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               all blocks and all nodes(if a linked list has been created using HAL_MDMA_LinkedList_
  67:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  68:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      *** Polling mode IO operation ***
  69:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      =================================
  70:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
  71:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use HAL_MDMA_Start() to start MDMA transfer after the configuration of Source
  72:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               address and destination address and the Length of data to be transferred.
  73:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use HAL_MDMA_PollForTransfer() to poll for the end of current transfer or a transfer 
  74:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****              In this case a fixed Timeout can be configured by User depending from his application.
  75:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use HAL_MDMA_Abort() function to abort the current transfer : blocking method this AP
  76:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               when the abort ends or timeout (should not be called from an interrupt service routin
  77:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  78:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      *** Interrupt mode IO operation ***
  79:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      ===================================
  80:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
  81:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Configure the MDMA interrupt priority using HAL_NVIC_SetPriority()
  82:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Enable the MDMA IRQ handler using HAL_NVIC_EnableIRQ()
  83:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use HAL_MDMA_Start_IT() to start MDMA transfer after the configuration of
  84:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               Source address and destination address and the Length of data to be transferred. In t
  85:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               case the MDMA interrupt is configured.
  86:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use HAL_MDMA_IRQHandler() called under MDMA_IRQHandler() Interrupt subroutine
  87:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) At the end of data transfer HAL_MDMA_IRQHandler() function is executed and user can
  88:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               add his own function by customization of function pointer XferCpltCallback and
ARM GAS  /tmp/ccsqcIPI.s 			page 3


  89:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               XferErrorCallback (i.e a member of MDMA handle structure).
  90:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  91:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use HAL_MDMA_Abort_IT() function to abort the current transfer : non-blocking method.
  92:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               then the callback XferAbortCallback (if specified  by the user) is asserted once the 
  93:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (could be called from an interrupt service routine).
  94:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
  95:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           (+) Use functions HAL_MDMA_RegisterCallback and HAL_MDMA_UnRegisterCallback respectevely 
  96:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               from the following list :
  97:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) XferCpltCallback            : transfer complete callback.
  98:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) XferBufferCpltCallback      : buffer transfer complete callback.
  99:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) XferBlockCpltCallback       : block transfer complete callback.
 100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) XferRepeatBlockCpltCallback : repeated block transfer complete callback.
 101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) XferErrorCallback           : transfer error callback.
 102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) XferAbortCallback           : transfer abort complete callback.
 103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
 105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****          (+)  If the transfer Request corresponds to SW request (MDMA_REQUEST_SW) User can use func
 106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               trigger requests manually. Function HAL_MDMA_GenerateSWRequest must be used with the 
 107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) This function returns an error if used while the Transfer has ended or not start
 108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) If used while the current request has not been served yet (current request trans
 109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                 this function returns an error and the new request is ignored.
 110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               Generally this function should be used in conjunctions with the MDMA callbacks:
 112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) example 1:
 113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) Configure a transfer with request set to MDMA_REQUEST_SW and trigger mode se
 114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) Register a callback for buffer transfer complete (using callback ID set to H
 115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) After calling HAL_MDMA_Start_IT the MDMA will issue the transfer of a first 
 116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) When the buffer transfer complete callback is asserted first buffer has been
 117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                    request using HAL_MDMA_GenerateSWRequest.
 118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               (++) example 2:
 120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) Configure a transfer with request set to MDMA_REQUEST_SW and trigger mode se
 121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) Register a callback for block transfer complete (using callback ID HAL_MDMA_
 122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) After calling HAL_MDMA_Start_IT the MDMA will issue the transfer of a first 
 123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                  (+++) When the block transfer complete callback is asserted the first block has be
 124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                    for a new block transfer request using HAL_MDMA_GenerateSWRequest.
 125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]  Use HAL_MDMA_GetState() function to return the MDMA state and HAL_MDMA_GetError() in case
 127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      *** MDMA HAL driver macros list ***
 129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      =============================================
 130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      [..]
 131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****        Below the list of most used macros in MDMA HAL driver.
 132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_ENABLE: Enable the specified MDMA Channel.
 134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_DISABLE: Disable the specified MDMA Channel.
 135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_GET_FLAG: Get the MDMA Channel pending flags.
 136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_CLEAR_FLAG: Clear the MDMA Channel pending flags.
 137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_ENABLE_IT: Enable the specified MDMA Channel interrupts.
 138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_DISABLE_IT: Disable the specified MDMA Channel interrupts.
 139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) __HAL_MDMA_GET_IT_SOURCE: Check whether the specified MDMA Channel interrupt has occurred
 140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      [..]
 142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (@) You can refer to the header file of the MDMA HAL driver for more useful macros.
 143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
 145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 4


 146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   @endverbatim
 147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /* Includes ------------------------------------------------------------------*/
 150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** #include "stm32h7xx_hal.h"
 151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup STM32H7xx_HAL_Driver
 153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
 154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @defgroup MDMA  MDMA
 157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief MDMA HAL module driver
 158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
 159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** #ifdef HAL_MDMA_MODULE_ENABLED
 162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /* Private typedef -----------------------------------------------------------*/
 164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /* Private constants ---------------------------------------------------------*/
 165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Private_Constants
 166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  * @{
 167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  */
 168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** #define HAL_TIMEOUT_MDMA_ABORT    5U    /* 5 ms */
 169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** #define HAL_MDMA_CHANNEL_SIZE     0x40U /* an MDMA instance channel size is 64 byte  */
 170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
 172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /* Private macro -------------------------------------------------------------*/
 174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /* Private variables ---------------------------------------------------------*/
 175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /* Private function prototypes -----------------------------------------------*/
 176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Private_Functions_Prototypes
 177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
 178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** static void MDMA_SetConfig(MDMA_HandleTypeDef *hmdma, uint32_t SrcAddress, uint32_t DstAddress, uin
 180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** static void MDMA_Init(MDMA_HandleTypeDef *hmdma);
 181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
 184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Exported_Functions MDMA Exported Functions
 187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
 188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Exported_Functions_Group1
 191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
 192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @verbatim
 193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
 194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****              ##### Initialization and de-initialization functions  #####
 195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
 196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
 197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     This section provides functions allowing to :
 198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       Initialize and de-initialize the MDMA channel.
 199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       Register and Unregister MDMA callbacks
 200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
 201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     The HAL_MDMA_Init() function follows the MDMA channel configuration procedures as described in
 202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     reference manual.
ARM GAS  /tmp/ccsqcIPI.s 			page 5


 203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     The HAL_MDMA_DeInit function allows to deinitialize the MDMA channel.
 204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     HAL_MDMA_RegisterCallback and  HAL_MDMA_UnRegisterCallback functions allows
 205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     respectevely to register/unregister an MDMA callback function.
 206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @endverbatim
 208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
 209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Initializes the MDMA according to the specified
 213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *         parameters in the MDMA_InitTypeDef and create the associated handle.
 214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma: Pointer to a MDMA_HandleTypeDef structure that contains
 215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *               the configuration information for the specified MDMA Channel.
 216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_Init(MDMA_HandleTypeDef *hmdma)
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart = HAL_GetTick();
 221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
 224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_STREAM_ALL_INSTANCE(hmdma->Instance));
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_PRIORITY(hmdma->Init.Priority));
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_ENDIANNESS_MODE(hmdma->Init.Endianness));
 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_REQUEST(hmdma->Init.Request));
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_INC(hmdma->Init.SourceInc));
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_INC(hmdma->Init.DestinationInc));
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_DATASIZE(hmdma->Init.SourceDataSize));
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_DATASIZE(hmdma->Init.DestDataSize));
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DATA_ALIGNMENT(hmdma->Init.DataAlignment));
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_BURST(hmdma->Init.SourceBurst));
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_BURST(hmdma->Init.DestBurst));
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BUFFER_TRANSFER_LENGTH(hmdma->Init.BufferTransferLength));
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_TRIGGER_MODE(hmdma->Init.TransferTriggerMode));
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(hmdma->Init.SourceBlockAddressOffset));
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(hmdma->Init.DestBlockAddressOffset));
 244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Allocate lock resource */
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
 248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Change MDMA peripheral state */
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->State = HAL_MDMA_STATE_BUSY;
 251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Disable the MDMA channel */
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_MDMA_DISABLE(hmdma);
 254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check if the MDMA channel is effectively disabled */
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   while((hmdma->Instance->CCR & MDMA_CCR_EN) != 0U)
 257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Check for the Timeout */
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if((HAL_GetTick() - tickstart ) > HAL_TIMEOUT_MDMA_ABORT)
ARM GAS  /tmp/ccsqcIPI.s 			page 6


 260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Update error code */
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->ErrorCode = HAL_MDMA_ERROR_TIMEOUT;
 263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Change the MDMA state */
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->State = HAL_MDMA_STATE_ERROR;
 266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       return HAL_ERROR;
 268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Initialize the MDMA channel registers */
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_Init(hmdma);
 273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Reset the MDMA first/last linkedlist node addresses and node counter */
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->FirstLinkedListNodeAddress  = 0;
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LastLinkedListNodeAddress   = 0;
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LinkedListNodeCounter  = 0;
 278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Initialize the error code */
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->ErrorCode = HAL_MDMA_ERROR_NONE;
 281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Initialize the MDMA state */
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->State = HAL_MDMA_STATE_READY;
 284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  DeInitializes the MDMA peripheral
 290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma: pointer to a MDMA_HandleTypeDef structure that contains
 291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *               the configuration information for the specified MDMA Channel.
 292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_DeInit(MDMA_HandleTypeDef *hmdma)
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
 299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Disable the selected MDMA Channelx */
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_MDMA_DISABLE(hmdma);
 305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Reset MDMA Channel control register */
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CCR  = 0;
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTCR = 0;
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBNDTR = 0;
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CSAR = 0;
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CDAR = 0;
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBRUR = 0;
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CLAR = 0;
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTBR = 0;
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMAR = 0;
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMDR = 0;
ARM GAS  /tmp/ccsqcIPI.s 			page 7


 317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Clear all flags */
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_MDMA_CLEAR_FLAG(hmdma,(MDMA_FLAG_TE | MDMA_FLAG_CTC | MDMA_FLAG_BRT | MDMA_FLAG_BT | MDMA_F
 320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Reset the  MDMA first/last linkedlist node addresses and node counter */
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->FirstLinkedListNodeAddress  = 0;
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LastLinkedListNodeAddress   = 0;
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LinkedListNodeCounter  = 0;
 325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Initialize the error code */
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->ErrorCode = HAL_MDMA_ERROR_NONE;
 328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Initialize the MDMA state */
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->State = HAL_MDMA_STATE_RESET;
 331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Release Lock */
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
 334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Config the Post request Mask address and Mask data
 340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma      : pointer to a MDMA_HandleTypeDef structure that contains
 341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                               the configuration information for the specified MDMA Channel.
 342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  MaskAddress: specifies the address to be updated (written) with MaskData after a reques
 343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  MaskData:    specifies the value to be written to MaskAddress after a request is served
 344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                      MaskAddress and MaskData could be used to automatically clear a peripheral
 345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_ConfigPostRequestMask(MDMA_HandleTypeDef *hmdma, uint32_t MaskAddress, u
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef  status = HAL_OK;
 350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
 353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
 359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
 361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* if HW request set Post Request MaskAddress and MaskData,  */
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if((hmdma->Instance->CTCR & MDMA_CTCR_SWRM) == 0U)
 364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Set the HW request clear Mask and Data */
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->Instance->CMAR = MaskAddress;
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->Instance->CMDR = MaskData;
 368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /*
 370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       -If the request is done by SW : BWM could be set to 1 or 0.
 371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       -If the request is done by a peripheral :
 372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****          If mask address not set (0) => BWM must be set to 0
 373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****          If mask address set (different than 0) => BWM could be set to 1 or 0
ARM GAS  /tmp/ccsqcIPI.s 			page 8


 374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       */
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(MaskAddress == 0U)
 376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->Instance->CTCR &=  ~MDMA_CTCR_BWM;
 378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       else
 380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->Instance->CTCR |=  MDMA_CTCR_BWM;
 382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else
 385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Return error status */
 387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       status =  HAL_ERROR;
 388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
 391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Return error status */
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     status =  HAL_ERROR;
 394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Release Lock */
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
 397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return status;
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Register callbacks
 403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma:                pointer to a MDMA_HandleTypeDef structure that contains
 404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                               the configuration information for the specified MDMA Channel.
 405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  CallbackID:           User Callback identifier
 406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  pCallback:            pointer to callbacsk function.
 407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_RegisterCallback(MDMA_HandleTypeDef *hmdma, HAL_MDMA_CallbackIDTypeDef C
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef status = HAL_OK;
 412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
 415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
 421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
 423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     switch (CallbackID)
 425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_CPLT_CB_ID:
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferCpltCallback = pCallback;
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_BUFFERCPLT_CB_ID:
ARM GAS  /tmp/ccsqcIPI.s 			page 9


 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBufferCpltCallback = pCallback;
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_BLOCKCPLT_CB_ID:
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBlockCpltCallback = pCallback;
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_REPBLOCKCPLT_CB_ID:
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferRepeatBlockCpltCallback = pCallback;
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_ERROR_CB_ID:
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferErrorCallback = pCallback;
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_ABORT_CB_ID:
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferAbortCallback = pCallback;
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     default:
 451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
 455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Return error status */
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     status =  HAL_ERROR;
 458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Release Lock */
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
 462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return status;
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  UnRegister callbacks
 468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma:                 pointer to a MDMA_HandleTypeDef structure that contains
 469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                               the configuration information for the specified MDMA Channel.
 470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  CallbackID:           User Callback identifier
 471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                               a HAL_MDMA_CallbackIDTypeDef ENUM as parameter.
 472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_UnRegisterCallback(MDMA_HandleTypeDef *hmdma, HAL_MDMA_CallbackIDTypeDef
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef status = HAL_OK;
 477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
 480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
 486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
ARM GAS  /tmp/ccsqcIPI.s 			page 10


 488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     switch (CallbackID)
 490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_CPLT_CB_ID:
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferCpltCallback = NULL;
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_BUFFERCPLT_CB_ID:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBufferCpltCallback = NULL;
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_BLOCKCPLT_CB_ID:
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBlockCpltCallback = NULL;
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_REPBLOCKCPLT_CB_ID:
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferRepeatBlockCpltCallback = NULL;
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_ERROR_CB_ID:
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferErrorCallback = NULL;
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case  HAL_MDMA_XFER_ABORT_CB_ID:
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferAbortCallback = NULL;
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     case   HAL_MDMA_XFER_ALL_CB_ID:
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferCpltCallback = NULL;
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBufferCpltCallback = NULL;
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBlockCpltCallback = NULL;
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferRepeatBlockCpltCallback = NULL;
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferErrorCallback = NULL;
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferAbortCallback = NULL;
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     default:
 525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       status = HAL_ERROR;
 526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
 530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     status = HAL_ERROR;
 532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Release Lock */
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
 536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return status;
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
 542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Exported_Functions_Group2
ARM GAS  /tmp/ccsqcIPI.s 			page 11


 545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  *
 546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @verbatim
 547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
 548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                       #####  Linked list operation functions  #####
 549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
 550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]  This section provides functions allowing to:
 551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Create a linked list node
 552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Add a node to the MDMA linked list
 553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Remove a node from the MDMA linked list
 554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Enable/Disable linked list circular mode
 555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @endverbatim
 556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
 557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Initializes an MDMA Link Node according to the specified
 561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *         parameters in the pMDMA_LinkedListNodeConfig .
 562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  pNode: Pointer to a MDMA_LinkNodeTypeDef structure that contains Linked list node
 563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *         registers configurations.
 564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  pNodeConfig: Pointer to a MDMA_LinkNodeConfTypeDef structure that contains
 565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *               the configuration information for the specified MDMA Linked List Node.
 566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_LinkedList_CreateNode(MDMA_LinkNodeTypeDef *pNode, MDMA_LinkNodeConfType
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t addressMask;
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t blockoffset;
 572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral state */
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((pNode == NULL) || (pNodeConfig == NULL))
 575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_PRIORITY(pNodeConfig->Init.Priority));
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_ENDIANNESS_MODE(pNodeConfig->Init.Endianness));
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_REQUEST(pNodeConfig->Init.Request));
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_INC(pNodeConfig->Init.SourceInc));
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_INC(pNodeConfig->Init.DestinationInc));
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_DATASIZE(pNodeConfig->Init.SourceDataSize));
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_DATASIZE(pNodeConfig->Init.DestDataSize));
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DATA_ALIGNMENT(pNodeConfig->Init.DataAlignment));
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_BURST(pNodeConfig->Init.SourceBurst));
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_BURST(pNodeConfig->Init.DestBurst));
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BUFFER_TRANSFER_LENGTH(pNodeConfig->Init.BufferTransferLength));
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_TRIGGER_MODE(pNodeConfig->Init.TransferTriggerMode));
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(pNodeConfig->Init.SourceBlockAddressOffset));
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(pNodeConfig->Init.DestBlockAddressOffset));
 594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_LENGTH(pNodeConfig->BlockDataLength));
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_COUNT(pNodeConfig->BlockCount));
 597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure next Link node Address Register to zero */
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CLAR =  0;
 601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 12


 602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure the Link Node registers*/
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CTBR   = 0;
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CMAR   = 0;
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CMDR   = 0;
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->Reserved = 0;
 607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Write new CTCR Register value */
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CTCR =  pNodeConfig->Init.SourceInc | pNodeConfig->Init.DestinationInc | \
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       pNodeConfig->Init.DataAlignment| pNodeConfig->Init.SourceBurst            | \
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNodeConfig->Init.DestBurst                                             | \
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           ((pNodeConfig->Init.BufferTransferLength - 1U) << MDMA_CTCR_TLEN_Pos) | \
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             pNodeConfig->Init.TransferTriggerMode;
 615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* If SW request set the CTCR register to SW Request Mode*/
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(pNodeConfig->Init.Request == MDMA_REQUEST_SW)
 618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CTCR |= MDMA_CTCR_SWRM;
 620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /*
 623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   -If the request is done by SW : BWM could be set to 1 or 0.
 624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   -If the request is done by a peripheral :
 625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      If mask address not set (0) => BWM must be set to 0
 626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****      If mask address set (different than 0) => BWM could be set to 1 or 0
 627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((pNodeConfig->Init.Request == MDMA_REQUEST_SW) || (pNodeConfig->PostRequestMaskAddress != 0U))
 629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CTCR |=  MDMA_CTCR_BWM;
 631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Set the new CBNDTR Register value */
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CBNDTR = ((pNodeConfig->BlockCount - 1U) << MDMA_CBNDTR_BRC_Pos) & MDMA_CBNDTR_BRC;
 635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* if block source address offset is negative set the Block Repeat Source address Update Mode to 
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(pNodeConfig->Init.SourceBlockAddressOffset < 0)
 638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBNDTR |= MDMA_CBNDTR_BRSUM;
 640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : source repeat block offset */
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     blockoffset = (uint32_t)(- pNodeConfig->Init.SourceBlockAddressOffset);
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR = blockoffset & 0x0000FFFFU;
 643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
 645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : source repeat block offset */
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR = (((uint32_t) pNodeConfig->Init.SourceBlockAddressOffset) & 0x0000FFFFU);
 648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* if block destination address offset is negative set the Block Repeat destination address Updat
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(pNodeConfig->Init.DestBlockAddressOffset < 0)
 652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBNDTR |= MDMA_CBNDTR_BRDUM;
 654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : destination repeat block offset */
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     blockoffset = (uint32_t)(- pNodeConfig->Init.DestBlockAddressOffset);
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
ARM GAS  /tmp/ccsqcIPI.s 			page 13


 659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : destination repeat block offset */
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR |= ((((uint32_t)pNodeConfig->Init.DestBlockAddressOffset) & 0x0000FFFFU) << MDMA_C
 662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure MDMA Link Node data length */
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CBNDTR |=  pNodeConfig->BlockDataLength;
 666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure MDMA Link Node destination address */
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CDAR = pNodeConfig->DstAddress;
 669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure MDMA Link Node Source address */
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CSAR = pNodeConfig->SrcAddress;
 672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* if HW request set the HW request and the requet CleraMask and ClearData MaskData,  */
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(pNodeConfig->Init.Request != MDMA_REQUEST_SW)
 675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the HW request in CTBR register  */
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CTBR = pNodeConfig->Init.Request & MDMA_CTBR_TSEL;
 678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the HW request clear Mask and Data */
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CMAR = pNodeConfig->PostRequestMaskAddress;
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CMDR = pNodeConfig->PostRequestMaskData;
 681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   addressMask = pNodeConfig->SrcAddress & 0xFF000000U;
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*The AHBSbus is used as source (read operation) on channel x */
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CTBR |= MDMA_CTBR_SBUS;
 688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   addressMask = pNodeConfig->DstAddress & 0xFF000000U;
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*The AHB bus is used as destination (write operation) on channel x */
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CTBR |= MDMA_CTBR_DBUS;
 695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Connect a node to the linked list.
 702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma    : Pointer to a MDMA_HandleTypeDef structure that contains
 703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                    the configuration information for the specified MDMA Channel.
 704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  pNewNode : Pointer to a MDMA_LinkNodeTypeDef structure that contains Linked list node
 705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                    to be add to the list.
 706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param pPrevNode : Pointer to the new node position in the linked list or zero to insert the ne
 707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                    at the end of the list
 708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
 709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_LinkedList_AddNode(MDMA_HandleTypeDef *hmdma, MDMA_LinkNodeTypeDef *pNew
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_LinkNodeTypeDef *pNode;
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t counter = 0, nodeInserted = 0;
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
ARM GAS  /tmp/ccsqcIPI.s 			page 14


 716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((hmdma == NULL) || (pNewNode == NULL))
 719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
 725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
 727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change MDMA peripheral state */
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_BUSY;
 730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Check if this is the first node (after the Inititlization node) */
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if((uint32_t)hmdma->FirstLinkedListNodeAddress == 0U)
 733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(pPrevNode == NULL)
 735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* if this is the first node after the initialization
 737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         connect this node to the node 0 by updating
 738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         the MDMA channel CLAR register to this node address */
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->Instance->CLAR = (uint32_t)pNewNode;
 740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Set the MDMA handle First linked List node*/
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = pNewNode;
 742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /*reset New node link */
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNewNode->CLAR = 0;
 745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update the Handle last node address */
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LastLinkedListNodeAddress = pNewNode;
 748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LinkedListNodeCounter = 1;
 750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       else
 752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hal_status = HAL_ERROR;
 754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else if(hmdma->FirstLinkedListNodeAddress != pNewNode)
 757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Check if the node to insert already exists*/
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       pNode = hmdma->FirstLinkedListNodeAddress;
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       while((counter < hmdma->LinkedListNodeCounter) && (hal_status == HAL_OK))
 761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if(pNode->CLAR == (uint32_t)pNewNode)
 763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hal_status = HAL_ERROR; /* error this node already exist in the linked list and it is not
 765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNode = (MDMA_LinkNodeTypeDef *)pNode->CLAR;
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         counter++;
 768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(hal_status == HAL_OK)
 771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Check if the previous node is the last one in the current list or zero */
ARM GAS  /tmp/ccsqcIPI.s 			page 15


 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if((pPrevNode == hmdma->LastLinkedListNodeAddress) || (pPrevNode == NULL))
 774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* insert the new node at the end of the list */
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           pNewNode->CLAR = hmdma->LastLinkedListNodeAddress->CLAR;
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LastLinkedListNodeAddress->CLAR = (uint32_t)pNewNode;
 778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle last node address */
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LastLinkedListNodeAddress = pNewNode;
 780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Increment the linked list node counter */
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LinkedListNodeCounter++;
 782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         else
 784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /*insert the new node after the pPreviousNode node */
 786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           pNode = hmdma->FirstLinkedListNodeAddress;
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           counter = 0;
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           while((counter < hmdma->LinkedListNodeCounter) && (nodeInserted == 0U))
 789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             counter++;
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             if(pNode == pPrevNode)
 792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             {
 793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               /*Insert the new node after the previous one */
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               pNewNode->CLAR = pNode->CLAR;
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               pNode->CLAR = (uint32_t)pNewNode;
 796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               /* Increment the linked list node counter */
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               hmdma->LinkedListNodeCounter++;
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               nodeInserted = 1;
 799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             else
 801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             {
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               pNode = (MDMA_LinkNodeTypeDef *)pNode->CLAR;
 803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           }
 805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           if(nodeInserted == 0U)
 807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             hal_status = HAL_ERROR;
 809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           }
 810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else
 814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hal_status = HAL_ERROR;
 816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
 820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_READY;
 822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return hal_status;
 824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
 826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
 829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 16


 830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Return error status */
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_BUSY;
 832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Disconnect/Remove a node from the transfer linked list.
 837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma : Pointer to a MDMA_HandleTypeDef structure that contains
 838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                 the configuration information for the specified MDMA Channel.
 839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  pNode : Pointer to a MDMA_LinkNodeTypeDef structure that contains Linked list node
 840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                 to be removed from the list.
 841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
 842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_LinkedList_RemoveNode(MDMA_HandleTypeDef *hmdma, MDMA_LinkNodeTypeDef *p
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_LinkNodeTypeDef *ptmpNode;
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t counter = 0, nodeDeleted = 0;
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((hmdma == NULL) || (pNode == NULL))
 852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
 858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
 860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change MDMA peripheral state */
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_BUSY;
 863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* If first and last node are null (no nodes in the list) : return error*/
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(((uint32_t)hmdma->FirstLinkedListNodeAddress == 0U) || ((uint32_t)hmdma->LastLinkedListNodeA
 866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hal_status = HAL_ERROR;
 868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else if(hmdma->FirstLinkedListNodeAddress == pNode) /* Deleting first node */
 870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Delete 1st node */
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(hmdma->LastLinkedListNodeAddress == pNode)
 873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /*if the last node is at the same time the first one (1 single node after the init node 0)
 875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         then update the last node too */
 876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = 0;
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LastLinkedListNodeAddress  = 0;
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LinkedListNodeCounter = 0;
 880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->Instance->CLAR = 0;
 882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       else
 884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if((uint32_t)hmdma->FirstLinkedListNodeAddress == hmdma->LastLinkedListNodeAddress->CLAR)
 886:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
ARM GAS  /tmp/ccsqcIPI.s 			page 17


 887:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* if last node is looping to first (circular list) one update the last node connection *
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LastLinkedListNodeAddress->CLAR = pNode->CLAR;
 889:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 890:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 891:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* if deleting the first node after the initialization
 892:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         connect the next node to the node 0 by updating
 893:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         the MDMA channel CLAR register to this node address */
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->Instance->CLAR = pNode->CLAR;
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = (MDMA_LinkNodeTypeDef *)hmdma->Instance->CLAR;
 896:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update the Handle node counter */
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LinkedListNodeCounter--;
 898:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 899:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 900:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else /* Deleting any other node */
 901:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 902:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /*Deleted node is not the first one : find it  */
 903:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       ptmpNode = hmdma->FirstLinkedListNodeAddress;
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       while((counter < hmdma->LinkedListNodeCounter) && (nodeDeleted == 0U))
 905:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         counter++;
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if(ptmpNode->CLAR == ((uint32_t)pNode))
 908:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 909:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* if deleting the last node */
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           if(pNode == hmdma->LastLinkedListNodeAddress)
 911:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 912:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             /*Update the linked list last node address in the handle*/
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             hmdma->LastLinkedListNodeAddress = ptmpNode;
 914:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           }
 915:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* update the next node link after deleting pMDMA_LinkedListNode */
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           ptmpNode->CLAR = pNode->CLAR;
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           nodeDeleted = 1;
 918:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle node counter */
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LinkedListNodeCounter--;
 920:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 921:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         else
 922:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           ptmpNode = (MDMA_LinkNodeTypeDef *)ptmpNode->CLAR;
 924:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 925:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 926:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(nodeDeleted == 0U)
 928:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 929:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* last node reashed without finding the node to delete : return error */
 930:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hal_status = HAL_ERROR;
 931:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 932:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 933:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 934:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
 936:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_READY;
 938:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return hal_status;
 940:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 941:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
 942:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 943:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
ARM GAS  /tmp/ccsqcIPI.s 			page 18


 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
 945:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 946:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Return error status */
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_BUSY;
 948:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 950:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 951:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 952:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Make the linked list circular by connecting the last node to the first.
 953:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma : Pointer to a MDMA_HandleTypeDef structure that contains
 954:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                 the configuration information for the specified MDMA Channel.
 955:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
 956:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
 957:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_LinkedList_EnableCircularMode(MDMA_HandleTypeDef *hmdma)
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 960:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 961:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
 963:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
 965:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 966:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 967:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
 969:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
 971:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 972:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change MDMA peripheral state */
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_BUSY;
 974:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 975:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* If first and last node are null (no nodes in the list) : return error*/
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(((uint32_t)hmdma->FirstLinkedListNodeAddress == 0U) || ((uint32_t)hmdma->LastLinkedListNodeA
 977:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 978:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hal_status = HAL_ERROR;
 979:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 980:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else
 981:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 982:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* to enable circular mode Last Node should be connected to first node */
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->LastLinkedListNodeAddress->CLAR = (uint32_t)hmdma->FirstLinkedListNodeAddress;
 984:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 985:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 986:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 987:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process unlocked */
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
 989:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->State = HAL_MDMA_STATE_READY;
 991:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return hal_status;
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 994:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 995:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
 996:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Disable the linked list circular mode by setting the last node connection to null
 997:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma : Pointer to a MDMA_HandleTypeDef structure that contains
 998:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                 the configuration information for the specified MDMA Channel.
 999:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1000:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
ARM GAS  /tmp/ccsqcIPI.s 			page 19


1001:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_LinkedList_DisableCircularMode(MDMA_HandleTypeDef *hmdma)
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
1004:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1005:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1007:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1009:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1010:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1011:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
1013:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
1015:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1016:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change MDMA peripheral state */
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_BUSY;
1018:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1019:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* If first and last node are null (no nodes in the list) : return error*/
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(((uint32_t)hmdma->FirstLinkedListNodeAddress == 0U) || ((uint32_t)hmdma->LastLinkedListNodeA
1021:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1022:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hal_status = HAL_ERROR;
1023:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1024:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else
1025:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1026:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* to disable circular mode Last Node should be connected to NULL */
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->LastLinkedListNodeAddress->CLAR = 0;
1028:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1029:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1030:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1031:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process unlocked */
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_UNLOCK(hmdma);
1033:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->State = HAL_MDMA_STATE_READY;
1035:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return hal_status;
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1038:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1039:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1040:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
1041:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1042:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1043:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Exported_Functions_Group3
1044:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  *
1045:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @verbatim
1046:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
1047:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                       #####  IO operation functions  #####
1048:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
1049:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]  This section provides functions allowing to:
1050:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Configure the source, destination address and data length and Start MDMA transfer
1051:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Configure the source, destination address and data length and
1052:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           Start MDMA transfer with interrupt
1053:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Abort MDMA transfer
1054:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Poll for transfer complete
1055:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Generate a SW request (when Request is set to MDMA_REQUEST_SW)
1056:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Handle MDMA interrupt request
1057:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 20


1058:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @endverbatim
1059:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
1060:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1061:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1062:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1063:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Starts the MDMA Transfer.
1064:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma           : pointer to a MDMA_HandleTypeDef structure that contains
1065:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                           the configuration information for the specified MDMA Channel.
1066:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  SrcAddress      : The source memory Buffer address
1067:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  DstAddress      : The destination memory Buffer address
1068:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  BlockDataLength : The length of a block transfer in bytes
1069:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  BlockCount      : The number of a blocks to be transfer
1070:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1071:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1072:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_Start(MDMA_HandleTypeDef *hmdma, uint32_t SrcAddress, uint32_t DstAddres
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1074:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_LENGTH(BlockDataLength));
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_COUNT(BlockCount));
1077:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1078:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1080:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1082:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1083:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1084:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
1086:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
1088:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1089:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change MDMA peripheral state */
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_BUSY;
1091:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1092:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Initialize the error code */
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_NONE;
1094:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1095:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Disable the peripheral */
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_DISABLE(hmdma);
1097:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1098:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Configure the source, destination address and the data length */
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     MDMA_SetConfig(hmdma, SrcAddress, DstAddress, BlockDataLength, BlockCount);
1100:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1101:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Enable the Peripheral */
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_ENABLE(hmdma);
1103:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(hmdma->Init.Request == MDMA_REQUEST_SW)
1105:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1106:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* activate If SW request mode*/
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->Instance->CCR |=  MDMA_CCR_SWRQ;
1108:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1109:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1110:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1111:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1112:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
1114:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 21


1115:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Return error status */
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_BUSY;
1117:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1118:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1121:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1122:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1123:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Starts the MDMA Transfer with interrupts enabled.
1124:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma           : pointer to a MDMA_HandleTypeDef structure that contains
1125:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                           the configuration information for the specified MDMA Channel.
1126:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  SrcAddress      : The source memory Buffer address
1127:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  DstAddress      : The destination memory Buffer address
1128:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  BlockDataLength : The length of a block transfer in bytes
1129:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  BlockCount      : The number of a blocks to be transfer
1130:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1131:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1132:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_Start_IT(MDMA_HandleTypeDef *hmdma, uint32_t SrcAddress, uint32_t DstAdd
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1134:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_LENGTH(BlockDataLength));
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_COUNT(BlockCount));
1137:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1138:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1140:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1142:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1143:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1144:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Process locked */
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_LOCK(hmdma);
1146:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_READY == hmdma->State)
1148:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1149:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change MDMA peripheral state */
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_BUSY;
1151:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1152:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Initialize the error code */
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_NONE;
1154:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1155:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Disable the peripheral */
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_DISABLE(hmdma);
1157:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1158:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Configure the source, destination address and the data length */
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     MDMA_SetConfig(hmdma, SrcAddress, DstAddress, BlockDataLength, BlockCount);
1160:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1161:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Enable Common interrupts i.e Transfer Error IT and Channel Transfer Complete IT*/
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_ENABLE_IT(hmdma, (MDMA_IT_TE | MDMA_IT_CTC));
1163:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(hmdma->XferBlockCpltCallback != NULL)
1165:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1166:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* if Block transfer complete Callback is set enable the corresponding IT*/
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_ENABLE_IT(hmdma, MDMA_IT_BT);
1168:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1169:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(hmdma->XferRepeatBlockCpltCallback != NULL)
1171:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
ARM GAS  /tmp/ccsqcIPI.s 			page 22


1172:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* if Repeated Block transfer complete Callback is set enable the corresponding IT*/
1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_ENABLE_IT(hmdma, MDMA_IT_BRT);
1174:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1175:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(hmdma->XferBufferCpltCallback != NULL)
1177:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1178:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* if buffer transfer complete Callback is set enable the corresponding IT*/
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_ENABLE_IT(hmdma, MDMA_IT_BFTC);
1180:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1181:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1182:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Enable the Peripheral */
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_ENABLE(hmdma);
1184:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(hmdma->Init.Request == MDMA_REQUEST_SW)
1186:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1187:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* activate If SW request mode*/
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->Instance->CCR |=  MDMA_CCR_SWRQ;
1189:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1190:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1191:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1192:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1193:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
1195:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1196:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Return error status */
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_BUSY;
1198:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1199:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1202:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1203:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1204:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Aborts the MDMA Transfer.
1205:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma  : pointer to a MDMA_HandleTypeDef structure that contains
1206:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                 the configuration information for the specified MDMA Channel.
1207:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *
1208:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @note  After disabling a MDMA Channel, a check for wait until the MDMA Channel is
1209:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *        effectively disabled is added. If a Channel is disabled
1210:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *        while a data transfer is ongoing, the current data will be transferred
1211:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *        and the Channel will be effectively disabled only after the transfer of
1212:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *        this single data is finished.
1213:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1214:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1215:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_Abort(MDMA_HandleTypeDef *hmdma)
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart =  HAL_GetTick();
1218:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1221:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1224:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_BUSY != hmdma->State)
1226:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_NO_XFER;
1228:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 23


1229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process Unlocked */
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
1231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Disable all the transfer interrupts */
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_DISABLE_IT(hmdma, (MDMA_IT_TE | MDMA_IT_CTC | MDMA_IT_BT | MDMA_IT_BRT | MDMA_IT_BFT
1238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Disable the channel */
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_DISABLE(hmdma);
1241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Check if the MDMA Channel is effectively disabled */
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     while((hmdma->Instance->CCR & MDMA_CCR_EN) != 0U)
1244:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1245:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Check for the Timeout */
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if( (HAL_GetTick()  - tickstart ) > HAL_TIMEOUT_MDMA_ABORT)
1247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1248:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code */
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_TIMEOUT;
1250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1251:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Process Unlocked */
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         __HAL_UNLOCK(hmdma);
1253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1254:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Change the MDMA state */
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->State = HAL_MDMA_STATE_ERROR;
1256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         return HAL_ERROR;
1258:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1260:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1261:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Clear all interrupt flags */
1262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_CLEAR_FLAG(hmdma, (MDMA_FLAG_TE | MDMA_FLAG_CTC | MDMA_FLAG_BT | MDMA_FLAG_BRT | MDM
1263:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1264:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process Unlocked */
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
1266:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Change the MDMA state*/
1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_READY;
1269:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1270:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
1272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1273:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1274:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Aborts the MDMA Transfer in Interrupt mode.
1276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma  : pointer to a MDMA_HandleTypeDef structure that contains
1277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                 the configuration information for the specified MDMA Channel.
1278:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1279:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_Abort_IT(MDMA_HandleTypeDef *hmdma)
1281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1282:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1284:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
ARM GAS  /tmp/ccsqcIPI.s 			page 24


1286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1287:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_BUSY != hmdma->State)
1289:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1290:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* No transfer ongoing */
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_NO_XFER;
1292:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1294:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1296:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1297:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set Abort State  */
1298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_ABORT;
1299:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Disable the stream */
1301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_DISABLE(hmdma);
1302:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1303:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
1305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1306:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Polling for transfer complete.
1309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma:          pointer to a MDMA_HandleTypeDef structure that contains
1310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                        the configuration information for the specified MDMA Channel.
1311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  CompleteLevel: Specifies the MDMA level complete.
1312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  Timeout:       Timeout duration.
1313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_PollForTransfer(MDMA_HandleTypeDef *hmdma, HAL_MDMA_LevelCompleteTypeDef
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t levelFlag, errorFlag;
1318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart;
1319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1320:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_LEVEL_COMPLETE(CompleteLevel));
1322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1325:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1328:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(HAL_MDMA_STATE_BUSY != hmdma->State)
1330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1331:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* No transfer ongoing */
1332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_NO_XFER;
1333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1337:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Get the level transfer complete flag */
1338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   levelFlag = ((CompleteLevel == HAL_MDMA_FULL_TRANSFER)  ? MDMA_FLAG_CTC  : \
1339:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                (CompleteLevel == HAL_MDMA_BUFFER_TRANSFER)? MDMA_FLAG_BFTC : \
1340:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                (CompleteLevel == HAL_MDMA_BLOCK_TRANSFER) ? MDMA_FLAG_BT   : \
1341:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                MDMA_FLAG_BRT);
1342:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 25


1343:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1344:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Get timeout */
1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   tickstart = HAL_GetTick();
1346:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   while(__HAL_MDMA_GET_FLAG(hmdma, levelFlag) == 0U)
1348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if((__HAL_MDMA_GET_FLAG(hmdma, MDMA_FLAG_TE) != 0U))
1350:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1351:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Get the transfer error source flag */
1352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       errorFlag = hmdma->Instance->CESR;
1353:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_TED) == 0U)
1355:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1356:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Read Transfer error  */
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_READ_XFER;
1358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1359:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       else
1360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1361:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Write Transfer error */
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_WRITE_XFER;
1363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1364:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_TEMD) != 0U)
1366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Error Mask Data */
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_MASK_DATA;
1369:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1370:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_TELD) != 0U)
1372:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1373:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Error Linked list */
1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_LINKED_LIST;
1375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1376:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_ASE) != 0U)
1378:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1379:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Address/Size alignment error */
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_ALIGNMENT;
1381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1382:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_BSE) != 0U)
1384:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1385:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Block Size error */
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_BLOCK_SIZE;
1387:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1388:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (void) HAL_MDMA_Abort(hmdma); /* if error then abort the current transfer */
1390:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1391:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /*
1392:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         Note that the Abort function will
1393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           - Clear all transfer flags
1394:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           - Unlock
1395:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           - Set the State
1396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       */
1397:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       return HAL_ERROR;
1399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 26


1400:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1401:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1402:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Check for the Timeout */
1403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(Timeout != HAL_MAX_DELAY)
1404:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(((HAL_GetTick() - tickstart ) > Timeout) || (Timeout == 0U))
1406:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1407:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code */
1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_TIMEOUT;
1409:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         (void) HAL_MDMA_Abort(hmdma); /* if timeout then abort the current transfer */
1411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1412:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /*
1413:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           Note that the Abort function will
1414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             - Clear all transfer flags
1415:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             - Unlock
1416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             - Set the State
1417:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         */
1418:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         return HAL_ERROR;
1420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1421:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1423:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Clear the transfer level flag */
1425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(CompleteLevel == HAL_MDMA_BUFFER_TRANSFER)
1426:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_BFTC);
1428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1429:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1430:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else if(CompleteLevel == HAL_MDMA_BLOCK_TRANSFER)
1431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_CLEAR_FLAG(hmdma, (MDMA_FLAG_BFTC | MDMA_FLAG_BT));
1433:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1434:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else if(CompleteLevel == HAL_MDMA_REPEAT_BLOCK_TRANSFER)
1436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_CLEAR_FLAG(hmdma, (MDMA_FLAG_BFTC | MDMA_FLAG_BT | MDMA_FLAG_BRT));
1438:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else if(CompleteLevel == HAL_MDMA_FULL_TRANSFER)
1440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_CLEAR_FLAG(hmdma, (MDMA_FLAG_BRT | MDMA_FLAG_BT | MDMA_FLAG_BFTC | MDMA_FLAG_CTC));
1442:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process unlocked */
1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
1445:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_READY;
1447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1449:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1450:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1451:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1452:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return HAL_OK;
1454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1455:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1456:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
ARM GAS  /tmp/ccsqcIPI.s 			page 27


1457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Generate an MDMA SW request trigger to activate the request on the given Channel.
1458:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma:       pointer to a MDMA_HandleTypeDef structure that contains
1459:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                     the configuration information for the specified MDMA Stream.
1460:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1462:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_StatusTypeDef HAL_MDMA_GenerateSWRequest(MDMA_HandleTypeDef *hmdma)
1463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t request_mode;
1465:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1466:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
1467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma == NULL)
1468:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1470:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1471:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1472:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Get the softawre request mode */
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   request_mode = hmdma->Instance->CTCR & MDMA_CTCR_SWRM;
1474:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((hmdma->Instance->CCR &  MDMA_CCR_EN) == 0U)
1476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1477:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* if no Transfer on going (MDMA enable bit not set) return error */
1478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_NO_XFER;
1479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else if(((hmdma->Instance->CISR &  MDMA_CISR_CRQA) != 0U) || (request_mode == 0U))
1483:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1484:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* if an MDMA ongoing request has not yet end or if request mode is not SW request return error
1485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->ErrorCode = HAL_MDMA_ERROR_BUSY;
1486:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_ERROR;
1488:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1490:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1491:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the SW request bit to activate the request on the Channel */
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CCR |= MDMA_CCR_SWRQ;
1493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return HAL_OK;
1495:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1498:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1499:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Handles MDMA interrupt request.
1500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma: pointer to a MDMA_HandleTypeDef structure that contains
1501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *               the configuration information for the specified MDMA Channel.
1502:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval None
1503:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** void HAL_MDMA_IRQHandler(MDMA_HandleTypeDef *hmdma)
1505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __IO uint32_t count = 0;
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t timeout = SystemCoreClock / 9600U;
1508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t generalIntFlag, errorFlag;
1510:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1511:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* General Interrupt Flag management ****************************************/
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   generalIntFlag =  1UL << ((((uint32_t)hmdma->Instance - (uint32_t)(MDMA_Channel0))/HAL_MDMA_CHANN
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((MDMA->GISR0 & generalIntFlag) == 0U)
ARM GAS  /tmp/ccsqcIPI.s 			page 28


1514:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1515:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     return; /* the  General interrupt flag for the current channel is down , nothing to do */
1516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Transfer Error Interrupt management ***************************************/
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((__HAL_MDMA_GET_FLAG(hmdma, MDMA_FLAG_TE) != 0U))
1520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(__HAL_MDMA_GET_IT_SOURCE(hmdma, MDMA_IT_TE) != 0U)
1522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1523:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Disable the transfer error interrupt */
1524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_DISABLE_IT(hmdma, MDMA_IT_TE);
1525:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1526:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Get the transfer error source flag */
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       errorFlag = hmdma->Instance->CESR;
1528:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_TED) == 0U)
1530:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Read Transfer error  */
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_READ_XFER;
1533:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1534:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       else
1535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1536:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Write Transfer error */
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_WRITE_XFER;
1538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1539:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_TEMD) != 0U)
1541:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1542:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Error Mask Data */
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_MASK_DATA;
1544:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1545:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_TELD) != 0U)
1547:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1548:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Error Linked list */
1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_LINKED_LIST;
1550:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1551:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_ASE) != 0U)
1553:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1554:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Address/Size alignment error */
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_ALIGNMENT;
1556:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1557:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if((errorFlag & MDMA_CESR_BSE) != 0U)
1559:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1560:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update error code : Block Size error error */
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->ErrorCode |= HAL_MDMA_ERROR_BLOCK_SIZE;
1562:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1563:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1564:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Clear the transfer error flags */
1565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_TE);
1566:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1567:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1568:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Buffer Transfer Complete Interrupt management ******************************/
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((__HAL_MDMA_GET_FLAG(hmdma, MDMA_FLAG_BFTC) != 0U))
ARM GAS  /tmp/ccsqcIPI.s 			page 29


1571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(__HAL_MDMA_GET_IT_SOURCE(hmdma, MDMA_IT_BFTC) != 0U)
1573:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Clear the buffer transfer complete flag */
1575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_BFTC);
1576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(hmdma->XferBufferCpltCallback != NULL)
1578:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1579:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Buffer transfer callback */
1580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->XferBufferCpltCallback(hmdma);
1581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Block Transfer Complete Interrupt management ******************************/
1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((__HAL_MDMA_GET_FLAG(hmdma, MDMA_FLAG_BT) != 0U))
1587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(__HAL_MDMA_GET_IT_SOURCE(hmdma, MDMA_IT_BT) != 0U)
1589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Clear the block transfer complete flag */
1591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_BT);
1592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(hmdma->XferBlockCpltCallback != NULL)
1594:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Block transfer callback */
1596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->XferBlockCpltCallback(hmdma);
1597:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1598:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1599:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1601:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Repeated Block Transfer Complete Interrupt management ******************************/
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((__HAL_MDMA_GET_FLAG(hmdma, MDMA_FLAG_BRT) != 0U))
1603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(__HAL_MDMA_GET_IT_SOURCE(hmdma, MDMA_IT_BRT) != 0U)
1605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Clear the repeat block transfer complete flag */
1607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_BRT);
1608:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(hmdma->XferRepeatBlockCpltCallback != NULL)
1610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Repeated Block transfer callback */
1612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->XferRepeatBlockCpltCallback(hmdma);
1613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1615:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1616:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Channel Transfer Complete Interrupt management ***********************************/
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((__HAL_MDMA_GET_FLAG(hmdma, MDMA_FLAG_CTC) != 0U))
1619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if(__HAL_MDMA_GET_IT_SOURCE(hmdma, MDMA_IT_CTC) != 0U)
1621:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1622:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Disable all the transfer interrupts */
1623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_DISABLE_IT(hmdma, (MDMA_IT_TE | MDMA_IT_CTC | MDMA_IT_BT | MDMA_IT_BRT | MDMA_IT_B
1624:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(HAL_MDMA_STATE_ABORT == hmdma->State)
1626:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1627:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Process Unlocked */
ARM GAS  /tmp/ccsqcIPI.s 			page 30


1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         __HAL_UNLOCK(hmdma);
1629:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Change the DMA state */
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->State = HAL_MDMA_STATE_READY;
1632:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if(hmdma->XferAbortCallback != NULL)
1634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
1635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->XferAbortCallback(hmdma);
1636:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
1637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         return;
1638:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1640:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Clear the Channel Transfer Complete flag */
1641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_CTC);
1642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1643:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Process Unlocked */
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       __HAL_UNLOCK(hmdma);
1645:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1646:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Change MDMA peripheral state */
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->State = HAL_MDMA_STATE_READY;
1648:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if(hmdma->XferCpltCallback != NULL)
1650:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Channel Transfer Complete callback */
1652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->XferCpltCallback(hmdma);
1653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1654:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1657:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* manage error case */
1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma->ErrorCode != HAL_MDMA_ERROR_NONE)
1659:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->State = HAL_MDMA_STATE_ABORT;
1661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1662:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Disable the channel */
1663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_MDMA_DISABLE(hmdma);
1664:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     do
1666:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       if (++count > timeout)
1668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
1669:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         break;
1670:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
1671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     while((hmdma->Instance->CCR & MDMA_CCR_EN) != 0U);
1673:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Process Unlocked */
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     __HAL_UNLOCK(hmdma);
1676:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if((hmdma->Instance->CCR & MDMA_CCR_EN) != 0U)
1678:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Change the MDMA state to error if MDMA disable fails */
1680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->State = HAL_MDMA_STATE_ERROR;
1681:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1682:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     else
1683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Change the MDMA state to Ready if MDMA disable success */
ARM GAS  /tmp/ccsqcIPI.s 			page 31


1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->State = HAL_MDMA_STATE_READY;
1686:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1688:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     if (hmdma->XferErrorCallback != NULL)
1690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
1691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       /* Transfer error callback */
1692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferErrorCallback(hmdma);
1693:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
1694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1696:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
1699:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1700:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1701:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Exported_Functions_Group4
1702:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  *
1703:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @verbatim
1704:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
1705:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                     ##### State and Errors functions #####
1706:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****  ===============================================================================
1707:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     [..]
1708:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     This subsection provides functions allowing to
1709:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Check the MDMA state
1710:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       (+) Get error code
1711:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** @endverbatim
1713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
1714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1716:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1717:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Returns the MDMA state.
1718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma: pointer to a MDMA_HandleTypeDef structure that contains
1719:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *               the configuration information for the specified MDMA Channel.
1720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL state
1721:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1722:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** HAL_MDMA_StateTypeDef HAL_MDMA_GetState(const MDMA_HandleTypeDef *hmdma)
1723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return hmdma->State;
1725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1727:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1728:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Return the MDMA error code
1729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma : pointer to a MDMA_HandleTypeDef structure that contains
1730:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *              the configuration information for the specified MDMA Channel.
1731:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval MDMA Error Code
1732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1733:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** uint32_t HAL_MDMA_GetError(const MDMA_HandleTypeDef *hmdma)
1734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
1735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return hmdma->ErrorCode;
1736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
1737:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1738:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
1740:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 32


1742:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1743:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @}
1744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1745:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1746:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /** @addtogroup MDMA_Private_Functions
1747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @{
1748:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1750:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1751:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Sets the MDMA Transfer parameter.
1752:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma:       pointer to a MDMA_HandleTypeDef structure that contains
1753:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                     the configuration information for the specified MDMA Channel.
1754:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  SrcAddress: The source memory Buffer address
1755:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  DstAddress: The destination memory Buffer address
1756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  BlockDataLength : The length of a block transfer in bytes
1757:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  BlockCount: The number of blocks to be transferred
1758:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval HAL status
1759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** static void MDMA_SetConfig(MDMA_HandleTypeDef *hmdma, uint32_t SrcAddress, uint32_t DstAddress, uin
1761:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
  28              		.loc 1 1761 1 view -0
  29              		.cfi_startproc
  30              		@ args = 4, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
  33              		.loc 1 1761 1 is_stmt 0 view .LVU1
  34 0000 30B4     		push	{r4, r5}
  35              	.LCFI0:
  36              		.cfi_def_cfa_offset 8
  37              		.cfi_offset 4, -8
  38              		.cfi_offset 5, -4
1762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t addressMask;
  39              		.loc 1 1762 3 is_stmt 1 view .LVU2
1763:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure the MDMA Channel data length */
1765:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MODIFY_REG(hmdma->Instance->CBNDTR ,MDMA_CBNDTR_BNDT, (BlockDataLength & MDMA_CBNDTR_BNDT));
  40              		.loc 1 1765 3 view .LVU3
  41 0002 0568     		ldr	r5, [r0]
  42 0004 6C69     		ldr	r4, [r5, #20]
  43 0006 DFF884C0 		ldr	ip, .L7
  44 000a 04EA0C0C 		and	ip, r4, ip
  45 000e C3F31003 		ubfx	r3, r3, #0, #17
  46              	.LVL1:
  47              		.loc 1 1765 3 is_stmt 0 view .LVU4
  48 0012 4CEA0303 		orr	r3, ip, r3
  49 0016 6B61     		str	r3, [r5, #20]
1766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure the MDMA block repeat count */
1768:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MODIFY_REG(hmdma->Instance->CBNDTR , MDMA_CBNDTR_BRC , ((BlockCount - 1U) << MDMA_CBNDTR_BRC_Pos)
  50              		.loc 1 1768 3 is_stmt 1 view .LVU5
  51 0018 0568     		ldr	r5, [r0]
  52 001a 6B69     		ldr	r3, [r5, #20]
  53 001c C3F31303 		ubfx	r3, r3, #0, #20
  54 0020 029C     		ldr	r4, [sp, #8]
  55 0022 013C     		subs	r4, r4, #1
  56 0024 43EA0453 		orr	r3, r3, r4, lsl #20
  57 0028 6B61     		str	r3, [r5, #20]
ARM GAS  /tmp/ccsqcIPI.s 			page 33


1769:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Clear all interrupt flags */
1771:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __HAL_MDMA_CLEAR_FLAG(hmdma, MDMA_FLAG_TE | MDMA_FLAG_CTC | MDMA_CISR_BRTIF | MDMA_CISR_BTIF | MD
  58              		.loc 1 1771 3 view .LVU6
  59 002a 0368     		ldr	r3, [r0]
  60 002c 1F24     		movs	r4, #31
  61 002e 5C60     		str	r4, [r3, #4]
1772:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure MDMA Channel destination address */
1774:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CDAR = DstAddress;
  62              		.loc 1 1774 3 view .LVU7
  63              		.loc 1 1774 8 is_stmt 0 view .LVU8
  64 0030 0368     		ldr	r3, [r0]
  65              		.loc 1 1774 25 view .LVU9
  66 0032 DA61     		str	r2, [r3, #28]
1775:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Configure MDMA Channel Source address */
1777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CSAR = SrcAddress;
  67              		.loc 1 1777 3 is_stmt 1 view .LVU10
  68              		.loc 1 1777 8 is_stmt 0 view .LVU11
  69 0034 0368     		ldr	r3, [r0]
  70              		.loc 1 1777 25 view .LVU12
  71 0036 9961     		str	r1, [r3, #24]
1778:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   addressMask = SrcAddress & 0xFF000000U;
  72              		.loc 1 1779 3 is_stmt 1 view .LVU13
  73              		.loc 1 1779 15 is_stmt 0 view .LVU14
  74 0038 01F07F41 		and	r1, r1, #-16777216
  75              	.LVL2:
1780:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
  76              		.loc 1 1780 3 is_stmt 1 view .LVU15
  77              		.loc 1 1780 5 is_stmt 0 view .LVU16
  78 003c 0029     		cmp	r1, #0
  79 003e 18BF     		it	ne
  80 0040 B1F1005F 		cmpne	r1, #536870912
  81 0044 15D1     		bne	.L2
1781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1782:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*The AHBSbus is used as source (read operation) on channel x */
1783:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTBR |= MDMA_CTBR_SBUS;
  82              		.loc 1 1783 5 is_stmt 1 view .LVU17
  83              		.loc 1 1783 10 is_stmt 0 view .LVU18
  84 0046 0168     		ldr	r1, [r0]
  85              	.LVL3:
  86              		.loc 1 1783 20 view .LVU19
  87 0048 8B6A     		ldr	r3, [r1, #40]
  88              	.LVL4:
  89              		.loc 1 1783 27 view .LVU20
  90 004a 43F48033 		orr	r3, r3, #65536
  91 004e 8B62     		str	r3, [r1, #40]
  92              	.L3:
1784:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1785:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1786:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*The AXI bus is used as source (read operation) on channel x */
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTBR &= (~MDMA_CTBR_SBUS);
1789:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 34


1791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   addressMask = DstAddress & 0xFF000000U;
  93              		.loc 1 1791 3 is_stmt 1 view .LVU21
  94              		.loc 1 1791 15 is_stmt 0 view .LVU22
  95 0050 02F07F42 		and	r2, r2, #-16777216
  96              	.LVL5:
1792:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
  97              		.loc 1 1792 3 is_stmt 1 view .LVU23
  98              		.loc 1 1792 5 is_stmt 0 view .LVU24
  99 0054 002A     		cmp	r2, #0
 100 0056 18BF     		it	ne
 101 0058 B2F1005F 		cmpne	r2, #536870912
 102 005c 0FD1     		bne	.L4
1793:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*The AHB bus is used as destination (write operation) on channel x */
1795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTBR |= MDMA_CTBR_DBUS;
 103              		.loc 1 1795 5 is_stmt 1 view .LVU25
 104              		.loc 1 1795 10 is_stmt 0 view .LVU26
 105 005e 0268     		ldr	r2, [r0]
 106              	.LVL6:
 107              		.loc 1 1795 20 view .LVU27
 108 0060 936A     		ldr	r3, [r2, #40]
 109              		.loc 1 1795 27 view .LVU28
 110 0062 43F40033 		orr	r3, r3, #131072
 111 0066 9362     		str	r3, [r2, #40]
 112              	.L5:
1796:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1799:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*The AXI bus is used as destination (write operation) on channel x */
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTBR &= (~MDMA_CTBR_DBUS);
1801:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1803:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Set the linked list register to the first node of the list */
1804:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CLAR = (uint32_t)hmdma->FirstLinkedListNodeAddress;
 113              		.loc 1 1804 3 is_stmt 1 view .LVU29
 114              		.loc 1 1804 42 is_stmt 0 view .LVU30
 115 0068 C26D     		ldr	r2, [r0, #92]
 116              		.loc 1 1804 8 view .LVU31
 117 006a 0368     		ldr	r3, [r0]
 118              		.loc 1 1804 25 view .LVU32
 119 006c 5A62     		str	r2, [r3, #36]
1805:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 120              		.loc 1 1805 1 view .LVU33
 121 006e 30BC     		pop	{r4, r5}
 122              	.LCFI1:
 123              		.cfi_remember_state
 124              		.cfi_restore 5
 125              		.cfi_restore 4
 126              		.cfi_def_cfa_offset 0
 127              	.LVL7:
 128              		.loc 1 1805 1 view .LVU34
 129 0070 7047     		bx	lr
 130              	.LVL8:
 131              	.L2:
 132              	.LCFI2:
 133              		.cfi_restore_state
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
ARM GAS  /tmp/ccsqcIPI.s 			page 35


 134              		.loc 1 1788 5 is_stmt 1 view .LVU35
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 135              		.loc 1 1788 10 is_stmt 0 view .LVU36
 136 0072 0168     		ldr	r1, [r0]
 137              	.LVL9:
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 138              		.loc 1 1788 20 view .LVU37
 139 0074 8B6A     		ldr	r3, [r1, #40]
 140              	.LVL10:
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 141              		.loc 1 1788 27 view .LVU38
 142 0076 23F48033 		bic	r3, r3, #65536
 143 007a 8B62     		str	r3, [r1, #40]
1788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 144              		.loc 1 1788 27 view .LVU39
 145 007c E8E7     		b	.L3
 146              	.LVL11:
 147              	.L4:
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 148              		.loc 1 1800 5 is_stmt 1 view .LVU40
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 149              		.loc 1 1800 10 is_stmt 0 view .LVU41
 150 007e 0268     		ldr	r2, [r0]
 151              	.LVL12:
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 152              		.loc 1 1800 20 view .LVU42
 153 0080 936A     		ldr	r3, [r2, #40]
1800:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 154              		.loc 1 1800 27 view .LVU43
 155 0082 23F40033 		bic	r3, r3, #131072
 156 0086 9362     		str	r3, [r2, #40]
 157 0088 EEE7     		b	.L5
 158              	.L8:
 159 008a 00BF     		.align	2
 160              	.L7:
 161 008c 0000FEFF 		.word	-131072
 162              		.cfi_endproc
 163              	.LFE163:
 165              		.section	.text.MDMA_Init,"ax",%progbits
 166              		.align	1
 167              		.syntax unified
 168              		.thumb
 169              		.thumb_func
 171              	MDMA_Init:
 172              	.LVL13:
 173              	.LFB164:
1806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1807:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** /**
1808:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @brief  Initializes the MDMA handle according to the specified
1809:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *         parameters in the MDMA_InitTypeDef
1810:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @param  hmdma:       pointer to a MDMA_HandleTypeDef structure that contains
1811:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   *                     the configuration information for the specified MDMA Channel.
1812:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   * @retval None
1813:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   */
1814:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** static void MDMA_Init(MDMA_HandleTypeDef *hmdma)
1815:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** {
 174              		.loc 1 1815 1 is_stmt 1 view -0
ARM GAS  /tmp/ccsqcIPI.s 			page 36


 175              		.cfi_startproc
 176              		@ args = 0, pretend = 0, frame = 0
 177              		@ frame_needed = 0, uses_anonymous_args = 0
 178              		@ link register save eliminated.
1816:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t blockoffset;
 179              		.loc 1 1816 3 view .LVU45
1817:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1818:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Prepare the MDMA Channel configuration */
1819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CCR = hmdma->Init.Priority  | hmdma->Init.Endianness;
 180              		.loc 1 1819 3 view .LVU46
 181              		.loc 1 1819 37 is_stmt 0 view .LVU47
 182 0000 C368     		ldr	r3, [r0, #12]
 183              		.loc 1 1819 61 view .LVU48
 184 0002 0169     		ldr	r1, [r0, #16]
 185              		.loc 1 1819 8 view .LVU49
 186 0004 0268     		ldr	r2, [r0]
 187              		.loc 1 1819 48 view .LVU50
 188 0006 0B43     		orrs	r3, r3, r1
 189              		.loc 1 1819 24 view .LVU51
 190 0008 D360     		str	r3, [r2, #12]
1820:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Write new CTCR Register value */
1822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTCR =  hmdma->Init.SourceInc      | hmdma->Init.DestinationInc | \
 191              		.loc 1 1822 3 is_stmt 1 view .LVU52
 192              		.loc 1 1822 39 is_stmt 0 view .LVU53
 193 000a 4369     		ldr	r3, [r0, #20]
 194              		.loc 1 1822 68 view .LVU54
 195 000c 8269     		ldr	r2, [r0, #24]
 196              		.loc 1 1822 55 view .LVU55
 197 000e 1343     		orrs	r3, r3, r2
1823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.SourceDataSize | hmdma->Init.DestDataSize   | \
 198              		.loc 1 1823 39 view .LVU56
 199 0010 C269     		ldr	r2, [r0, #28]
1822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.SourceDataSize | hmdma->Init.DestDataSize   | \
 200              		.loc 1 1822 84 view .LVU57
 201 0012 1343     		orrs	r3, r3, r2
 202              		.loc 1 1823 68 view .LVU58
 203 0014 026A     		ldr	r2, [r0, #32]
 204              		.loc 1 1823 55 view .LVU59
 205 0016 1343     		orrs	r3, r3, r2
1824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.DataAlignment  | hmdma->Init.SourceBurst    | \
 206              		.loc 1 1824 39 view .LVU60
 207 0018 426A     		ldr	r2, [r0, #36]
1823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.SourceDataSize | hmdma->Init.DestDataSize   | \
 208              		.loc 1 1823 84 view .LVU61
 209 001a 1343     		orrs	r3, r3, r2
 210              		.loc 1 1824 68 view .LVU62
 211 001c C26A     		ldr	r2, [r0, #44]
 212              		.loc 1 1824 55 view .LVU63
 213 001e 1343     		orrs	r3, r3, r2
1825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.DestBurst                                   | \
 214              		.loc 1 1825 39 view .LVU64
 215 0020 026B     		ldr	r2, [r0, #48]
1824:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.DataAlignment  | hmdma->Init.SourceBurst    | \
 216              		.loc 1 1824 84 view .LVU65
 217 0022 1343     		orrs	r3, r3, r2
1826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            ((hmdma->Init.BufferTransferLength - 1U) << MDMA_CTCR_TLEN_Pos) | \
ARM GAS  /tmp/ccsqcIPI.s 			page 37


 218              		.loc 1 1826 41 view .LVU66
 219 0024 826A     		ldr	r2, [r0, #40]
 220              		.loc 1 1826 63 view .LVU67
 221 0026 013A     		subs	r2, r2, #1
1825:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.DestBurst                                   | \
 222              		.loc 1 1825 84 view .LVU68
 223 0028 43EA8243 		orr	r3, r3, r2, lsl #18
1827:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.TransferTriggerMode;
 224              		.loc 1 1827 39 view .LVU69
 225 002c 8168     		ldr	r1, [r0, #8]
1822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.SourceDataSize | hmdma->Init.DestDataSize   | \
 226              		.loc 1 1822 8 view .LVU70
 227 002e 0268     		ldr	r2, [r0]
1826:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            ((hmdma->Init.BufferTransferLength - 1U) << MDMA_CTCR_TLEN_Pos) | \
 228              		.loc 1 1826 92 view .LVU71
 229 0030 0B43     		orrs	r3, r3, r1
1822:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                            hmdma->Init.SourceDataSize | hmdma->Init.DestDataSize   | \
 230              		.loc 1 1822 25 view .LVU72
 231 0032 1361     		str	r3, [r2, #16]
1828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1829:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* If SW request set the CTCR register to SW Request Mode */
1830:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma->Init.Request == MDMA_REQUEST_SW)
 232              		.loc 1 1830 3 is_stmt 1 view .LVU73
 233              		.loc 1 1830 17 is_stmt 0 view .LVU74
 234 0034 4368     		ldr	r3, [r0, #4]
 235              		.loc 1 1830 5 view .LVU75
 236 0036 B3F1804F 		cmp	r3, #1073741824
 237 003a 1BD0     		beq	.L17
 238              	.L10:
1831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1832:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*
1833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     -If the request is done by SW : BWM could be set to 1 or 0.
1834:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     -If the request is done by a peripheral :
1835:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     If mask address not set (0) => BWM must be set to 0
1836:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     If mask address set (different than 0) => BWM could be set to 1 or 0
1837:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     */
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTCR |= (MDMA_CTCR_SWRM | MDMA_CTCR_BWM);
1839:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1840:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1841:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Reset CBNDTR Register */
1842:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBNDTR = 0;
 239              		.loc 1 1842 3 is_stmt 1 view .LVU76
 240              		.loc 1 1842 8 is_stmt 0 view .LVU77
 241 003c 0368     		ldr	r3, [r0]
 242              		.loc 1 1842 27 view .LVU78
 243 003e 0022     		movs	r2, #0
 244 0040 5A61     		str	r2, [r3, #20]
1843:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1844:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* if block source address offset is negative set the Block Repeat Source address Update Mode to 
1845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma->Init.SourceBlockAddressOffset < 0)
 245              		.loc 1 1845 3 is_stmt 1 view .LVU79
 246              		.loc 1 1845 17 is_stmt 0 view .LVU80
 247 0042 436B     		ldr	r3, [r0, #52]
 248              		.loc 1 1845 5 view .LVU81
 249 0044 9342     		cmp	r3, r2
 250 0046 1BDB     		blt	.L18
1846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
ARM GAS  /tmp/ccsqcIPI.s 			page 38


1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBNDTR |= MDMA_CBNDTR_BRSUM;
1848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : source repeat block offset */
1849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     blockoffset = (uint32_t)(- hmdma->Init.SourceBlockAddressOffset);
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR = (blockoffset & 0x0000FFFFU);
1851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1852:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1854:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : source repeat block offset */
1855:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR = (((uint32_t)hmdma->Init.SourceBlockAddressOffset) & 0x0000FFFFU);
 251              		.loc 1 1855 5 is_stmt 1 view .LVU82
 252              		.loc 1 1855 10 is_stmt 0 view .LVU83
 253 0048 0268     		ldr	r2, [r0]
 254              		.loc 1 1855 80 view .LVU84
 255 004a 9BB2     		uxth	r3, r3
 256              		.loc 1 1855 28 view .LVU85
 257 004c 1362     		str	r3, [r2, #32]
 258              	.L12:
1856:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1858:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* If block destination address offset is negative set the Block Repeat destination address Updat
1859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma->Init.DestBlockAddressOffset < 0)
 259              		.loc 1 1859 3 is_stmt 1 view .LVU86
 260              		.loc 1 1859 17 is_stmt 0 view .LVU87
 261 004e 836B     		ldr	r3, [r0, #56]
 262              		.loc 1 1859 5 view .LVU88
 263 0050 002B     		cmp	r3, #0
 264 0052 20DB     		blt	.L19
1860:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBNDTR |= MDMA_CBNDTR_BRDUM;
1862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : destination repeat block offset */
1863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     blockoffset = (uint32_t)(- hmdma->Init.DestBlockAddressOffset);
1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
1865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1866:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else
1867:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1868:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : destination repeat block offset */
1869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR |= ((((uint32_t)hmdma->Init.DestBlockAddressOffset) & 0x0000FFFFU) << MD
 265              		.loc 1 1869 5 is_stmt 1 view .LVU89
 266              		.loc 1 1869 10 is_stmt 0 view .LVU90
 267 0054 0168     		ldr	r1, [r0]
 268              		.loc 1 1869 20 view .LVU91
 269 0056 0A6A     		ldr	r2, [r1, #32]
 270              		.loc 1 1869 28 view .LVU92
 271 0058 42EA0343 		orr	r3, r2, r3, lsl #16
 272 005c 0B62     		str	r3, [r1, #32]
 273              	.L14:
1870:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1871:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* if HW request set the HW request and the requet CleraMask and ClearData MaskData, */
1873:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if(hmdma->Init.Request != MDMA_REQUEST_SW)
 274              		.loc 1 1873 3 is_stmt 1 view .LVU93
 275              		.loc 1 1873 17 is_stmt 0 view .LVU94
 276 005e 4368     		ldr	r3, [r0, #4]
 277              		.loc 1 1873 5 view .LVU95
 278 0060 B3F1804F 		cmp	r3, #1073741824
 279 0064 24D0     		beq	.L15
1874:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
ARM GAS  /tmp/ccsqcIPI.s 			page 39


1875:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the HW request in CTRB register  */
1876:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTBR = hmdma->Init.Request & MDMA_CTBR_TSEL;
 280              		.loc 1 1876 5 is_stmt 1 view .LVU96
 281              		.loc 1 1876 10 is_stmt 0 view .LVU97
 282 0066 0268     		ldr	r2, [r0]
 283              		.loc 1 1876 49 view .LVU98
 284 0068 DBB2     		uxtb	r3, r3
 285              		.loc 1 1876 27 view .LVU99
 286 006a 9362     		str	r3, [r2, #40]
 287              	.L16:
1877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   else /* SW request : reset the CTBR register */
1879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
1880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CTBR = 0;
1881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
1882:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
1883:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Write Link Address Register */
1884:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CLAR =  0;
 288              		.loc 1 1884 3 is_stmt 1 view .LVU100
 289              		.loc 1 1884 8 is_stmt 0 view .LVU101
 290 006c 0368     		ldr	r3, [r0]
 291              		.loc 1 1884 25 view .LVU102
 292 006e 0022     		movs	r2, #0
 293 0070 5A62     		str	r2, [r3, #36]
1885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 294              		.loc 1 1885 1 view .LVU103
 295 0072 7047     		bx	lr
 296              	.L17:
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 297              		.loc 1 1838 5 is_stmt 1 view .LVU104
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 298              		.loc 1 1838 10 is_stmt 0 view .LVU105
 299 0074 0268     		ldr	r2, [r0]
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 300              		.loc 1 1838 20 view .LVU106
 301 0076 1369     		ldr	r3, [r2, #16]
1838:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 302              		.loc 1 1838 27 view .LVU107
 303 0078 43F04043 		orr	r3, r3, #-1073741824
 304 007c 1361     		str	r3, [r2, #16]
 305 007e DDE7     		b	.L10
 306              	.L18:
1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : source repeat block offset */
 307              		.loc 1 1847 5 is_stmt 1 view .LVU108
1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : source repeat block offset */
 308              		.loc 1 1847 10 is_stmt 0 view .LVU109
 309 0080 0268     		ldr	r2, [r0]
1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : source repeat block offset */
 310              		.loc 1 1847 20 view .LVU110
 311 0082 5369     		ldr	r3, [r2, #20]
1847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : source repeat block offset */
 312              		.loc 1 1847 29 view .LVU111
 313 0084 43F48023 		orr	r3, r3, #262144
 314 0088 5361     		str	r3, [r2, #20]
1849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR = (blockoffset & 0x0000FFFFU);
 315              		.loc 1 1849 5 is_stmt 1 view .LVU112
1849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR = (blockoffset & 0x0000FFFFU);
ARM GAS  /tmp/ccsqcIPI.s 			page 40


 316              		.loc 1 1849 43 is_stmt 0 view .LVU113
 317 008a 436B     		ldr	r3, [r0, #52]
1849:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR = (blockoffset & 0x0000FFFFU);
 318              		.loc 1 1849 30 view .LVU114
 319 008c 5B42     		rsbs	r3, r3, #0
 320              	.LVL14:
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 321              		.loc 1 1850 5 is_stmt 1 view .LVU115
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 322              		.loc 1 1850 10 is_stmt 0 view .LVU116
 323 008e 0268     		ldr	r2, [r0]
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 324              		.loc 1 1850 43 view .LVU117
 325 0090 9BB2     		uxth	r3, r3
 326              	.LVL15:
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 327              		.loc 1 1850 28 view .LVU118
 328 0092 1362     		str	r3, [r2, #32]
 329              	.LVL16:
1850:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 330              		.loc 1 1850 28 view .LVU119
 331 0094 DBE7     		b	.L12
 332              	.LVL17:
 333              	.L19:
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : destination repeat block offset */
 334              		.loc 1 1861 5 is_stmt 1 view .LVU120
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : destination repeat block offset */
 335              		.loc 1 1861 10 is_stmt 0 view .LVU121
 336 0096 0268     		ldr	r2, [r0]
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : destination repeat block offset */
 337              		.loc 1 1861 20 view .LVU122
 338 0098 5369     		ldr	r3, [r2, #20]
1861:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Write new CBRUR Register value : destination repeat block offset */
 339              		.loc 1 1861 29 view .LVU123
 340 009a 43F40023 		orr	r3, r3, #524288
 341 009e 5361     		str	r3, [r2, #20]
1863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 342              		.loc 1 1863 5 is_stmt 1 view .LVU124
1863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 343              		.loc 1 1863 43 is_stmt 0 view .LVU125
 344 00a0 836B     		ldr	r3, [r0, #56]
1863:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     hmdma->Instance->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 345              		.loc 1 1863 30 view .LVU126
 346 00a2 5A42     		rsbs	r2, r3, #0
 347              	.LVL18:
1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 348              		.loc 1 1864 5 is_stmt 1 view .LVU127
1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 349              		.loc 1 1864 10 is_stmt 0 view .LVU128
 350 00a4 0168     		ldr	r1, [r0]
1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 351              		.loc 1 1864 20 view .LVU129
 352 00a6 0B6A     		ldr	r3, [r1, #32]
1864:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 353              		.loc 1 1864 28 view .LVU130
 354 00a8 43EA0243 		orr	r3, r3, r2, lsl #16
 355 00ac 0B62     		str	r3, [r1, #32]
ARM GAS  /tmp/ccsqcIPI.s 			page 41


 356 00ae D6E7     		b	.L14
 357              	.LVL19:
 358              	.L15:
1880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 359              		.loc 1 1880 5 is_stmt 1 view .LVU131
1880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 360              		.loc 1 1880 10 is_stmt 0 view .LVU132
 361 00b0 0368     		ldr	r3, [r0]
1880:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 362              		.loc 1 1880 27 view .LVU133
 363 00b2 0022     		movs	r2, #0
 364 00b4 9A62     		str	r2, [r3, #40]
 365 00b6 D9E7     		b	.L16
 366              		.cfi_endproc
 367              	.LFE164:
 369              		.section	.text.HAL_MDMA_Init,"ax",%progbits
 370              		.align	1
 371              		.global	HAL_MDMA_Init
 372              		.syntax unified
 373              		.thumb
 374              		.thumb_func
 376              	HAL_MDMA_Init:
 377              	.LVL20:
 378              	.LFB144:
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart = HAL_GetTick();
 379              		.loc 1 219 1 is_stmt 1 view -0
 380              		.cfi_startproc
 381              		@ args = 0, pretend = 0, frame = 0
 382              		@ frame_needed = 0, uses_anonymous_args = 0
 219:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart = HAL_GetTick();
 383              		.loc 1 219 1 is_stmt 0 view .LVU135
 384 0000 38B5     		push	{r3, r4, r5, lr}
 385              	.LCFI3:
 386              		.cfi_def_cfa_offset 16
 387              		.cfi_offset 3, -16
 388              		.cfi_offset 4, -12
 389              		.cfi_offset 5, -8
 390              		.cfi_offset 14, -4
 391 0002 0446     		mov	r4, r0
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 392              		.loc 1 220 3 is_stmt 1 view .LVU136
 220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 393              		.loc 1 220 24 is_stmt 0 view .LVU137
 394 0004 FFF7FEFF 		bl	HAL_GetTick
 395              	.LVL21:
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 396              		.loc 1 223 3 is_stmt 1 view .LVU138
 223:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 397              		.loc 1 223 5 is_stmt 0 view .LVU139
 398 0008 44B3     		cbz	r4, .L24
 399 000a 0546     		mov	r5, r0
 229:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_PRIORITY(hmdma->Init.Priority));
 400              		.loc 1 229 3 is_stmt 1 view .LVU140
 230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_ENDIANNESS_MODE(hmdma->Init.Endianness));
 401              		.loc 1 230 3 view .LVU141
 231:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_REQUEST(hmdma->Init.Request));
 402              		.loc 1 231 3 view .LVU142
ARM GAS  /tmp/ccsqcIPI.s 			page 42


 232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_INC(hmdma->Init.SourceInc));
 403              		.loc 1 232 3 view .LVU143
 233:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_INC(hmdma->Init.DestinationInc));
 404              		.loc 1 233 3 view .LVU144
 234:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_DATASIZE(hmdma->Init.SourceDataSize));
 405              		.loc 1 234 3 view .LVU145
 235:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_DATASIZE(hmdma->Init.DestDataSize));
 406              		.loc 1 235 3 view .LVU146
 236:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DATA_ALIGNMENT(hmdma->Init.DataAlignment));
 407              		.loc 1 236 3 view .LVU147
 237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_BURST(hmdma->Init.SourceBurst));
 408              		.loc 1 237 3 view .LVU148
 238:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_BURST(hmdma->Init.DestBurst));
 409              		.loc 1 238 3 view .LVU149
 239:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BUFFER_TRANSFER_LENGTH(hmdma->Init.BufferTransferLength));
 410              		.loc 1 239 3 view .LVU150
 240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_TRIGGER_MODE(hmdma->Init.TransferTriggerMode));
 411              		.loc 1 240 3 view .LVU151
 241:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(hmdma->Init.SourceBlockAddressOffset));
 412              		.loc 1 241 3 view .LVU152
 242:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(hmdma->Init.DestBlockAddressOffset));
 413              		.loc 1 242 3 view .LVU153
 243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 414              		.loc 1 243 3 view .LVU154
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 415              		.loc 1 247 3 view .LVU155
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 416              		.loc 1 247 3 view .LVU156
 417 000c 0023     		movs	r3, #0
 418 000e 84F83C30 		strb	r3, [r4, #60]
 247:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 419              		.loc 1 247 3 view .LVU157
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 420              		.loc 1 250 3 view .LVU158
 250:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 421              		.loc 1 250 16 is_stmt 0 view .LVU159
 422 0012 0223     		movs	r3, #2
 423 0014 84F83D30 		strb	r3, [r4, #61]
 253:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 424              		.loc 1 253 3 is_stmt 1 view .LVU160
 425 0018 2268     		ldr	r2, [r4]
 426 001a D368     		ldr	r3, [r2, #12]
 427 001c 23F00103 		bic	r3, r3, #1
 428 0020 D360     		str	r3, [r2, #12]
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 429              		.loc 1 256 3 view .LVU161
 430              	.LVL22:
 431              	.L22:
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 432              		.loc 1 256 46 view .LVU162
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 433              		.loc 1 256 15 is_stmt 0 view .LVU163
 434 0022 2368     		ldr	r3, [r4]
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 435              		.loc 1 256 25 view .LVU164
 436 0024 DB68     		ldr	r3, [r3, #12]
 256:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
ARM GAS  /tmp/ccsqcIPI.s 			page 43


 437              		.loc 1 256 46 view .LVU165
 438 0026 13F0010F 		tst	r3, #1
 439 002a 0BD0     		beq	.L26
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 440              		.loc 1 259 5 is_stmt 1 view .LVU166
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 441              		.loc 1 259 9 is_stmt 0 view .LVU167
 442 002c FFF7FEFF 		bl	HAL_GetTick
 443              	.LVL23:
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 444              		.loc 1 259 23 discriminator 1 view .LVU168
 445 0030 431B     		subs	r3, r0, r5
 259:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 446              		.loc 1 259 7 discriminator 1 view .LVU169
 447 0032 052B     		cmp	r3, #5
 448 0034 F5D9     		bls	.L22
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 449              		.loc 1 262 7 is_stmt 1 view .LVU170
 262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 450              		.loc 1 262 24 is_stmt 0 view .LVU171
 451 0036 4023     		movs	r3, #64
 452 0038 A366     		str	r3, [r4, #104]
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 453              		.loc 1 265 7 is_stmt 1 view .LVU172
 265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 454              		.loc 1 265 20 is_stmt 0 view .LVU173
 455 003a 0323     		movs	r3, #3
 456 003c 84F83D30 		strb	r3, [r4, #61]
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 457              		.loc 1 267 7 is_stmt 1 view .LVU174
 267:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 458              		.loc 1 267 14 is_stmt 0 view .LVU175
 459 0040 0120     		movs	r0, #1
 460 0042 0AE0     		b	.L21
 461              	.L26:
 272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 462              		.loc 1 272 3 is_stmt 1 view .LVU176
 463 0044 2046     		mov	r0, r4
 464 0046 FFF7FEFF 		bl	MDMA_Init
 465              	.LVL24:
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LastLinkedListNodeAddress   = 0;
 466              		.loc 1 275 3 view .LVU177
 275:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LastLinkedListNodeAddress   = 0;
 467              		.loc 1 275 38 is_stmt 0 view .LVU178
 468 004a 0020     		movs	r0, #0
 469 004c E065     		str	r0, [r4, #92]
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LinkedListNodeCounter  = 0;
 470              		.loc 1 276 3 is_stmt 1 view .LVU179
 276:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LinkedListNodeCounter  = 0;
 471              		.loc 1 276 38 is_stmt 0 view .LVU180
 472 004e 2066     		str	r0, [r4, #96]
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 473              		.loc 1 277 3 is_stmt 1 view .LVU181
 277:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 474              		.loc 1 277 33 is_stmt 0 view .LVU182
 475 0050 6066     		str	r0, [r4, #100]
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 44


 476              		.loc 1 280 3 is_stmt 1 view .LVU183
 280:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 477              		.loc 1 280 20 is_stmt 0 view .LVU184
 478 0052 A066     		str	r0, [r4, #104]
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 479              		.loc 1 283 3 is_stmt 1 view .LVU185
 283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 480              		.loc 1 283 16 is_stmt 0 view .LVU186
 481 0054 0123     		movs	r3, #1
 482 0056 84F83D30 		strb	r3, [r4, #61]
 285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 483              		.loc 1 285 3 is_stmt 1 view .LVU187
 484              	.LVL25:
 485              	.L21:
 286:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 486              		.loc 1 286 1 is_stmt 0 view .LVU188
 487 005a 38BD     		pop	{r3, r4, r5, pc}
 488              	.LVL26:
 489              	.L24:
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 490              		.loc 1 225 12 view .LVU189
 491 005c 0120     		movs	r0, #1
 492              	.LVL27:
 225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 493              		.loc 1 225 12 view .LVU190
 494 005e FCE7     		b	.L21
 495              		.cfi_endproc
 496              	.LFE144:
 498              		.section	.text.HAL_MDMA_DeInit,"ax",%progbits
 499              		.align	1
 500              		.global	HAL_MDMA_DeInit
 501              		.syntax unified
 502              		.thumb
 503              		.thumb_func
 505              	HAL_MDMA_DeInit:
 506              	.LVL28:
 507              	.LFB145:
 295:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 508              		.loc 1 295 1 is_stmt 1 view -0
 509              		.cfi_startproc
 510              		@ args = 0, pretend = 0, frame = 0
 511              		@ frame_needed = 0, uses_anonymous_args = 0
 512              		@ link register save eliminated.
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 513              		.loc 1 298 3 view .LVU192
 298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 514              		.loc 1 298 5 is_stmt 0 view .LVU193
 515 0000 0346     		mov	r3, r0
 516 0002 0028     		cmp	r0, #0
 517 0004 25D0     		beq	.L29
 304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 518              		.loc 1 304 3 is_stmt 1 view .LVU194
 519 0006 0168     		ldr	r1, [r0]
 520 0008 CA68     		ldr	r2, [r1, #12]
 521 000a 22F00102 		bic	r2, r2, #1
 522 000e CA60     		str	r2, [r1, #12]
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTCR = 0;
ARM GAS  /tmp/ccsqcIPI.s 			page 45


 523              		.loc 1 307 3 view .LVU195
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTCR = 0;
 524              		.loc 1 307 8 is_stmt 0 view .LVU196
 525 0010 0268     		ldr	r2, [r0]
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTCR = 0;
 526              		.loc 1 307 25 view .LVU197
 527 0012 0020     		movs	r0, #0
 528              	.LVL29:
 307:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTCR = 0;
 529              		.loc 1 307 25 view .LVU198
 530 0014 D060     		str	r0, [r2, #12]
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBNDTR = 0;
 531              		.loc 1 308 3 is_stmt 1 view .LVU199
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBNDTR = 0;
 532              		.loc 1 308 8 is_stmt 0 view .LVU200
 533 0016 1A68     		ldr	r2, [r3]
 308:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBNDTR = 0;
 534              		.loc 1 308 25 view .LVU201
 535 0018 1061     		str	r0, [r2, #16]
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CSAR = 0;
 536              		.loc 1 309 3 is_stmt 1 view .LVU202
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CSAR = 0;
 537              		.loc 1 309 8 is_stmt 0 view .LVU203
 538 001a 1A68     		ldr	r2, [r3]
 309:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CSAR = 0;
 539              		.loc 1 309 27 view .LVU204
 540 001c 5061     		str	r0, [r2, #20]
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CDAR = 0;
 541              		.loc 1 310 3 is_stmt 1 view .LVU205
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CDAR = 0;
 542              		.loc 1 310 8 is_stmt 0 view .LVU206
 543 001e 1A68     		ldr	r2, [r3]
 310:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CDAR = 0;
 544              		.loc 1 310 25 view .LVU207
 545 0020 9061     		str	r0, [r2, #24]
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBRUR = 0;
 546              		.loc 1 311 3 is_stmt 1 view .LVU208
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBRUR = 0;
 547              		.loc 1 311 8 is_stmt 0 view .LVU209
 548 0022 1A68     		ldr	r2, [r3]
 311:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CBRUR = 0;
 549              		.loc 1 311 25 view .LVU210
 550 0024 D061     		str	r0, [r2, #28]
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CLAR = 0;
 551              		.loc 1 312 3 is_stmt 1 view .LVU211
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CLAR = 0;
 552              		.loc 1 312 8 is_stmt 0 view .LVU212
 553 0026 1A68     		ldr	r2, [r3]
 312:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CLAR = 0;
 554              		.loc 1 312 26 view .LVU213
 555 0028 1062     		str	r0, [r2, #32]
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTBR = 0;
 556              		.loc 1 313 3 is_stmt 1 view .LVU214
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTBR = 0;
 557              		.loc 1 313 8 is_stmt 0 view .LVU215
 558 002a 1A68     		ldr	r2, [r3]
 313:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CTBR = 0;
ARM GAS  /tmp/ccsqcIPI.s 			page 46


 559              		.loc 1 313 25 view .LVU216
 560 002c 5062     		str	r0, [r2, #36]
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMAR = 0;
 561              		.loc 1 314 3 is_stmt 1 view .LVU217
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMAR = 0;
 562              		.loc 1 314 8 is_stmt 0 view .LVU218
 563 002e 1A68     		ldr	r2, [r3]
 314:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMAR = 0;
 564              		.loc 1 314 25 view .LVU219
 565 0030 9062     		str	r0, [r2, #40]
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMDR = 0;
 566              		.loc 1 315 3 is_stmt 1 view .LVU220
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMDR = 0;
 567              		.loc 1 315 8 is_stmt 0 view .LVU221
 568 0032 1A68     		ldr	r2, [r3]
 315:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->Instance->CMDR = 0;
 569              		.loc 1 315 25 view .LVU222
 570 0034 1063     		str	r0, [r2, #48]
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 571              		.loc 1 316 3 is_stmt 1 view .LVU223
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 572              		.loc 1 316 8 is_stmt 0 view .LVU224
 573 0036 1A68     		ldr	r2, [r3]
 316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 574              		.loc 1 316 25 view .LVU225
 575 0038 5063     		str	r0, [r2, #52]
 319:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 576              		.loc 1 319 3 is_stmt 1 view .LVU226
 577 003a 1A68     		ldr	r2, [r3]
 578 003c 1F21     		movs	r1, #31
 579 003e 5160     		str	r1, [r2, #4]
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LastLinkedListNodeAddress   = 0;
 580              		.loc 1 322 3 view .LVU227
 322:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LastLinkedListNodeAddress   = 0;
 581              		.loc 1 322 38 is_stmt 0 view .LVU228
 582 0040 D865     		str	r0, [r3, #92]
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LinkedListNodeCounter  = 0;
 583              		.loc 1 323 3 is_stmt 1 view .LVU229
 323:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   hmdma->LinkedListNodeCounter  = 0;
 584              		.loc 1 323 38 is_stmt 0 view .LVU230
 585 0042 1866     		str	r0, [r3, #96]
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 586              		.loc 1 324 3 is_stmt 1 view .LVU231
 324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 587              		.loc 1 324 33 is_stmt 0 view .LVU232
 588 0044 5866     		str	r0, [r3, #100]
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 589              		.loc 1 327 3 is_stmt 1 view .LVU233
 327:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 590              		.loc 1 327 20 is_stmt 0 view .LVU234
 591 0046 9866     		str	r0, [r3, #104]
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 592              		.loc 1 330 3 is_stmt 1 view .LVU235
 330:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 593              		.loc 1 330 16 is_stmt 0 view .LVU236
 594 0048 83F83D00 		strb	r0, [r3, #61]
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 47


 595              		.loc 1 333 3 is_stmt 1 view .LVU237
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 596              		.loc 1 333 3 view .LVU238
 597 004c 83F83C00 		strb	r0, [r3, #60]
 333:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 598              		.loc 1 333 3 view .LVU239
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 599              		.loc 1 335 3 view .LVU240
 335:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 600              		.loc 1 335 10 is_stmt 0 view .LVU241
 601 0050 7047     		bx	lr
 602              	.LVL30:
 603              	.L29:
 300:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 604              		.loc 1 300 12 view .LVU242
 605 0052 0120     		movs	r0, #1
 606              	.LVL31:
 336:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 607              		.loc 1 336 1 view .LVU243
 608 0054 7047     		bx	lr
 609              		.cfi_endproc
 610              	.LFE145:
 612              		.section	.text.HAL_MDMA_ConfigPostRequestMask,"ax",%progbits
 613              		.align	1
 614              		.global	HAL_MDMA_ConfigPostRequestMask
 615              		.syntax unified
 616              		.thumb
 617              		.thumb_func
 619              	HAL_MDMA_ConfigPostRequestMask:
 620              	.LVL32:
 621              	.LFB146:
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef  status = HAL_OK;
 622              		.loc 1 348 1 is_stmt 1 view -0
 623              		.cfi_startproc
 624              		@ args = 0, pretend = 0, frame = 0
 625              		@ frame_needed = 0, uses_anonymous_args = 0
 626              		@ link register save eliminated.
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 627              		.loc 1 349 3 view .LVU245
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 628              		.loc 1 352 3 view .LVU246
 352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 629              		.loc 1 352 5 is_stmt 0 view .LVU247
 630 0000 68B3     		cbz	r0, .L34
 631 0002 0346     		mov	r3, r0
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 632              		.loc 1 358 3 is_stmt 1 view .LVU248
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 633              		.loc 1 358 3 view .LVU249
 634 0004 90F83C00 		ldrb	r0, [r0, #60]	@ zero_extendqisi2
 635              	.LVL33:
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 636              		.loc 1 358 3 is_stmt 0 view .LVU250
 637 0008 0128     		cmp	r0, #1
 638 000a 2AD0     		beq	.L35
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 639              		.loc 1 358 3 is_stmt 1 discriminator 2 view .LVU251
ARM GAS  /tmp/ccsqcIPI.s 			page 48


 640 000c 0120     		movs	r0, #1
 641 000e 83F83C00 		strb	r0, [r3, #60]
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 642              		.loc 1 358 3 discriminator 2 view .LVU252
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 643              		.loc 1 360 3 view .LVU253
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 644              		.loc 1 360 35 is_stmt 0 view .LVU254
 645 0012 93F83D00 		ldrb	r0, [r3, #61]	@ zero_extendqisi2
 646 0016 C0B2     		uxtb	r0, r0
 360:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 647              		.loc 1 360 5 view .LVU255
 648 0018 0128     		cmp	r0, #1
 649 001a 04D0     		beq	.L42
 393:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 650              		.loc 1 393 12 view .LVU256
 651 001c 0120     		movs	r0, #1
 652              	.LVL34:
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 653              		.loc 1 396 3 is_stmt 1 view .LVU257
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 654              		.loc 1 396 3 view .LVU258
 655 001e 0022     		movs	r2, #0
 656              	.LVL35:
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 657              		.loc 1 396 3 is_stmt 0 view .LVU259
 658 0020 83F83C20 		strb	r2, [r3, #60]
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 659              		.loc 1 396 3 is_stmt 1 view .LVU260
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 660              		.loc 1 398 3 view .LVU261
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 661              		.loc 1 399 1 is_stmt 0 view .LVU262
 662 0024 7047     		bx	lr
 663              	.LVL36:
 664              	.L42:
 348:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef  status = HAL_OK;
 665              		.loc 1 348 1 view .LVU263
 666 0026 30B4     		push	{r4, r5}
 667              	.LCFI4:
 668              		.cfi_def_cfa_offset 8
 669              		.cfi_offset 4, -8
 670              		.cfi_offset 5, -4
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 671              		.loc 1 363 5 is_stmt 1 view .LVU264
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 672              		.loc 1 363 14 is_stmt 0 view .LVU265
 673 0028 1C68     		ldr	r4, [r3]
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 674              		.loc 1 363 24 view .LVU266
 675 002a 2569     		ldr	r5, [r4, #16]
 363:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 676              		.loc 1 363 7 view .LVU267
 677 002c 15F0804F 		tst	r5, #1073741824
 678 0030 09D1     		bne	.L32
 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->Instance->CMDR = MaskData;
 679              		.loc 1 366 7 is_stmt 1 view .LVU268
ARM GAS  /tmp/ccsqcIPI.s 			page 49


 366:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->Instance->CMDR = MaskData;
 680              		.loc 1 366 29 is_stmt 0 view .LVU269
 681 0032 2163     		str	r1, [r4, #48]
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 682              		.loc 1 367 7 is_stmt 1 view .LVU270
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 683              		.loc 1 367 12 is_stmt 0 view .LVU271
 684 0034 1868     		ldr	r0, [r3]
 367:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 685              		.loc 1 367 29 view .LVU272
 686 0036 4263     		str	r2, [r0, #52]
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 687              		.loc 1 375 7 is_stmt 1 view .LVU273
 375:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 688              		.loc 1 375 9 is_stmt 0 view .LVU274
 689 0038 51B9     		cbnz	r1, .L33
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 690              		.loc 1 377 9 is_stmt 1 view .LVU275
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 691              		.loc 1 377 14 is_stmt 0 view .LVU276
 692 003a 1968     		ldr	r1, [r3]
 693              	.LVL37:
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 694              		.loc 1 377 24 view .LVU277
 695 003c 0A69     		ldr	r2, [r1, #16]
 696              	.LVL38:
 377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 697              		.loc 1 377 31 view .LVU278
 698 003e 22F00042 		bic	r2, r2, #-2147483648
 699 0042 0A61     		str	r2, [r1, #16]
 700              	.LVL39:
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 701              		.loc 1 349 22 view .LVU279
 702 0044 0020     		movs	r0, #0
 703              	.L32:
 704              	.LVL40:
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 705              		.loc 1 396 3 is_stmt 1 view .LVU280
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 706              		.loc 1 396 3 view .LVU281
 707 0046 0022     		movs	r2, #0
 708 0048 83F83C20 		strb	r2, [r3, #60]
 396:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 709              		.loc 1 396 3 view .LVU282
 398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 710              		.loc 1 398 3 view .LVU283
 399:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 711              		.loc 1 399 1 is_stmt 0 view .LVU284
 712 004c 30BC     		pop	{r4, r5}
 713              	.LCFI5:
 714              		.cfi_remember_state
 715              		.cfi_restore 5
 716              		.cfi_restore 4
 717              		.cfi_def_cfa_offset 0
 718 004e 7047     		bx	lr
 719              	.LVL41:
 720              	.L33:
ARM GAS  /tmp/ccsqcIPI.s 			page 50


 721              	.LCFI6:
 722              		.cfi_restore_state
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 723              		.loc 1 381 9 is_stmt 1 view .LVU285
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 724              		.loc 1 381 14 is_stmt 0 view .LVU286
 725 0050 1968     		ldr	r1, [r3]
 726              	.LVL42:
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 727              		.loc 1 381 24 view .LVU287
 728 0052 0A69     		ldr	r2, [r1, #16]
 729              	.LVL43:
 381:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 730              		.loc 1 381 31 view .LVU288
 731 0054 42F00042 		orr	r2, r2, #-2147483648
 732 0058 0A61     		str	r2, [r1, #16]
 733              	.LVL44:
 349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 734              		.loc 1 349 22 view .LVU289
 735 005a 0020     		movs	r0, #0
 736 005c F3E7     		b	.L32
 737              	.LVL45:
 738              	.L34:
 739              	.LCFI7:
 740              		.cfi_def_cfa_offset 0
 741              		.cfi_restore 4
 742              		.cfi_restore 5
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 743              		.loc 1 354 12 view .LVU290
 744 005e 0120     		movs	r0, #1
 745              	.LVL46:
 354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 746              		.loc 1 354 12 view .LVU291
 747 0060 7047     		bx	lr
 748              	.LVL47:
 749              	.L35:
 358:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 750              		.loc 1 358 3 discriminator 1 view .LVU292
 751 0062 0220     		movs	r0, #2
 752 0064 7047     		bx	lr
 753              		.cfi_endproc
 754              	.LFE146:
 756              		.section	.text.HAL_MDMA_RegisterCallback,"ax",%progbits
 757              		.align	1
 758              		.global	HAL_MDMA_RegisterCallback
 759              		.syntax unified
 760              		.thumb
 761              		.thumb_func
 763              	HAL_MDMA_RegisterCallback:
 764              	.LVL48:
 765              	.LFB147:
 410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef status = HAL_OK;
 766              		.loc 1 410 1 is_stmt 1 view -0
 767              		.cfi_startproc
 768              		@ args = 0, pretend = 0, frame = 0
 769              		@ frame_needed = 0, uses_anonymous_args = 0
 770              		@ link register save eliminated.
ARM GAS  /tmp/ccsqcIPI.s 			page 51


 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 771              		.loc 1 411 3 view .LVU294
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 772              		.loc 1 414 3 view .LVU295
 414:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 773              		.loc 1 414 5 is_stmt 0 view .LVU296
 774 0000 0346     		mov	r3, r0
 775 0002 58B3     		cbz	r0, .L53
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 776              		.loc 1 420 3 is_stmt 1 view .LVU297
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 777              		.loc 1 420 3 view .LVU298
 778 0004 90F83C00 		ldrb	r0, [r0, #60]	@ zero_extendqisi2
 779              	.LVL49:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 780              		.loc 1 420 3 is_stmt 0 view .LVU299
 781 0008 0128     		cmp	r0, #1
 782 000a 29D0     		beq	.L54
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 783              		.loc 1 420 3 is_stmt 1 discriminator 2 view .LVU300
 784 000c 0120     		movs	r0, #1
 785 000e 83F83C00 		strb	r0, [r3, #60]
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 786              		.loc 1 420 3 discriminator 2 view .LVU301
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 787              		.loc 1 422 3 view .LVU302
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 788              		.loc 1 422 35 is_stmt 0 view .LVU303
 789 0012 93F83D00 		ldrb	r0, [r3, #61]	@ zero_extendqisi2
 790 0016 C0B2     		uxtb	r0, r0
 422:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 791              		.loc 1 422 5 view .LVU304
 792 0018 0128     		cmp	r0, #1
 793 001a 04D0     		beq	.L57
 457:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 794              		.loc 1 457 12 view .LVU305
 795 001c 0120     		movs	r0, #1
 796              	.L45:
 797              	.LVL50:
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 798              		.loc 1 461 3 is_stmt 1 view .LVU306
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 799              		.loc 1 461 3 view .LVU307
 800 001e 0022     		movs	r2, #0
 801              	.LVL51:
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 802              		.loc 1 461 3 is_stmt 0 view .LVU308
 803 0020 83F83C20 		strb	r2, [r3, #60]
 461:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 804              		.loc 1 461 3 is_stmt 1 view .LVU309
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 805              		.loc 1 463 3 view .LVU310
 463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 806              		.loc 1 463 10 is_stmt 0 view .LVU311
 807 0024 7047     		bx	lr
 808              	.LVL52:
 809              	.L57:
ARM GAS  /tmp/ccsqcIPI.s 			page 52


 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 810              		.loc 1 424 5 is_stmt 1 view .LVU312
 811 0026 0529     		cmp	r1, #5
 812 0028 16D8     		bhi	.L56
 813 002a DFE801F0 		tbb	[pc, r1]
 814              	.L47:
 815 002e 03       		.byte	(.L52-.L47)/2
 816 002f 06       		.byte	(.L51-.L47)/2
 817 0030 09       		.byte	(.L50-.L47)/2
 818 0031 0C       		.byte	(.L49-.L47)/2
 819 0032 0F       		.byte	(.L48-.L47)/2
 820 0033 12       		.byte	(.L46-.L47)/2
 821              		.p2align 1
 822              	.L52:
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 823              		.loc 1 427 7 view .LVU313
 427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 824              		.loc 1 427 31 is_stmt 0 view .LVU314
 825 0034 5A64     		str	r2, [r3, #68]
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 826              		.loc 1 428 7 is_stmt 1 view .LVU315
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 827              		.loc 1 411 21 is_stmt 0 view .LVU316
 828 0036 0846     		mov	r0, r1
 428:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 829              		.loc 1 428 7 view .LVU317
 830 0038 F1E7     		b	.L45
 831              	.L51:
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 832              		.loc 1 431 7 is_stmt 1 view .LVU318
 431:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 833              		.loc 1 431 37 is_stmt 0 view .LVU319
 834 003a 9A64     		str	r2, [r3, #72]
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 835              		.loc 1 432 7 is_stmt 1 view .LVU320
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 836              		.loc 1 411 21 is_stmt 0 view .LVU321
 837 003c 0020     		movs	r0, #0
 432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 838              		.loc 1 432 7 view .LVU322
 839 003e EEE7     		b	.L45
 840              	.L50:
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 841              		.loc 1 435 7 is_stmt 1 view .LVU323
 435:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 842              		.loc 1 435 36 is_stmt 0 view .LVU324
 843 0040 DA64     		str	r2, [r3, #76]
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 844              		.loc 1 436 7 is_stmt 1 view .LVU325
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 845              		.loc 1 411 21 is_stmt 0 view .LVU326
 846 0042 0020     		movs	r0, #0
 436:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 847              		.loc 1 436 7 view .LVU327
 848 0044 EBE7     		b	.L45
 849              	.L49:
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
ARM GAS  /tmp/ccsqcIPI.s 			page 53


 850              		.loc 1 439 7 is_stmt 1 view .LVU328
 439:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 851              		.loc 1 439 42 is_stmt 0 view .LVU329
 852 0046 1A65     		str	r2, [r3, #80]
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 853              		.loc 1 440 7 is_stmt 1 view .LVU330
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 854              		.loc 1 411 21 is_stmt 0 view .LVU331
 855 0048 0020     		movs	r0, #0
 440:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 856              		.loc 1 440 7 view .LVU332
 857 004a E8E7     		b	.L45
 858              	.L48:
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 859              		.loc 1 443 7 is_stmt 1 view .LVU333
 443:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 860              		.loc 1 443 32 is_stmt 0 view .LVU334
 861 004c 5A65     		str	r2, [r3, #84]
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 862              		.loc 1 444 7 is_stmt 1 view .LVU335
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 863              		.loc 1 411 21 is_stmt 0 view .LVU336
 864 004e 0020     		movs	r0, #0
 444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 865              		.loc 1 444 7 view .LVU337
 866 0050 E5E7     		b	.L45
 867              	.L46:
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 868              		.loc 1 447 7 is_stmt 1 view .LVU338
 447:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 869              		.loc 1 447 32 is_stmt 0 view .LVU339
 870 0052 9A65     		str	r2, [r3, #88]
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 871              		.loc 1 448 7 is_stmt 1 view .LVU340
 411:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 872              		.loc 1 411 21 is_stmt 0 view .LVU341
 873 0054 0020     		movs	r0, #0
 448:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 874              		.loc 1 448 7 view .LVU342
 875 0056 E2E7     		b	.L45
 876              	.L56:
 424:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 877              		.loc 1 424 5 view .LVU343
 878 0058 0020     		movs	r0, #0
 879 005a E0E7     		b	.L45
 880              	.LVL53:
 881              	.L53:
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 882              		.loc 1 416 12 view .LVU344
 883 005c 0120     		movs	r0, #1
 884              	.LVL54:
 416:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 885              		.loc 1 416 12 view .LVU345
 886 005e 7047     		bx	lr
 887              	.L54:
 420:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 888              		.loc 1 420 3 discriminator 1 view .LVU346
ARM GAS  /tmp/ccsqcIPI.s 			page 54


 889 0060 0220     		movs	r0, #2
 464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 890              		.loc 1 464 1 view .LVU347
 891 0062 7047     		bx	lr
 892              		.cfi_endproc
 893              	.LFE147:
 895              		.section	.text.HAL_MDMA_UnRegisterCallback,"ax",%progbits
 896              		.align	1
 897              		.global	HAL_MDMA_UnRegisterCallback
 898              		.syntax unified
 899              		.thumb
 900              		.thumb_func
 902              	HAL_MDMA_UnRegisterCallback:
 903              	.LVL55:
 904              	.LFB148:
 475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef status = HAL_OK;
 905              		.loc 1 475 1 is_stmt 1 view -0
 906              		.cfi_startproc
 907              		@ args = 0, pretend = 0, frame = 0
 908              		@ frame_needed = 0, uses_anonymous_args = 0
 909              		@ link register save eliminated.
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 910              		.loc 1 476 3 view .LVU349
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 911              		.loc 1 479 3 view .LVU350
 479:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 912              		.loc 1 479 5 is_stmt 0 view .LVU351
 913 0000 0346     		mov	r3, r0
 914 0002 0028     		cmp	r0, #0
 915 0004 33D0     		beq	.L69
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 916              		.loc 1 485 3 is_stmt 1 view .LVU352
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 917              		.loc 1 485 3 view .LVU353
 918 0006 90F83C20 		ldrb	r2, [r0, #60]	@ zero_extendqisi2
 919 000a 012A     		cmp	r2, #1
 920 000c 31D0     		beq	.L70
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 921              		.loc 1 485 3 discriminator 2 view .LVU354
 922 000e 0122     		movs	r2, #1
 923 0010 80F83C20 		strb	r2, [r0, #60]
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 924              		.loc 1 485 3 discriminator 2 view .LVU355
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 925              		.loc 1 487 3 view .LVU356
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 926              		.loc 1 487 35 is_stmt 0 view .LVU357
 927 0014 90F83D00 		ldrb	r0, [r0, #61]	@ zero_extendqisi2
 928              	.LVL56:
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 929              		.loc 1 487 35 view .LVU358
 930 0018 C0B2     		uxtb	r0, r0
 487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 931              		.loc 1 487 5 view .LVU359
 932 001a 9042     		cmp	r0, r2
 933 001c 04D0     		beq	.L73
 531:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
ARM GAS  /tmp/ccsqcIPI.s 			page 55


 934              		.loc 1 531 12 view .LVU360
 935 001e 0120     		movs	r0, #1
 936              	.L60:
 937              	.LVL57:
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 938              		.loc 1 535 3 is_stmt 1 view .LVU361
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 939              		.loc 1 535 3 view .LVU362
 940 0020 0022     		movs	r2, #0
 941 0022 83F83C20 		strb	r2, [r3, #60]
 535:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 942              		.loc 1 535 3 view .LVU363
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 943              		.loc 1 537 3 view .LVU364
 537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 944              		.loc 1 537 10 is_stmt 0 view .LVU365
 945 0026 7047     		bx	lr
 946              	.LVL58:
 947              	.L73:
 489:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 948              		.loc 1 489 5 is_stmt 1 view .LVU366
 949 0028 0629     		cmp	r1, #6
 950 002a F9D8     		bhi	.L60
 951 002c DFE801F0 		tbb	[pc, r1]
 952              	.L62:
 953 0030 04       		.byte	(.L68-.L62)/2
 954 0031 08       		.byte	(.L67-.L62)/2
 955 0032 0B       		.byte	(.L66-.L62)/2
 956 0033 0E       		.byte	(.L65-.L62)/2
 957 0034 11       		.byte	(.L64-.L62)/2
 958 0035 14       		.byte	(.L63-.L62)/2
 959 0036 17       		.byte	(.L61-.L62)/2
 960 0037 00       		.p2align 1
 961              	.L68:
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 962              		.loc 1 492 7 view .LVU367
 492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 963              		.loc 1 492 31 is_stmt 0 view .LVU368
 964 0038 0022     		movs	r2, #0
 965 003a 5A64     		str	r2, [r3, #68]
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 966              		.loc 1 493 7 is_stmt 1 view .LVU369
 476:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 967              		.loc 1 476 21 is_stmt 0 view .LVU370
 968 003c 0846     		mov	r0, r1
 493:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 969              		.loc 1 493 7 view .LVU371
 970 003e EFE7     		b	.L60
 971              	.L67:
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 972              		.loc 1 496 7 is_stmt 1 view .LVU372
 496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 973              		.loc 1 496 37 is_stmt 0 view .LVU373
 974 0040 0020     		movs	r0, #0
 975 0042 9864     		str	r0, [r3, #72]
 497:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 976              		.loc 1 497 7 is_stmt 1 view .LVU374
ARM GAS  /tmp/ccsqcIPI.s 			page 56


 977 0044 ECE7     		b	.L60
 978              	.L66:
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 979              		.loc 1 500 7 view .LVU375
 500:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 980              		.loc 1 500 36 is_stmt 0 view .LVU376
 981 0046 0020     		movs	r0, #0
 982 0048 D864     		str	r0, [r3, #76]
 501:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 983              		.loc 1 501 7 is_stmt 1 view .LVU377
 984 004a E9E7     		b	.L60
 985              	.L65:
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 986              		.loc 1 504 7 view .LVU378
 504:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 987              		.loc 1 504 42 is_stmt 0 view .LVU379
 988 004c 0020     		movs	r0, #0
 989 004e 1865     		str	r0, [r3, #80]
 505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 990              		.loc 1 505 7 is_stmt 1 view .LVU380
 991 0050 E6E7     		b	.L60
 992              	.L64:
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 993              		.loc 1 508 7 view .LVU381
 508:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 994              		.loc 1 508 32 is_stmt 0 view .LVU382
 995 0052 0020     		movs	r0, #0
 996 0054 5865     		str	r0, [r3, #84]
 509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 997              		.loc 1 509 7 is_stmt 1 view .LVU383
 998 0056 E3E7     		b	.L60
 999              	.L63:
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 1000              		.loc 1 512 7 view .LVU384
 512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 1001              		.loc 1 512 32 is_stmt 0 view .LVU385
 1002 0058 0020     		movs	r0, #0
 1003 005a 9865     		str	r0, [r3, #88]
 513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1004              		.loc 1 513 7 is_stmt 1 view .LVU386
 1005 005c E0E7     		b	.L60
 1006              	.L61:
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBufferCpltCallback = NULL;
 1007              		.loc 1 516 7 view .LVU387
 516:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBufferCpltCallback = NULL;
 1008              		.loc 1 516 31 is_stmt 0 view .LVU388
 1009 005e 0020     		movs	r0, #0
 1010 0060 5864     		str	r0, [r3, #68]
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBlockCpltCallback = NULL;
 1011              		.loc 1 517 7 is_stmt 1 view .LVU389
 517:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferBlockCpltCallback = NULL;
 1012              		.loc 1 517 37 is_stmt 0 view .LVU390
 1013 0062 9864     		str	r0, [r3, #72]
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferRepeatBlockCpltCallback = NULL;
 1014              		.loc 1 518 7 is_stmt 1 view .LVU391
 518:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferRepeatBlockCpltCallback = NULL;
 1015              		.loc 1 518 36 is_stmt 0 view .LVU392
ARM GAS  /tmp/ccsqcIPI.s 			page 57


 1016 0064 D864     		str	r0, [r3, #76]
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferErrorCallback = NULL;
 1017              		.loc 1 519 7 is_stmt 1 view .LVU393
 519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferErrorCallback = NULL;
 1018              		.loc 1 519 42 is_stmt 0 view .LVU394
 1019 0066 1865     		str	r0, [r3, #80]
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferAbortCallback = NULL;
 1020              		.loc 1 520 7 is_stmt 1 view .LVU395
 520:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       hmdma->XferAbortCallback = NULL;
 1021              		.loc 1 520 32 is_stmt 0 view .LVU396
 1022 0068 5865     		str	r0, [r3, #84]
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 1023              		.loc 1 521 7 is_stmt 1 view .LVU397
 521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       break;
 1024              		.loc 1 521 32 is_stmt 0 view .LVU398
 1025 006a 9865     		str	r0, [r3, #88]
 522:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1026              		.loc 1 522 7 is_stmt 1 view .LVU399
 1027 006c D8E7     		b	.L60
 1028              	.LVL59:
 1029              	.L69:
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1030              		.loc 1 481 12 is_stmt 0 view .LVU400
 1031 006e 0120     		movs	r0, #1
 1032              	.LVL60:
 481:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1033              		.loc 1 481 12 view .LVU401
 1034 0070 7047     		bx	lr
 1035              	.LVL61:
 1036              	.L70:
 485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1037              		.loc 1 485 3 discriminator 1 view .LVU402
 1038 0072 0220     		movs	r0, #2
 1039              	.LVL62:
 538:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1040              		.loc 1 538 1 view .LVU403
 1041 0074 7047     		bx	lr
 1042              		.cfi_endproc
 1043              	.LFE148:
 1045              		.section	.text.HAL_MDMA_LinkedList_CreateNode,"ax",%progbits
 1046              		.align	1
 1047              		.global	HAL_MDMA_LinkedList_CreateNode
 1048              		.syntax unified
 1049              		.thumb
 1050              		.thumb_func
 1052              	HAL_MDMA_LinkedList_CreateNode:
 1053              	.LVL63:
 1054              	.LFB149:
 569:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t addressMask;
 1055              		.loc 1 569 1 is_stmt 1 view -0
 1056              		.cfi_startproc
 1057              		@ args = 0, pretend = 0, frame = 0
 1058              		@ frame_needed = 0, uses_anonymous_args = 0
 1059              		@ link register save eliminated.
 570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t blockoffset;
 1060              		.loc 1 570 3 view .LVU405
 571:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 58


 1061              		.loc 1 571 3 view .LVU406
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1062              		.loc 1 574 3 view .LVU407
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1063              		.loc 1 574 13 is_stmt 0 view .LVU408
 1064 0000 0246     		mov	r2, r0
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1065              		.loc 1 574 38 view .LVU409
 1066 0002 0B46     		mov	r3, r1
 574:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1067              		.loc 1 574 5 view .LVU410
 1068 0004 0029     		cmp	r1, #0
 1069 0006 18BF     		it	ne
 1070 0008 0028     		cmpne	r0, #0
 1071 000a 7CD0     		beq	.L85
 580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_ENDIANNESS_MODE(pNodeConfig->Init.Endianness));
 1072              		.loc 1 580 3 is_stmt 1 view .LVU411
 581:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_REQUEST(pNodeConfig->Init.Request));
 1073              		.loc 1 581 3 view .LVU412
 582:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_INC(pNodeConfig->Init.SourceInc));
 1074              		.loc 1 582 3 view .LVU413
 583:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_INC(pNodeConfig->Init.DestinationInc));
 1075              		.loc 1 583 3 view .LVU414
 584:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_DATASIZE(pNodeConfig->Init.SourceDataSize));
 1076              		.loc 1 584 3 view .LVU415
 585:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_DATASIZE(pNodeConfig->Init.DestDataSize));
 1077              		.loc 1 585 3 view .LVU416
 586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DATA_ALIGNMENT(pNodeConfig->Init.DataAlignment));
 1078              		.loc 1 586 3 view .LVU417
 587:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_SOURCE_BURST(pNodeConfig->Init.SourceBurst));
 1079              		.loc 1 587 3 view .LVU418
 588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_DESTINATION_BURST(pNodeConfig->Init.DestBurst));
 1080              		.loc 1 588 3 view .LVU419
 589:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BUFFER_TRANSFER_LENGTH(pNodeConfig->Init.BufferTransferLength));
 1081              		.loc 1 589 3 view .LVU420
 590:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_TRANSFER_TRIGGER_MODE(pNodeConfig->Init.TransferTriggerMode));
 1082              		.loc 1 590 3 view .LVU421
 591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(pNodeConfig->Init.SourceBlockAddressOffset));
 1083              		.loc 1 591 3 view .LVU422
 592:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_ADDR_OFFSET(pNodeConfig->Init.DestBlockAddressOffset));
 1084              		.loc 1 592 3 view .LVU423
 593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1085              		.loc 1 593 3 view .LVU424
 595:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_COUNT(pNodeConfig->BlockCount));
 1086              		.loc 1 595 3 view .LVU425
 596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1087              		.loc 1 596 3 view .LVU426
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1088              		.loc 1 600 3 view .LVU427
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1089              		.loc 1 600 15 is_stmt 0 view .LVU428
 1090 000c 0021     		movs	r1, #0
 1091              	.LVL64:
 600:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1092              		.loc 1 600 15 view .LVU429
 1093 000e 4161     		str	r1, [r0, #20]
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CMAR   = 0;
ARM GAS  /tmp/ccsqcIPI.s 			page 59


 1094              		.loc 1 603 3 is_stmt 1 view .LVU430
 603:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CMAR   = 0;
 1095              		.loc 1 603 17 is_stmt 0 view .LVU431
 1096 0010 8161     		str	r1, [r0, #24]
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CMDR   = 0;
 1097              		.loc 1 604 3 is_stmt 1 view .LVU432
 604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->CMDR   = 0;
 1098              		.loc 1 604 17 is_stmt 0 view .LVU433
 1099 0012 0162     		str	r1, [r0, #32]
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->Reserved = 0;
 1100              		.loc 1 605 3 is_stmt 1 view .LVU434
 605:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   pNode->Reserved = 0;
 1101              		.loc 1 605 17 is_stmt 0 view .LVU435
 1102 0014 4162     		str	r1, [r0, #36]
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1103              		.loc 1 606 3 is_stmt 1 view .LVU436
 606:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1104              		.loc 1 606 19 is_stmt 0 view .LVU437
 1105 0016 C161     		str	r1, [r0, #28]
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 1106              		.loc 1 609 3 is_stmt 1 view .LVU438
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 1107              		.loc 1 609 35 is_stmt 0 view .LVU439
 1108 0018 1969     		ldr	r1, [r3, #16]
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 1109              		.loc 1 609 65 view .LVU440
 1110 001a 5869     		ldr	r0, [r3, #20]
 1111              	.LVL65:
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 1112              		.loc 1 609 46 view .LVU441
 1113 001c 0143     		orrs	r1, r1, r0
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       pNodeConfig->Init.DataAlignment| pNodeConfig->Init.SourceBurst            | \
 1114              		.loc 1 610 22 view .LVU442
 1115 001e 9869     		ldr	r0, [r3, #24]
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 1116              		.loc 1 609 81 view .LVU443
 1117 0020 0143     		orrs	r1, r1, r0
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       pNodeConfig->Init.DataAlignment| pNodeConfig->Init.SourceBurst            | \
 1118              		.loc 1 610 57 view .LVU444
 1119 0022 D869     		ldr	r0, [r3, #28]
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       pNodeConfig->Init.DataAlignment| pNodeConfig->Init.SourceBurst            | \
 1120              		.loc 1 610 38 view .LVU445
 1121 0024 0143     		orrs	r1, r1, r0
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNodeConfig->Init.DestBurst                                             | \
 1122              		.loc 1 611 24 view .LVU446
 1123 0026 186A     		ldr	r0, [r3, #32]
 610:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       pNodeConfig->Init.DataAlignment| pNodeConfig->Init.SourceBurst            | \
 1124              		.loc 1 610 81 view .LVU447
 1125 0028 0143     		orrs	r1, r1, r0
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNodeConfig->Init.DestBurst                                             | \
 1126              		.loc 1 611 57 view .LVU448
 1127 002a 986A     		ldr	r0, [r3, #40]
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNodeConfig->Init.DestBurst                                             | \
 1128              		.loc 1 611 38 view .LVU449
 1129 002c 0143     		orrs	r1, r1, r0
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           ((pNodeConfig->Init.BufferTransferLength - 1U) << MDMA_CTCR_TLEN_Pos) | \
 1130              		.loc 1 612 26 view .LVU450
ARM GAS  /tmp/ccsqcIPI.s 			page 60


 1131 002e D86A     		ldr	r0, [r3, #44]
 611:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         pNodeConfig->Init.DestBurst                                             | \
 1132              		.loc 1 611 81 view .LVU451
 1133 0030 0143     		orrs	r1, r1, r0
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             pNodeConfig->Init.TransferTriggerMode;
 1134              		.loc 1 613 30 view .LVU452
 1135 0032 586A     		ldr	r0, [r3, #36]
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             pNodeConfig->Init.TransferTriggerMode;
 1136              		.loc 1 613 52 view .LVU453
 1137 0034 0138     		subs	r0, r0, #1
 612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           ((pNodeConfig->Init.BufferTransferLength - 1U) << MDMA_CTCR_TLEN_Pos) | \
 1138              		.loc 1 612 81 view .LVU454
 1139 0036 41EA8041 		orr	r1, r1, r0, lsl #18
 614:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1140              		.loc 1 614 30 view .LVU455
 1141 003a 5868     		ldr	r0, [r3, #4]
 613:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             pNodeConfig->Init.TransferTriggerMode;
 1142              		.loc 1 613 81 view .LVU456
 1143 003c 0143     		orrs	r1, r1, r0
 609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNodeConfig->Init.SourceDataSize | pNodeConfig->Init.DestDataSize           | \
 1144              		.loc 1 609 15 view .LVU457
 1145 003e 1160     		str	r1, [r2]
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1146              		.loc 1 617 3 is_stmt 1 view .LVU458
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1147              		.loc 1 617 23 is_stmt 0 view .LVU459
 1148 0040 1968     		ldr	r1, [r3]
 617:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1149              		.loc 1 617 5 view .LVU460
 1150 0042 B1F1804F 		cmp	r1, #1073741824
 1151 0046 45D0     		beq	.L87
 1152              	.L76:
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1153              		.loc 1 628 3 is_stmt 1 view .LVU461
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1154              		.loc 1 628 24 is_stmt 0 view .LVU462
 1155 0048 1968     		ldr	r1, [r3]
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1156              		.loc 1 628 5 view .LVU463
 1157 004a B1F1804F 		cmp	r1, #1073741824
 1158 004e 01D0     		beq	.L77
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1159              		.loc 1 628 68 discriminator 1 view .LVU464
 1160 0050 996C     		ldr	r1, [r3, #72]
 628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1161              		.loc 1 628 53 discriminator 1 view .LVU465
 1162 0052 19B1     		cbz	r1, .L78
 1163              	.L77:
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1164              		.loc 1 630 5 is_stmt 1 view .LVU466
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1165              		.loc 1 630 10 is_stmt 0 view .LVU467
 1166 0054 1168     		ldr	r1, [r2]
 630:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1167              		.loc 1 630 17 view .LVU468
 1168 0056 41F00041 		orr	r1, r1, #-2147483648
 1169 005a 1160     		str	r1, [r2]
ARM GAS  /tmp/ccsqcIPI.s 			page 61


 1170              	.L78:
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1171              		.loc 1 634 3 is_stmt 1 view .LVU469
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1172              		.loc 1 634 32 is_stmt 0 view .LVU470
 1173 005c 596C     		ldr	r1, [r3, #68]
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1174              		.loc 1 634 45 view .LVU471
 1175 005e 0139     		subs	r1, r1, #1
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1176              		.loc 1 634 75 view .LVU472
 1177 0060 0905     		lsls	r1, r1, #20
 634:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1178              		.loc 1 634 17 view .LVU473
 1179 0062 5160     		str	r1, [r2, #4]
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1180              		.loc 1 637 3 is_stmt 1 view .LVU474
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1181              		.loc 1 637 23 is_stmt 0 view .LVU475
 1182 0064 196B     		ldr	r1, [r3, #48]
 637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1183              		.loc 1 637 5 view .LVU476
 1184 0066 0029     		cmp	r1, #0
 1185 0068 39DB     		blt	.L88
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1186              		.loc 1 647 5 is_stmt 1 view .LVU477
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1187              		.loc 1 647 77 is_stmt 0 view .LVU478
 1188 006a 89B2     		uxth	r1, r1
 647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1189              		.loc 1 647 18 view .LVU479
 1190 006c 1161     		str	r1, [r2, #16]
 1191              	.L80:
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1192              		.loc 1 651 3 is_stmt 1 view .LVU480
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1193              		.loc 1 651 23 is_stmt 0 view .LVU481
 1194 006e 596B     		ldr	r1, [r3, #52]
 651:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1195              		.loc 1 651 5 view .LVU482
 1196 0070 0029     		cmp	r1, #0
 1197 0072 3DDB     		blt	.L89
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1198              		.loc 1 661 5 is_stmt 1 view .LVU483
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1199              		.loc 1 661 10 is_stmt 0 view .LVU484
 1200 0074 1069     		ldr	r0, [r2, #16]
 661:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1201              		.loc 1 661 18 view .LVU485
 1202 0076 40EA0141 		orr	r1, r0, r1, lsl #16
 1203 007a 1161     		str	r1, [r2, #16]
 1204              	.L82:
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1205              		.loc 1 665 3 is_stmt 1 view .LVU486
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1206              		.loc 1 665 8 is_stmt 0 view .LVU487
 1207 007c 5168     		ldr	r1, [r2, #4]
ARM GAS  /tmp/ccsqcIPI.s 			page 62


 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1208              		.loc 1 665 32 view .LVU488
 1209 007e 186C     		ldr	r0, [r3, #64]
 665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1210              		.loc 1 665 17 view .LVU489
 1211 0080 0143     		orrs	r1, r1, r0
 1212 0082 5160     		str	r1, [r2, #4]
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1213              		.loc 1 668 3 is_stmt 1 view .LVU490
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1214              		.loc 1 668 28 is_stmt 0 view .LVU491
 1215 0084 D96B     		ldr	r1, [r3, #60]
 668:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1216              		.loc 1 668 15 view .LVU492
 1217 0086 D160     		str	r1, [r2, #12]
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1218              		.loc 1 671 3 is_stmt 1 view .LVU493
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1219              		.loc 1 671 28 is_stmt 0 view .LVU494
 1220 0088 996B     		ldr	r1, [r3, #56]
 671:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1221              		.loc 1 671 15 view .LVU495
 1222 008a 9160     		str	r1, [r2, #8]
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1223              		.loc 1 674 3 is_stmt 1 view .LVU496
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1224              		.loc 1 674 23 is_stmt 0 view .LVU497
 1225 008c 1968     		ldr	r1, [r3]
 674:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1226              		.loc 1 674 5 view .LVU498
 1227 008e B1F1804F 		cmp	r1, #1073741824
 1228 0092 05D0     		beq	.L83
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the HW request clear Mask and Data */
 1229              		.loc 1 677 5 is_stmt 1 view .LVU499
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the HW request clear Mask and Data */
 1230              		.loc 1 677 45 is_stmt 0 view .LVU500
 1231 0094 C9B2     		uxtb	r1, r1
 677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /* Set the HW request clear Mask and Data */
 1232              		.loc 1 677 17 view .LVU501
 1233 0096 9161     		str	r1, [r2, #24]
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CMDR = pNodeConfig->PostRequestMaskData;
 1234              		.loc 1 679 5 is_stmt 1 view .LVU502
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CMDR = pNodeConfig->PostRequestMaskData;
 1235              		.loc 1 679 30 is_stmt 0 view .LVU503
 1236 0098 996C     		ldr	r1, [r3, #72]
 679:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CMDR = pNodeConfig->PostRequestMaskData;
 1237              		.loc 1 679 17 view .LVU504
 1238 009a 1162     		str	r1, [r2, #32]
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1239              		.loc 1 680 5 is_stmt 1 view .LVU505
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1240              		.loc 1 680 30 is_stmt 0 view .LVU506
 1241 009c D96C     		ldr	r1, [r3, #76]
 680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1242              		.loc 1 680 17 view .LVU507
 1243 009e 5162     		str	r1, [r2, #36]
 1244              	.L83:
ARM GAS  /tmp/ccsqcIPI.s 			page 63


 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 1245              		.loc 1 683 3 is_stmt 1 view .LVU508
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 1246              		.loc 1 683 28 is_stmt 0 view .LVU509
 1247 00a0 996B     		ldr	r1, [r3, #56]
 683:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 1248              		.loc 1 683 15 view .LVU510
 1249 00a2 01F07F41 		and	r1, r1, #-16777216
 1250              	.LVL66:
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1251              		.loc 1 684 3 is_stmt 1 view .LVU511
 684:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1252              		.loc 1 684 5 is_stmt 0 view .LVU512
 1253 00a6 0029     		cmp	r1, #0
 1254 00a8 18BF     		it	ne
 1255 00aa B1F1005F 		cmpne	r1, #536870912
 1256 00ae 03D1     		bne	.L84
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1257              		.loc 1 687 5 is_stmt 1 view .LVU513
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1258              		.loc 1 687 10 is_stmt 0 view .LVU514
 1259 00b0 9169     		ldr	r1, [r2, #24]
 1260              	.LVL67:
 687:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1261              		.loc 1 687 17 view .LVU515
 1262 00b2 41F48031 		orr	r1, r1, #65536
 1263 00b6 9161     		str	r1, [r2, #24]
 1264              	.LVL68:
 1265              	.L84:
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 1266              		.loc 1 690 3 is_stmt 1 view .LVU516
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 1267              		.loc 1 690 28 is_stmt 0 view .LVU517
 1268 00b8 DB6B     		ldr	r3, [r3, #60]
 1269              	.LVL69:
 690:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((addressMask == 0x20000000U) || (addressMask == 0x00000000U))
 1270              		.loc 1 690 15 view .LVU518
 1271 00ba 03F07F43 		and	r3, r3, #-16777216
 1272              	.LVL70:
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1273              		.loc 1 691 3 is_stmt 1 view .LVU519
 691:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1274              		.loc 1 691 5 is_stmt 0 view .LVU520
 1275 00be 002B     		cmp	r3, #0
 1276 00c0 18BF     		it	ne
 1277 00c2 B3F1005F 		cmpne	r3, #536870912
 1278 00c6 20D1     		bne	.L86
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1279              		.loc 1 694 5 is_stmt 1 view .LVU521
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1280              		.loc 1 694 10 is_stmt 0 view .LVU522
 1281 00c8 9369     		ldr	r3, [r2, #24]
 1282              	.LVL71:
 694:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1283              		.loc 1 694 17 view .LVU523
 1284 00ca 43F40033 		orr	r3, r3, #131072
 1285 00ce 9361     		str	r3, [r2, #24]
ARM GAS  /tmp/ccsqcIPI.s 			page 64


 1286              	.LVL72:
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 1287              		.loc 1 697 10 view .LVU524
 1288 00d0 0020     		movs	r0, #0
 1289 00d2 7047     		bx	lr
 1290              	.LVL73:
 1291              	.L87:
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1292              		.loc 1 619 5 is_stmt 1 view .LVU525
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1293              		.loc 1 619 10 is_stmt 0 view .LVU526
 1294 00d4 1168     		ldr	r1, [r2]
 619:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1295              		.loc 1 619 17 view .LVU527
 1296 00d6 41F08041 		orr	r1, r1, #1073741824
 1297 00da 1160     		str	r1, [r2]
 1298 00dc B4E7     		b	.L76
 1299              	.L88:
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : source repeat block offset */
 1300              		.loc 1 639 5 is_stmt 1 view .LVU528
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : source repeat block offset */
 1301              		.loc 1 639 10 is_stmt 0 view .LVU529
 1302 00de 5168     		ldr	r1, [r2, #4]
 639:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : source repeat block offset */
 1303              		.loc 1 639 19 view .LVU530
 1304 00e0 41F48021 		orr	r1, r1, #262144
 1305 00e4 5160     		str	r1, [r2, #4]
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR = blockoffset & 0x0000FFFFU;
 1306              		.loc 1 641 5 is_stmt 1 view .LVU531
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR = blockoffset & 0x0000FFFFU;
 1307              		.loc 1 641 49 is_stmt 0 view .LVU532
 1308 00e6 196B     		ldr	r1, [r3, #48]
 641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR = blockoffset & 0x0000FFFFU;
 1309              		.loc 1 641 30 view .LVU533
 1310 00e8 4942     		rsbs	r1, r1, #0
 1311              	.LVL74:
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1312              		.loc 1 642 5 is_stmt 1 view .LVU534
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1313              		.loc 1 642 32 is_stmt 0 view .LVU535
 1314 00ea 89B2     		uxth	r1, r1
 1315              	.LVL75:
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1316              		.loc 1 642 18 view .LVU536
 1317 00ec 1161     		str	r1, [r2, #16]
 1318              	.LVL76:
 642:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1319              		.loc 1 642 18 view .LVU537
 1320 00ee BEE7     		b	.L80
 1321              	.LVL77:
 1322              	.L89:
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : destination repeat block offset */
 1323              		.loc 1 653 5 is_stmt 1 view .LVU538
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : destination repeat block offset */
 1324              		.loc 1 653 10 is_stmt 0 view .LVU539
 1325 00f0 5168     		ldr	r1, [r2, #4]
 653:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     /*write new CBRUR Register value : destination repeat block offset */
ARM GAS  /tmp/ccsqcIPI.s 			page 65


 1326              		.loc 1 653 19 view .LVU540
 1327 00f2 41F40021 		orr	r1, r1, #524288
 1328 00f6 5160     		str	r1, [r2, #4]
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 1329              		.loc 1 655 5 is_stmt 1 view .LVU541
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 1330              		.loc 1 655 49 is_stmt 0 view .LVU542
 1331 00f8 596B     		ldr	r1, [r3, #52]
 655:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     pNode->CBRUR |= ((blockoffset & 0x0000FFFFU) << MDMA_CBRUR_DUV_Pos);
 1332              		.loc 1 655 30 view .LVU543
 1333 00fa 4842     		rsbs	r0, r1, #0
 1334              	.LVL78:
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1335              		.loc 1 656 5 is_stmt 1 view .LVU544
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1336              		.loc 1 656 10 is_stmt 0 view .LVU545
 1337 00fc 1169     		ldr	r1, [r2, #16]
 656:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1338              		.loc 1 656 18 view .LVU546
 1339 00fe 41EA0041 		orr	r1, r1, r0, lsl #16
 1340 0102 1161     		str	r1, [r2, #16]
 1341 0104 BAE7     		b	.L82
 1342              	.LVL79:
 1343              	.L85:
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1344              		.loc 1 576 12 view .LVU547
 1345 0106 0120     		movs	r0, #1
 1346              	.LVL80:
 576:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1347              		.loc 1 576 12 view .LVU548
 1348 0108 7047     		bx	lr
 1349              	.LVL81:
 1350              	.L86:
 697:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 1351              		.loc 1 697 10 view .LVU549
 1352 010a 0020     		movs	r0, #0
 698:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1353              		.loc 1 698 1 view .LVU550
 1354 010c 7047     		bx	lr
 1355              		.cfi_endproc
 1356              	.LFE149:
 1358              		.section	.text.HAL_MDMA_LinkedList_AddNode,"ax",%progbits
 1359              		.align	1
 1360              		.global	HAL_MDMA_LinkedList_AddNode
 1361              		.syntax unified
 1362              		.thumb
 1363              		.thumb_func
 1365              	HAL_MDMA_LinkedList_AddNode:
 1366              	.LVL82:
 1367              	.LFB150:
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_LinkNodeTypeDef *pNode;
 1368              		.loc 1 712 1 is_stmt 1 view -0
 1369              		.cfi_startproc
 1370              		@ args = 0, pretend = 0, frame = 0
 1371              		@ frame_needed = 0, uses_anonymous_args = 0
 1372              		@ link register save eliminated.
 713:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t counter = 0, nodeInserted = 0;
ARM GAS  /tmp/ccsqcIPI.s 			page 66


 1373              		.loc 1 713 3 view .LVU552
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1374              		.loc 1 714 3 view .LVU553
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1375              		.loc 1 715 3 view .LVU554
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1376              		.loc 1 718 3 view .LVU555
 718:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1377              		.loc 1 718 5 is_stmt 0 view .LVU556
 1378 0000 0029     		cmp	r1, #0
 1379 0002 18BF     		it	ne
 1380 0004 0028     		cmpne	r0, #0
 1381 0006 6BD0     		beq	.L105
 1382 0008 0346     		mov	r3, r0
 1383 000a 8C46     		mov	ip, r1
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1384              		.loc 1 724 3 is_stmt 1 view .LVU557
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1385              		.loc 1 724 3 view .LVU558
 1386 000c 90F83C10 		ldrb	r1, [r0, #60]	@ zero_extendqisi2
 1387              	.LVL83:
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1388              		.loc 1 724 3 is_stmt 0 view .LVU559
 1389 0010 0129     		cmp	r1, #1
 1390 0012 67D0     		beq	.L106
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1391              		.loc 1 724 3 is_stmt 1 discriminator 2 view .LVU560
 1392 0014 0121     		movs	r1, #1
 1393 0016 80F83C10 		strb	r1, [r0, #60]
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1394              		.loc 1 724 3 discriminator 2 view .LVU561
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1395              		.loc 1 726 3 view .LVU562
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1396              		.loc 1 726 35 is_stmt 0 view .LVU563
 1397 001a 90F83D00 		ldrb	r0, [r0, #61]	@ zero_extendqisi2
 1398              	.LVL84:
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1399              		.loc 1 726 35 view .LVU564
 1400 001e C0B2     		uxtb	r0, r0
 726:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1401              		.loc 1 726 5 view .LVU565
 1402 0020 8842     		cmp	r0, r1
 1403 0022 04D0     		beq	.L115
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1404              		.loc 1 828 5 is_stmt 1 view .LVU566
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1405              		.loc 1 828 5 view .LVU567
 1406 0024 0022     		movs	r2, #0
 1407              	.LVL85:
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1408              		.loc 1 828 5 is_stmt 0 view .LVU568
 1409 0026 83F83C20 		strb	r2, [r3, #60]
 828:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1410              		.loc 1 828 5 is_stmt 1 view .LVU569
 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1411              		.loc 1 831 5 view .LVU570
ARM GAS  /tmp/ccsqcIPI.s 			page 67


 831:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1412              		.loc 1 831 12 is_stmt 0 view .LVU571
 1413 002a 0220     		movs	r0, #2
 1414 002c 7047     		bx	lr
 1415              	.LVL86:
 1416              	.L115:
 712:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_LinkNodeTypeDef *pNode;
 1417              		.loc 1 712 1 view .LVU572
 1418 002e F0B4     		push	{r4, r5, r6, r7}
 1419              	.LCFI8:
 1420              		.cfi_def_cfa_offset 16
 1421              		.cfi_offset 4, -16
 1422              		.cfi_offset 5, -12
 1423              		.cfi_offset 6, -8
 1424              		.cfi_offset 7, -4
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1425              		.loc 1 729 5 is_stmt 1 view .LVU573
 729:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1426              		.loc 1 729 18 is_stmt 0 view .LVU574
 1427 0030 0221     		movs	r1, #2
 1428 0032 83F83D10 		strb	r1, [r3, #61]
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1429              		.loc 1 732 5 is_stmt 1 view .LVU575
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1430              		.loc 1 732 23 is_stmt 0 view .LVU576
 1431 0036 DF6D     		ldr	r7, [r3, #92]
 732:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1432              		.loc 1 732 7 view .LVU577
 1433 0038 2FB1     		cbz	r7, .L116
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1434              		.loc 1 756 10 is_stmt 1 view .LVU578
 756:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1435              		.loc 1 756 12 is_stmt 0 view .LVU579
 1436 003a 6745     		cmp	r7, ip
 1437 003c 48D0     		beq	.L94
 759:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       while((counter < hmdma->LinkedListNodeCounter) && (hal_status == HAL_OK))
 1438              		.loc 1 759 13 view .LVU580
 1439 003e 3C46     		mov	r4, r7
 715:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1440              		.loc 1 715 21 view .LVU581
 1441 0040 0026     		movs	r6, #0
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1442              		.loc 1 714 12 view .LVU582
 1443 0042 3146     		mov	r1, r6
 1444 0044 10E0     		b	.L95
 1445              	.L116:
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1446              		.loc 1 734 7 is_stmt 1 view .LVU583
 734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1447              		.loc 1 734 9 is_stmt 0 view .LVU584
 1448 0046 002A     		cmp	r2, #0
 1449 0048 42D1     		bne	.L94
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Set the MDMA handle First linked List node*/
 1450              		.loc 1 739 9 is_stmt 1 view .LVU585
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Set the MDMA handle First linked List node*/
 1451              		.loc 1 739 14 is_stmt 0 view .LVU586
 1452 004a 1A68     		ldr	r2, [r3]
ARM GAS  /tmp/ccsqcIPI.s 			page 68


 1453              	.LVL87:
 739:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Set the MDMA handle First linked List node*/
 1454              		.loc 1 739 31 view .LVU587
 1455 004c C2F824C0 		str	ip, [r2, #36]
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1456              		.loc 1 741 9 is_stmt 1 view .LVU588
 741:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1457              		.loc 1 741 43 is_stmt 0 view .LVU589
 1458 0050 C3F85CC0 		str	ip, [r3, #92]
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1459              		.loc 1 744 9 is_stmt 1 view .LVU590
 744:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1460              		.loc 1 744 24 is_stmt 0 view .LVU591
 1461 0054 0020     		movs	r0, #0
 1462 0056 CCF81400 		str	r0, [ip, #20]
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1463              		.loc 1 747 9 is_stmt 1 view .LVU592
 747:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1464              		.loc 1 747 42 is_stmt 0 view .LVU593
 1465 005a C3F860C0 		str	ip, [r3, #96]
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1466              		.loc 1 749 9 is_stmt 1 view .LVU594
 749:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1467              		.loc 1 749 38 is_stmt 0 view .LVU595
 1468 005e 0122     		movs	r2, #1
 1469 0060 5A66     		str	r2, [r3, #100]
 1470 0062 35E0     		b	.L94
 1471              	.LVL88:
 1472              	.L96:
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         counter++;
 1473              		.loc 1 766 9 is_stmt 1 view .LVU596
 766:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         counter++;
 1474              		.loc 1 766 46 is_stmt 0 view .LVU597
 1475 0064 6469     		ldr	r4, [r4, #20]
 1476              	.LVL89:
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1477              		.loc 1 767 9 is_stmt 1 view .LVU598
 767:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1478              		.loc 1 767 16 is_stmt 0 view .LVU599
 1479 0066 0131     		adds	r1, r1, #1
 1480              	.LVL90:
 1481              	.L95:
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1482              		.loc 1 760 54 is_stmt 1 view .LVU600
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1483              		.loc 1 760 29 is_stmt 0 view .LVU601
 1484 0068 5D6E     		ldr	r5, [r3, #100]
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1485              		.loc 1 760 54 view .LVU602
 1486 006a 8D42     		cmp	r5, r1
 1487 006c 05D9     		bls	.L97
 760:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1488              		.loc 1 760 54 discriminator 1 view .LVU603
 1489 006e 26B9     		cbnz	r6, .L97
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1490              		.loc 1 762 9 is_stmt 1 view .LVU604
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
ARM GAS  /tmp/ccsqcIPI.s 			page 69


 1491              		.loc 1 762 17 is_stmt 0 view .LVU605
 1492 0070 6569     		ldr	r5, [r4, #20]
 762:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1493              		.loc 1 762 11 view .LVU606
 1494 0072 6545     		cmp	r5, ip
 1495 0074 F6D1     		bne	.L96
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1496              		.loc 1 764 22 view .LVU607
 1497 0076 0646     		mov	r6, r0
 1498              	.LVL91:
 764:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1499              		.loc 1 764 22 view .LVU608
 1500 0078 F4E7     		b	.L96
 1501              	.LVL92:
 1502              	.L97:
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1503              		.loc 1 770 7 is_stmt 1 view .LVU609
 770:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1504              		.loc 1 770 9 is_stmt 0 view .LVU610
 1505 007a 46BB     		cbnz	r6, .L109
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1506              		.loc 1 773 9 is_stmt 1 view .LVU611
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1507              		.loc 1 773 31 is_stmt 0 view .LVU612
 1508 007c 196E     		ldr	r1, [r3, #96]
 1509              	.LVL93:
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1510              		.loc 1 773 11 view .LVU613
 1511 007e 9142     		cmp	r1, r2
 1512 0080 03D0     		beq	.L99
 773:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1513              		.loc 1 773 60 discriminator 1 view .LVU614
 1514 0082 12B1     		cbz	r2, .L99
 714:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1515              		.loc 1 714 25 view .LVU615
 1516 0084 0025     		movs	r5, #0
 787:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           while((counter < hmdma->LinkedListNodeCounter) && (nodeInserted == 0U))
 1517              		.loc 1 787 19 view .LVU616
 1518 0086 2946     		mov	r1, r5
 1519 0088 15E0     		b	.L100
 1520              	.L99:
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LastLinkedListNodeAddress->CLAR = (uint32_t)pNewNode;
 1521              		.loc 1 776 11 is_stmt 1 view .LVU617
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LastLinkedListNodeAddress->CLAR = (uint32_t)pNewNode;
 1522              		.loc 1 776 60 is_stmt 0 view .LVU618
 1523 008a 4A69     		ldr	r2, [r1, #20]
 1524              	.LVL94:
 776:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           hmdma->LastLinkedListNodeAddress->CLAR = (uint32_t)pNewNode;
 1525              		.loc 1 776 26 view .LVU619
 1526 008c CCF81420 		str	r2, [ip, #20]
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle last node address */
 1527              		.loc 1 777 11 is_stmt 1 view .LVU620
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle last node address */
 1528              		.loc 1 777 16 is_stmt 0 view .LVU621
 1529 0090 1A6E     		ldr	r2, [r3, #96]
 777:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle last node address */
 1530              		.loc 1 777 50 view .LVU622
ARM GAS  /tmp/ccsqcIPI.s 			page 70


 1531 0092 C2F814C0 		str	ip, [r2, #20]
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Increment the linked list node counter */
 1532              		.loc 1 779 11 is_stmt 1 view .LVU623
 779:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Increment the linked list node counter */
 1533              		.loc 1 779 44 is_stmt 0 view .LVU624
 1534 0096 C3F860C0 		str	ip, [r3, #96]
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1535              		.loc 1 781 11 is_stmt 1 view .LVU625
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1536              		.loc 1 781 16 is_stmt 0 view .LVU626
 1537 009a 5A6E     		ldr	r2, [r3, #100]
 781:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1538              		.loc 1 781 39 view .LVU627
 1539 009c 0132     		adds	r2, r2, #1
 1540 009e 5A66     		str	r2, [r3, #100]
 1541 00a0 3046     		mov	r0, r6
 1542 00a2 15E0     		b	.L94
 1543              	.LVL95:
 1544              	.L117:
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               pNode->CLAR = (uint32_t)pNewNode;
 1545              		.loc 1 794 15 is_stmt 1 view .LVU628
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               pNode->CLAR = (uint32_t)pNewNode;
 1546              		.loc 1 794 37 is_stmt 0 view .LVU629
 1547 00a4 7C69     		ldr	r4, [r7, #20]
 794:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               pNode->CLAR = (uint32_t)pNewNode;
 1548              		.loc 1 794 30 view .LVU630
 1549 00a6 CCF81440 		str	r4, [ip, #20]
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               /* Increment the linked list node counter */
 1550              		.loc 1 795 15 is_stmt 1 view .LVU631
 795:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               /* Increment the linked list node counter */
 1551              		.loc 1 795 27 is_stmt 0 view .LVU632
 1552 00aa C7F814C0 		str	ip, [r7, #20]
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               nodeInserted = 1;
 1553              		.loc 1 797 15 is_stmt 1 view .LVU633
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               nodeInserted = 1;
 1554              		.loc 1 797 20 is_stmt 0 view .LVU634
 1555 00ae 5C6E     		ldr	r4, [r3, #100]
 797:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****               nodeInserted = 1;
 1556              		.loc 1 797 43 view .LVU635
 1557 00b0 0134     		adds	r4, r4, #1
 1558 00b2 5C66     		str	r4, [r3, #100]
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 1559              		.loc 1 798 15 is_stmt 1 view .LVU636
 1560              	.LVL96:
 798:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 1561              		.loc 1 798 28 is_stmt 0 view .LVU637
 1562 00b4 0125     		movs	r5, #1
 1563              	.LVL97:
 1564              	.L100:
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1565              		.loc 1 788 58 is_stmt 1 view .LVU638
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1566              		.loc 1 788 33 is_stmt 0 view .LVU639
 1567 00b6 5C6E     		ldr	r4, [r3, #100]
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1568              		.loc 1 788 58 view .LVU640
 1569 00b8 8C42     		cmp	r4, r1
ARM GAS  /tmp/ccsqcIPI.s 			page 71


 1570 00ba 05D9     		bls	.L103
 788:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1571              		.loc 1 788 58 discriminator 1 view .LVU641
 1572 00bc 25B9     		cbnz	r5, .L103
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             if(pNode == pPrevNode)
 1573              		.loc 1 790 13 is_stmt 1 view .LVU642
 790:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             if(pNode == pPrevNode)
 1574              		.loc 1 790 20 is_stmt 0 view .LVU643
 1575 00be 0131     		adds	r1, r1, #1
 1576              	.LVL98:
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             {
 1577              		.loc 1 791 13 is_stmt 1 view .LVU644
 791:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             {
 1578              		.loc 1 791 15 is_stmt 0 view .LVU645
 1579 00c0 9742     		cmp	r7, r2
 1580 00c2 EFD0     		beq	.L117
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 1581              		.loc 1 802 15 is_stmt 1 view .LVU646
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 1582              		.loc 1 802 52 is_stmt 0 view .LVU647
 1583 00c4 7F69     		ldr	r7, [r7, #20]
 1584              	.LVL99:
 802:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****             }
 1585              		.loc 1 802 52 view .LVU648
 1586 00c6 F6E7     		b	.L100
 1587              	.L103:
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1588              		.loc 1 806 11 is_stmt 1 view .LVU649
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1589              		.loc 1 806 13 is_stmt 0 view .LVU650
 1590 00c8 15B1     		cbz	r5, .L94
 1591 00ca 3046     		mov	r0, r6
 1592 00cc 00E0     		b	.L94
 1593              	.LVL100:
 1594              	.L109:
 806:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1595              		.loc 1 806 13 view .LVU651
 1596 00ce 3046     		mov	r0, r6
 1597              	.LVL101:
 1598              	.L94:
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1599              		.loc 1 819 5 is_stmt 1 view .LVU652
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1600              		.loc 1 819 5 view .LVU653
 1601 00d0 0022     		movs	r2, #0
 1602 00d2 83F83C20 		strb	r2, [r3, #60]
 819:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1603              		.loc 1 819 5 view .LVU654
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1604              		.loc 1 821 5 view .LVU655
 821:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1605              		.loc 1 821 18 is_stmt 0 view .LVU656
 1606 00d6 0122     		movs	r2, #1
 1607 00d8 83F83D20 		strb	r2, [r3, #61]
 823:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1608              		.loc 1 823 5 is_stmt 1 view .LVU657
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 72


 1609              		.loc 1 833 1 is_stmt 0 view .LVU658
 1610 00dc F0BC     		pop	{r4, r5, r6, r7}
 1611              	.LCFI9:
 1612              		.cfi_restore 7
 1613              		.cfi_restore 6
 1614              		.cfi_restore 5
 1615              		.cfi_restore 4
 1616              		.cfi_def_cfa_offset 0
 1617 00de 7047     		bx	lr
 1618              	.LVL102:
 1619              	.L105:
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1620              		.loc 1 720 12 view .LVU659
 1621 00e0 0120     		movs	r0, #1
 1622              	.LVL103:
 720:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1623              		.loc 1 720 12 view .LVU660
 1624 00e2 7047     		bx	lr
 1625              	.LVL104:
 1626              	.L106:
 724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1627              		.loc 1 724 3 discriminator 1 view .LVU661
 1628 00e4 0220     		movs	r0, #2
 1629              	.LVL105:
 833:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1630              		.loc 1 833 1 view .LVU662
 1631 00e6 7047     		bx	lr
 1632              		.cfi_endproc
 1633              	.LFE150:
 1635              		.section	.text.HAL_MDMA_LinkedList_RemoveNode,"ax",%progbits
 1636              		.align	1
 1637              		.global	HAL_MDMA_LinkedList_RemoveNode
 1638              		.syntax unified
 1639              		.thumb
 1640              		.thumb_func
 1642              	HAL_MDMA_LinkedList_RemoveNode:
 1643              	.LVL106:
 1644              	.LFB151:
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_LinkNodeTypeDef *ptmpNode;
 1645              		.loc 1 845 1 is_stmt 1 view -0
 1646              		.cfi_startproc
 1647              		@ args = 0, pretend = 0, frame = 0
 1648              		@ frame_needed = 0, uses_anonymous_args = 0
 1649              		@ link register save eliminated.
 846:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t counter = 0, nodeDeleted = 0;
 1650              		.loc 1 846 3 view .LVU664
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1651              		.loc 1 847 3 view .LVU665
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1652              		.loc 1 848 3 view .LVU666
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1653              		.loc 1 851 3 view .LVU667
 851:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1654              		.loc 1 851 5 is_stmt 0 view .LVU668
 1655 0000 0029     		cmp	r1, #0
 1656 0002 18BF     		it	ne
 1657 0004 0028     		cmpne	r0, #0
ARM GAS  /tmp/ccsqcIPI.s 			page 73


 1658 0006 60D0     		beq	.L130
 1659 0008 0346     		mov	r3, r0
 1660 000a 8C46     		mov	ip, r1
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1661              		.loc 1 857 3 is_stmt 1 view .LVU669
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1662              		.loc 1 857 3 view .LVU670
 1663 000c 90F83C20 		ldrb	r2, [r0, #60]	@ zero_extendqisi2
 1664 0010 012A     		cmp	r2, #1
 1665 0012 5CD0     		beq	.L131
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1666              		.loc 1 857 3 discriminator 2 view .LVU671
 1667 0014 0122     		movs	r2, #1
 1668 0016 80F83C20 		strb	r2, [r0, #60]
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1669              		.loc 1 857 3 discriminator 2 view .LVU672
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1670              		.loc 1 859 3 view .LVU673
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1671              		.loc 1 859 35 is_stmt 0 view .LVU674
 1672 001a 90F83D00 		ldrb	r0, [r0, #61]	@ zero_extendqisi2
 1673              	.LVL107:
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1674              		.loc 1 859 35 view .LVU675
 1675 001e C0B2     		uxtb	r0, r0
 859:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1676              		.loc 1 859 5 view .LVU676
 1677 0020 9042     		cmp	r0, r2
 1678 0022 04D0     		beq	.L137
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1679              		.loc 1 944 5 is_stmt 1 view .LVU677
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1680              		.loc 1 944 5 view .LVU678
 1681 0024 0022     		movs	r2, #0
 1682 0026 83F83C20 		strb	r2, [r3, #60]
 944:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1683              		.loc 1 944 5 view .LVU679
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1684              		.loc 1 947 5 view .LVU680
 947:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1685              		.loc 1 947 12 is_stmt 0 view .LVU681
 1686 002a 0220     		movs	r0, #2
 1687 002c 7047     		bx	lr
 1688              	.L137:
 845:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   MDMA_LinkNodeTypeDef *ptmpNode;
 1689              		.loc 1 845 1 view .LVU682
 1690 002e 30B4     		push	{r4, r5}
 1691              	.LCFI10:
 1692              		.cfi_def_cfa_offset 8
 1693              		.cfi_offset 4, -8
 1694              		.cfi_offset 5, -4
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1695              		.loc 1 862 5 is_stmt 1 view .LVU683
 862:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1696              		.loc 1 862 18 is_stmt 0 view .LVU684
 1697 0030 0222     		movs	r2, #2
 1698 0032 83F83D20 		strb	r2, [r3, #61]
ARM GAS  /tmp/ccsqcIPI.s 			page 74


 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1699              		.loc 1 865 5 is_stmt 1 view .LVU685
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1700              		.loc 1 865 24 is_stmt 0 view .LVU686
 1701 0036 DA6D     		ldr	r2, [r3, #92]
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1702              		.loc 1 865 7 view .LVU687
 1703 0038 002A     		cmp	r2, #0
 1704 003a 3ED0     		beq	.L121
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1705              		.loc 1 865 79 discriminator 1 view .LVU688
 1706 003c 196E     		ldr	r1, [r3, #96]
 1707              	.LVL108:
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1708              		.loc 1 865 60 discriminator 1 view .LVU689
 1709 003e 0029     		cmp	r1, #0
 1710 0040 3BD0     		beq	.L121
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1711              		.loc 1 865 123 discriminator 2 view .LVU690
 1712 0042 5C6E     		ldr	r4, [r3, #100]
 865:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1713              		.loc 1 865 114 discriminator 2 view .LVU691
 1714 0044 002C     		cmp	r4, #0
 1715 0046 38D0     		beq	.L121
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1716              		.loc 1 869 10 is_stmt 1 view .LVU692
 869:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1717              		.loc 1 869 12 is_stmt 0 view .LVU693
 1718 0048 6245     		cmp	r2, ip
 1719 004a 02D0     		beq	.L138
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1720              		.loc 1 847 25 view .LVU694
 1721 004c 0025     		movs	r5, #0
 847:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1722              		.loc 1 847 12 view .LVU695
 1723 004e 2946     		mov	r1, r5
 1724 0050 25E0     		b	.L122
 1725              	.L138:
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1726              		.loc 1 872 7 is_stmt 1 view .LVU696
 872:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1727              		.loc 1 872 9 is_stmt 0 view .LVU697
 1728 0052 6145     		cmp	r1, ip
 1729 0054 0ED0     		beq	.L139
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1730              		.loc 1 885 9 is_stmt 1 view .LVU698
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1731              		.loc 1 885 91 is_stmt 0 view .LVU699
 1732 0056 4869     		ldr	r0, [r1, #20]
 885:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1733              		.loc 1 885 11 view .LVU700
 1734 0058 8242     		cmp	r2, r0
 1735 005a 12D0     		beq	.L140
 1736              	.L124:
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = (MDMA_LinkNodeTypeDef *)hmdma->Instance->CLAR;
 1737              		.loc 1 894 9 is_stmt 1 view .LVU701
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = (MDMA_LinkNodeTypeDef *)hmdma->Instance->CLAR;
ARM GAS  /tmp/ccsqcIPI.s 			page 75


 1738              		.loc 1 894 14 is_stmt 0 view .LVU702
 1739 005c 1A68     		ldr	r2, [r3]
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = (MDMA_LinkNodeTypeDef *)hmdma->Instance->CLAR;
 1740              		.loc 1 894 38 view .LVU703
 1741 005e DCF81410 		ldr	r1, [ip, #20]
 894:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->FirstLinkedListNodeAddress = (MDMA_LinkNodeTypeDef *)hmdma->Instance->CLAR;
 1742              		.loc 1 894 31 view .LVU704
 1743 0062 5162     		str	r1, [r2, #36]
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update the Handle node counter */
 1744              		.loc 1 895 9 is_stmt 1 view .LVU705
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update the Handle node counter */
 1745              		.loc 1 895 74 is_stmt 0 view .LVU706
 1746 0064 1A68     		ldr	r2, [r3]
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update the Handle node counter */
 1747              		.loc 1 895 84 view .LVU707
 1748 0066 526A     		ldr	r2, [r2, #36]
 895:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         /* Update the Handle node counter */
 1749              		.loc 1 895 43 view .LVU708
 1750 0068 DA65     		str	r2, [r3, #92]
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1751              		.loc 1 897 9 is_stmt 1 view .LVU709
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1752              		.loc 1 897 14 is_stmt 0 view .LVU710
 1753 006a 5A6E     		ldr	r2, [r3, #100]
 897:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1754              		.loc 1 897 37 view .LVU711
 1755 006c 013A     		subs	r2, r2, #1
 1756 006e 5A66     		str	r2, [r3, #100]
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1757              		.loc 1 848 21 view .LVU712
 1758 0070 0020     		movs	r0, #0
 1759 0072 22E0     		b	.L121
 1760              	.L139:
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LastLinkedListNodeAddress  = 0;
 1761              		.loc 1 877 9 is_stmt 1 view .LVU713
 877:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LastLinkedListNodeAddress  = 0;
 1762              		.loc 1 877 43 is_stmt 0 view .LVU714
 1763 0074 0020     		movs	r0, #0
 1764 0076 D865     		str	r0, [r3, #92]
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LinkedListNodeCounter = 0;
 1765              		.loc 1 878 9 is_stmt 1 view .LVU715
 878:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         hmdma->LinkedListNodeCounter = 0;
 1766              		.loc 1 878 43 is_stmt 0 view .LVU716
 1767 0078 1866     		str	r0, [r3, #96]
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1768              		.loc 1 879 9 is_stmt 1 view .LVU717
 879:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1769              		.loc 1 879 38 is_stmt 0 view .LVU718
 1770 007a 5866     		str	r0, [r3, #100]
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1771              		.loc 1 881 9 is_stmt 1 view .LVU719
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1772              		.loc 1 881 14 is_stmt 0 view .LVU720
 1773 007c 1A68     		ldr	r2, [r3]
 881:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 1774              		.loc 1 881 31 view .LVU721
 1775 007e 5062     		str	r0, [r2, #36]
ARM GAS  /tmp/ccsqcIPI.s 			page 76


 1776 0080 1BE0     		b	.L121
 1777              	.L140:
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1778              		.loc 1 888 11 is_stmt 1 view .LVU722
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1779              		.loc 1 888 57 is_stmt 0 view .LVU723
 1780 0082 DCF81420 		ldr	r2, [ip, #20]
 888:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1781              		.loc 1 888 50 view .LVU724
 1782 0086 4A61     		str	r2, [r1, #20]
 1783 0088 E8E7     		b	.L124
 1784              	.LVL109:
 1785              	.L142:
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1786              		.loc 1 910 11 is_stmt 1 view .LVU725
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1787              		.loc 1 910 28 is_stmt 0 view .LVU726
 1788 008a 1C6E     		ldr	r4, [r3, #96]
 910:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           {
 1789              		.loc 1 910 13 view .LVU727
 1790 008c 6445     		cmp	r4, ip
 1791 008e 10D0     		beq	.L141
 1792              	.L126:
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           nodeDeleted = 1;
 1793              		.loc 1 916 11 is_stmt 1 view .LVU728
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           nodeDeleted = 1;
 1794              		.loc 1 916 33 is_stmt 0 view .LVU729
 1795 0090 DCF81440 		ldr	r4, [ip, #20]
 916:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           nodeDeleted = 1;
 1796              		.loc 1 916 26 view .LVU730
 1797 0094 5461     		str	r4, [r2, #20]
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle node counter */
 1798              		.loc 1 917 11 is_stmt 1 view .LVU731
 1799              	.LVL110:
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1800              		.loc 1 919 11 view .LVU732
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1801              		.loc 1 919 16 is_stmt 0 view .LVU733
 1802 0096 5C6E     		ldr	r4, [r3, #100]
 919:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1803              		.loc 1 919 39 view .LVU734
 1804 0098 013C     		subs	r4, r4, #1
 1805 009a 5C66     		str	r4, [r3, #100]
 917:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           /* Update the Handle node counter */
 1806              		.loc 1 917 23 view .LVU735
 1807 009c 0125     		movs	r5, #1
 1808              	.LVL111:
 1809              	.L122:
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1810              		.loc 1 904 54 is_stmt 1 view .LVU736
 1811 009e 5C6E     		ldr	r4, [r3, #100]
 1812 00a0 8C42     		cmp	r4, r1
 1813 00a2 08D9     		bls	.L128
 904:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1814              		.loc 1 904 54 is_stmt 0 discriminator 1 view .LVU737
 1815 00a4 3DB9     		cbnz	r5, .L128
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if(ptmpNode->CLAR == ((uint32_t)pNode))
ARM GAS  /tmp/ccsqcIPI.s 			page 77


 1816              		.loc 1 906 9 is_stmt 1 view .LVU738
 906:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         if(ptmpNode->CLAR == ((uint32_t)pNode))
 1817              		.loc 1 906 16 is_stmt 0 view .LVU739
 1818 00a6 0131     		adds	r1, r1, #1
 1819              	.LVL112:
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1820              		.loc 1 907 9 is_stmt 1 view .LVU740
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1821              		.loc 1 907 20 is_stmt 0 view .LVU741
 1822 00a8 5469     		ldr	r4, [r2, #20]
 907:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 1823              		.loc 1 907 11 view .LVU742
 1824 00aa 6445     		cmp	r4, ip
 1825 00ac EDD0     		beq	.L142
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1826              		.loc 1 923 11 is_stmt 1 view .LVU743
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1827              		.loc 1 923 54 is_stmt 0 view .LVU744
 1828 00ae 5269     		ldr	r2, [r2, #20]
 1829              	.LVL113:
 923:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 1830              		.loc 1 923 54 view .LVU745
 1831 00b0 F5E7     		b	.L122
 1832              	.L141:
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           }
 1833              		.loc 1 913 13 is_stmt 1 view .LVU746
 913:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****           }
 1834              		.loc 1 913 46 is_stmt 0 view .LVU747
 1835 00b2 1A66     		str	r2, [r3, #96]
 1836 00b4 ECE7     		b	.L126
 1837              	.L128:
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1838              		.loc 1 927 7 is_stmt 1 view .LVU748
 927:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 1839              		.loc 1 927 9 is_stmt 0 view .LVU749
 1840 00b6 05B1     		cbz	r5, .L121
 848:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1841              		.loc 1 848 21 view .LVU750
 1842 00b8 0020     		movs	r0, #0
 1843              	.LVL114:
 1844              	.L121:
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1845              		.loc 1 935 5 is_stmt 1 view .LVU751
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1846              		.loc 1 935 5 view .LVU752
 1847 00ba 0022     		movs	r2, #0
 1848 00bc 83F83C20 		strb	r2, [r3, #60]
 935:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1849              		.loc 1 935 5 view .LVU753
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1850              		.loc 1 937 5 view .LVU754
 937:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1851              		.loc 1 937 18 is_stmt 0 view .LVU755
 1852 00c0 0122     		movs	r2, #1
 1853 00c2 83F83D20 		strb	r2, [r3, #61]
 939:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1854              		.loc 1 939 5 is_stmt 1 view .LVU756
ARM GAS  /tmp/ccsqcIPI.s 			page 78


 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1855              		.loc 1 949 1 is_stmt 0 view .LVU757
 1856 00c6 30BC     		pop	{r4, r5}
 1857              	.LCFI11:
 1858              		.cfi_restore 5
 1859              		.cfi_restore 4
 1860              		.cfi_def_cfa_offset 0
 1861 00c8 7047     		bx	lr
 1862              	.LVL115:
 1863              	.L130:
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1864              		.loc 1 853 12 view .LVU758
 1865 00ca 0120     		movs	r0, #1
 1866              	.LVL116:
 853:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1867              		.loc 1 853 12 view .LVU759
 1868 00cc 7047     		bx	lr
 1869              	.LVL117:
 1870              	.L131:
 857:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1871              		.loc 1 857 3 discriminator 1 view .LVU760
 1872 00ce 0220     		movs	r0, #2
 1873              	.LVL118:
 949:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1874              		.loc 1 949 1 view .LVU761
 1875 00d0 7047     		bx	lr
 1876              		.cfi_endproc
 1877              	.LFE151:
 1879              		.section	.text.HAL_MDMA_LinkedList_EnableCircularMode,"ax",%progbits
 1880              		.align	1
 1881              		.global	HAL_MDMA_LinkedList_EnableCircularMode
 1882              		.syntax unified
 1883              		.thumb
 1884              		.thumb_func
 1886              	HAL_MDMA_LinkedList_EnableCircularMode:
 1887              	.LVL119:
 1888              	.LFB152:
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1889              		.loc 1 958 1 is_stmt 1 view -0
 1890              		.cfi_startproc
 1891              		@ args = 0, pretend = 0, frame = 0
 1892              		@ frame_needed = 0, uses_anonymous_args = 0
 1893              		@ link register save eliminated.
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1894              		.loc 1 959 3 view .LVU763
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1895              		.loc 1 962 3 view .LVU764
 962:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1896              		.loc 1 962 5 is_stmt 0 view .LVU765
 1897 0000 30B3     		cbz	r0, .L146
 1898 0002 0346     		mov	r3, r0
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1899              		.loc 1 968 3 is_stmt 1 view .LVU766
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1900              		.loc 1 968 3 view .LVU767
 1901 0004 90F83C20 		ldrb	r2, [r0, #60]	@ zero_extendqisi2
 1902 0008 012A     		cmp	r2, #1
ARM GAS  /tmp/ccsqcIPI.s 			page 79


 1903 000a 23D0     		beq	.L147
 958:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 1904              		.loc 1 958 1 is_stmt 0 view .LVU768
 1905 000c 10B4     		push	{r4}
 1906              	.LCFI12:
 1907              		.cfi_def_cfa_offset 4
 1908              		.cfi_offset 4, -4
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1909              		.loc 1 968 3 is_stmt 1 discriminator 2 view .LVU769
 1910 000e 0122     		movs	r2, #1
 1911 0010 80F83C20 		strb	r2, [r0, #60]
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1912              		.loc 1 968 3 discriminator 2 view .LVU770
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1913              		.loc 1 970 3 view .LVU771
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1914              		.loc 1 970 35 is_stmt 0 view .LVU772
 1915 0014 90F83D00 		ldrb	r0, [r0, #61]	@ zero_extendqisi2
 1916              	.LVL120:
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1917              		.loc 1 970 35 view .LVU773
 1918 0018 C0B2     		uxtb	r0, r0
 970:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 1919              		.loc 1 970 5 view .LVU774
 1920 001a 9042     		cmp	r0, r2
 1921 001c 09D0     		beq	.L153
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1922              		.loc 1 959 21 view .LVU775
 1923 001e 0020     		movs	r0, #0
 1924              	.L145:
 1925              	.LVL121:
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1926              		.loc 1 988 3 is_stmt 1 view .LVU776
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1927              		.loc 1 988 3 view .LVU777
 1928 0020 0022     		movs	r2, #0
 1929 0022 83F83C20 		strb	r2, [r3, #60]
 988:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1930              		.loc 1 988 3 view .LVU778
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1931              		.loc 1 990 3 view .LVU779
 990:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1932              		.loc 1 990 16 is_stmt 0 view .LVU780
 1933 0026 0122     		movs	r2, #1
 1934 0028 83F83D20 		strb	r2, [r3, #61]
 992:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 1935              		.loc 1 992 3 is_stmt 1 view .LVU781
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1936              		.loc 1 993 1 is_stmt 0 view .LVU782
 1937 002c 5DF8044B 		ldr	r4, [sp], #4
 1938              	.LCFI13:
 1939              		.cfi_remember_state
 1940              		.cfi_restore 4
 1941              		.cfi_def_cfa_offset 0
 1942 0030 7047     		bx	lr
 1943              	.LVL122:
 1944              	.L153:
ARM GAS  /tmp/ccsqcIPI.s 			page 80


 1945              	.LCFI14:
 1946              		.cfi_restore_state
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1947              		.loc 1 973 5 is_stmt 1 view .LVU783
 973:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1948              		.loc 1 973 18 is_stmt 0 view .LVU784
 1949 0032 0222     		movs	r2, #2
 1950 0034 83F83D20 		strb	r2, [r3, #61]
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1951              		.loc 1 976 5 is_stmt 1 view .LVU785
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1952              		.loc 1 976 24 is_stmt 0 view .LVU786
 1953 0038 DA6D     		ldr	r2, [r3, #92]
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1954              		.loc 1 976 7 view .LVU787
 1955 003a 002A     		cmp	r2, #0
 1956 003c F0D0     		beq	.L145
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1957              		.loc 1 976 79 discriminator 1 view .LVU788
 1958 003e 196E     		ldr	r1, [r3, #96]
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1959              		.loc 1 976 60 discriminator 1 view .LVU789
 1960 0040 0029     		cmp	r1, #0
 1961 0042 EDD0     		beq	.L145
 976:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 1962              		.loc 1 976 114 discriminator 2 view .LVU790
 1963 0044 5C6E     		ldr	r4, [r3, #100]
 1964 0046 002C     		cmp	r4, #0
 1965 0048 EAD0     		beq	.L145
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 1966              		.loc 1 983 7 is_stmt 1 view .LVU791
 983:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 1967              		.loc 1 983 46 is_stmt 0 view .LVU792
 1968 004a 4A61     		str	r2, [r1, #20]
 959:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1969              		.loc 1 959 21 view .LVU793
 1970 004c 0020     		movs	r0, #0
 1971 004e E7E7     		b	.L145
 1972              	.LVL123:
 1973              	.L146:
 1974              	.LCFI15:
 1975              		.cfi_def_cfa_offset 0
 1976              		.cfi_restore 4
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1977              		.loc 1 964 12 view .LVU794
 1978 0050 0120     		movs	r0, #1
 1979              	.LVL124:
 964:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 1980              		.loc 1 964 12 view .LVU795
 1981 0052 7047     		bx	lr
 1982              	.LVL125:
 1983              	.L147:
 968:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 1984              		.loc 1 968 3 discriminator 1 view .LVU796
 1985 0054 0220     		movs	r0, #2
 1986              	.LVL126:
 993:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 81


 1987              		.loc 1 993 1 view .LVU797
 1988 0056 7047     		bx	lr
 1989              		.cfi_endproc
 1990              	.LFE152:
 1992              		.section	.text.HAL_MDMA_LinkedList_DisableCircularMode,"ax",%progbits
 1993              		.align	1
 1994              		.global	HAL_MDMA_LinkedList_DisableCircularMode
 1995              		.syntax unified
 1996              		.thumb
 1997              		.thumb_func
 1999              	HAL_MDMA_LinkedList_DisableCircularMode:
 2000              	.LVL127:
 2001              	.LFB153:
1002:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   HAL_StatusTypeDef hal_status = HAL_OK;
 2002              		.loc 1 1002 1 is_stmt 1 view -0
 2003              		.cfi_startproc
 2004              		@ args = 0, pretend = 0, frame = 0
 2005              		@ frame_needed = 0, uses_anonymous_args = 0
 2006              		@ link register save eliminated.
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2007              		.loc 1 1003 3 view .LVU799
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2008              		.loc 1 1006 3 view .LVU800
1006:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2009              		.loc 1 1006 5 is_stmt 0 view .LVU801
 2010 0000 0346     		mov	r3, r0
 2011 0002 10B3     		cbz	r0, .L157
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2012              		.loc 1 1012 3 is_stmt 1 view .LVU802
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2013              		.loc 1 1012 3 view .LVU803
 2014 0004 90F83C20 		ldrb	r2, [r0, #60]	@ zero_extendqisi2
 2015 0008 012A     		cmp	r2, #1
 2016 000a 20D0     		beq	.L158
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2017              		.loc 1 1012 3 discriminator 2 view .LVU804
 2018 000c 0122     		movs	r2, #1
 2019 000e 80F83C20 		strb	r2, [r0, #60]
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2020              		.loc 1 1012 3 discriminator 2 view .LVU805
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2021              		.loc 1 1014 3 view .LVU806
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2022              		.loc 1 1014 35 is_stmt 0 view .LVU807
 2023 0012 90F83D00 		ldrb	r0, [r0, #61]	@ zero_extendqisi2
 2024              	.LVL128:
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2025              		.loc 1 1014 35 view .LVU808
 2026 0016 C0B2     		uxtb	r0, r0
1014:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2027              		.loc 1 1014 5 view .LVU809
 2028 0018 9042     		cmp	r0, r2
 2029 001a 07D0     		beq	.L160
1003:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2030              		.loc 1 1003 21 view .LVU810
 2031 001c 0020     		movs	r0, #0
 2032              	.L156:
ARM GAS  /tmp/ccsqcIPI.s 			page 82


 2033              	.LVL129:
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2034              		.loc 1 1032 3 is_stmt 1 view .LVU811
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2035              		.loc 1 1032 3 view .LVU812
 2036 001e 0022     		movs	r2, #0
 2037 0020 83F83C20 		strb	r2, [r3, #60]
1032:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2038              		.loc 1 1032 3 view .LVU813
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2039              		.loc 1 1034 3 view .LVU814
1034:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2040              		.loc 1 1034 16 is_stmt 0 view .LVU815
 2041 0024 0122     		movs	r2, #1
 2042 0026 83F83D20 		strb	r2, [r3, #61]
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2043              		.loc 1 1036 3 is_stmt 1 view .LVU816
1036:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2044              		.loc 1 1036 10 is_stmt 0 view .LVU817
 2045 002a 7047     		bx	lr
 2046              	.LVL130:
 2047              	.L160:
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2048              		.loc 1 1017 5 is_stmt 1 view .LVU818
1017:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2049              		.loc 1 1017 18 is_stmt 0 view .LVU819
 2050 002c 0222     		movs	r2, #2
 2051 002e 83F83D20 		strb	r2, [r3, #61]
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2052              		.loc 1 1020 5 is_stmt 1 view .LVU820
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2053              		.loc 1 1020 24 is_stmt 0 view .LVU821
 2054 0032 DA6D     		ldr	r2, [r3, #92]
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2055              		.loc 1 1020 7 view .LVU822
 2056 0034 002A     		cmp	r2, #0
 2057 0036 F2D0     		beq	.L156
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2058              		.loc 1 1020 79 discriminator 1 view .LVU823
 2059 0038 1A6E     		ldr	r2, [r3, #96]
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2060              		.loc 1 1020 60 discriminator 1 view .LVU824
 2061 003a 002A     		cmp	r2, #0
 2062 003c EFD0     		beq	.L156
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2063              		.loc 1 1020 123 discriminator 2 view .LVU825
 2064 003e 596E     		ldr	r1, [r3, #100]
1020:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2065              		.loc 1 1020 114 discriminator 2 view .LVU826
 2066 0040 0029     		cmp	r1, #0
 2067 0042 ECD0     		beq	.L156
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2068              		.loc 1 1027 7 is_stmt 1 view .LVU827
1027:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2069              		.loc 1 1027 46 is_stmt 0 view .LVU828
 2070 0044 0020     		movs	r0, #0
 2071 0046 5061     		str	r0, [r2, #20]
ARM GAS  /tmp/ccsqcIPI.s 			page 83


 2072 0048 E9E7     		b	.L156
 2073              	.LVL131:
 2074              	.L157:
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2075              		.loc 1 1008 12 view .LVU829
 2076 004a 0120     		movs	r0, #1
 2077              	.LVL132:
1008:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2078              		.loc 1 1008 12 view .LVU830
 2079 004c 7047     		bx	lr
 2080              	.LVL133:
 2081              	.L158:
1012:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2082              		.loc 1 1012 3 discriminator 1 view .LVU831
 2083 004e 0220     		movs	r0, #2
 2084              	.LVL134:
1037:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2085              		.loc 1 1037 1 view .LVU832
 2086 0050 7047     		bx	lr
 2087              		.cfi_endproc
 2088              	.LFE153:
 2090              		.section	.text.HAL_MDMA_Start,"ax",%progbits
 2091              		.align	1
 2092              		.global	HAL_MDMA_Start
 2093              		.syntax unified
 2094              		.thumb
 2095              		.thumb_func
 2097              	HAL_MDMA_Start:
 2098              	.LVL135:
 2099              	.LFB154:
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
 2100              		.loc 1 1073 1 is_stmt 1 view -0
 2101              		.cfi_startproc
 2102              		@ args = 4, pretend = 0, frame = 0
 2103              		@ frame_needed = 0, uses_anonymous_args = 0
1075:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_COUNT(BlockCount));
 2104              		.loc 1 1075 3 view .LVU834
1076:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2105              		.loc 1 1076 3 view .LVU835
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2106              		.loc 1 1079 3 view .LVU836
1079:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2107              		.loc 1 1079 5 is_stmt 0 view .LVU837
 2108 0000 0028     		cmp	r0, #0
 2109 0002 35D0     		beq	.L164
1073:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
 2110              		.loc 1 1073 1 view .LVU838
 2111 0004 30B5     		push	{r4, r5, lr}
 2112              	.LCFI16:
 2113              		.cfi_def_cfa_offset 12
 2114              		.cfi_offset 4, -12
 2115              		.cfi_offset 5, -8
 2116              		.cfi_offset 14, -4
 2117 0006 83B0     		sub	sp, sp, #12
 2118              	.LCFI17:
 2119              		.cfi_def_cfa_offset 24
 2120 0008 0446     		mov	r4, r0
ARM GAS  /tmp/ccsqcIPI.s 			page 84


1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2121              		.loc 1 1085 3 is_stmt 1 view .LVU839
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2122              		.loc 1 1085 3 view .LVU840
 2123 000a 90F83C00 		ldrb	r0, [r0, #60]	@ zero_extendqisi2
 2124              	.LVL136:
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2125              		.loc 1 1085 3 is_stmt 0 view .LVU841
 2126 000e 0128     		cmp	r0, #1
 2127 0010 30D0     		beq	.L165
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2128              		.loc 1 1085 3 is_stmt 1 discriminator 2 view .LVU842
 2129 0012 0120     		movs	r0, #1
 2130 0014 84F83C00 		strb	r0, [r4, #60]
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2131              		.loc 1 1085 3 discriminator 2 view .LVU843
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2132              		.loc 1 1087 3 view .LVU844
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2133              		.loc 1 1087 35 is_stmt 0 view .LVU845
 2134 0018 94F83D00 		ldrb	r0, [r4, #61]	@ zero_extendqisi2
 2135 001c C0B2     		uxtb	r0, r0
1087:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2136              		.loc 1 1087 5 view .LVU846
 2137 001e 0128     		cmp	r0, #1
 2138 0020 05D0     		beq	.L171
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2139              		.loc 1 1113 5 is_stmt 1 view .LVU847
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2140              		.loc 1 1113 5 view .LVU848
 2141 0022 0023     		movs	r3, #0
 2142              	.LVL137:
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2143              		.loc 1 1113 5 is_stmt 0 view .LVU849
 2144 0024 84F83C30 		strb	r3, [r4, #60]
1113:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2145              		.loc 1 1113 5 is_stmt 1 view .LVU850
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2146              		.loc 1 1116 5 view .LVU851
1116:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2147              		.loc 1 1116 12 is_stmt 0 view .LVU852
 2148 0028 0220     		movs	r0, #2
 2149              	.LVL138:
 2150              	.L162:
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2151              		.loc 1 1120 1 view .LVU853
 2152 002a 03B0     		add	sp, sp, #12
 2153              	.LCFI18:
 2154              		.cfi_remember_state
 2155              		.cfi_def_cfa_offset 12
 2156              		@ sp needed
 2157 002c 30BD     		pop	{r4, r5, pc}
 2158              	.LVL139:
 2159              	.L171:
 2160              	.LCFI19:
 2161              		.cfi_restore_state
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 85


 2162              		.loc 1 1090 5 is_stmt 1 view .LVU854
1090:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2163              		.loc 1 1090 18 is_stmt 0 view .LVU855
 2164 002e 0220     		movs	r0, #2
 2165 0030 84F83D00 		strb	r0, [r4, #61]
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2166              		.loc 1 1093 5 is_stmt 1 view .LVU856
1093:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2167              		.loc 1 1093 22 is_stmt 0 view .LVU857
 2168 0034 0020     		movs	r0, #0
 2169 0036 A066     		str	r0, [r4, #104]
1096:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2170              		.loc 1 1096 5 is_stmt 1 view .LVU858
 2171 0038 2568     		ldr	r5, [r4]
 2172 003a E868     		ldr	r0, [r5, #12]
 2173 003c 20F00100 		bic	r0, r0, #1
 2174 0040 E860     		str	r0, [r5, #12]
1099:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2175              		.loc 1 1099 5 view .LVU859
 2176 0042 0698     		ldr	r0, [sp, #24]
 2177 0044 0090     		str	r0, [sp]
 2178 0046 2046     		mov	r0, r4
 2179 0048 FFF7FEFF 		bl	MDMA_SetConfig
 2180              	.LVL140:
1102:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2181              		.loc 1 1102 5 view .LVU860
 2182 004c 2268     		ldr	r2, [r4]
 2183 004e D368     		ldr	r3, [r2, #12]
 2184 0050 43F00103 		orr	r3, r3, #1
 2185 0054 D360     		str	r3, [r2, #12]
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2186              		.loc 1 1104 5 view .LVU861
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2187              		.loc 1 1104 19 is_stmt 0 view .LVU862
 2188 0056 6368     		ldr	r3, [r4, #4]
1104:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2189              		.loc 1 1104 7 view .LVU863
 2190 0058 B3F1804F 		cmp	r3, #1073741824
 2191 005c 01D0     		beq	.L172
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2192              		.loc 1 1119 10 view .LVU864
 2193 005e 0020     		movs	r0, #0
 2194 0060 E3E7     		b	.L162
 2195              	.L172:
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2196              		.loc 1 1107 7 is_stmt 1 view .LVU865
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2197              		.loc 1 1107 12 is_stmt 0 view .LVU866
 2198 0062 2268     		ldr	r2, [r4]
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2199              		.loc 1 1107 22 view .LVU867
 2200 0064 D368     		ldr	r3, [r2, #12]
1107:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2201              		.loc 1 1107 28 view .LVU868
 2202 0066 43F48033 		orr	r3, r3, #65536
 2203 006a D360     		str	r3, [r2, #12]
1119:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
ARM GAS  /tmp/ccsqcIPI.s 			page 86


 2204              		.loc 1 1119 10 view .LVU869
 2205 006c 0020     		movs	r0, #0
 2206 006e DCE7     		b	.L162
 2207              	.LVL141:
 2208              	.L164:
 2209              	.LCFI20:
 2210              		.cfi_def_cfa_offset 0
 2211              		.cfi_restore 4
 2212              		.cfi_restore 5
 2213              		.cfi_restore 14
1081:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2214              		.loc 1 1081 12 view .LVU870
 2215 0070 0120     		movs	r0, #1
 2216              	.LVL142:
1120:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2217              		.loc 1 1120 1 view .LVU871
 2218 0072 7047     		bx	lr
 2219              	.LVL143:
 2220              	.L165:
 2221              	.LCFI21:
 2222              		.cfi_def_cfa_offset 24
 2223              		.cfi_offset 4, -12
 2224              		.cfi_offset 5, -8
 2225              		.cfi_offset 14, -4
1085:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2226              		.loc 1 1085 3 discriminator 1 view .LVU872
 2227 0074 0220     		movs	r0, #2
 2228 0076 D8E7     		b	.L162
 2229              		.cfi_endproc
 2230              	.LFE154:
 2232              		.section	.text.HAL_MDMA_Start_IT,"ax",%progbits
 2233              		.align	1
 2234              		.global	HAL_MDMA_Start_IT
 2235              		.syntax unified
 2236              		.thumb
 2237              		.thumb_func
 2239              	HAL_MDMA_Start_IT:
 2240              	.LVL144:
 2241              	.LFB155:
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
 2242              		.loc 1 1133 1 is_stmt 1 view -0
 2243              		.cfi_startproc
 2244              		@ args = 4, pretend = 0, frame = 0
 2245              		@ frame_needed = 0, uses_anonymous_args = 0
1135:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   assert_param(IS_MDMA_BLOCK_COUNT(BlockCount));
 2246              		.loc 1 1135 3 view .LVU874
1136:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2247              		.loc 1 1136 3 view .LVU875
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2248              		.loc 1 1139 3 view .LVU876
1139:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2249              		.loc 1 1139 5 is_stmt 0 view .LVU877
 2250 0000 0028     		cmp	r0, #0
 2251 0002 4FD0     		beq	.L179
1133:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the parameters */
 2252              		.loc 1 1133 1 view .LVU878
 2253 0004 30B5     		push	{r4, r5, lr}
ARM GAS  /tmp/ccsqcIPI.s 			page 87


 2254              	.LCFI22:
 2255              		.cfi_def_cfa_offset 12
 2256              		.cfi_offset 4, -12
 2257              		.cfi_offset 5, -8
 2258              		.cfi_offset 14, -4
 2259 0006 83B0     		sub	sp, sp, #12
 2260              	.LCFI23:
 2261              		.cfi_def_cfa_offset 24
 2262 0008 0446     		mov	r4, r0
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2263              		.loc 1 1145 3 is_stmt 1 view .LVU879
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2264              		.loc 1 1145 3 view .LVU880
 2265 000a 90F83C00 		ldrb	r0, [r0, #60]	@ zero_extendqisi2
 2266              	.LVL145:
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2267              		.loc 1 1145 3 is_stmt 0 view .LVU881
 2268 000e 0128     		cmp	r0, #1
 2269 0010 4AD0     		beq	.L180
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2270              		.loc 1 1145 3 is_stmt 1 discriminator 2 view .LVU882
 2271 0012 0120     		movs	r0, #1
 2272 0014 84F83C00 		strb	r0, [r4, #60]
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2273              		.loc 1 1145 3 discriminator 2 view .LVU883
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2274              		.loc 1 1147 3 view .LVU884
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2275              		.loc 1 1147 35 is_stmt 0 view .LVU885
 2276 0018 94F83D00 		ldrb	r0, [r4, #61]	@ zero_extendqisi2
 2277 001c C0B2     		uxtb	r0, r0
1147:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2278              		.loc 1 1147 5 view .LVU886
 2279 001e 0128     		cmp	r0, #1
 2280 0020 05D0     		beq	.L186
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2281              		.loc 1 1194 5 is_stmt 1 view .LVU887
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2282              		.loc 1 1194 5 view .LVU888
 2283 0022 0023     		movs	r3, #0
 2284              	.LVL146:
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2285              		.loc 1 1194 5 is_stmt 0 view .LVU889
 2286 0024 84F83C30 		strb	r3, [r4, #60]
1194:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2287              		.loc 1 1194 5 is_stmt 1 view .LVU890
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2288              		.loc 1 1197 5 view .LVU891
1197:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2289              		.loc 1 1197 12 is_stmt 0 view .LVU892
 2290 0028 0220     		movs	r0, #2
 2291              	.LVL147:
 2292              	.L174:
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2293              		.loc 1 1201 1 view .LVU893
 2294 002a 03B0     		add	sp, sp, #12
 2295              	.LCFI24:
ARM GAS  /tmp/ccsqcIPI.s 			page 88


 2296              		.cfi_remember_state
 2297              		.cfi_def_cfa_offset 12
 2298              		@ sp needed
 2299 002c 30BD     		pop	{r4, r5, pc}
 2300              	.LVL148:
 2301              	.L186:
 2302              	.LCFI25:
 2303              		.cfi_restore_state
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2304              		.loc 1 1150 5 is_stmt 1 view .LVU894
1150:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2305              		.loc 1 1150 18 is_stmt 0 view .LVU895
 2306 002e 0220     		movs	r0, #2
 2307 0030 84F83D00 		strb	r0, [r4, #61]
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2308              		.loc 1 1153 5 is_stmt 1 view .LVU896
1153:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2309              		.loc 1 1153 22 is_stmt 0 view .LVU897
 2310 0034 0020     		movs	r0, #0
 2311 0036 A066     		str	r0, [r4, #104]
1156:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2312              		.loc 1 1156 5 is_stmt 1 view .LVU898
 2313 0038 2568     		ldr	r5, [r4]
 2314 003a E868     		ldr	r0, [r5, #12]
 2315 003c 20F00100 		bic	r0, r0, #1
 2316 0040 E860     		str	r0, [r5, #12]
1159:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2317              		.loc 1 1159 5 view .LVU899
 2318 0042 0698     		ldr	r0, [sp, #24]
 2319 0044 0090     		str	r0, [sp]
 2320 0046 2046     		mov	r0, r4
 2321 0048 FFF7FEFF 		bl	MDMA_SetConfig
 2322              	.LVL149:
1162:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2323              		.loc 1 1162 5 view .LVU900
 2324 004c 2268     		ldr	r2, [r4]
 2325 004e D368     		ldr	r3, [r2, #12]
 2326 0050 43F00603 		orr	r3, r3, #6
 2327 0054 D360     		str	r3, [r2, #12]
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2328              		.loc 1 1164 5 view .LVU901
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2329              		.loc 1 1164 13 is_stmt 0 view .LVU902
 2330 0056 E36C     		ldr	r3, [r4, #76]
1164:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2331              		.loc 1 1164 7 view .LVU903
 2332 0058 23B1     		cbz	r3, .L176
1167:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2333              		.loc 1 1167 7 is_stmt 1 view .LVU904
 2334 005a 2268     		ldr	r2, [r4]
 2335 005c D368     		ldr	r3, [r2, #12]
 2336 005e 43F01003 		orr	r3, r3, #16
 2337 0062 D360     		str	r3, [r2, #12]
 2338              	.L176:
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2339              		.loc 1 1170 5 view .LVU905
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
ARM GAS  /tmp/ccsqcIPI.s 			page 89


 2340              		.loc 1 1170 13 is_stmt 0 view .LVU906
 2341 0064 236D     		ldr	r3, [r4, #80]
1170:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2342              		.loc 1 1170 7 view .LVU907
 2343 0066 23B1     		cbz	r3, .L177
1173:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2344              		.loc 1 1173 7 is_stmt 1 view .LVU908
 2345 0068 2268     		ldr	r2, [r4]
 2346 006a D368     		ldr	r3, [r2, #12]
 2347 006c 43F00803 		orr	r3, r3, #8
 2348 0070 D360     		str	r3, [r2, #12]
 2349              	.L177:
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2350              		.loc 1 1176 5 view .LVU909
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2351              		.loc 1 1176 13 is_stmt 0 view .LVU910
 2352 0072 A36C     		ldr	r3, [r4, #72]
1176:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2353              		.loc 1 1176 7 view .LVU911
 2354 0074 23B1     		cbz	r3, .L178
1179:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2355              		.loc 1 1179 7 is_stmt 1 view .LVU912
 2356 0076 2268     		ldr	r2, [r4]
 2357 0078 D368     		ldr	r3, [r2, #12]
 2358 007a 43F02003 		orr	r3, r3, #32
 2359 007e D360     		str	r3, [r2, #12]
 2360              	.L178:
1183:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2361              		.loc 1 1183 5 view .LVU913
 2362 0080 2268     		ldr	r2, [r4]
 2363 0082 D368     		ldr	r3, [r2, #12]
 2364 0084 43F00103 		orr	r3, r3, #1
 2365 0088 D360     		str	r3, [r2, #12]
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2366              		.loc 1 1185 5 view .LVU914
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2367              		.loc 1 1185 19 is_stmt 0 view .LVU915
 2368 008a 6368     		ldr	r3, [r4, #4]
1185:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2369              		.loc 1 1185 7 view .LVU916
 2370 008c B3F1804F 		cmp	r3, #1073741824
 2371 0090 01D0     		beq	.L187
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2372              		.loc 1 1200 10 view .LVU917
 2373 0092 0020     		movs	r0, #0
 2374 0094 C9E7     		b	.L174
 2375              	.L187:
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2376              		.loc 1 1188 7 is_stmt 1 view .LVU918
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2377              		.loc 1 1188 12 is_stmt 0 view .LVU919
 2378 0096 2268     		ldr	r2, [r4]
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2379              		.loc 1 1188 22 view .LVU920
 2380 0098 D368     		ldr	r3, [r2, #12]
1188:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 2381              		.loc 1 1188 28 view .LVU921
ARM GAS  /tmp/ccsqcIPI.s 			page 90


 2382 009a 43F48033 		orr	r3, r3, #65536
 2383 009e D360     		str	r3, [r2, #12]
1200:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2384              		.loc 1 1200 10 view .LVU922
 2385 00a0 0020     		movs	r0, #0
 2386 00a2 C2E7     		b	.L174
 2387              	.LVL150:
 2388              	.L179:
 2389              	.LCFI26:
 2390              		.cfi_def_cfa_offset 0
 2391              		.cfi_restore 4
 2392              		.cfi_restore 5
 2393              		.cfi_restore 14
1141:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2394              		.loc 1 1141 12 view .LVU923
 2395 00a4 0120     		movs	r0, #1
 2396              	.LVL151:
1201:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2397              		.loc 1 1201 1 view .LVU924
 2398 00a6 7047     		bx	lr
 2399              	.LVL152:
 2400              	.L180:
 2401              	.LCFI27:
 2402              		.cfi_def_cfa_offset 24
 2403              		.cfi_offset 4, -12
 2404              		.cfi_offset 5, -8
 2405              		.cfi_offset 14, -4
1145:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2406              		.loc 1 1145 3 discriminator 1 view .LVU925
 2407 00a8 0220     		movs	r0, #2
 2408 00aa BEE7     		b	.L174
 2409              		.cfi_endproc
 2410              	.LFE155:
 2412              		.section	.text.HAL_MDMA_Abort,"ax",%progbits
 2413              		.align	1
 2414              		.global	HAL_MDMA_Abort
 2415              		.syntax unified
 2416              		.thumb
 2417              		.thumb_func
 2419              	HAL_MDMA_Abort:
 2420              	.LVL153:
 2421              	.LFB156:
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart =  HAL_GetTick();
 2422              		.loc 1 1216 1 is_stmt 1 view -0
 2423              		.cfi_startproc
 2424              		@ args = 0, pretend = 0, frame = 0
 2425              		@ frame_needed = 0, uses_anonymous_args = 0
1216:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart =  HAL_GetTick();
 2426              		.loc 1 1216 1 is_stmt 0 view .LVU927
 2427 0000 38B5     		push	{r3, r4, r5, lr}
 2428              	.LCFI28:
 2429              		.cfi_def_cfa_offset 16
 2430              		.cfi_offset 3, -16
 2431              		.cfi_offset 4, -12
 2432              		.cfi_offset 5, -8
 2433              		.cfi_offset 14, -4
 2434 0002 0446     		mov	r4, r0
ARM GAS  /tmp/ccsqcIPI.s 			page 91


1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2435              		.loc 1 1217 3 is_stmt 1 view .LVU928
1217:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2436              		.loc 1 1217 25 is_stmt 0 view .LVU929
 2437 0004 FFF7FEFF 		bl	HAL_GetTick
 2438              	.LVL154:
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2439              		.loc 1 1220 3 is_stmt 1 view .LVU930
1220:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2440              		.loc 1 1220 5 is_stmt 0 view .LVU931
 2441 0008 002C     		cmp	r4, #0
 2442 000a 35D0     		beq	.L193
 2443 000c 0546     		mov	r5, r0
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2444              		.loc 1 1225 3 is_stmt 1 view .LVU932
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2445              		.loc 1 1225 34 is_stmt 0 view .LVU933
 2446 000e 94F83D30 		ldrb	r3, [r4, #61]	@ zero_extendqisi2
 2447 0012 DBB2     		uxtb	r3, r3
1225:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2448              		.loc 1 1225 5 view .LVU934
 2449 0014 022B     		cmp	r3, #2
 2450 0016 06D0     		beq	.L190
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2451              		.loc 1 1227 5 is_stmt 1 view .LVU935
1227:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2452              		.loc 1 1227 22 is_stmt 0 view .LVU936
 2453 0018 8023     		movs	r3, #128
 2454 001a A366     		str	r3, [r4, #104]
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2455              		.loc 1 1230 5 is_stmt 1 view .LVU937
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2456              		.loc 1 1230 5 view .LVU938
 2457 001c 0023     		movs	r3, #0
 2458 001e 84F83C30 		strb	r3, [r4, #60]
1230:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2459              		.loc 1 1230 5 view .LVU939
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2460              		.loc 1 1232 5 view .LVU940
1232:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2461              		.loc 1 1232 12 is_stmt 0 view .LVU941
 2462 0022 0120     		movs	r0, #1
 2463              	.LVL155:
 2464              	.L189:
1272:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2465              		.loc 1 1272 1 view .LVU942
 2466 0024 38BD     		pop	{r3, r4, r5, pc}
 2467              	.LVL156:
 2468              	.L190:
1237:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2469              		.loc 1 1237 5 is_stmt 1 view .LVU943
 2470 0026 2268     		ldr	r2, [r4]
 2471 0028 D368     		ldr	r3, [r2, #12]
 2472 002a 23F03E03 		bic	r3, r3, #62
 2473 002e D360     		str	r3, [r2, #12]
1240:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2474              		.loc 1 1240 5 view .LVU944
ARM GAS  /tmp/ccsqcIPI.s 			page 92


 2475 0030 2268     		ldr	r2, [r4]
 2476 0032 D368     		ldr	r3, [r2, #12]
 2477 0034 23F00103 		bic	r3, r3, #1
 2478 0038 D360     		str	r3, [r2, #12]
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2479              		.loc 1 1243 5 view .LVU945
 2480              	.LVL157:
 2481              	.L191:
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2482              		.loc 1 1243 48 view .LVU946
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2483              		.loc 1 1243 17 is_stmt 0 view .LVU947
 2484 003a 2368     		ldr	r3, [r4]
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2485              		.loc 1 1243 27 view .LVU948
 2486 003c DA68     		ldr	r2, [r3, #12]
1243:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2487              		.loc 1 1243 48 view .LVU949
 2488 003e 12F0010F 		tst	r2, #1
 2489 0042 10D0     		beq	.L195
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2490              		.loc 1 1246 7 is_stmt 1 view .LVU950
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2491              		.loc 1 1246 12 is_stmt 0 view .LVU951
 2492 0044 FFF7FEFF 		bl	HAL_GetTick
 2493              	.LVL158:
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2494              		.loc 1 1246 27 discriminator 1 view .LVU952
 2495 0048 431B     		subs	r3, r0, r5
1246:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2496              		.loc 1 1246 9 discriminator 1 view .LVU953
 2497 004a 052B     		cmp	r3, #5
 2498 004c F5D9     		bls	.L191
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2499              		.loc 1 1249 9 is_stmt 1 view .LVU954
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2500              		.loc 1 1249 14 is_stmt 0 view .LVU955
 2501 004e A36E     		ldr	r3, [r4, #104]
1249:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2502              		.loc 1 1249 26 view .LVU956
 2503 0050 43F04003 		orr	r3, r3, #64
 2504 0054 A366     		str	r3, [r4, #104]
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2505              		.loc 1 1252 9 is_stmt 1 view .LVU957
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2506              		.loc 1 1252 9 view .LVU958
 2507 0056 0023     		movs	r3, #0
 2508 0058 84F83C30 		strb	r3, [r4, #60]
1252:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2509              		.loc 1 1252 9 view .LVU959
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2510              		.loc 1 1255 9 view .LVU960
1255:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2511              		.loc 1 1255 22 is_stmt 0 view .LVU961
 2512 005c 0323     		movs	r3, #3
 2513 005e 84F83D30 		strb	r3, [r4, #61]
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
ARM GAS  /tmp/ccsqcIPI.s 			page 93


 2514              		.loc 1 1257 9 is_stmt 1 view .LVU962
1257:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2515              		.loc 1 1257 16 is_stmt 0 view .LVU963
 2516 0062 0120     		movs	r0, #1
 2517 0064 DEE7     		b	.L189
 2518              	.L195:
1262:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2519              		.loc 1 1262 5 is_stmt 1 view .LVU964
 2520 0066 1F22     		movs	r2, #31
 2521 0068 5A60     		str	r2, [r3, #4]
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2522              		.loc 1 1265 5 view .LVU965
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2523              		.loc 1 1265 5 view .LVU966
 2524 006a 0020     		movs	r0, #0
 2525 006c 84F83C00 		strb	r0, [r4, #60]
1265:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2526              		.loc 1 1265 5 view .LVU967
1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2527              		.loc 1 1268 5 view .LVU968
1268:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2528              		.loc 1 1268 18 is_stmt 0 view .LVU969
 2529 0070 0123     		movs	r3, #1
 2530 0072 84F83D30 		strb	r3, [r4, #61]
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2531              		.loc 1 1271 3 is_stmt 1 view .LVU970
1271:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2532              		.loc 1 1271 10 is_stmt 0 view .LVU971
 2533 0076 D5E7     		b	.L189
 2534              	.LVL159:
 2535              	.L193:
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2536              		.loc 1 1222 12 view .LVU972
 2537 0078 0120     		movs	r0, #1
 2538              	.LVL160:
1222:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2539              		.loc 1 1222 12 view .LVU973
 2540 007a D3E7     		b	.L189
 2541              		.cfi_endproc
 2542              	.LFE156:
 2544              		.section	.text.HAL_MDMA_Abort_IT,"ax",%progbits
 2545              		.align	1
 2546              		.global	HAL_MDMA_Abort_IT
 2547              		.syntax unified
 2548              		.thumb
 2549              		.thumb_func
 2551              	HAL_MDMA_Abort_IT:
 2552              	.LVL161:
 2553              	.LFB157:
1281:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   /* Check the MDMA peripheral handle */
 2554              		.loc 1 1281 1 is_stmt 1 view -0
 2555              		.cfi_startproc
 2556              		@ args = 0, pretend = 0, frame = 0
 2557              		@ frame_needed = 0, uses_anonymous_args = 0
 2558              		@ link register save eliminated.
1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2559              		.loc 1 1283 3 view .LVU975
ARM GAS  /tmp/ccsqcIPI.s 			page 94


1283:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2560              		.loc 1 1283 5 is_stmt 0 view .LVU976
 2561 0000 90B1     		cbz	r0, .L199
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2562              		.loc 1 1288 3 is_stmt 1 view .LVU977
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2563              		.loc 1 1288 34 is_stmt 0 view .LVU978
 2564 0002 90F83D30 		ldrb	r3, [r0, #61]	@ zero_extendqisi2
 2565 0006 DBB2     		uxtb	r3, r3
1288:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2566              		.loc 1 1288 5 view .LVU979
 2567 0008 022B     		cmp	r3, #2
 2568 000a 03D0     		beq	.L198
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2569              		.loc 1 1291 5 is_stmt 1 view .LVU980
1291:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2570              		.loc 1 1291 22 is_stmt 0 view .LVU981
 2571 000c 8023     		movs	r3, #128
 2572 000e 8366     		str	r3, [r0, #104]
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2573              		.loc 1 1293 5 is_stmt 1 view .LVU982
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2574              		.loc 1 1293 12 is_stmt 0 view .LVU983
 2575 0010 0120     		movs	r0, #1
 2576              	.LVL162:
1293:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2577              		.loc 1 1293 12 view .LVU984
 2578 0012 7047     		bx	lr
 2579              	.LVL163:
 2580              	.L198:
1298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2581              		.loc 1 1298 5 is_stmt 1 view .LVU985
1298:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2582              		.loc 1 1298 18 is_stmt 0 view .LVU986
 2583 0014 0423     		movs	r3, #4
 2584 0016 80F83D30 		strb	r3, [r0, #61]
1301:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2585              		.loc 1 1301 5 is_stmt 1 view .LVU987
 2586 001a 0268     		ldr	r2, [r0]
 2587 001c D368     		ldr	r3, [r2, #12]
 2588 001e 23F00103 		bic	r3, r3, #1
 2589 0022 D360     		str	r3, [r2, #12]
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2590              		.loc 1 1304 3 view .LVU988
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2591              		.loc 1 1304 10 is_stmt 0 view .LVU989
 2592 0024 0020     		movs	r0, #0
 2593              	.LVL164:
1304:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2594              		.loc 1 1304 10 view .LVU990
 2595 0026 7047     		bx	lr
 2596              	.LVL165:
 2597              	.L199:
1285:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2598              		.loc 1 1285 12 view .LVU991
 2599 0028 0120     		movs	r0, #1
 2600              	.LVL166:
ARM GAS  /tmp/ccsqcIPI.s 			page 95


1305:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2601              		.loc 1 1305 1 view .LVU992
 2602 002a 7047     		bx	lr
 2603              		.cfi_endproc
 2604              	.LFE157:
 2606              		.section	.text.HAL_MDMA_PollForTransfer,"ax",%progbits
 2607              		.align	1
 2608              		.global	HAL_MDMA_PollForTransfer
 2609              		.syntax unified
 2610              		.thumb
 2611              		.thumb_func
 2613              	HAL_MDMA_PollForTransfer:
 2614              	.LVL167:
 2615              	.LFB158:
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t levelFlag, errorFlag;
 2616              		.loc 1 1316 1 is_stmt 1 view -0
 2617              		.cfi_startproc
 2618              		@ args = 0, pretend = 0, frame = 0
 2619              		@ frame_needed = 0, uses_anonymous_args = 0
1317:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t tickstart;
 2620              		.loc 1 1317 3 view .LVU994
1318:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2621              		.loc 1 1318 3 view .LVU995
1321:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2622              		.loc 1 1321 3 view .LVU996
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2623              		.loc 1 1324 3 view .LVU997
1324:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2624              		.loc 1 1324 5 is_stmt 0 view .LVU998
 2625 0000 0028     		cmp	r0, #0
 2626 0002 00F08980 		beq	.L220
1316:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t levelFlag, errorFlag;
 2627              		.loc 1 1316 1 view .LVU999
 2628 0006 2DE9F843 		push	{r3, r4, r5, r6, r7, r8, r9, lr}
 2629              	.LCFI29:
 2630              		.cfi_def_cfa_offset 32
 2631              		.cfi_offset 3, -32
 2632              		.cfi_offset 4, -28
 2633              		.cfi_offset 5, -24
 2634              		.cfi_offset 6, -20
 2635              		.cfi_offset 7, -16
 2636              		.cfi_offset 8, -12
 2637              		.cfi_offset 9, -8
 2638              		.cfi_offset 14, -4
 2639 000a 0F46     		mov	r7, r1
 2640 000c 1646     		mov	r6, r2
 2641 000e 0546     		mov	r5, r0
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2642              		.loc 1 1329 3 is_stmt 1 view .LVU1000
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2643              		.loc 1 1329 34 is_stmt 0 view .LVU1001
 2644 0010 90F83D30 		ldrb	r3, [r0, #61]	@ zero_extendqisi2
 2645 0014 DBB2     		uxtb	r3, r3
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2646              		.loc 1 1329 5 view .LVU1002
 2647 0016 022B     		cmp	r3, #2
 2648 0018 04D0     		beq	.L202
ARM GAS  /tmp/ccsqcIPI.s 			page 96


1332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2649              		.loc 1 1332 5 is_stmt 1 view .LVU1003
1332:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2650              		.loc 1 1332 22 is_stmt 0 view .LVU1004
 2651 001a 8023     		movs	r3, #128
 2652 001c 8366     		str	r3, [r0, #104]
1334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2653              		.loc 1 1334 5 is_stmt 1 view .LVU1005
1334:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2654              		.loc 1 1334 12 is_stmt 0 view .LVU1006
 2655 001e 0120     		movs	r0, #1
 2656              	.LVL168:
 2657              	.L201:
1454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2658              		.loc 1 1454 1 view .LVU1007
 2659 0020 BDE8F883 		pop	{r3, r4, r5, r6, r7, r8, r9, pc}
 2660              	.LVL169:
 2661              	.L202:
1338:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****                (CompleteLevel == HAL_MDMA_BUFFER_TRANSFER)? MDMA_FLAG_BFTC : \
 2662              		.loc 1 1338 3 is_stmt 1 view .LVU1008
 2663 0024 0129     		cmp	r1, #1
 2664 0026 24D0     		beq	.L221
 2665 0028 0229     		cmp	r1, #2
 2666 002a 25D0     		beq	.L222
 2667 002c 39BB     		cbnz	r1, .L223
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2668              		.loc 1 1329 5 is_stmt 0 view .LVU1009
 2669 002e 4FF00208 		mov	r8, #2
 2670              	.L203:
 2671              	.LVL170:
1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2672              		.loc 1 1345 3 is_stmt 1 view .LVU1010
1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2673              		.loc 1 1345 15 is_stmt 0 view .LVU1011
 2674 0032 FFF7FEFF 		bl	HAL_GetTick
 2675              	.LVL171:
1345:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2676              		.loc 1 1345 15 view .LVU1012
 2677 0036 8146     		mov	r9, r0
 2678              	.LVL172:
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2679              		.loc 1 1347 3 is_stmt 1 view .LVU1013
 2680              	.L212:
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2681              		.loc 1 1347 47 view .LVU1014
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2682              		.loc 1 1347 9 is_stmt 0 view .LVU1015
 2683 0038 2B68     		ldr	r3, [r5]
 2684 003a 1C68     		ldr	r4, [r3]
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2685              		.loc 1 1347 47 view .LVU1016
 2686 003c 14EA080F 		tst	r4, r8
 2687 0040 4ED1     		bne	.L229
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2688              		.loc 1 1349 5 is_stmt 1 view .LVU1017
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2689              		.loc 1 1349 9 is_stmt 0 view .LVU1018
ARM GAS  /tmp/ccsqcIPI.s 			page 97


 2690 0042 1C68     		ldr	r4, [r3]
1349:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2691              		.loc 1 1349 7 view .LVU1019
 2692 0044 14F0010F 		tst	r4, #1
 2693 0048 1CD1     		bne	.L230
1403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2694              		.loc 1 1403 5 is_stmt 1 view .LVU1020
1403:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 2695              		.loc 1 1403 7 is_stmt 0 view .LVU1021
 2696 004a B6F1FF3F 		cmp	r6, #-1
 2697 004e F3D0     		beq	.L212
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2698              		.loc 1 1405 7 is_stmt 1 view .LVU1022
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2699              		.loc 1 1405 12 is_stmt 0 view .LVU1023
 2700 0050 FFF7FEFF 		bl	HAL_GetTick
 2701              	.LVL173:
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2702              		.loc 1 1405 26 discriminator 1 view .LVU1024
 2703 0054 A0EB0900 		sub	r0, r0, r9
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2704              		.loc 1 1405 9 discriminator 1 view .LVU1025
 2705 0058 B042     		cmp	r0, r6
 2706 005a 01D8     		bhi	.L213
1405:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2707              		.loc 1 1405 51 discriminator 1 view .LVU1026
 2708 005c 002E     		cmp	r6, #0
 2709 005e EBD1     		bne	.L212
 2710              	.L213:
1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2711              		.loc 1 1408 9 is_stmt 1 view .LVU1027
1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2712              		.loc 1 1408 14 is_stmt 0 view .LVU1028
 2713 0060 AB6E     		ldr	r3, [r5, #104]
1408:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2714              		.loc 1 1408 26 view .LVU1029
 2715 0062 43F04003 		orr	r3, r3, #64
 2716 0066 AB66     		str	r3, [r5, #104]
1410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2717              		.loc 1 1410 9 is_stmt 1 view .LVU1030
1410:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2718              		.loc 1 1410 16 is_stmt 0 view .LVU1031
 2719 0068 2846     		mov	r0, r5
 2720 006a FFF7FEFF 		bl	HAL_MDMA_Abort
 2721              	.LVL174:
1419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2722              		.loc 1 1419 9 is_stmt 1 view .LVU1032
1419:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2723              		.loc 1 1419 16 is_stmt 0 view .LVU1033
 2724 006e 0120     		movs	r0, #1
 2725 0070 D6E7     		b	.L201
 2726              	.LVL175:
 2727              	.L221:
1329:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2728              		.loc 1 1329 5 view .LVU1034
 2729 0072 4FF01008 		mov	r8, #16
 2730 0076 DCE7     		b	.L203
ARM GAS  /tmp/ccsqcIPI.s 			page 98


 2731              	.L222:
 2732 0078 4FF00808 		mov	r8, #8
 2733 007c D9E7     		b	.L203
 2734              	.L223:
 2735 007e 4FF00408 		mov	r8, #4
 2736 0082 D6E7     		b	.L203
 2737              	.LVL176:
 2738              	.L230:
1352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2739              		.loc 1 1352 7 is_stmt 1 view .LVU1035
1352:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2740              		.loc 1 1352 17 is_stmt 0 view .LVU1036
 2741 0084 9B68     		ldr	r3, [r3, #8]
 2742              	.LVL177:
1354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2743              		.loc 1 1354 7 is_stmt 1 view .LVU1037
1354:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2744              		.loc 1 1354 9 is_stmt 0 view .LVU1038
 2745 0086 13F0800F 		tst	r3, #128
 2746 008a 24D1     		bne	.L206
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2747              		.loc 1 1357 9 is_stmt 1 view .LVU1039
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2748              		.loc 1 1357 14 is_stmt 0 view .LVU1040
 2749 008c AA6E     		ldr	r2, [r5, #104]
1357:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2750              		.loc 1 1357 26 view .LVU1041
 2751 008e 42F00102 		orr	r2, r2, #1
 2752 0092 AA66     		str	r2, [r5, #104]
 2753              	.L207:
1365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2754              		.loc 1 1365 7 is_stmt 1 view .LVU1042
1365:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2755              		.loc 1 1365 9 is_stmt 0 view .LVU1043
 2756 0094 13F4007F 		tst	r3, #512
 2757 0098 03D0     		beq	.L208
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2758              		.loc 1 1368 9 is_stmt 1 view .LVU1044
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2759              		.loc 1 1368 14 is_stmt 0 view .LVU1045
 2760 009a AA6E     		ldr	r2, [r5, #104]
1368:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2761              		.loc 1 1368 26 view .LVU1046
 2762 009c 42F00402 		orr	r2, r2, #4
 2763 00a0 AA66     		str	r2, [r5, #104]
 2764              	.L208:
1371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2765              		.loc 1 1371 7 is_stmt 1 view .LVU1047
1371:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2766              		.loc 1 1371 9 is_stmt 0 view .LVU1048
 2767 00a2 13F4807F 		tst	r3, #256
 2768 00a6 03D0     		beq	.L209
1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2769              		.loc 1 1374 9 is_stmt 1 view .LVU1049
1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2770              		.loc 1 1374 14 is_stmt 0 view .LVU1050
 2771 00a8 AA6E     		ldr	r2, [r5, #104]
ARM GAS  /tmp/ccsqcIPI.s 			page 99


1374:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2772              		.loc 1 1374 26 view .LVU1051
 2773 00aa 42F00802 		orr	r2, r2, #8
 2774 00ae AA66     		str	r2, [r5, #104]
 2775              	.L209:
1377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2776              		.loc 1 1377 7 is_stmt 1 view .LVU1052
1377:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2777              		.loc 1 1377 9 is_stmt 0 view .LVU1053
 2778 00b0 13F4806F 		tst	r3, #1024
 2779 00b4 03D0     		beq	.L210
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2780              		.loc 1 1380 9 is_stmt 1 view .LVU1054
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2781              		.loc 1 1380 14 is_stmt 0 view .LVU1055
 2782 00b6 AA6E     		ldr	r2, [r5, #104]
1380:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2783              		.loc 1 1380 26 view .LVU1056
 2784 00b8 42F01002 		orr	r2, r2, #16
 2785 00bc AA66     		str	r2, [r5, #104]
 2786              	.L210:
1383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2787              		.loc 1 1383 7 is_stmt 1 view .LVU1057
1383:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 2788              		.loc 1 1383 9 is_stmt 0 view .LVU1058
 2789 00be 13F4006F 		tst	r3, #2048
 2790 00c2 03D0     		beq	.L211
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2791              		.loc 1 1386 9 is_stmt 1 view .LVU1059
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2792              		.loc 1 1386 14 is_stmt 0 view .LVU1060
 2793 00c4 AB6E     		ldr	r3, [r5, #104]
 2794              	.LVL178:
1386:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2795              		.loc 1 1386 26 view .LVU1061
 2796 00c6 43F02003 		orr	r3, r3, #32
 2797 00ca AB66     		str	r3, [r5, #104]
 2798              	.L211:
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2799              		.loc 1 1389 7 is_stmt 1 view .LVU1062
1389:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2800              		.loc 1 1389 14 is_stmt 0 view .LVU1063
 2801 00cc 2846     		mov	r0, r5
 2802 00ce FFF7FEFF 		bl	HAL_MDMA_Abort
 2803              	.LVL179:
1398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2804              		.loc 1 1398 7 is_stmt 1 view .LVU1064
1398:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2805              		.loc 1 1398 14 is_stmt 0 view .LVU1065
 2806 00d2 0120     		movs	r0, #1
 2807 00d4 A4E7     		b	.L201
 2808              	.LVL180:
 2809              	.L206:
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2810              		.loc 1 1362 9 is_stmt 1 view .LVU1066
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2811              		.loc 1 1362 14 is_stmt 0 view .LVU1067
ARM GAS  /tmp/ccsqcIPI.s 			page 100


 2812 00d6 AA6E     		ldr	r2, [r5, #104]
1362:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 2813              		.loc 1 1362 26 view .LVU1068
 2814 00d8 42F00202 		orr	r2, r2, #2
 2815 00dc AA66     		str	r2, [r5, #104]
 2816 00de D9E7     		b	.L207
 2817              	.LVL181:
 2818              	.L229:
1425:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2819              		.loc 1 1425 3 is_stmt 1 view .LVU1069
 2820 00e0 032F     		cmp	r7, #3
 2821 00e2 1BD8     		bhi	.L224
 2822 00e4 DFE807F0 		tbb	[pc, r7]
 2823              	.L216:
 2824 00e8 0E       		.byte	(.L219-.L216)/2
 2825 00e9 02       		.byte	(.L218-.L216)/2
 2826 00ea 06       		.byte	(.L217-.L216)/2
 2827 00eb 0A       		.byte	(.L215-.L216)/2
 2828              		.p2align 1
 2829              	.L218:
1427:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2830              		.loc 1 1427 5 view .LVU1070
 2831 00ec 1022     		movs	r2, #16
 2832 00ee 5A60     		str	r2, [r3, #4]
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2833              		.loc 1 1453 10 is_stmt 0 view .LVU1071
 2834 00f0 0020     		movs	r0, #0
 2835 00f2 95E7     		b	.L201
 2836              	.L217:
1432:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2837              		.loc 1 1432 5 is_stmt 1 view .LVU1072
 2838 00f4 1822     		movs	r2, #24
 2839 00f6 5A60     		str	r2, [r3, #4]
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2840              		.loc 1 1453 10 is_stmt 0 view .LVU1073
 2841 00f8 0020     		movs	r0, #0
 2842 00fa 91E7     		b	.L201
 2843              	.L215:
1437:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2844              		.loc 1 1437 5 is_stmt 1 view .LVU1074
 2845 00fc 1C22     		movs	r2, #28
 2846 00fe 5A60     		str	r2, [r3, #4]
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2847              		.loc 1 1453 10 is_stmt 0 view .LVU1075
 2848 0100 0020     		movs	r0, #0
 2849 0102 8DE7     		b	.L201
 2850              	.L219:
1441:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2851              		.loc 1 1441 5 is_stmt 1 view .LVU1076
 2852 0104 1E22     		movs	r2, #30
 2853 0106 5A60     		str	r2, [r3, #4]
1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2854              		.loc 1 1444 5 view .LVU1077
1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2855              		.loc 1 1444 5 view .LVU1078
 2856 0108 0023     		movs	r3, #0
 2857 010a 85F83C30 		strb	r3, [r5, #60]
ARM GAS  /tmp/ccsqcIPI.s 			page 101


1444:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2858              		.loc 1 1444 5 view .LVU1079
1446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2859              		.loc 1 1446 5 view .LVU1080
1446:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2860              		.loc 1 1446 18 is_stmt 0 view .LVU1081
 2861 010e 0123     		movs	r3, #1
 2862 0110 85F83D30 		strb	r3, [r5, #61]
1453:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 2863              		.loc 1 1453 10 view .LVU1082
 2864 0114 3846     		mov	r0, r7
 2865 0116 83E7     		b	.L201
 2866              	.LVL182:
 2867              	.L220:
 2868              	.LCFI30:
 2869              		.cfi_def_cfa_offset 0
 2870              		.cfi_restore 3
 2871              		.cfi_restore 4
 2872              		.cfi_restore 5
 2873              		.cfi_restore 6
 2874              		.cfi_restore 7
 2875              		.cfi_restore 8
 2876              		.cfi_restore 9
 2877              		.cfi_restore 14
1326:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2878              		.loc 1 1326 12 view .LVU1083
 2879 0118 0120     		movs	r0, #1
 2880              	.LVL183:
1454:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2881              		.loc 1 1454 1 view .LVU1084
 2882 011a 7047     		bx	lr
 2883              	.LVL184:
 2884              	.L224:
 2885              	.LCFI31:
 2886              		.cfi_def_cfa_offset 32
 2887              		.cfi_offset 3, -32
 2888              		.cfi_offset 4, -28
 2889              		.cfi_offset 5, -24
 2890              		.cfi_offset 6, -20
 2891              		.cfi_offset 7, -16
 2892              		.cfi_offset 8, -12
 2893              		.cfi_offset 9, -8
 2894              		.cfi_offset 14, -4
1347:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2895              		.loc 1 1347 47 view .LVU1085
 2896 011c 0120     		movs	r0, #1
 2897 011e 7FE7     		b	.L201
 2898              		.cfi_endproc
 2899              	.LFE158:
 2901              		.section	.text.HAL_MDMA_GenerateSWRequest,"ax",%progbits
 2902              		.align	1
 2903              		.global	HAL_MDMA_GenerateSWRequest
 2904              		.syntax unified
 2905              		.thumb
 2906              		.thumb_func
 2908              	HAL_MDMA_GenerateSWRequest:
 2909              	.LVL185:
ARM GAS  /tmp/ccsqcIPI.s 			page 102


 2910              	.LFB159:
1463:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t request_mode;
 2911              		.loc 1 1463 1 is_stmt 1 view -0
 2912              		.cfi_startproc
 2913              		@ args = 0, pretend = 0, frame = 0
 2914              		@ frame_needed = 0, uses_anonymous_args = 0
 2915              		@ link register save eliminated.
1464:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2916              		.loc 1 1464 3 view .LVU1087
1467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2917              		.loc 1 1467 3 view .LVU1088
1467:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2918              		.loc 1 1467 5 is_stmt 0 view .LVU1089
 2919 0000 0146     		mov	r1, r0
 2920 0002 D8B1     		cbz	r0, .L236
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2921              		.loc 1 1473 3 is_stmt 1 view .LVU1090
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2922              		.loc 1 1473 23 is_stmt 0 view .LVU1091
 2923 0004 0368     		ldr	r3, [r0]
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2924              		.loc 1 1473 33 view .LVU1092
 2925 0006 1A69     		ldr	r2, [r3, #16]
1473:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2926              		.loc 1 1473 16 view .LVU1093
 2927 0008 02F08042 		and	r2, r2, #1073741824
 2928              	.LVL186:
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2929              		.loc 1 1475 3 is_stmt 1 view .LVU1094
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2930              		.loc 1 1475 22 is_stmt 0 view .LVU1095
 2931 000c D868     		ldr	r0, [r3, #12]
 2932              	.LVL187:
1475:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2933              		.loc 1 1475 5 view .LVU1096
 2934 000e 10F0010F 		tst	r0, #1
 2935 0012 09D0     		beq	.L237
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2936              		.loc 1 1482 8 is_stmt 1 view .LVU1097
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2937              		.loc 1 1482 28 is_stmt 0 view .LVU1098
 2938 0014 1868     		ldr	r0, [r3]
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2939              		.loc 1 1482 10 view .LVU1099
 2940 0016 10F4803F 		tst	r0, #65536
 2941 001a 00D1     		bne	.L234
1482:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 2942              		.loc 1 1482 61 discriminator 1 view .LVU1100
 2943 001c 42B9     		cbnz	r2, .L235
 2944              	.L234:
1485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2945              		.loc 1 1485 5 is_stmt 1 view .LVU1101
1485:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2946              		.loc 1 1485 22 is_stmt 0 view .LVU1102
 2947 001e 4FF48073 		mov	r3, #256
 2948 0022 8B66     		str	r3, [r1, #104]
1487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
ARM GAS  /tmp/ccsqcIPI.s 			page 103


 2949              		.loc 1 1487 5 is_stmt 1 view .LVU1103
1487:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2950              		.loc 1 1487 12 is_stmt 0 view .LVU1104
 2951 0024 0120     		movs	r0, #1
 2952 0026 7047     		bx	lr
 2953              	.L237:
1478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2954              		.loc 1 1478 5 is_stmt 1 view .LVU1105
1478:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2955              		.loc 1 1478 22 is_stmt 0 view .LVU1106
 2956 0028 8023     		movs	r3, #128
 2957 002a 8B66     		str	r3, [r1, #104]
1480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2958              		.loc 1 1480 5 is_stmt 1 view .LVU1107
1480:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2959              		.loc 1 1480 12 is_stmt 0 view .LVU1108
 2960 002c 0120     		movs	r0, #1
 2961 002e 7047     		bx	lr
 2962              	.L235:
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2963              		.loc 1 1492 5 is_stmt 1 view .LVU1109
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2964              		.loc 1 1492 20 is_stmt 0 view .LVU1110
 2965 0030 DA68     		ldr	r2, [r3, #12]
 2966              	.LVL188:
1492:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2967              		.loc 1 1492 26 view .LVU1111
 2968 0032 42F48032 		orr	r2, r2, #65536
 2969 0036 DA60     		str	r2, [r3, #12]
1494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2970              		.loc 1 1494 5 is_stmt 1 view .LVU1112
1494:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2971              		.loc 1 1494 12 is_stmt 0 view .LVU1113
 2972 0038 0020     		movs	r0, #0
 2973 003a 7047     		bx	lr
 2974              	.LVL189:
 2975              	.L236:
1469:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   }
 2976              		.loc 1 1469 12 view .LVU1114
 2977 003c 0120     		movs	r0, #1
 2978              	.LVL190:
1496:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 2979              		.loc 1 1496 1 view .LVU1115
 2980 003e 7047     		bx	lr
 2981              		.cfi_endproc
 2982              	.LFE159:
 2984              		.section	.text.HAL_MDMA_IRQHandler,"ax",%progbits
 2985              		.align	1
 2986              		.global	HAL_MDMA_IRQHandler
 2987              		.syntax unified
 2988              		.thumb
 2989              		.thumb_func
 2991              	HAL_MDMA_IRQHandler:
 2992              	.LVL191:
 2993              	.LFB160:
1505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __IO uint32_t count = 0;
 2994              		.loc 1 1505 1 is_stmt 1 view -0
ARM GAS  /tmp/ccsqcIPI.s 			page 104


 2995              		.cfi_startproc
 2996              		@ args = 0, pretend = 0, frame = 8
 2997              		@ frame_needed = 0, uses_anonymous_args = 0
1505:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   __IO uint32_t count = 0;
 2998              		.loc 1 1505 1 is_stmt 0 view .LVU1117
 2999 0000 30B5     		push	{r4, r5, lr}
 3000              	.LCFI32:
 3001              		.cfi_def_cfa_offset 12
 3002              		.cfi_offset 4, -12
 3003              		.cfi_offset 5, -8
 3004              		.cfi_offset 14, -4
 3005 0002 83B0     		sub	sp, sp, #12
 3006              	.LCFI33:
 3007              		.cfi_def_cfa_offset 24
1506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t timeout = SystemCoreClock / 9600U;
 3008              		.loc 1 1506 3 is_stmt 1 view .LVU1118
1506:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   uint32_t timeout = SystemCoreClock / 9600U;
 3009              		.loc 1 1506 17 is_stmt 0 view .LVU1119
 3010 0004 0023     		movs	r3, #0
 3011 0006 0193     		str	r3, [sp, #4]
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3012              		.loc 1 1507 3 is_stmt 1 view .LVU1120
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3013              		.loc 1 1507 38 is_stmt 0 view .LVU1121
 3014 0008 674B     		ldr	r3, .L259
 3015 000a 1D68     		ldr	r5, [r3]
1507:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3016              		.loc 1 1507 12 view .LVU1122
 3017 000c 674B     		ldr	r3, .L259+4
 3018 000e A3FB0535 		umull	r3, r5, r3, r5
 3019              	.LVL192:
1509:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3020              		.loc 1 1509 3 is_stmt 1 view .LVU1123
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((MDMA->GISR0 & generalIntFlag) == 0U)
 3021              		.loc 1 1512 3 view .LVU1124
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((MDMA->GISR0 & generalIntFlag) == 0U)
 3022              		.loc 1 1512 46 is_stmt 0 view .LVU1125
 3023 0012 0168     		ldr	r1, [r0]
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((MDMA->GISR0 & generalIntFlag) == 0U)
 3024              		.loc 1 1512 57 view .LVU1126
 3025 0014 664B     		ldr	r3, .L259+8
 3026 0016 0B44     		add	r3, r3, r1
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((MDMA->GISR0 & generalIntFlag) == 0U)
 3027              		.loc 1 1512 109 view .LVU1127
 3028 0018 C3F38413 		ubfx	r3, r3, #6, #5
1512:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   if((MDMA->GISR0 & generalIntFlag) == 0U)
 3029              		.loc 1 1512 18 view .LVU1128
 3030 001c 0122     		movs	r2, #1
 3031 001e 02FA03F3 		lsl	r3, r2, r3
 3032              	.LVL193:
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3033              		.loc 1 1513 3 is_stmt 1 view .LVU1129
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3034              		.loc 1 1513 11 is_stmt 0 view .LVU1130
 3035 0022 4FF0A442 		mov	r2, #1375731712
 3036 0026 1268     		ldr	r2, [r2]
1513:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
ARM GAS  /tmp/ccsqcIPI.s 			page 105


 3037              		.loc 1 1513 5 view .LVU1131
 3038 0028 1A42     		tst	r2, r3
 3039 002a 00F0A580 		beq	.L238
 3040 002e 0446     		mov	r4, r0
 3041 0030 AD0A     		lsrs	r5, r5, #10
 3042              	.LVL194:
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3043              		.loc 1 1519 3 is_stmt 1 view .LVU1132
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3044              		.loc 1 1519 7 is_stmt 0 view .LVU1133
 3045 0032 0B68     		ldr	r3, [r1]
 3046              	.LVL195:
1519:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3047              		.loc 1 1519 5 view .LVU1134
 3048 0034 13F0010F 		tst	r3, #1
 3049 0038 2FD0     		beq	.L240
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3050              		.loc 1 1521 5 is_stmt 1 view .LVU1135
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3051              		.loc 1 1521 8 is_stmt 0 view .LVU1136
 3052 003a CB68     		ldr	r3, [r1, #12]
1521:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3053              		.loc 1 1521 7 view .LVU1137
 3054 003c 13F0020F 		tst	r3, #2
 3055 0040 2BD0     		beq	.L240
1524:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3056              		.loc 1 1524 7 is_stmt 1 view .LVU1138
 3057 0042 CB68     		ldr	r3, [r1, #12]
 3058 0044 23F00203 		bic	r3, r3, #2
 3059 0048 CB60     		str	r3, [r1, #12]
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3060              		.loc 1 1527 7 view .LVU1139
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3061              		.loc 1 1527 24 is_stmt 0 view .LVU1140
 3062 004a 0268     		ldr	r2, [r0]
1527:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3063              		.loc 1 1527 17 view .LVU1141
 3064 004c 9368     		ldr	r3, [r2, #8]
 3065              	.LVL196:
1529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3066              		.loc 1 1529 7 is_stmt 1 view .LVU1142
1529:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3067              		.loc 1 1529 9 is_stmt 0 view .LVU1143
 3068 004e 13F0800F 		tst	r3, #128
 3069 0052 40F09380 		bne	.L241
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3070              		.loc 1 1532 9 is_stmt 1 view .LVU1144
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3071              		.loc 1 1532 14 is_stmt 0 view .LVU1145
 3072 0056 816E     		ldr	r1, [r0, #104]
 3073              	.LVL197:
1532:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3074              		.loc 1 1532 26 view .LVU1146
 3075 0058 41F00101 		orr	r1, r1, #1
 3076 005c 8166     		str	r1, [r0, #104]
 3077              	.L242:
1540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
ARM GAS  /tmp/ccsqcIPI.s 			page 106


 3078              		.loc 1 1540 7 is_stmt 1 view .LVU1147
1540:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3079              		.loc 1 1540 9 is_stmt 0 view .LVU1148
 3080 005e 13F4007F 		tst	r3, #512
 3081 0062 03D0     		beq	.L243
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3082              		.loc 1 1543 9 is_stmt 1 view .LVU1149
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3083              		.loc 1 1543 14 is_stmt 0 view .LVU1150
 3084 0064 A16E     		ldr	r1, [r4, #104]
1543:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3085              		.loc 1 1543 26 view .LVU1151
 3086 0066 41F00401 		orr	r1, r1, #4
 3087 006a A166     		str	r1, [r4, #104]
 3088              	.L243:
1546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3089              		.loc 1 1546 7 is_stmt 1 view .LVU1152
1546:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3090              		.loc 1 1546 9 is_stmt 0 view .LVU1153
 3091 006c 13F4807F 		tst	r3, #256
 3092 0070 03D0     		beq	.L244
1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3093              		.loc 1 1549 9 is_stmt 1 view .LVU1154
1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3094              		.loc 1 1549 14 is_stmt 0 view .LVU1155
 3095 0072 A16E     		ldr	r1, [r4, #104]
1549:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3096              		.loc 1 1549 26 view .LVU1156
 3097 0074 41F00801 		orr	r1, r1, #8
 3098 0078 A166     		str	r1, [r4, #104]
 3099              	.L244:
1552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3100              		.loc 1 1552 7 is_stmt 1 view .LVU1157
1552:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3101              		.loc 1 1552 9 is_stmt 0 view .LVU1158
 3102 007a 13F4806F 		tst	r3, #1024
 3103 007e 03D0     		beq	.L245
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3104              		.loc 1 1555 9 is_stmt 1 view .LVU1159
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3105              		.loc 1 1555 14 is_stmt 0 view .LVU1160
 3106 0080 A16E     		ldr	r1, [r4, #104]
1555:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3107              		.loc 1 1555 26 view .LVU1161
 3108 0082 41F01001 		orr	r1, r1, #16
 3109 0086 A166     		str	r1, [r4, #104]
 3110              	.L245:
1558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3111              		.loc 1 1558 7 is_stmt 1 view .LVU1162
1558:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3112              		.loc 1 1558 9 is_stmt 0 view .LVU1163
 3113 0088 13F4006F 		tst	r3, #2048
 3114 008c 03D0     		beq	.L246
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3115              		.loc 1 1561 9 is_stmt 1 view .LVU1164
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3116              		.loc 1 1561 14 is_stmt 0 view .LVU1165
ARM GAS  /tmp/ccsqcIPI.s 			page 107


 3117 008e A36E     		ldr	r3, [r4, #104]
 3118              	.LVL198:
1561:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3119              		.loc 1 1561 26 view .LVU1166
 3120 0090 43F02003 		orr	r3, r3, #32
 3121 0094 A366     		str	r3, [r4, #104]
 3122              	.L246:
1565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3123              		.loc 1 1565 7 is_stmt 1 view .LVU1167
 3124 0096 0123     		movs	r3, #1
1565:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3125              		.loc 1 1565 7 is_stmt 0 view .LVU1168
 3126 0098 5360     		str	r3, [r2, #4]
 3127              	.LVL199:
 3128              	.L240:
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3129              		.loc 1 1570 3 is_stmt 1 view .LVU1169
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3130              		.loc 1 1570 7 is_stmt 0 view .LVU1170
 3131 009a 2368     		ldr	r3, [r4]
 3132 009c 1A68     		ldr	r2, [r3]
1570:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3133              		.loc 1 1570 5 view .LVU1171
 3134 009e 12F0100F 		tst	r2, #16
 3135 00a2 09D0     		beq	.L247
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3136              		.loc 1 1572 5 is_stmt 1 view .LVU1172
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3137              		.loc 1 1572 8 is_stmt 0 view .LVU1173
 3138 00a4 DA68     		ldr	r2, [r3, #12]
1572:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3139              		.loc 1 1572 7 view .LVU1174
 3140 00a6 12F0200F 		tst	r2, #32
 3141 00aa 05D0     		beq	.L247
1575:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3142              		.loc 1 1575 7 is_stmt 1 view .LVU1175
 3143 00ac 1022     		movs	r2, #16
 3144 00ae 5A60     		str	r2, [r3, #4]
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3145              		.loc 1 1577 7 view .LVU1176
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3146              		.loc 1 1577 15 is_stmt 0 view .LVU1177
 3147 00b0 A36C     		ldr	r3, [r4, #72]
1577:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3148              		.loc 1 1577 9 view .LVU1178
 3149 00b2 0BB1     		cbz	r3, .L247
1580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3150              		.loc 1 1580 9 is_stmt 1 view .LVU1179
 3151 00b4 2046     		mov	r0, r4
 3152              	.LVL200:
1580:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3153              		.loc 1 1580 9 is_stmt 0 view .LVU1180
 3154 00b6 9847     		blx	r3
 3155              	.LVL201:
 3156              	.L247:
1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3157              		.loc 1 1586 3 is_stmt 1 view .LVU1181
ARM GAS  /tmp/ccsqcIPI.s 			page 108


1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3158              		.loc 1 1586 7 is_stmt 0 view .LVU1182
 3159 00b8 2368     		ldr	r3, [r4]
 3160 00ba 1A68     		ldr	r2, [r3]
1586:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3161              		.loc 1 1586 5 view .LVU1183
 3162 00bc 12F0080F 		tst	r2, #8
 3163 00c0 09D0     		beq	.L248
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3164              		.loc 1 1588 5 is_stmt 1 view .LVU1184
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3165              		.loc 1 1588 8 is_stmt 0 view .LVU1185
 3166 00c2 DA68     		ldr	r2, [r3, #12]
1588:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3167              		.loc 1 1588 7 view .LVU1186
 3168 00c4 12F0100F 		tst	r2, #16
 3169 00c8 05D0     		beq	.L248
1591:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3170              		.loc 1 1591 7 is_stmt 1 view .LVU1187
 3171 00ca 0822     		movs	r2, #8
 3172 00cc 5A60     		str	r2, [r3, #4]
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3173              		.loc 1 1593 7 view .LVU1188
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3174              		.loc 1 1593 15 is_stmt 0 view .LVU1189
 3175 00ce E36C     		ldr	r3, [r4, #76]
1593:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3176              		.loc 1 1593 9 view .LVU1190
 3177 00d0 0BB1     		cbz	r3, .L248
1596:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3178              		.loc 1 1596 9 is_stmt 1 view .LVU1191
 3179 00d2 2046     		mov	r0, r4
 3180 00d4 9847     		blx	r3
 3181              	.LVL202:
 3182              	.L248:
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3183              		.loc 1 1602 3 view .LVU1192
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3184              		.loc 1 1602 7 is_stmt 0 view .LVU1193
 3185 00d6 2368     		ldr	r3, [r4]
 3186 00d8 1A68     		ldr	r2, [r3]
1602:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3187              		.loc 1 1602 5 view .LVU1194
 3188 00da 12F0040F 		tst	r2, #4
 3189 00de 09D0     		beq	.L249
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3190              		.loc 1 1604 5 is_stmt 1 view .LVU1195
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3191              		.loc 1 1604 8 is_stmt 0 view .LVU1196
 3192 00e0 DA68     		ldr	r2, [r3, #12]
1604:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3193              		.loc 1 1604 7 view .LVU1197
 3194 00e2 12F0080F 		tst	r2, #8
 3195 00e6 05D0     		beq	.L249
1607:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3196              		.loc 1 1607 7 is_stmt 1 view .LVU1198
 3197 00e8 0422     		movs	r2, #4
ARM GAS  /tmp/ccsqcIPI.s 			page 109


 3198 00ea 5A60     		str	r2, [r3, #4]
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3199              		.loc 1 1609 7 view .LVU1199
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3200              		.loc 1 1609 15 is_stmt 0 view .LVU1200
 3201 00ec 236D     		ldr	r3, [r4, #80]
1609:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3202              		.loc 1 1609 9 view .LVU1201
 3203 00ee 0BB1     		cbz	r3, .L249
1612:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3204              		.loc 1 1612 9 is_stmt 1 view .LVU1202
 3205 00f0 2046     		mov	r0, r4
 3206 00f2 9847     		blx	r3
 3207              	.LVL203:
 3208              	.L249:
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3209              		.loc 1 1618 3 view .LVU1203
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3210              		.loc 1 1618 7 is_stmt 0 view .LVU1204
 3211 00f4 2368     		ldr	r3, [r4]
 3212 00f6 1A68     		ldr	r2, [r3]
1618:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3213              		.loc 1 1618 5 view .LVU1205
 3214 00f8 12F0020F 		tst	r2, #2
 3215 00fc 19D0     		beq	.L250
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3216              		.loc 1 1620 5 is_stmt 1 view .LVU1206
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3217              		.loc 1 1620 8 is_stmt 0 view .LVU1207
 3218 00fe DA68     		ldr	r2, [r3, #12]
1620:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3219              		.loc 1 1620 7 view .LVU1208
 3220 0100 12F0040F 		tst	r2, #4
 3221 0104 15D0     		beq	.L250
1623:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3222              		.loc 1 1623 7 is_stmt 1 view .LVU1209
 3223 0106 DA68     		ldr	r2, [r3, #12]
 3224 0108 22F03E02 		bic	r2, r2, #62
 3225 010c DA60     		str	r2, [r3, #12]
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3226              		.loc 1 1625 7 view .LVU1210
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3227              		.loc 1 1625 39 is_stmt 0 view .LVU1211
 3228 010e 94F83D30 		ldrb	r3, [r4, #61]	@ zero_extendqisi2
 3229 0112 DBB2     		uxtb	r3, r3
1625:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3230              		.loc 1 1625 9 view .LVU1212
 3231 0114 042B     		cmp	r3, #4
 3232 0116 36D0     		beq	.L258
1641:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3233              		.loc 1 1641 7 is_stmt 1 view .LVU1213
 3234 0118 2368     		ldr	r3, [r4]
 3235 011a 0222     		movs	r2, #2
 3236 011c 5A60     		str	r2, [r3, #4]
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3237              		.loc 1 1644 7 view .LVU1214
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
ARM GAS  /tmp/ccsqcIPI.s 			page 110


 3238              		.loc 1 1644 7 view .LVU1215
 3239 011e 0023     		movs	r3, #0
 3240 0120 84F83C30 		strb	r3, [r4, #60]
1644:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3241              		.loc 1 1644 7 view .LVU1216
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3242              		.loc 1 1647 7 view .LVU1217
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3243              		.loc 1 1647 20 is_stmt 0 view .LVU1218
 3244 0124 0123     		movs	r3, #1
1647:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3245              		.loc 1 1647 20 view .LVU1219
 3246 0126 84F83D30 		strb	r3, [r4, #61]
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3247              		.loc 1 1649 7 is_stmt 1 view .LVU1220
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3248              		.loc 1 1649 15 is_stmt 0 view .LVU1221
 3249 012a 636C     		ldr	r3, [r4, #68]
1649:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3250              		.loc 1 1649 9 view .LVU1222
 3251 012c 0BB1     		cbz	r3, .L250
1652:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3252              		.loc 1 1652 9 is_stmt 1 view .LVU1223
 3253 012e 2046     		mov	r0, r4
 3254 0130 9847     		blx	r3
 3255              	.LVL204:
 3256              	.L250:
1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3257              		.loc 1 1658 3 view .LVU1224
1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3258              		.loc 1 1658 11 is_stmt 0 view .LVU1225
 3259 0132 A36E     		ldr	r3, [r4, #104]
1658:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   {
 3260              		.loc 1 1658 5 view .LVU1226
 3261 0134 03B3     		cbz	r3, .L238
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3262              		.loc 1 1660 5 is_stmt 1 view .LVU1227
1660:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3263              		.loc 1 1660 18 is_stmt 0 view .LVU1228
 3264 0136 0423     		movs	r3, #4
 3265 0138 84F83D30 		strb	r3, [r4, #61]
1663:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3266              		.loc 1 1663 5 is_stmt 1 view .LVU1229
 3267 013c 2268     		ldr	r2, [r4]
 3268 013e D368     		ldr	r3, [r2, #12]
 3269 0140 23F00103 		bic	r3, r3, #1
 3270 0144 D360     		str	r3, [r2, #12]
 3271              	.L254:
1665:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3272              		.loc 1 1665 5 view .LVU1230
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3273              		.loc 1 1667 7 view .LVU1231
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
 3274              		.loc 1 1667 11 is_stmt 0 view .LVU1232
 3275 0146 019B     		ldr	r3, [sp, #4]
 3276 0148 0133     		adds	r3, r3, #1
1667:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       {
ARM GAS  /tmp/ccsqcIPI.s 			page 111


 3277              		.loc 1 1667 10 view .LVU1233
 3278 014a 0193     		str	r3, [sp, #4]
 3279 014c AB42     		cmp	r3, r5
 3280 014e 04D8     		bhi	.L253
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3281              		.loc 1 1672 48 is_stmt 1 view .LVU1234
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3282              		.loc 1 1672 17 is_stmt 0 view .LVU1235
 3283 0150 2368     		ldr	r3, [r4]
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3284              		.loc 1 1672 27 view .LVU1236
 3285 0152 DB68     		ldr	r3, [r3, #12]
1672:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3286              		.loc 1 1672 48 view .LVU1237
 3287 0154 13F0010F 		tst	r3, #1
 3288 0158 F5D1     		bne	.L254
 3289              	.L253:
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3290              		.loc 1 1675 5 is_stmt 1 view .LVU1238
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3291              		.loc 1 1675 5 view .LVU1239
 3292 015a 0023     		movs	r3, #0
 3293 015c 84F83C30 		strb	r3, [r4, #60]
1675:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3294              		.loc 1 1675 5 view .LVU1240
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3295              		.loc 1 1677 5 view .LVU1241
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3296              		.loc 1 1677 14 is_stmt 0 view .LVU1242
 3297 0160 2368     		ldr	r3, [r4]
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3298              		.loc 1 1677 24 view .LVU1243
 3299 0162 DB68     		ldr	r3, [r3, #12]
1677:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3300              		.loc 1 1677 7 view .LVU1244
 3301 0164 13F0010F 		tst	r3, #1
 3302 0168 19D0     		beq	.L255
1680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3303              		.loc 1 1680 7 is_stmt 1 view .LVU1245
1680:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3304              		.loc 1 1680 20 is_stmt 0 view .LVU1246
 3305 016a 0323     		movs	r3, #3
 3306 016c 84F83D30 		strb	r3, [r4, #61]
 3307              	.L256:
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3308              		.loc 1 1689 5 is_stmt 1 view .LVU1247
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3309              		.loc 1 1689 14 is_stmt 0 view .LVU1248
 3310 0170 636D     		ldr	r3, [r4, #84]
1689:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     {
 3311              		.loc 1 1689 8 view .LVU1249
 3312 0172 0BB1     		cbz	r3, .L238
1692:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3313              		.loc 1 1692 7 is_stmt 1 view .LVU1250
 3314 0174 2046     		mov	r0, r4
 3315 0176 9847     		blx	r3
 3316              	.LVL205:
ARM GAS  /tmp/ccsqcIPI.s 			page 112


 3317              	.L238:
1695:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3318              		.loc 1 1695 1 is_stmt 0 view .LVU1251
 3319 0178 03B0     		add	sp, sp, #12
 3320              	.LCFI34:
 3321              		.cfi_remember_state
 3322              		.cfi_def_cfa_offset 12
 3323              		@ sp needed
 3324 017a 30BD     		pop	{r4, r5, pc}
 3325              	.LVL206:
 3326              	.L241:
 3327              	.LCFI35:
 3328              		.cfi_restore_state
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3329              		.loc 1 1537 9 is_stmt 1 view .LVU1252
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3330              		.loc 1 1537 14 is_stmt 0 view .LVU1253
 3331 017c 816E     		ldr	r1, [r0, #104]
 3332              	.LVL207:
1537:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
 3333              		.loc 1 1537 26 view .LVU1254
 3334 017e 41F00201 		orr	r1, r1, #2
 3335 0182 8166     		str	r1, [r0, #104]
 3336 0184 6BE7     		b	.L242
 3337              	.LVL208:
 3338              	.L258:
1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3339              		.loc 1 1628 9 is_stmt 1 view .LVU1255
1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3340              		.loc 1 1628 9 view .LVU1256
 3341 0186 0023     		movs	r3, #0
 3342 0188 84F83C30 		strb	r3, [r4, #60]
1628:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3343              		.loc 1 1628 9 view .LVU1257
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3344              		.loc 1 1631 9 view .LVU1258
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3345              		.loc 1 1631 22 is_stmt 0 view .LVU1259
 3346 018c 0123     		movs	r3, #1
1631:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3347              		.loc 1 1631 22 view .LVU1260
 3348 018e 84F83D30 		strb	r3, [r4, #61]
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 3349              		.loc 1 1633 9 is_stmt 1 view .LVU1261
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 3350              		.loc 1 1633 17 is_stmt 0 view .LVU1262
 3351 0192 A36D     		ldr	r3, [r4, #88]
1633:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         {
 3352              		.loc 1 1633 11 view .LVU1263
 3353 0194 002B     		cmp	r3, #0
 3354 0196 EFD0     		beq	.L238
1635:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****         }
 3355              		.loc 1 1635 11 is_stmt 1 view .LVU1264
 3356 0198 2046     		mov	r0, r4
 3357 019a 9847     		blx	r3
 3358              	.LVL209:
1637:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****       }
ARM GAS  /tmp/ccsqcIPI.s 			page 113


 3359              		.loc 1 1637 9 view .LVU1265
 3360 019c ECE7     		b	.L238
 3361              	.L255:
1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3362              		.loc 1 1685 7 view .LVU1266
1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3363              		.loc 1 1685 20 is_stmt 0 view .LVU1267
 3364 019e 0123     		movs	r3, #1
1685:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****     }
 3365              		.loc 1 1685 20 view .LVU1268
 3366 01a0 84F83D30 		strb	r3, [r4, #61]
 3367 01a4 E4E7     		b	.L256
 3368              	.L260:
 3369 01a6 00BF     		.align	2
 3370              	.L259:
 3371 01a8 00000000 		.word	SystemCoreClock
 3372 01ac B5814E1B 		.word	*********
 3373 01b0 C0FFFFAD 		.word	-1375731776
 3374              		.cfi_endproc
 3375              	.LFE160:
 3377              		.section	.text.HAL_MDMA_GetState,"ax",%progbits
 3378              		.align	1
 3379              		.global	HAL_MDMA_GetState
 3380              		.syntax unified
 3381              		.thumb
 3382              		.thumb_func
 3384              	HAL_MDMA_GetState:
 3385              	.LVL210:
 3386              	.LFB161:
1723:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return hmdma->State;
 3387              		.loc 1 1723 1 is_stmt 1 view -0
 3388              		.cfi_startproc
 3389              		@ args = 0, pretend = 0, frame = 0
 3390              		@ frame_needed = 0, uses_anonymous_args = 0
 3391              		@ link register save eliminated.
1724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 3392              		.loc 1 1724 3 view .LVU1270
1724:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 3393              		.loc 1 1724 15 is_stmt 0 view .LVU1271
 3394 0000 90F83D00 		ldrb	r0, [r0, #61]	@ zero_extendqisi2
 3395              	.LVL211:
1725:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3396              		.loc 1 1725 1 view .LVU1272
 3397 0004 7047     		bx	lr
 3398              		.cfi_endproc
 3399              	.LFE161:
 3401              		.section	.text.HAL_MDMA_GetError,"ax",%progbits
 3402              		.align	1
 3403              		.global	HAL_MDMA_GetError
 3404              		.syntax unified
 3405              		.thumb
 3406              		.thumb_func
 3408              	HAL_MDMA_GetError:
 3409              	.LVL212:
 3410              	.LFB162:
1734:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c ****   return hmdma->ErrorCode;
 3411              		.loc 1 1734 1 is_stmt 1 view -0
ARM GAS  /tmp/ccsqcIPI.s 			page 114


 3412              		.cfi_startproc
 3413              		@ args = 0, pretend = 0, frame = 0
 3414              		@ frame_needed = 0, uses_anonymous_args = 0
 3415              		@ link register save eliminated.
1735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 3416              		.loc 1 1735 3 view .LVU1274
1735:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** }
 3417              		.loc 1 1735 15 is_stmt 0 view .LVU1275
 3418 0000 806E     		ldr	r0, [r0, #104]
 3419              	.LVL213:
1736:Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c **** 
 3420              		.loc 1 1736 1 view .LVU1276
 3421 0002 7047     		bx	lr
 3422              		.cfi_endproc
 3423              	.LFE162:
 3425              		.text
 3426              	.Letext0:
 3427              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 3428              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 3429              		.file 4 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/stm32h723xx.h"
 3430              		.file 5 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_def.h"
 3431              		.file 6 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal_mdma.h"
 3432              		.file 7 "Drivers/CMSIS/Device/ST/STM32H7xx/Include/system_stm32h7xx.h"
 3433              		.file 8 "Drivers/STM32H7xx_HAL_Driver/Inc/stm32h7xx_hal.h"
ARM GAS  /tmp/ccsqcIPI.s 			page 115


DEFINED SYMBOLS
                            *ABS*:00000000 stm32h7xx_hal_mdma.c
     /tmp/ccsqcIPI.s:20     .text.MDMA_SetConfig:00000000 $t
     /tmp/ccsqcIPI.s:25     .text.MDMA_SetConfig:00000000 MDMA_SetConfig
     /tmp/ccsqcIPI.s:161    .text.MDMA_SetConfig:0000008c $d
     /tmp/ccsqcIPI.s:166    .text.MDMA_Init:00000000 $t
     /tmp/ccsqcIPI.s:171    .text.MDMA_Init:00000000 MDMA_Init
     /tmp/ccsqcIPI.s:370    .text.HAL_MDMA_Init:00000000 $t
     /tmp/ccsqcIPI.s:376    .text.HAL_MDMA_Init:00000000 HAL_MDMA_Init
     /tmp/ccsqcIPI.s:499    .text.HAL_MDMA_DeInit:00000000 $t
     /tmp/ccsqcIPI.s:505    .text.HAL_MDMA_DeInit:00000000 HAL_MDMA_DeInit
     /tmp/ccsqcIPI.s:613    .text.HAL_MDMA_ConfigPostRequestMask:00000000 $t
     /tmp/ccsqcIPI.s:619    .text.HAL_MDMA_ConfigPostRequestMask:00000000 HAL_MDMA_ConfigPostRequestMask
     /tmp/ccsqcIPI.s:757    .text.HAL_MDMA_RegisterCallback:00000000 $t
     /tmp/ccsqcIPI.s:763    .text.HAL_MDMA_RegisterCallback:00000000 HAL_MDMA_RegisterCallback
     /tmp/ccsqcIPI.s:815    .text.HAL_MDMA_RegisterCallback:0000002e $d
     /tmp/ccsqcIPI.s:821    .text.HAL_MDMA_RegisterCallback:00000034 $t
     /tmp/ccsqcIPI.s:896    .text.HAL_MDMA_UnRegisterCallback:00000000 $t
     /tmp/ccsqcIPI.s:902    .text.HAL_MDMA_UnRegisterCallback:00000000 HAL_MDMA_UnRegisterCallback
     /tmp/ccsqcIPI.s:953    .text.HAL_MDMA_UnRegisterCallback:00000030 $d
     /tmp/ccsqcIPI.s:1046   .text.HAL_MDMA_LinkedList_CreateNode:00000000 $t
     /tmp/ccsqcIPI.s:1052   .text.HAL_MDMA_LinkedList_CreateNode:00000000 HAL_MDMA_LinkedList_CreateNode
     /tmp/ccsqcIPI.s:1359   .text.HAL_MDMA_LinkedList_AddNode:00000000 $t
     /tmp/ccsqcIPI.s:1365   .text.HAL_MDMA_LinkedList_AddNode:00000000 HAL_MDMA_LinkedList_AddNode
     /tmp/ccsqcIPI.s:1636   .text.HAL_MDMA_LinkedList_RemoveNode:00000000 $t
     /tmp/ccsqcIPI.s:1642   .text.HAL_MDMA_LinkedList_RemoveNode:00000000 HAL_MDMA_LinkedList_RemoveNode
     /tmp/ccsqcIPI.s:1880   .text.HAL_MDMA_LinkedList_EnableCircularMode:00000000 $t
     /tmp/ccsqcIPI.s:1886   .text.HAL_MDMA_LinkedList_EnableCircularMode:00000000 HAL_MDMA_LinkedList_EnableCircularMode
     /tmp/ccsqcIPI.s:1993   .text.HAL_MDMA_LinkedList_DisableCircularMode:00000000 $t
     /tmp/ccsqcIPI.s:1999   .text.HAL_MDMA_LinkedList_DisableCircularMode:00000000 HAL_MDMA_LinkedList_DisableCircularMode
     /tmp/ccsqcIPI.s:2091   .text.HAL_MDMA_Start:00000000 $t
     /tmp/ccsqcIPI.s:2097   .text.HAL_MDMA_Start:00000000 HAL_MDMA_Start
     /tmp/ccsqcIPI.s:2233   .text.HAL_MDMA_Start_IT:00000000 $t
     /tmp/ccsqcIPI.s:2239   .text.HAL_MDMA_Start_IT:00000000 HAL_MDMA_Start_IT
     /tmp/ccsqcIPI.s:2413   .text.HAL_MDMA_Abort:00000000 $t
     /tmp/ccsqcIPI.s:2419   .text.HAL_MDMA_Abort:00000000 HAL_MDMA_Abort
     /tmp/ccsqcIPI.s:2545   .text.HAL_MDMA_Abort_IT:00000000 $t
     /tmp/ccsqcIPI.s:2551   .text.HAL_MDMA_Abort_IT:00000000 HAL_MDMA_Abort_IT
     /tmp/ccsqcIPI.s:2607   .text.HAL_MDMA_PollForTransfer:00000000 $t
     /tmp/ccsqcIPI.s:2613   .text.HAL_MDMA_PollForTransfer:00000000 HAL_MDMA_PollForTransfer
     /tmp/ccsqcIPI.s:2824   .text.HAL_MDMA_PollForTransfer:000000e8 $d
     /tmp/ccsqcIPI.s:2828   .text.HAL_MDMA_PollForTransfer:000000ec $t
     /tmp/ccsqcIPI.s:2902   .text.HAL_MDMA_GenerateSWRequest:00000000 $t
     /tmp/ccsqcIPI.s:2908   .text.HAL_MDMA_GenerateSWRequest:00000000 HAL_MDMA_GenerateSWRequest
     /tmp/ccsqcIPI.s:2985   .text.HAL_MDMA_IRQHandler:00000000 $t
     /tmp/ccsqcIPI.s:2991   .text.HAL_MDMA_IRQHandler:00000000 HAL_MDMA_IRQHandler
     /tmp/ccsqcIPI.s:3371   .text.HAL_MDMA_IRQHandler:000001a8 $d
     /tmp/ccsqcIPI.s:3378   .text.HAL_MDMA_GetState:00000000 $t
     /tmp/ccsqcIPI.s:3384   .text.HAL_MDMA_GetState:00000000 HAL_MDMA_GetState
     /tmp/ccsqcIPI.s:3402   .text.HAL_MDMA_GetError:00000000 $t
     /tmp/ccsqcIPI.s:3408   .text.HAL_MDMA_GetError:00000000 HAL_MDMA_GetError
     /tmp/ccsqcIPI.s:960    .text.HAL_MDMA_UnRegisterCallback:00000037 $d
     /tmp/ccsqcIPI.s:960    .text.HAL_MDMA_UnRegisterCallback:00000038 $t

UNDEFINED SYMBOLS
HAL_GetTick
SystemCoreClock
ARM GAS  /tmp/ccsqcIPI.s 			page 116


