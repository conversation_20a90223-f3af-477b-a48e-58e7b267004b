ARM GAS  /tmp/cc39sGFU.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"timers.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/FreeRTOS/Source/timers.c"
  19              		.section	.text.prvGetNextExpireTime,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	prvGetNextExpireTime:
  26              	.LVL0:
  27              	.LFB18:
   1:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* Standard includes. */
  29:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #include <stdlib.h>
  30:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  31:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* Defining MPU_WRAPPERS_INCLUDED_FROM_API_FILE prevents task.h from redefining
ARM GAS  /tmp/cc39sGFU.s 			page 2


  32:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** all the API functions to use the MPU wrappers.  That should only be done when
  33:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** task.h is included from an application file. */
  34:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #define MPU_WRAPPERS_INCLUDED_FROM_API_FILE
  35:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #include "FreeRTOS.h"
  37:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #include "task.h"
  38:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #include "queue.h"
  39:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #include "timers.h"
  40:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  41:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if ( INCLUDE_xTimerPendFunctionCall == 1 ) && ( configUSE_TIMERS == 0 )
  42:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	#error configUSE_TIMERS must be set to 1 to make the xTimerPendFunctionCall() function available.
  43:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif
  44:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  45:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* Lint e9021, e961 and e750 are suppressed as a MISRA exception justified
  46:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** because the MPU ports require MPU_WRAPPERS_INCLUDED_FROM_API_FILE to be defined
  47:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** for the header files above, but not in this file, in order to generate the
  48:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** correct privileged Vs unprivileged linkage and placement. */
  49:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #undef MPU_WRAPPERS_INCLUDED_FROM_API_FILE /*lint !e9021 !e961 !e750. */
  50:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  51:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  52:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* This entire source file will be skipped if the application is not configured
  53:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** to include software timer functionality.  This #if is closed at the very bottom
  54:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** of this file.  If you want to include software timer functionality then ensure
  55:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** configUSE_TIMERS is set to 1 in FreeRTOSConfig.h. */
  56:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if ( configUSE_TIMERS == 1 )
  57:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  58:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* Misc definitions. */
  59:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #define tmrNO_DELAY		( TickType_t ) 0U
  60:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  61:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* The name assigned to the timer service task.  This can be overridden by
  62:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** defining trmTIMER_SERVICE_TASK_NAME in FreeRTOSConfig.h. */
  63:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #ifndef configTIMER_SERVICE_TASK_NAME
  64:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	#define configTIMER_SERVICE_TASK_NAME "Tmr Svc"
  65:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif
  66:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  67:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* Bit definitions used in the ucStatus member of a timer structure. */
  68:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #define tmrSTATUS_IS_ACTIVE					( ( uint8_t ) 0x01 )
  69:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #define tmrSTATUS_IS_STATICALLY_ALLOCATED	( ( uint8_t ) 0x02 )
  70:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #define tmrSTATUS_IS_AUTORELOAD				( ( uint8_t ) 0x04 )
  71:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* The definition of the timers themselves. */
  73:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** typedef struct tmrTimerControl /* The old naming convention is used to prevent breaking kernel awar
  74:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
  75:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	const char				*pcTimerName;		/*<< Text name.  This is not used by the kernel, it is included simpl
  76:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	ListItem_t				xTimerListItem;		/*<< Standard linked list item as used by all kernel features for e
  77:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	TickType_t				xTimerPeriodInTicks;/*<< How quickly and often the timer expires. */
  78:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	void 					*pvTimerID;			/*<< An ID to identify the timer.  This allows the timer to be identified 
  79:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	TimerCallbackFunction_t	pxCallbackFunction;	/*<< The function that will be called when the timer e
  80:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	#if( configUSE_TRACE_FACILITY == 1 )
  81:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		UBaseType_t			uxTimerNumber;		/*<< An ID assigned by trace tools such as FreeRTOS+Trace */
  82:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	#endif
  83:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	uint8_t 				ucStatus;			/*<< Holds bits to say if the timer was statically allocated or not, and i
  84:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** } xTIMER;
  85:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  86:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* The old xTIMER name is maintained above then typedefed to the new Timer_t
  87:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** name below to enable the use of older kernel aware debuggers. */
  88:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** typedef xTIMER Timer_t;
ARM GAS  /tmp/cc39sGFU.s 			page 3


  89:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* The definition of messages that can be sent and received on the timer queue.
  91:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Two types of message can be queued - messages that manipulate a software timer,
  92:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** and messages that request the execution of a non-timer related callback.  The
  93:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** two message types are defined in two separate structures, xTimerParametersType
  94:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** and xCallbackParametersType respectively. */
  95:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** typedef struct tmrTimerParameters
  96:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
  97:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	TickType_t			xMessageValue;		/*<< An optional value used by a subset of commands, for example, whe
  98:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *			pxTimer;			/*<< The timer to which the command will be applied. */
  99:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** } TimerParameter_t;
 100:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 101:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 102:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** typedef struct tmrCallbackParameters
 103:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 104:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	PendedFunction_t	pxCallbackFunction;	/* << The callback function to execute. */
 105:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	void *pvParameter1;						/* << The value that will be used as the callback functions first paramet
 106:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	uint32_t ulParameter2;					/* << The value that will be used as the callback functions second para
 107:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** } CallbackParameters_t;
 108:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 109:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* The structure that contains the two message types, along with an identifier
 110:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** that is used to determine which message type is valid. */
 111:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** typedef struct tmrTimerQueueMessage
 112:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 113:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	BaseType_t			xMessageID;			/*<< The command being sent to the timer service task. */
 114:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	union
 115:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 116:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		TimerParameter_t xTimerParameters;
 117:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 118:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Don't include xCallbackParameters if it is not going to be used as
 119:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		it makes the structure (and therefore the timer queue) larger. */
 120:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#if ( INCLUDE_xTimerPendFunctionCall == 1 )
 121:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			CallbackParameters_t xCallbackParameters;
 122:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#endif /* INCLUDE_xTimerPendFunctionCall */
 123:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	} u;
 124:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** } DaemonTaskMessage_t;
 125:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 126:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*lint -save -e956 A manual analysis and inspection has been used to determine
 127:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** which static variables must be declared volatile. */
 128:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 129:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* The list in which active timers are stored.  Timers are referenced in expire
 130:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** time order, with the nearest expiry time at the front of the list.  Only the
 131:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** timer service task is allowed to access these lists.
 132:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** xActiveTimerList1 and xActiveTimerList2 could be at function scope but that
 133:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** breaks some kernel aware debuggers, and debuggers that reply on removing the
 134:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static qualifier. */
 135:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static List_t xActiveTimerList1;
 136:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static List_t xActiveTimerList2;
 137:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static List_t *pxCurrentTimerList;
 138:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static List_t *pxOverflowTimerList;
 139:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 140:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /* A queue that is used to send commands to the timer service task. */
 141:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static QueueHandle_t xTimerQueue = NULL;
 142:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static TaskHandle_t xTimerTaskHandle = NULL;
 143:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 144:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*lint -restore */
 145:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
ARM GAS  /tmp/cc39sGFU.s 			page 4


 146:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 147:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 148:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if( configSUPPORT_STATIC_ALLOCATION == 1 )
 149:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 150:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* If static allocation is supported then the application must provide the
 151:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	following callback function - which enables the application to optionally
 152:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	provide the memory that will be used by the timer task as the task's stack
 153:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	and TCB. */
 154:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	extern void vApplicationGetTimerTaskMemory( StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **pp
 155:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 156:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif
 157:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 158:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 159:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Initialise the infrastructure used by the timer service task if it has not
 160:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * been initialised already.
 161:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 162:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvCheckForValidListAndQueue( void ) PRIVILEGED_FUNCTION;
 163:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 164:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 165:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * The timer service task (daemon).  Timer functionality is controlled by this
 166:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * task.  Other tasks communicate with the timer service task using the
 167:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * xTimerQueue queue.
 168:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 169:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static portTASK_FUNCTION_PROTO( prvTimerTask, pvParameters ) PRIVILEGED_FUNCTION;
 170:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 171:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 172:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Called by the timer service task to interpret and process a command it
 173:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * received on the timer queue.
 174:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 175:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvProcessReceivedCommands( void ) PRIVILEGED_FUNCTION;
 176:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 177:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 178:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Insert the timer into either xActiveTimerList1, or xActiveTimerList2,
 179:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * depending on if the expire time causes a timer counter overflow.
 180:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 181:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static BaseType_t prvInsertTimerInActiveList( Timer_t * const pxTimer, const TickType_t xNextExpiry
 182:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 183:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 184:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * An active timer has reached its expire time.  Reload the timer if it is an
 185:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * auto-reload timer, then call its callback.
 186:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 187:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvProcessExpiredTimer( const TickType_t xNextExpireTime, const TickType_t xTimeNow ) P
 188:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 189:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 190:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * The tick count has overflowed.  Switch the timer lists after ensuring the
 191:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * current timer list does not still reference some timers.
 192:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 193:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvSwitchTimerLists( void ) PRIVILEGED_FUNCTION;
 194:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 196:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Obtain the current tick count, setting *pxTimerListsWereSwitched to pdTRUE
 197:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * if a tick count overflow occurred since prvSampleTimeNow() was last called.
 198:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 199:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static TickType_t prvSampleTimeNow( BaseType_t * const pxTimerListsWereSwitched ) PRIVILEGED_FUNCTI
 200:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 201:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 202:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * If the timer list contains any active timers then return the expire time of
ARM GAS  /tmp/cc39sGFU.s 			page 5


 203:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * the timer that will expire first and set *pxListWasEmpty to false.  If the
 204:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * timer list does not contain any timers then return 0 and set *pxListWasEmpty
 205:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * to pdTRUE.
 206:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 207:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static TickType_t prvGetNextExpireTime( BaseType_t * const pxListWasEmpty ) PRIVILEGED_FUNCTION;
 208:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 209:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 210:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * If a timer has expired, process it.  Otherwise, block the timer service task
 211:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * until either a timer does expire or a command is received.
 212:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 213:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvProcessTimerOrBlockTask( const TickType_t xNextExpireTime, BaseType_t xListWasEmpty 
 214:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 215:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*
 216:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * Called after a Timer_t structure has been allocated either statically or
 217:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  * dynamically to fill in the structure's members.
 218:Middlewares/Third_Party/FreeRTOS/Source/timers.c ****  */
 219:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvInitialiseNewTimer(	const char * const pcTimerName,			/*lint !e971 Unqualified char 
 220:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									const TickType_t xTimerPeriodInTicks,
 221:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									const UBaseType_t uxAutoReload,
 222:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									void * const pvTimerID,
 223:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									TimerCallbackFunction_t pxCallbackFunction,
 224:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									Timer_t *pxNewTimer ) PRIVILEGED_FUNCTION;
 225:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 226:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 227:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerCreateTimerTask( void )
 228:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 229:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn = pdFAIL;
 230:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 231:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* This function is called when the scheduler is started if
 232:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configUSE_TIMERS is set to 1.  Check that the infrastructure used by the
 233:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	timer service task has been created/initialised.  If timers have already
 234:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	been created then the initialisation will already have been performed. */
 235:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	prvCheckForValidListAndQueue();
 236:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 237:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( xTimerQueue != NULL )
 238:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 239:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#if( configSUPPORT_STATIC_ALLOCATION == 1 )
 240:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 241:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			StaticTask_t *pxTimerTaskTCBBuffer = NULL;
 242:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			StackType_t *pxTimerTaskStackBuffer = NULL;
 243:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			uint32_t ulTimerTaskStackSize;
 244:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 245:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			vApplicationGetTimerTaskMemory( &pxTimerTaskTCBBuffer, &pxTimerTaskStackBuffer, &ulTimerTaskStac
 246:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xTimerTaskHandle = xTaskCreateStatic(	prvTimerTask,
 247:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													configTIMER_SERVICE_TASK_NAME,
 248:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													ulTimerTaskStackSize,
 249:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													NULL,
 250:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													( ( UBaseType_t ) configTIMER_TASK_PRIORITY ) | portPRIVILEGE_BIT,
 251:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													pxTimerTaskStackBuffer,
 252:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													pxTimerTaskTCBBuffer );
 253:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 254:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xTimerTaskHandle != NULL )
 255:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 256:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				xReturn = pdPASS;
 257:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 258:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 259:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#else
ARM GAS  /tmp/cc39sGFU.s 			page 6


 260:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 261:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xReturn = xTaskCreate(	prvTimerTask,
 262:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									configTIMER_SERVICE_TASK_NAME,
 263:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									configTIMER_TASK_STACK_DEPTH,
 264:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									NULL,
 265:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									( ( UBaseType_t ) configTIMER_TASK_PRIORITY ) | portPRIVILEGE_BIT,
 266:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									&xTimerTaskHandle );
 267:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 268:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#endif /* configSUPPORT_STATIC_ALLOCATION */
 269:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 270:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	else
 271:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 272:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		mtCOVERAGE_TEST_MARKER();
 273:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 274:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 275:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xReturn );
 276:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 277:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 278:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 279:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 280:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if( configSUPPORT_DYNAMIC_ALLOCATION == 1 )
 281:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 282:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	TimerHandle_t xTimerCreate(	const char * const pcTimerName,			/*lint !e971 Unqualified char types 
 283:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 								const TickType_t xTimerPeriodInTicks,
 284:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 								const UBaseType_t uxAutoReload,
 285:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 								void * const pvTimerID,
 286:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 								TimerCallbackFunction_t pxCallbackFunction )
 287:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 288:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *pxNewTimer;
 289:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 290:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pvPortMalloc( sizeof( Timer_t ) ); /*lint !e9087 !e9079 All values ret
 291:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 292:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( pxNewTimer != NULL )
 293:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 294:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Status is thus far zero as the timer is not created statically
 295:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			and has not been started.  The auto-reload bit may get set in
 296:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			prvInitialiseNewTimer. */
 297:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxNewTimer->ucStatus = 0x00;
 298:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			prvInitialiseNewTimer( pcTimerName, xTimerPeriodInTicks, uxAutoReload, pvTimerID, pxCallbackFunc
 299:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 300:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 301:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		return pxNewTimer;
 302:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 303:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 304:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif /* configSUPPORT_DYNAMIC_ALLOCATION */
 305:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 306:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 307:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if( configSUPPORT_STATIC_ALLOCATION == 1 )
 308:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 309:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	TimerHandle_t xTimerCreateStatic(	const char * const pcTimerName,		/*lint !e971 Unqualified char t
 310:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 										const TickType_t xTimerPeriodInTicks,
 311:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 										const UBaseType_t uxAutoReload,
 312:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 										void * const pvTimerID,
 313:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 										TimerCallbackFunction_t pxCallbackFunction,
 314:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 										StaticTimer_t *pxTimerBuffer )
 315:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 316:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *pxNewTimer;
ARM GAS  /tmp/cc39sGFU.s 			page 7


 317:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 318:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#if( configASSERT_DEFINED == 1 )
 319:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 320:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Sanity check that the size of the structure used to declare a
 321:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			variable of type StaticTimer_t equals the size of the real timer
 322:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			structure. */
 323:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			volatile size_t xSize = sizeof( StaticTimer_t );
 324:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xSize == sizeof( Timer_t ) );
 325:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 326:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 327:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#endif /* configASSERT_DEFINED */
 328:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 329:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* A pointer to a StaticTimer_t structure MUST be provided, use it. */
 330:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		configASSERT( pxTimerBuffer );
 331:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pxTimerBuffer; /*lint !e740 !e9087 StaticTimer_t is a pointer to a Tim
 332:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 333:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( pxNewTimer != NULL )
 334:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 335:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Timers can be created statically or dynamically so note this
 336:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			timer was created statically in case it is later deleted.  The
 337:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			auto-reload bit may get set in prvInitialiseNewTimer(). */
 338:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxNewTimer->ucStatus = tmrSTATUS_IS_STATICALLY_ALLOCATED;
 339:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 340:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			prvInitialiseNewTimer( pcTimerName, xTimerPeriodInTicks, uxAutoReload, pvTimerID, pxCallbackFunc
 341:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 342:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 343:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		return pxNewTimer;
 344:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 345:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 346:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif /* configSUPPORT_STATIC_ALLOCATION */
 347:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 348:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 349:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvInitialiseNewTimer(	const char * const pcTimerName,			/*lint !e971 Unqualified char 
 350:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									const TickType_t xTimerPeriodInTicks,
 351:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									const UBaseType_t uxAutoReload,
 352:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									void * const pvTimerID,
 353:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									TimerCallbackFunction_t pxCallbackFunction,
 354:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 									Timer_t *pxNewTimer )
 355:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 356:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* 0 is not a valid value for xTimerPeriodInTicks. */
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( ( xTimerPeriodInTicks > 0 ) );
 358:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 359:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( pxNewTimer != NULL )
 360:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 361:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Ensure the infrastructure used by the timer service task has been
 362:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		created/initialised. */
 363:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		prvCheckForValidListAndQueue();
 364:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 365:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Initialise the timer structure members using the function
 366:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		parameters. */
 367:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pcTimerName = pcTimerName;
 368:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->xTimerPeriodInTicks = xTimerPeriodInTicks;
 369:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pvTimerID = pvTimerID;
 370:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pxCallbackFunction = pxCallbackFunction;
 371:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		vListInitialiseItem( &( pxNewTimer->xTimerListItem ) );
 372:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( uxAutoReload != pdFALSE )
 373:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
ARM GAS  /tmp/cc39sGFU.s 			page 8


 374:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxNewTimer->ucStatus |= tmrSTATUS_IS_AUTORELOAD;
 375:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 376:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		traceTIMER_CREATE( pxNewTimer );
 377:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 378:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 379:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 380:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 381:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerGenericCommand( TimerHandle_t xTimer, const BaseType_t xCommandID, const TickType_
 382:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 383:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn = pdFAIL;
 384:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** DaemonTaskMessage_t xMessage;
 385:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 386:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 387:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 388:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Send a message to the timer service task to perform a particular action
 389:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	on a particular timer definition. */
 390:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( xTimerQueue != NULL )
 391:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 392:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Send a command to the timer service task to start the xTimer timer. */
 393:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.xMessageID = xCommandID;
 394:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xTimerParameters.xMessageValue = xOptionalValue;
 395:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xTimerParameters.pxTimer = xTimer;
 396:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 397:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( xCommandID < tmrFIRST_FROM_ISR_COMMAND )
 398:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 399:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xTaskGetSchedulerState() == taskSCHEDULER_RUNNING )
 400:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 401:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				xReturn = xQueueSendToBack( xTimerQueue, &xMessage, xTicksToWait );
 402:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 403:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			else
 404:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 405:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				xReturn = xQueueSendToBack( xTimerQueue, &xMessage, tmrNO_DELAY );
 406:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 407:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 408:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 409:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 410:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xReturn = xQueueSendToBackFromISR( xTimerQueue, &xMessage, pxHigherPriorityTaskWoken );
 411:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 412:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 413:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		traceTIMER_COMMAND_SEND( xTimer, xCommandID, xOptionalValue, xReturn );
 414:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 415:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	else
 416:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 417:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		mtCOVERAGE_TEST_MARKER();
 418:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 419:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 420:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 421:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 422:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 423:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 424:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TaskHandle_t xTimerGetTimerDaemonTaskHandle( void )
 425:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 426:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* If xTimerGetTimerDaemonTaskHandle() is called before the scheduler has been
 427:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	started, then xTimerTaskHandle will be NULL. */
 428:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( ( xTimerTaskHandle != NULL ) );
 429:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xTimerTaskHandle;
 430:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
ARM GAS  /tmp/cc39sGFU.s 			page 9


 431:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 432:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 433:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimerGetPeriod( TimerHandle_t xTimer )
 434:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 435:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer = xTimer;
 436:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 437:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 438:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->xTimerPeriodInTicks;
 439:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 440:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 441:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 442:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** void vTimerSetReloadMode( TimerHandle_t xTimer, const UBaseType_t uxAutoReload )
 443:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 444:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 445:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 446:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 447:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 448:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 449:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( uxAutoReload != pdFALSE )
 450:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 451:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxTimer->ucStatus |= tmrSTATUS_IS_AUTORELOAD;
 452:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 453:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 454:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 455:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxTimer->ucStatus &= ~tmrSTATUS_IS_AUTORELOAD;
 456:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 457:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 458:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskEXIT_CRITICAL();
 459:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 460:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 461:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 462:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** UBaseType_t uxTimerGetReloadMode( TimerHandle_t xTimer )
 463:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 464:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 465:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** UBaseType_t uxReturn;
 466:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 467:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 468:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 469:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 470:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) == 0 )
 471:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 472:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Not an auto-reload timer. */
 473:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			uxReturn = ( UBaseType_t ) pdFALSE;
 474:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 475:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 476:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 477:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Is an auto-reload timer. */
 478:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			uxReturn = ( UBaseType_t ) pdTRUE;
 479:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 480:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 481:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskEXIT_CRITICAL();
 482:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 483:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return uxReturn;
 484:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 485:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 486:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 487:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimerGetExpiryTime( TimerHandle_t xTimer )
ARM GAS  /tmp/cc39sGFU.s 			page 10


 488:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 489:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 490:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xReturn;
 491:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 492:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 493:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xReturn = listGET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ) );
 494:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 495:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 496:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 497:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 498:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** const char * pcTimerGetName( TimerHandle_t xTimer ) /*lint !e971 Unqualified char types are allowed
 499:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 500:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer = xTimer;
 501:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 502:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 503:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->pcTimerName;
 504:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 505:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 506:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 507:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvProcessExpiredTimer( const TickType_t xNextExpireTime, const TickType_t xTimeNow )
 508:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 509:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xResult;
 510:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * const pxTimer = ( Timer_t * ) listGET_OWNER_OF_HEAD_ENTRY( pxCurrentTimerList ); /*lint !
 511:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 512:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Remove the timer from the list of active timers.  A check has already
 513:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	been performed to ensure the list is not empty. */
 514:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 515:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	traceTIMER_EXPIRED( pxTimer );
 516:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 517:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* If the timer is an auto-reload timer then calculate the next
 518:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	expiry time and re-insert the timer in the list of active timers. */
 519:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) != 0 )
 520:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 521:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* The timer is inserted into a list using a time relative to anything
 522:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		other than the current time.  It will therefore be inserted into the
 523:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		correct list relative to the time this task thinks it is now. */
 524:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( prvInsertTimerInActiveList( pxTimer, ( xNextExpireTime + pxTimer->xTimerPeriodInTicks ), xTim
 525:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 526:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* The timer expired before it was added to the active timer
 527:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			list.  Reload it now.  */
 528:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xNextExpireTime, NULL, tmr
 529:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xResult );
 530:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xResult;
 531:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 532:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 533:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 534:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			mtCOVERAGE_TEST_MARKER();
 535:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 536:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 537:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	else
 538:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 539:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 540:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		mtCOVERAGE_TEST_MARKER();
 541:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 542:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 543:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Call the timer callback. */
 544:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxTimer->pxCallbackFunction( ( TimerHandle_t ) pxTimer );
ARM GAS  /tmp/cc39sGFU.s 			page 11


 545:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 546:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 547:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 548:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static portTASK_FUNCTION( prvTimerTask, pvParameters )
 549:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 550:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xNextExpireTime;
 551:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xListWasEmpty;
 552:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 553:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Just to avoid compiler warnings. */
 554:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	( void ) pvParameters;
 555:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 556:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	#if( configUSE_DAEMON_TASK_STARTUP_HOOK == 1 )
 557:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 558:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		extern void vApplicationDaemonTaskStartupHook( void );
 559:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 560:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Allow the application writer to execute some code in the context of
 561:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		this task at the point the task starts executing.  This is useful if the
 562:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		application includes initialisation code that would benefit from
 563:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		executing after the scheduler has been started. */
 564:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		vApplicationDaemonTaskStartupHook();
 565:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 566:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	#endif /* configUSE_DAEMON_TASK_STARTUP_HOOK */
 567:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 568:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	for( ;; )
 569:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 570:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Query the timers list to see if it contains any timers, and if so,
 571:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		obtain the time at which the next timer will expire. */
 572:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xNextExpireTime = prvGetNextExpireTime( &xListWasEmpty );
 573:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 574:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* If a timer has expired, process it.  Otherwise, block this task
 575:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		until either a timer does expire, or a command is received. */
 576:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		prvProcessTimerOrBlockTask( xNextExpireTime, xListWasEmpty );
 577:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 578:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Empty the command queue. */
 579:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		prvProcessReceivedCommands();
 580:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 581:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 582:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 583:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 584:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvProcessTimerOrBlockTask( const TickType_t xNextExpireTime, BaseType_t xListWasEmpty 
 585:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 586:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 587:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerListsWereSwitched;
 588:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 589:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	vTaskSuspendAll();
 590:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 591:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Obtain the time now to make an assessment as to whether the timer
 592:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		has expired or not.  If obtaining the time causes the lists to switch
 593:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		then don't process this timer as any timers that remained in the list
 594:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		when the lists were switched will have been processed within the
 595:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		prvSampleTimeNow() function. */
 596:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xTimeNow = prvSampleTimeNow( &xTimerListsWereSwitched );
 597:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( xTimerListsWereSwitched == pdFALSE )
 598:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 599:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* The tick count has not overflowed, has the timer expired? */
 600:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( ( xListWasEmpty == pdFALSE ) && ( xNextExpireTime <= xTimeNow ) )
 601:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
ARM GAS  /tmp/cc39sGFU.s 			page 12


 602:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) xTaskResumeAll();
 603:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				prvProcessExpiredTimer( xNextExpireTime, xTimeNow );
 604:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 605:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			else
 606:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 607:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				/* The tick count has not overflowed, and the next expire
 608:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				time has not been reached yet.  This task should therefore
 609:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				block to wait for the next expire time or a command to be
 610:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				received - whichever comes first.  The following line cannot
 611:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				be reached unless xNextExpireTime > xTimeNow, except in the
 612:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case when the current timer list is empty. */
 613:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				if( xListWasEmpty != pdFALSE )
 614:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 615:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					/* The current timer list is empty - is the overflow list
 616:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					also empty? */
 617:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					xListWasEmpty = listLIST_IS_EMPTY( pxOverflowTimerList );
 618:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 619:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 620:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				vQueueWaitForMessageRestricted( xTimerQueue, ( xNextExpireTime - xTimeNow ), xListWasEmpty );
 621:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 622:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				if( xTaskResumeAll() == pdFALSE )
 623:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 624:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					/* Yield to wait for either a command to arrive, or the
 625:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					block time to expire.  If a command arrived between the
 626:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					critical section being exited and this yield then the yield
 627:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					will not cause the task to block. */
 628:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					portYIELD_WITHIN_API();
 629:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 630:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				else
 631:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 632:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					mtCOVERAGE_TEST_MARKER();
 633:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 634:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 635:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 636:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 637:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 638:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xTaskResumeAll();
 639:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 640:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 641:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 642:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 643:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 644:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static TickType_t prvGetNextExpireTime( BaseType_t * const pxListWasEmpty )
 645:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
  28              		.loc 1 645 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		@ link register save eliminated.
 646:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xNextExpireTime;
  33              		.loc 1 646 1 view .LVU1
 647:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 648:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Timers are listed in expiry time order, with the head of the list
 649:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	referencing the task that will expire first.  Obtain the time at which
 650:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	the timer with the nearest expiry time will expire.  If there are no
 651:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	active timers then just set the next expire time to 0.  That will cause
 652:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	this task to unblock when the tick count overflows, at which point the
ARM GAS  /tmp/cc39sGFU.s 			page 13


 653:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	timer lists will be switched and the next expiry time can be
 654:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	re-assessed.  */
 655:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	*pxListWasEmpty = listLIST_IS_EMPTY( pxCurrentTimerList );
  34              		.loc 1 655 2 view .LVU2
  35              		.loc 1 655 20 is_stmt 0 view .LVU3
  36 0000 064B     		ldr	r3, .L6
  37 0002 1A68     		ldr	r2, [r3]
  38 0004 1368     		ldr	r3, [r2]
  39 0006 2BB9     		cbnz	r3, .L4
  40              		.loc 1 655 20 discriminator 1 view .LVU4
  41 0008 0123     		movs	r3, #1
  42              	.L2:
  43              		.loc 1 655 18 discriminator 4 view .LVU5
  44 000a 0360     		str	r3, [r0]
 656:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( *pxListWasEmpty == pdFALSE )
  45              		.loc 1 656 2 is_stmt 1 view .LVU6
  46              		.loc 1 656 4 is_stmt 0 view .LVU7
  47 000c 23B9     		cbnz	r3, .L5
 657:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 658:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xNextExpireTime = listGET_ITEM_VALUE_OF_HEAD_ENTRY( pxCurrentTimerList );
  48              		.loc 1 658 3 is_stmt 1 view .LVU8
  49              		.loc 1 658 21 is_stmt 0 view .LVU9
  50 000e D368     		ldr	r3, [r2, #12]
  51              		.loc 1 658 19 view .LVU10
  52 0010 1868     		ldr	r0, [r3]
  53              	.LVL1:
  54              		.loc 1 658 19 view .LVU11
  55 0012 7047     		bx	lr
  56              	.LVL2:
  57              	.L4:
 655:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( *pxListWasEmpty == pdFALSE )
  58              		.loc 1 655 20 discriminator 2 view .LVU12
  59 0014 0023     		movs	r3, #0
  60 0016 F8E7     		b	.L2
  61              	.L5:
 659:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 660:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	else
 661:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 662:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Ensure the task unblocks when the tick count rolls over. */
 663:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xNextExpireTime = ( TickType_t ) 0U;
  62              		.loc 1 663 19 view .LVU13
  63 0018 0020     		movs	r0, #0
  64              	.LVL3:
 664:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 665:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 666:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xNextExpireTime;
  65              		.loc 1 666 2 is_stmt 1 view .LVU14
 667:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
  66              		.loc 1 667 1 is_stmt 0 view .LVU15
  67 001a 7047     		bx	lr
  68              	.L7:
  69              		.align	2
  70              	.L6:
  71 001c 00000000 		.word	pxCurrentTimerList
  72              		.cfi_endproc
  73              	.LFE18:
  75              		.section	.text.prvInsertTimerInActiveList,"ax",%progbits
ARM GAS  /tmp/cc39sGFU.s 			page 14


  76              		.align	1
  77              		.syntax unified
  78              		.thumb
  79              		.thumb_func
  81              	prvInsertTimerInActiveList:
  82              	.LVL4:
  83              	.LFB20:
 668:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 669:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 670:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static TickType_t prvSampleTimeNow( BaseType_t * const pxTimerListsWereSwitched )
 671:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 672:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 673:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static TickType_t xLastTime = ( TickType_t ) 0U; /*lint !e956 Variable is only acce
 674:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 675:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xTimeNow = xTaskGetTickCount();
 676:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 677:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( xTimeNow < xLastTime )
 678:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 679:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		prvSwitchTimerLists();
 680:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		*pxTimerListsWereSwitched = pdTRUE;
 681:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 682:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	else
 683:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 684:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		*pxTimerListsWereSwitched = pdFALSE;
 685:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 686:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 687:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xLastTime = xTimeNow;
 688:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 689:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xTimeNow;
 690:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 691:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 692:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 693:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static BaseType_t prvInsertTimerInActiveList( Timer_t * const pxTimer, const TickType_t xNextExpiry
 694:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
  84              		.loc 1 694 1 is_stmt 1 view -0
  85              		.cfi_startproc
  86              		@ args = 0, pretend = 0, frame = 0
  87              		@ frame_needed = 0, uses_anonymous_args = 0
  88              		.loc 1 694 1 is_stmt 0 view .LVU17
  89 0000 08B5     		push	{r3, lr}
  90              	.LCFI0:
  91              		.cfi_def_cfa_offset 8
  92              		.cfi_offset 3, -8
  93              		.cfi_offset 14, -4
 695:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xProcessTimerNow = pdFALSE;
  94              		.loc 1 695 1 is_stmt 1 view .LVU18
  95              	.LVL5:
 696:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 697:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	listSET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ), xNextExpiryTime );
  96              		.loc 1 697 2 view .LVU19
  97 0002 4160     		str	r1, [r0, #4]
 698:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
  98              		.loc 1 698 2 view .LVU20
  99 0004 0061     		str	r0, [r0, #16]
 699:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 700:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	if( xNextExpiryTime <= xTimeNow )
 100              		.loc 1 700 2 view .LVU21
ARM GAS  /tmp/cc39sGFU.s 			page 15


 101              		.loc 1 700 4 is_stmt 0 view .LVU22
 102 0006 9142     		cmp	r1, r2
 103 0008 0CD8     		bhi	.L9
 701:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 702:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Has the expiry time elapsed between the command to start/reset a
 703:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		timer was issued, and the time the command was processed? */
 704:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( ( ( TickType_t ) ( xTimeNow - xCommandTime ) ) >= pxTimer->xTimerPeriodInTicks ) /*lint !e961
 104              		.loc 1 704 3 is_stmt 1 view .LVU23
 105              		.loc 1 704 35 is_stmt 0 view .LVU24
 106 000a D21A     		subs	r2, r2, r3
 107              	.LVL6:
 108              		.loc 1 704 64 view .LVU25
 109 000c 8369     		ldr	r3, [r0, #24]
 110              	.LVL7:
 111              		.loc 1 704 5 view .LVU26
 112 000e 9A42     		cmp	r2, r3
 113 0010 01D3     		bcc	.L14
 705:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 706:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* The time between a command being issued and the command being
 707:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			processed actually exceeds the timers period.  */
 708:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xProcessTimerNow = pdTRUE;
 114              		.loc 1 708 21 view .LVU27
 115 0012 0120     		movs	r0, #1
 116              	.LVL8:
 117              	.L8:
 709:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 710:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 711:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 712:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			vListInsert( pxOverflowTimerList, &( pxTimer->xTimerListItem ) );
 713:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 714:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 715:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	else
 716:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 717:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( ( xTimeNow < xCommandTime ) && ( xNextExpiryTime >= xCommandTime ) )
 718:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 719:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* If, since the command was issued, the tick count has overflowed
 720:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			but the expiry time has not, then the timer must have already passed
 721:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			its expiry time and should be processed immediately. */
 722:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xProcessTimerNow = pdTRUE;
 723:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 724:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 725:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 726:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 727:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 728:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 729:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 730:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xProcessTimerNow;
 731:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 118              		.loc 1 731 1 view .LVU28
 119 0014 08BD     		pop	{r3, pc}
 120              	.LVL9:
 121              	.L14:
 712:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 122              		.loc 1 712 4 is_stmt 1 view .LVU29
 123 0016 011D     		adds	r1, r0, #4
 124              	.LVL10:
 712:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
ARM GAS  /tmp/cc39sGFU.s 			page 16


 125              		.loc 1 712 4 is_stmt 0 view .LVU30
 126 0018 0B4B     		ldr	r3, .L16
 127 001a 1868     		ldr	r0, [r3]
 128              	.LVL11:
 712:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 129              		.loc 1 712 4 view .LVU31
 130 001c FFF7FEFF 		bl	vListInsert
 131              	.LVL12:
 695:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 132              		.loc 1 695 12 view .LVU32
 133 0020 0020     		movs	r0, #0
 134 0022 F7E7     		b	.L8
 135              	.LVL13:
 136              	.L9:
 717:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 137              		.loc 1 717 3 is_stmt 1 view .LVU33
 717:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 138              		.loc 1 717 35 is_stmt 0 view .LVU34
 139 0024 9A42     		cmp	r2, r3
 140 0026 2CBF     		ite	cs
 141 0028 0022     		movcs	r2, #0
 142              	.LVL14:
 717:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 143              		.loc 1 717 35 view .LVU35
 144 002a 0122     		movcc	r2, #1
 145 002c 9942     		cmp	r1, r3
 146 002e 38BF     		it	cc
 147 0030 0022     		movcc	r2, #0
 717:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 148              		.loc 1 717 5 view .LVU36
 149 0032 0AB1     		cbz	r2, .L15
 722:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 150              		.loc 1 722 21 view .LVU37
 151 0034 0120     		movs	r0, #1
 152              	.LVL15:
 730:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 153              		.loc 1 730 2 is_stmt 1 view .LVU38
 730:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 154              		.loc 1 730 9 is_stmt 0 view .LVU39
 155 0036 EDE7     		b	.L8
 156              	.LVL16:
 157              	.L15:
 726:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 158              		.loc 1 726 4 is_stmt 1 view .LVU40
 159 0038 011D     		adds	r1, r0, #4
 160              	.LVL17:
 726:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 161              		.loc 1 726 4 is_stmt 0 view .LVU41
 162 003a 044B     		ldr	r3, .L16+4
 163              	.LVL18:
 726:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 164              		.loc 1 726 4 view .LVU42
 165 003c 1868     		ldr	r0, [r3]
 166              	.LVL19:
 726:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 167              		.loc 1 726 4 view .LVU43
 168 003e FFF7FEFF 		bl	vListInsert
ARM GAS  /tmp/cc39sGFU.s 			page 17


 169              	.LVL20:
 695:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 170              		.loc 1 695 12 view .LVU44
 171 0042 0020     		movs	r0, #0
 172 0044 E6E7     		b	.L8
 173              	.L17:
 174 0046 00BF     		.align	2
 175              	.L16:
 176 0048 00000000 		.word	pxOverflowTimerList
 177 004c 00000000 		.word	pxCurrentTimerList
 178              		.cfi_endproc
 179              	.LFE20:
 181              		.section	.rodata.prvCheckForValidListAndQueue.str1.4,"aMS",%progbits,1
 182              		.align	2
 183              	.LC0:
 184 0000 546D7251 		.ascii	"TmrQ\000"
 184      00
 185              		.section	.text.prvCheckForValidListAndQueue,"ax",%progbits
 186              		.align	1
 187              		.syntax unified
 188              		.thumb
 189              		.thumb_func
 191              	prvCheckForValidListAndQueue:
 192              	.LFB23:
 732:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 733:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 734:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void	prvProcessReceivedCommands( void )
 735:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 736:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** DaemonTaskMessage_t xMessage;
 737:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer;
 738:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerListsWereSwitched, xResult;
 739:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 740:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 741:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	while( xQueueReceive( xTimerQueue, &xMessage, tmrNO_DELAY ) != pdFAIL ) /*lint !e603 xMessage does
 742:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 743:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#if ( INCLUDE_xTimerPendFunctionCall == 1 )
 744:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 745:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Negative commands are pended function calls rather than timer
 746:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			commands. */
 747:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xMessage.xMessageID < ( BaseType_t ) 0 )
 748:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 749:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				const CallbackParameters_t * const pxCallback = &( xMessage.u.xCallbackParameters );
 750:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 751:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				/* The timer uses the xCallbackParameters member to request a
 752:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				callback be executed.  Check the callback is not NULL. */
 753:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				configASSERT( pxCallback );
 754:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 755:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				/* Call the function. */
 756:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				pxCallback->pxCallbackFunction( pxCallback->pvParameter1, pxCallback->ulParameter2 );
 757:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 758:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			else
 759:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 760:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				mtCOVERAGE_TEST_MARKER();
 761:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 762:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 763:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		#endif /* INCLUDE_xTimerPendFunctionCall */
 764:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
ARM GAS  /tmp/cc39sGFU.s 			page 18


 765:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Commands that are positive are timer commands rather than pended
 766:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		function calls. */
 767:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( xMessage.xMessageID >= ( BaseType_t ) 0 )
 768:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 769:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* The messages uses the xTimerParameters member to work on a
 770:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			software timer. */
 771:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxTimer = xMessage.u.xTimerParameters.pxTimer;
 772:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 773:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( listIS_CONTAINED_WITHIN( NULL, &( pxTimer->xTimerListItem ) ) == pdFALSE ) /*lint !e961. The
 774:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 775:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				/* The timer is in a list, remove it. */
 776:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 777:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 778:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			else
 779:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 780:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				mtCOVERAGE_TEST_MARKER();
 781:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 782:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 783:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			traceTIMER_COMMAND_RECEIVED( pxTimer, xMessage.xMessageID, xMessage.u.xTimerParameters.xMessageV
 784:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 785:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* In this case the xTimerListsWereSwitched parameter is not used, but
 786:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			it must be present in the function call.  prvSampleTimeNow() must be
 787:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			called after the message is received from xTimerQueue so there is no
 788:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			possibility of a higher priority task adding a message to the message
 789:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			queue with a time that is ahead of the timer daemon task (because it
 790:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pre-empted the timer daemon task after the xTimeNow value was set). */
 791:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xTimeNow = prvSampleTimeNow( &xTimerListsWereSwitched );
 792:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 793:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			switch( xMessage.xMessageID )
 794:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 795:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_START :
 796:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_START_FROM_ISR :
 797:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_RESET :
 798:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_RESET_FROM_ISR :
 799:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_START_DONT_TRACE :
 800:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					/* Start or restart a timer. */
 801:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->ucStatus |= tmrSTATUS_IS_ACTIVE;
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					if( prvInsertTimerInActiveList( pxTimer,  xMessage.u.xTimerParameters.xMessageValue + pxTimer-
 803:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 804:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						/* The timer expired before it was added to the active
 805:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						timer list.  Process it now. */
 806:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						pxTimer->pxCallbackFunction( ( TimerHandle_t ) pxTimer );
 807:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						traceTIMER_EXPIRED( pxTimer );
 808:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 809:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) != 0 )
 810:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 811:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xMessage.u.xTimerParam
 812:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							configASSERT( xResult );
 813:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							( void ) xResult;
 814:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 815:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						else
 816:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 817:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							mtCOVERAGE_TEST_MARKER();
 818:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 819:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					}
 820:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					else
 821:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
ARM GAS  /tmp/cc39sGFU.s 			page 19


 822:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						mtCOVERAGE_TEST_MARKER();
 823:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					}
 824:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 825:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 826:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_STOP :
 827:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_STOP_FROM_ISR :
 828:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					/* The timer has already been removed from the active list. */
 829:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 830:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 831:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 832:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_CHANGE_PERIOD :
 833:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_CHANGE_PERIOD_FROM_ISR :
 834:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->ucStatus |= tmrSTATUS_IS_ACTIVE;
 835:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->xTimerPeriodInTicks = xMessage.u.xTimerParameters.xMessageValue;
 836:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					configASSERT( ( pxTimer->xTimerPeriodInTicks > 0 ) );
 837:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 838:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					/* The new period does not really have a reference, and can
 839:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					be longer or shorter than the old one.  The command time is
 840:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					therefore set to the current time, and as the period cannot
 841:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					be zero the next expiry time can only be in the future,
 842:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					meaning (unlike for the xTimerStart() case above) there is
 843:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					no fail case that needs to be handled here. */
 844:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					( void ) prvInsertTimerInActiveList( pxTimer, ( xTimeNow + pxTimer->xTimerPeriodInTicks ), xTi
 845:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 846:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 847:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				case tmrCOMMAND_DELETE :
 848:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					#if ( configSUPPORT_DYNAMIC_ALLOCATION == 1 )
 849:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 850:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						/* The timer has already been removed from the active list,
 851:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						just free up the memory if the memory was dynamically
 852:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						allocated. */
 853:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						if( ( pxTimer->ucStatus & tmrSTATUS_IS_STATICALLY_ALLOCATED ) == ( uint8_t ) 0 )
 854:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 855:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							vPortFree( pxTimer );
 856:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 857:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						else
 858:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 859:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 860:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 861:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					}
 862:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					#else
 863:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 864:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						/* If dynamic allocation is not enabled, the memory
 865:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						could not have been dynamically allocated. So there is
 866:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						no need to free the memory - just mark the timer as
 867:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						"not active". */
 868:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 869:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					}
 870:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					#endif /* configSUPPORT_DYNAMIC_ALLOCATION */
 871:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 872:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 873:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				default	:
 874:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					/* Don't expect to get here. */
 875:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 876:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 877:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 878:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
ARM GAS  /tmp/cc39sGFU.s 			page 20


 879:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 880:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 881:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 882:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvSwitchTimerLists( void )
 883:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 884:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xNextExpireTime, xReloadTime;
 885:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** List_t *pxTemp;
 886:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer;
 887:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xResult;
 888:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 889:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* The tick count has overflowed.  The timer lists must be switched.
 890:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	If there are any timers still referenced from the current timer list
 891:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	then they must have expired and should be processed before the lists
 892:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	are switched. */
 893:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	while( listLIST_IS_EMPTY( pxCurrentTimerList ) == pdFALSE )
 894:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 895:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xNextExpireTime = listGET_ITEM_VALUE_OF_HEAD_ENTRY( pxCurrentTimerList );
 896:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 897:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Remove the timer from the list. */
 898:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxTimer = ( Timer_t * ) listGET_OWNER_OF_HEAD_ENTRY( pxCurrentTimerList ); /*lint !e9087 !e9079 v
 899:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 900:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		traceTIMER_EXPIRED( pxTimer );
 901:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 902:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Execute its callback, then send a command to restart the timer if
 903:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		it is an auto-reload timer.  It cannot be restarted here as the lists
 904:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		have not yet been switched. */
 905:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxTimer->pxCallbackFunction( ( TimerHandle_t ) pxTimer );
 906:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 907:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) != 0 )
 908:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 909:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			/* Calculate the reload value, and if the reload value results in
 910:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			the timer going into the same timer list then it has already expired
 911:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			and the timer should be re-inserted into the current list so it is
 912:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			processed again within this loop.  Otherwise a command should be sent
 913:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			to restart the timer to ensure it is only inserted into a list after
 914:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			the lists have been swapped. */
 915:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xReloadTime = ( xNextExpireTime + pxTimer->xTimerPeriodInTicks );
 916:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xReloadTime > xNextExpireTime )
 917:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 918:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				listSET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ), xReloadTime );
 919:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
 920:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 921:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 922:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			else
 923:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 924:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xNextExpireTime, NULL, tm
 925:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				configASSERT( xResult );
 926:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) xResult;
 927:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 928:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 929:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 930:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 931:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			mtCOVERAGE_TEST_MARKER();
 932:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 933:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 934:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 935:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxTemp = pxCurrentTimerList;
ARM GAS  /tmp/cc39sGFU.s 			page 21


 936:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxCurrentTimerList = pxOverflowTimerList;
 937:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxOverflowTimerList = pxTemp;
 938:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 939:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 940:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 941:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** static void prvCheckForValidListAndQueue( void )
 942:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 193              		.loc 1 942 1 is_stmt 1 view -0
 194              		.cfi_startproc
 195              		@ args = 0, pretend = 0, frame = 0
 196              		@ frame_needed = 0, uses_anonymous_args = 0
 197 0000 30B5     		push	{r4, r5, lr}
 198              	.LCFI1:
 199              		.cfi_def_cfa_offset 12
 200              		.cfi_offset 4, -12
 201              		.cfi_offset 5, -8
 202              		.cfi_offset 14, -4
 203 0002 83B0     		sub	sp, sp, #12
 204              	.LCFI2:
 205              		.cfi_def_cfa_offset 24
 943:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Check that the list from which active timers are referenced, and the
 944:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	queue used to communicate with the timer service, have been
 945:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	initialised. */
 946:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 206              		.loc 1 946 2 view .LVU46
 207 0004 FFF7FEFF 		bl	vPortEnterCritical
 208              	.LVL21:
 947:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 948:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( xTimerQueue == NULL )
 209              		.loc 1 948 3 view .LVU47
 210              		.loc 1 948 19 is_stmt 0 view .LVU48
 211 0008 114B     		ldr	r3, .L22
 212 000a 1B68     		ldr	r3, [r3]
 213              		.loc 1 948 5 view .LVU49
 214 000c 1BB1     		cbz	r3, .L21
 215              	.L19:
 949:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 950:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			vListInitialise( &xActiveTimerList1 );
 951:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			vListInitialise( &xActiveTimerList2 );
 952:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxCurrentTimerList = &xActiveTimerList1;
 953:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxOverflowTimerList = &xActiveTimerList2;
 954:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 955:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			#if( configSUPPORT_STATIC_ALLOCATION == 1 )
 956:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 957:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				/* The timer queue is allocated statically in case
 958:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				configSUPPORT_DYNAMIC_ALLOCATION is 0. */
 959:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				static StaticQueue_t xStaticTimerQueue; /*lint !e956 Ok to declare in this manner to prevent ad
 960:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				static uint8_t ucStaticTimerQueueStorage[ ( size_t ) configTIMER_QUEUE_LENGTH * sizeof( DaemonT
 961:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 962:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				xTimerQueue = xQueueCreateStatic( ( UBaseType_t ) configTIMER_QUEUE_LENGTH, ( UBaseType_t ) siz
 963:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 964:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			#else
 965:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 966:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				xTimerQueue = xQueueCreate( ( UBaseType_t ) configTIMER_QUEUE_LENGTH, sizeof( DaemonTaskMessage
 967:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 968:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			#endif
 969:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
ARM GAS  /tmp/cc39sGFU.s 			page 22


 970:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			#if ( configQUEUE_REGISTRY_SIZE > 0 )
 971:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 972:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				if( xTimerQueue != NULL )
 973:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 974:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					vQueueAddToRegistry( xTimerQueue, "TmrQ" );
 975:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 976:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				else
 977:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 978:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					mtCOVERAGE_TEST_MARKER();
 979:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 980:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 981:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			#endif /* configQUEUE_REGISTRY_SIZE */
 982:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 983:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
 984:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 985:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			mtCOVERAGE_TEST_MARKER();
 216              		.loc 1 985 28 is_stmt 1 view .LVU50
 986:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 987:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 988:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskEXIT_CRITICAL();
 217              		.loc 1 988 2 view .LVU51
 218 000e FFF7FEFF 		bl	vPortExitCritical
 219              	.LVL22:
 989:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 220              		.loc 1 989 1 is_stmt 0 view .LVU52
 221 0012 03B0     		add	sp, sp, #12
 222              	.LCFI3:
 223              		.cfi_remember_state
 224              		.cfi_def_cfa_offset 12
 225              		@ sp needed
 226 0014 30BD     		pop	{r4, r5, pc}
 227              	.L21:
 228              	.LCFI4:
 229              		.cfi_restore_state
 950:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			vListInitialise( &xActiveTimerList2 );
 230              		.loc 1 950 4 is_stmt 1 view .LVU53
 231 0016 0F4D     		ldr	r5, .L22+4
 232 0018 2846     		mov	r0, r5
 233 001a FFF7FEFF 		bl	vListInitialise
 234              	.LVL23:
 951:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxCurrentTimerList = &xActiveTimerList1;
 235              		.loc 1 951 4 view .LVU54
 236 001e 0E4C     		ldr	r4, .L22+8
 237 0020 2046     		mov	r0, r4
 238 0022 FFF7FEFF 		bl	vListInitialise
 239              	.LVL24:
 952:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxOverflowTimerList = &xActiveTimerList2;
 240              		.loc 1 952 4 view .LVU55
 952:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			pxOverflowTimerList = &xActiveTimerList2;
 241              		.loc 1 952 23 is_stmt 0 view .LVU56
 242 0026 0D4B     		ldr	r3, .L22+12
 243 0028 1D60     		str	r5, [r3]
 953:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 244              		.loc 1 953 4 is_stmt 1 view .LVU57
 953:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 245              		.loc 1 953 24 is_stmt 0 view .LVU58
 246 002a 0D4B     		ldr	r3, .L22+16
ARM GAS  /tmp/cc39sGFU.s 			page 23


 247 002c 1C60     		str	r4, [r3]
 248              	.LBB45:
 959:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				static uint8_t ucStaticTimerQueueStorage[ ( size_t ) configTIMER_QUEUE_LENGTH * sizeof( DaemonT
 249              		.loc 1 959 5 is_stmt 1 view .LVU59
 960:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 250              		.loc 1 960 5 view .LVU60
 962:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 251              		.loc 1 962 5 view .LVU61
 962:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 252              		.loc 1 962 19 is_stmt 0 view .LVU62
 253 002e 0023     		movs	r3, #0
 254 0030 0093     		str	r3, [sp]
 255 0032 0C4B     		ldr	r3, .L22+20
 256 0034 0C4A     		ldr	r2, .L22+24
 257 0036 1021     		movs	r1, #16
 258 0038 0A20     		movs	r0, #10
 259 003a FFF7FEFF 		bl	xQueueGenericCreateStatic
 260              	.LVL25:
 962:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 261              		.loc 1 962 17 discriminator 1 view .LVU63
 262 003e 044B     		ldr	r3, .L22
 263 0040 1860     		str	r0, [r3]
 264              	.LBE45:
 972:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 265              		.loc 1 972 5 is_stmt 1 view .LVU64
 972:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 266              		.loc 1 972 7 is_stmt 0 view .LVU65
 267 0042 0028     		cmp	r0, #0
 268 0044 E3D0     		beq	.L19
 974:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 269              		.loc 1 974 6 is_stmt 1 view .LVU66
 270 0046 0949     		ldr	r1, .L22+28
 271 0048 FFF7FEFF 		bl	vQueueAddToRegistry
 272              	.LVL26:
 273 004c DFE7     		b	.L19
 274              	.L23:
 275 004e 00BF     		.align	2
 276              	.L22:
 277 0050 00000000 		.word	xTimerQueue
 278 0054 00000000 		.word	xActiveTimerList1
 279 0058 00000000 		.word	xActiveTimerList2
 280 005c 00000000 		.word	pxCurrentTimerList
 281 0060 00000000 		.word	pxOverflowTimerList
 282 0064 00000000 		.word	xStaticTimerQueue.0
 283 0068 00000000 		.word	ucStaticTimerQueueStorage.1
 284 006c 00000000 		.word	.LC0
 285              		.cfi_endproc
 286              	.LFE23:
 288              		.section	.text.prvInitialiseNewTimer,"ax",%progbits
 289              		.align	1
 290              		.syntax unified
 291              		.thumb
 292              		.thumb_func
 294              	prvInitialiseNewTimer:
 295              	.LVL27:
 296              	.LFB7:
 355:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* 0 is not a valid value for xTimerPeriodInTicks. */
ARM GAS  /tmp/cc39sGFU.s 			page 24


 297              		.loc 1 355 1 view -0
 298              		.cfi_startproc
 299              		@ args = 8, pretend = 0, frame = 0
 300              		@ frame_needed = 0, uses_anonymous_args = 0
 355:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* 0 is not a valid value for xTimerPeriodInTicks. */
 301              		.loc 1 355 1 is_stmt 0 view .LVU68
 302 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 303              	.LCFI5:
 304              		.cfi_def_cfa_offset 24
 305              		.cfi_offset 4, -24
 306              		.cfi_offset 5, -20
 307              		.cfi_offset 6, -16
 308              		.cfi_offset 7, -12
 309              		.cfi_offset 8, -8
 310              		.cfi_offset 14, -4
 311 0004 079F     		ldr	r7, [sp, #28]
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 312              		.loc 1 357 2 is_stmt 1 view .LVU69
 313 0006 41B9     		cbnz	r1, .L25
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 314              		.loc 1 357 2 discriminator 1 view .LVU70
 315              	.LBB46:
 316              	.LBI46:
 317              		.file 2 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h"
   1:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*
   2:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FreeRTOS Kernel V10.3.1
   3:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Copyright (C) 2020 Amazon.com, Inc. or its affiliates.  All Rights Reserved.
   4:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
   5:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Permission is hereby granted, free of charge, to any person obtaining a copy of
   6:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * this software and associated documentation files (the "Software"), to deal in
   7:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software without restriction, including without limitation the rights to
   8:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
   9:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * the Software, and to permit persons to whom the Software is furnished to do so,
  10:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * subject to the following conditions:
  11:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  12:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The above copyright notice and this permission notice shall be included in all
  13:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * copies or substantial portions of the Software.
  14:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  15:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  16:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  17:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  18:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  19:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  20:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  21:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  22:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://www.FreeRTOS.org
  23:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * http://aws.amazon.com/freertos
  24:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  25:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * 1 tab == 4 spaces!
  26:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  27:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  28:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  29:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef PORTMACRO_H
  30:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define PORTMACRO_H
  31:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  32:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef __cplusplus
  33:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern "C" {
ARM GAS  /tmp/cc39sGFU.s 			page 25


  34:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  35:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  36:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------
  37:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * Port specific definitions.
  38:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  39:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * The settings in this file configure FreeRTOS correctly for the
  40:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * given hardware and compiler.
  41:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *
  42:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  * These settings should not be altered.
  43:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  *-----------------------------------------------------------
  44:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h ****  */
  45:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  46:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Type definitions. */
  47:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCHAR		char
  48:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portFLOAT		float
  49:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDOUBLE		double
  50:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portLONG		long
  51:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSHORT		short
  52:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_TYPE	uint32_t
  53:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBASE_TYPE	long
  54:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  55:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef portSTACK_TYPE StackType_t;
  56:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef long BaseType_t;
  57:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** typedef unsigned long UBaseType_t;
  58:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  59:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if( configUSE_16_BIT_TICKS == 1 )
  60:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint16_t TickType_t;
  61:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffff
  62:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #else
  63:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	typedef uint32_t TickType_t;
  64:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portMAX_DELAY ( TickType_t ) 0xffffffffUL
  65:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  66:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* 32-bit tick type on a 32-bit architecture, so reads of the tick count do
  67:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	not need to be guarded with a critical section. */
  68:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portTICK_TYPE_IS_ATOMIC 1
  69:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
  70:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  71:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  72:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specifics. */
  73:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSTACK_GROWTH			( -1 )
  74:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTICK_PERIOD_MS			( ( TickType_t ) 1000 / configTICK_RATE_HZ )
  75:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portBYTE_ALIGNMENT			8
  76:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  77:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  78:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Scheduler utilities. */
  79:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD() 															\
  80:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {																				\
  81:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Set a PendSV to request a context switch. */								\
  82:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	portNVIC_INT_CTRL_REG = portNVIC_PENDSVSET_BIT;								\
  83:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 																				\
  84:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Barriers are normally not required but do ensure the code is completely	\
  85:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	within the specified behaviour for the architecture. */						\
  86:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "dsb" ::: "memory" );										\
  87:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "isb" );													\
  88:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
  89:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  90:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_INT_CTRL_REG		( * ( ( volatile uint32_t * ) 0xe000ed04 ) )
ARM GAS  /tmp/cc39sGFU.s 			page 26


  91:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNVIC_PENDSVSET_BIT		( 1UL << 28UL )
  92:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEND_SWITCHING_ISR( xSwitchRequired ) if( xSwitchRequired != pdFALSE ) portYIELD()
  93:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portYIELD_FROM_ISR( x ) portEND_SWITCHING_ISR( x )
  94:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
  95:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
  96:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Critical section management. */
  97:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortEnterCritical( void );
  98:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** extern void vPortExitCritical( void );
  99:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portSET_INTERRUPT_MASK_FROM_ISR()		ulPortRaiseBASEPRI()
 100:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portCLEAR_INTERRUPT_MASK_FROM_ISR(x)	vPortSetBASEPRI(x)
 101:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portDISABLE_INTERRUPTS()				vPortRaiseBASEPRI()
 102:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENABLE_INTERRUPTS()					vPortSetBASEPRI(0)
 103:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portENTER_CRITICAL()					vPortEnterCritical()
 104:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portEXIT_CRITICAL()						vPortExitCritical()
 105:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 106:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 107:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 108:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Task function macros as described on the FreeRTOS.org WEB site.  These are
 109:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** not necessary for to use this port.  They are defined so the common demo files
 110:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** (which build with all the ports) will build. */
 111:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION_PROTO( vFunction, pvParameters ) void vFunction( void *pvParameters )
 112:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portTASK_FUNCTION( vFunction, pvParameters ) void vFunction( void *pvParameters )
 113:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 114:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 115:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Tickless idle/low power functionality. */
 116:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portSUPPRESS_TICKS_AND_SLEEP
 117:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	extern void vPortSuppressTicksAndSleep( TickType_t xExpectedIdleTime );
 118:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portSUPPRESS_TICKS_AND_SLEEP( xExpectedIdleTime ) vPortSuppressTicksAndSleep( xExpectedIdl
 119:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 120:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 121:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 122:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* Architecture specific optimisations. */
 123:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef configUSE_PORT_OPTIMISED_TASK_SELECTION
 124:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define configUSE_PORT_OPTIMISED_TASK_SELECTION 1
 125:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 126:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 127:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #if configUSE_PORT_OPTIMISED_TASK_SELECTION == 1
 128:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 129:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Generic helper function. */
 130:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__attribute__( ( always_inline ) ) static inline uint8_t ucPortCountLeadingZeros( uint32_t ulBitma
 131:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 132:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	uint8_t ucReturn;
 133:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 134:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		__asm volatile ( "clz %0, %1" : "=r" ( ucReturn ) : "r" ( ulBitmap ) : "memory" );
 135:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		return ucReturn;
 136:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 137:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 138:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Check the configuration. */
 139:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#if( configMAX_PRIORITIES > 32 )
 140:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		#error configUSE_PORT_OPTIMISED_TASK_SELECTION can only be set to 1 when configMAX_PRIORITIES is 
 141:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#endif
 142:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 143:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Store/clear the ready priorities in a bit map. */
 144:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRECORD_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) |= ( 1UL 
 145:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portRESET_READY_PRIORITY( uxPriority, uxReadyPriorities ) ( uxReadyPriorities ) &= ~( 1UL 
 146:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 147:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/*-----------------------------------------------------------*/
ARM GAS  /tmp/cc39sGFU.s 			page 27


 148:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 149:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portGET_HIGHEST_PRIORITY( uxTopPriority, uxReadyPriorities ) uxTopPriority = ( 31UL - ( ui
 150:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 151:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif /* configUSE_PORT_OPTIMISED_TASK_SELECTION */
 152:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 153:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 154:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 155:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifdef configASSERT
 156:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	void vPortValidateInterruptPriority( void );
 157:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portASSERT_IF_INTERRUPT_PRIORITY_INVALID() 	vPortValidateInterruptPriority()
 158:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 159:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 160:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /* portNOP() is not required by this port. */
 161:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portNOP()
 162:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 163:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #define portINLINE	__inline
 164:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 165:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #ifndef portFORCE_INLINE
 166:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	#define portFORCE_INLINE inline __attribute__(( always_inline))
 167:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** #endif
 168:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 169:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static BaseType_t xPortIsInsideInterrupt( void )
 170:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 171:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulCurrentInterrupt;
 172:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** BaseType_t xReturn;
 173:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 174:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	/* Obtain the number of the currently executing interrupt. */
 175:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile( "mrs %0, ipsr" : "=r"( ulCurrentInterrupt ) :: "memory" );
 176:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 177:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	if( ulCurrentInterrupt == 0 )
 178:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 179:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdFALSE;
 180:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 181:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	else
 182:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	{
 183:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 		xReturn = pdTRUE;
 184:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	}
 185:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 186:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	return xReturn;
 187:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** }
 188:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 189:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** /*-----------------------------------------------------------*/
 190:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** portFORCE_INLINE static void vPortRaiseBASEPRI( void )
 318              		.loc 2 191 30 view .LVU71
 319              	.LBB47:
 192:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** uint32_t ulNewBASEPRI;
 320              		.loc 2 193 1 view .LVU72
 194:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 195:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 	__asm volatile
 321              		.loc 2 195 2 view .LVU73
 322              		.syntax unified
 323              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 324 0008 4FF05003 			mov r3, #80												
 325 000c 83F31188 		msr basepri, r3											
 326 0010 BFF36F8F 		isb														
ARM GAS  /tmp/cc39sGFU.s 			page 28


 327 0014 BFF34F8F 		dsb														
 328              	
 329              	@ 0 "" 2
 330              	.LVL28:
 331              		.thumb
 332              		.syntax unified
 333              	.L26:
 334              		.loc 2 195 2 is_stmt 0 view .LVU74
 335              	.LBE47:
 336              	.LBE46:
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 337              		.loc 1 357 2 is_stmt 1 discriminator 3 view .LVU75
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 338              		.loc 1 357 2 discriminator 3 view .LVU76
 339 0018 FEE7     		b	.L26
 340              	.LVL29:
 341              	.L25:
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 342              		.loc 1 357 2 is_stmt 0 discriminator 3 view .LVU77
 343 001a 0646     		mov	r6, r0
 344 001c 1446     		mov	r4, r2
 345 001e 1D46     		mov	r5, r3
 346 0020 8846     		mov	r8, r1
 357:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 347              		.loc 1 357 45 is_stmt 1 discriminator 2 view .LVU78
 359:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 348              		.loc 1 359 2 view .LVU79
 359:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 349              		.loc 1 359 4 is_stmt 0 view .LVU80
 350 0022 8FB1     		cbz	r7, .L24
 363:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 351              		.loc 1 363 3 is_stmt 1 view .LVU81
 352 0024 FFF7FEFF 		bl	prvCheckForValidListAndQueue
 353              	.LVL30:
 367:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->xTimerPeriodInTicks = xTimerPeriodInTicks;
 354              		.loc 1 367 3 view .LVU82
 367:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->xTimerPeriodInTicks = xTimerPeriodInTicks;
 355              		.loc 1 367 27 is_stmt 0 view .LVU83
 356 0028 3E60     		str	r6, [r7]
 368:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pvTimerID = pvTimerID;
 357              		.loc 1 368 3 is_stmt 1 view .LVU84
 368:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pvTimerID = pvTimerID;
 358              		.loc 1 368 35 is_stmt 0 view .LVU85
 359 002a C7F81880 		str	r8, [r7, #24]
 369:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pxCallbackFunction = pxCallbackFunction;
 360              		.loc 1 369 3 is_stmt 1 view .LVU86
 369:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer->pxCallbackFunction = pxCallbackFunction;
 361              		.loc 1 369 25 is_stmt 0 view .LVU87
 362 002e FD61     		str	r5, [r7, #28]
 370:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		vListInitialiseItem( &( pxNewTimer->xTimerListItem ) );
 363              		.loc 1 370 3 is_stmt 1 view .LVU88
 370:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		vListInitialiseItem( &( pxNewTimer->xTimerListItem ) );
 364              		.loc 1 370 34 is_stmt 0 view .LVU89
 365 0030 069B     		ldr	r3, [sp, #24]
 366 0032 3B62     		str	r3, [r7, #32]
 371:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( uxAutoReload != pdFALSE )
 367              		.loc 1 371 3 is_stmt 1 view .LVU90
ARM GAS  /tmp/cc39sGFU.s 			page 29


 368 0034 381D     		adds	r0, r7, #4
 369 0036 FFF7FEFF 		bl	vListInitialiseItem
 370              	.LVL31:
 372:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 371              		.loc 1 372 3 view .LVU91
 372:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 372              		.loc 1 372 5 is_stmt 0 view .LVU92
 373 003a 2CB1     		cbz	r4, .L24
 374:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 374              		.loc 1 374 4 is_stmt 1 view .LVU93
 374:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 375              		.loc 1 374 14 is_stmt 0 view .LVU94
 376 003c 97F82830 		ldrb	r3, [r7, #40]	@ zero_extendqisi2
 374:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 377              		.loc 1 374 25 view .LVU95
 378 0040 43F00403 		orr	r3, r3, #4
 379 0044 87F82830 		strb	r3, [r7, #40]
 376:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 380              		.loc 1 376 34 is_stmt 1 view .LVU96
 381              	.L24:
 378:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 382              		.loc 1 378 1 is_stmt 0 view .LVU97
 383 0048 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 378:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 384              		.loc 1 378 1 view .LVU98
 385              		.cfi_endproc
 386              	.LFE7:
 388              		.section	.rodata.xTimerCreateTimerTask.str1.4,"aMS",%progbits,1
 389              		.align	2
 390              	.LC1:
 391 0000 546D7220 		.ascii	"Tmr Svc\000"
 391      53766300 
 392              		.section	.text.xTimerCreateTimerTask,"ax",%progbits
 393              		.align	1
 394              		.global	xTimerCreateTimerTask
 395              		.syntax unified
 396              		.thumb
 397              		.thumb_func
 399              	xTimerCreateTimerTask:
 400              	.LFB4:
 228:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn = pdFAIL;
 401              		.loc 1 228 1 is_stmt 1 view -0
 402              		.cfi_startproc
 403              		@ args = 0, pretend = 0, frame = 16
 404              		@ frame_needed = 0, uses_anonymous_args = 0
 405 0000 10B5     		push	{r4, lr}
 406              	.LCFI6:
 407              		.cfi_def_cfa_offset 8
 408              		.cfi_offset 4, -8
 409              		.cfi_offset 14, -4
 410 0002 88B0     		sub	sp, sp, #32
 411              	.LCFI7:
 412              		.cfi_def_cfa_offset 40
 229:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 413              		.loc 1 229 1 view .LVU100
 414              	.LVL32:
 235:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
ARM GAS  /tmp/cc39sGFU.s 			page 30


 415              		.loc 1 235 2 view .LVU101
 416 0004 FFF7FEFF 		bl	prvCheckForValidListAndQueue
 417              	.LVL33:
 237:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 418              		.loc 1 237 2 view .LVU102
 237:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 419              		.loc 1 237 18 is_stmt 0 view .LVU103
 420 0008 124B     		ldr	r3, .L35
 421 000a 1B68     		ldr	r3, [r3]
 237:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 422              		.loc 1 237 4 view .LVU104
 423 000c CBB1     		cbz	r3, .L30
 424              	.LBB48:
 241:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			StackType_t *pxTimerTaskStackBuffer = NULL;
 425              		.loc 1 241 4 is_stmt 1 view .LVU105
 241:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			StackType_t *pxTimerTaskStackBuffer = NULL;
 426              		.loc 1 241 18 is_stmt 0 view .LVU106
 427 000e 0024     		movs	r4, #0
 428 0010 0594     		str	r4, [sp, #20]
 242:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			uint32_t ulTimerTaskStackSize;
 429              		.loc 1 242 4 is_stmt 1 view .LVU107
 242:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			uint32_t ulTimerTaskStackSize;
 430              		.loc 1 242 17 is_stmt 0 view .LVU108
 431 0012 0694     		str	r4, [sp, #24]
 243:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 432              		.loc 1 243 4 is_stmt 1 view .LVU109
 245:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xTimerTaskHandle = xTaskCreateStatic(	prvTimerTask,
 433              		.loc 1 245 4 view .LVU110
 434 0014 07AA     		add	r2, sp, #28
 435 0016 06A9     		add	r1, sp, #24
 436 0018 05A8     		add	r0, sp, #20
 437 001a FFF7FEFF 		bl	vApplicationGetTimerTaskMemory
 438              	.LVL34:
 246:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													configTIMER_SERVICE_TASK_NAME,
 439              		.loc 1 246 4 view .LVU111
 246:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													configTIMER_SERVICE_TASK_NAME,
 440              		.loc 1 246 23 is_stmt 0 view .LVU112
 441 001e 059B     		ldr	r3, [sp, #20]
 442 0020 0293     		str	r3, [sp, #8]
 443 0022 069B     		ldr	r3, [sp, #24]
 444 0024 0193     		str	r3, [sp, #4]
 445 0026 0223     		movs	r3, #2
 446 0028 0093     		str	r3, [sp]
 447 002a 2346     		mov	r3, r4
 448 002c 079A     		ldr	r2, [sp, #28]
 449 002e 0A49     		ldr	r1, .L35+4
 450 0030 0A48     		ldr	r0, .L35+8
 451 0032 FFF7FEFF 		bl	xTaskCreateStatic
 452              	.LVL35:
 246:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 													configTIMER_SERVICE_TASK_NAME,
 453              		.loc 1 246 21 discriminator 1 view .LVU113
 454 0036 0A4B     		ldr	r3, .L35+12
 455 0038 1860     		str	r0, [r3]
 254:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 456              		.loc 1 254 4 is_stmt 1 view .LVU114
 254:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 457              		.loc 1 254 6 is_stmt 0 view .LVU115
ARM GAS  /tmp/cc39sGFU.s 			page 31


 458 003a 10B1     		cbz	r0, .L30
 459              	.LBE48:
 277:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 460              		.loc 1 277 1 view .LVU116
 461 003c 0120     		movs	r0, #1
 462 003e 08B0     		add	sp, sp, #32
 463              	.LCFI8:
 464              		.cfi_remember_state
 465              		.cfi_def_cfa_offset 8
 466              		@ sp needed
 467 0040 10BD     		pop	{r4, pc}
 468              	.L30:
 469              	.LCFI9:
 470              		.cfi_restore_state
 471              	.LVL36:
 275:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 472              		.loc 1 275 2 is_stmt 1 discriminator 1 view .LVU117
 473              	.LBB49:
 474              	.LBI49:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 475              		.loc 2 191 30 view .LVU118
 476              	.LBB50:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 477              		.loc 2 193 1 view .LVU119
 478              		.loc 2 195 2 view .LVU120
 479              		.syntax unified
 480              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 481 0042 4FF05003 			mov r3, #80												
 482 0046 83F31188 		msr basepri, r3											
 483 004a BFF36F8F 		isb														
 484 004e BFF34F8F 		dsb														
 485              	
 486              	@ 0 "" 2
 487              		.thumb
 488              		.syntax unified
 489              	.L32:
 490              	.LBE50:
 491              	.LBE49:
 275:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 492              		.loc 1 275 2 discriminator 3 view .LVU121
 275:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 493              		.loc 1 275 2 discriminator 3 view .LVU122
 494 0052 FEE7     		b	.L32
 495              	.L36:
 496              		.align	2
 497              	.L35:
 498 0054 00000000 		.word	xTimerQueue
 499 0058 00000000 		.word	.LC1
 500 005c 00000000 		.word	prvTimerTask
 501 0060 00000000 		.word	xTimerTaskHandle
 502              		.cfi_endproc
 503              	.LFE4:
 505              		.section	.text.xTimerCreate,"ax",%progbits
 506              		.align	1
 507              		.global	xTimerCreate
 508              		.syntax unified
 509              		.thumb
ARM GAS  /tmp/cc39sGFU.s 			page 32


 510              		.thumb_func
 512              	xTimerCreate:
 513              	.LVL37:
 514              	.LFB5:
 287:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *pxNewTimer;
 515              		.loc 1 287 2 view -0
 516              		.cfi_startproc
 517              		@ args = 4, pretend = 0, frame = 0
 518              		@ frame_needed = 0, uses_anonymous_args = 0
 287:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *pxNewTimer;
 519              		.loc 1 287 2 is_stmt 0 view .LVU124
 520 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 521              	.LCFI10:
 522              		.cfi_def_cfa_offset 24
 523              		.cfi_offset 4, -24
 524              		.cfi_offset 5, -20
 525              		.cfi_offset 6, -16
 526              		.cfi_offset 7, -12
 527              		.cfi_offset 8, -8
 528              		.cfi_offset 14, -4
 529 0004 82B0     		sub	sp, sp, #8
 530              	.LCFI11:
 531              		.cfi_def_cfa_offset 32
 532 0006 0446     		mov	r4, r0
 533 0008 8846     		mov	r8, r1
 534 000a 1746     		mov	r7, r2
 535 000c 1D46     		mov	r5, r3
 288:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 536              		.loc 1 288 2 is_stmt 1 view .LVU125
 290:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 537              		.loc 1 290 3 view .LVU126
 290:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 538              		.loc 1 290 30 is_stmt 0 view .LVU127
 539 000e 2C20     		movs	r0, #44
 540              	.LVL38:
 290:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 541              		.loc 1 290 30 view .LVU128
 542 0010 FFF7FEFF 		bl	pvPortMalloc
 543              	.LVL39:
 292:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 544              		.loc 1 292 3 is_stmt 1 view .LVU129
 292:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 545              		.loc 1 292 5 is_stmt 0 view .LVU130
 546 0014 0646     		mov	r6, r0
 547 0016 58B1     		cbz	r0, .L37
 297:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			prvInitialiseNewTimer( pcTimerName, xTimerPeriodInTicks, uxAutoReload, pvTimerID, pxCallbackFunc
 548              		.loc 1 297 4 is_stmt 1 view .LVU131
 297:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			prvInitialiseNewTimer( pcTimerName, xTimerPeriodInTicks, uxAutoReload, pvTimerID, pxCallbackFunc
 549              		.loc 1 297 25 is_stmt 0 view .LVU132
 550 0018 0023     		movs	r3, #0
 551 001a 80F82830 		strb	r3, [r0, #40]
 298:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 552              		.loc 1 298 4 is_stmt 1 view .LVU133
 553 001e 0190     		str	r0, [sp, #4]
 554 0020 089B     		ldr	r3, [sp, #32]
 555 0022 0093     		str	r3, [sp]
 556 0024 2B46     		mov	r3, r5
ARM GAS  /tmp/cc39sGFU.s 			page 33


 557 0026 3A46     		mov	r2, r7
 558 0028 4146     		mov	r1, r8
 559 002a 2046     		mov	r0, r4
 560              	.LVL40:
 298:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 561              		.loc 1 298 4 is_stmt 0 view .LVU134
 562 002c FFF7FEFF 		bl	prvInitialiseNewTimer
 563              	.LVL41:
 301:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 564              		.loc 1 301 3 is_stmt 1 view .LVU135
 565              	.L37:
 302:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 566              		.loc 1 302 2 is_stmt 0 view .LVU136
 567 0030 3046     		mov	r0, r6
 568 0032 02B0     		add	sp, sp, #8
 569              	.LCFI12:
 570              		.cfi_def_cfa_offset 24
 571              		@ sp needed
 572 0034 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 302:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 573              		.loc 1 302 2 view .LVU137
 574              		.cfi_endproc
 575              	.LFE5:
 577              		.section	.text.xTimerCreateStatic,"ax",%progbits
 578              		.align	1
 579              		.global	xTimerCreateStatic
 580              		.syntax unified
 581              		.thumb
 582              		.thumb_func
 584              	xTimerCreateStatic:
 585              	.LVL42:
 586              	.LFB6:
 315:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *pxNewTimer;
 587              		.loc 1 315 2 is_stmt 1 view -0
 588              		.cfi_startproc
 589              		@ args = 8, pretend = 0, frame = 8
 590              		@ frame_needed = 0, uses_anonymous_args = 0
 315:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	Timer_t *pxNewTimer;
 591              		.loc 1 315 2 is_stmt 0 view .LVU139
 592 0000 10B5     		push	{r4, lr}
 593              	.LCFI13:
 594              		.cfi_def_cfa_offset 8
 595              		.cfi_offset 4, -8
 596              		.cfi_offset 14, -4
 597 0002 84B0     		sub	sp, sp, #16
 598              	.LCFI14:
 599              		.cfi_def_cfa_offset 24
 600 0004 8446     		mov	ip, r0
 316:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 601              		.loc 1 316 2 is_stmt 1 view .LVU140
 602              	.LBB51:
 323:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xSize == sizeof( Timer_t ) );
 603              		.loc 1 323 4 view .LVU141
 323:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xSize == sizeof( Timer_t ) );
 604              		.loc 1 323 20 is_stmt 0 view .LVU142
 605 0006 2C20     		movs	r0, #44
 606              	.LVL43:
ARM GAS  /tmp/cc39sGFU.s 			page 34


 323:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xSize == sizeof( Timer_t ) );
 607              		.loc 1 323 20 view .LVU143
 608 0008 0390     		str	r0, [sp, #12]
 324:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 609              		.loc 1 324 4 is_stmt 1 view .LVU144
 610 000a 0398     		ldr	r0, [sp, #12]
 611 000c 2C28     		cmp	r0, #44
 612 000e 08D0     		beq	.L41
 324:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 613              		.loc 1 324 4 discriminator 1 view .LVU145
 614              	.LBB52:
 615              	.LBI52:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 616              		.loc 2 191 30 view .LVU146
 617              	.LBB53:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 618              		.loc 2 193 1 view .LVU147
 619              		.loc 2 195 2 view .LVU148
 620              		.syntax unified
 621              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 622 0010 4FF05003 			mov r3, #80												
 623 0014 83F31188 		msr basepri, r3											
 624 0018 BFF36F8F 		isb														
 625 001c BFF34F8F 		dsb														
 626              	
 627              	@ 0 "" 2
 628              	.LVL44:
 629              		.thumb
 630              		.syntax unified
 631              	.L42:
 632              		.loc 2 195 2 is_stmt 0 view .LVU149
 633              	.LBE53:
 634              	.LBE52:
 324:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 635              		.loc 1 324 4 is_stmt 1 discriminator 3 view .LVU150
 324:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 636              		.loc 1 324 4 discriminator 3 view .LVU151
 637 0020 FEE7     		b	.L42
 638              	.LVL45:
 639              	.L41:
 324:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 640              		.loc 1 324 46 discriminator 2 view .LVU152
 325:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 641              		.loc 1 325 4 view .LVU153
 642 0022 0398     		ldr	r0, [sp, #12]
 643              	.LBE51:
 330:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pxTimerBuffer; /*lint !e740 !e9087 StaticTimer_t is a pointer to a Tim
 644              		.loc 1 330 3 view .LVU154
 645 0024 0798     		ldr	r0, [sp, #28]
 646 0026 60B1     		cbz	r0, .L46
 330:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pxTimerBuffer; /*lint !e740 !e9087 StaticTimer_t is a pointer to a Tim
 647              		.loc 1 330 32 discriminator 2 view .LVU155
 331:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 648              		.loc 1 331 3 view .LVU156
 649              	.LVL46:
 333:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 650              		.loc 1 333 3 view .LVU157
ARM GAS  /tmp/cc39sGFU.s 			page 35


 338:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 651              		.loc 1 338 4 view .LVU158
 338:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 652              		.loc 1 338 25 is_stmt 0 view .LVU159
 653 0028 0220     		movs	r0, #2
 654              	.LVL47:
 338:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 655              		.loc 1 338 25 view .LVU160
 656 002a 079C     		ldr	r4, [sp, #28]
 657 002c 84F82800 		strb	r0, [r4, #40]
 340:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 658              		.loc 1 340 4 is_stmt 1 view .LVU161
 659 0030 0194     		str	r4, [sp, #4]
 660 0032 0698     		ldr	r0, [sp, #24]
 661 0034 0090     		str	r0, [sp]
 662 0036 6046     		mov	r0, ip
 663 0038 FFF7FEFF 		bl	prvInitialiseNewTimer
 664              	.LVL48:
 343:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 665              		.loc 1 343 3 view .LVU162
 344:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 666              		.loc 1 344 2 is_stmt 0 view .LVU163
 667 003c 0798     		ldr	r0, [sp, #28]
 668 003e 04B0     		add	sp, sp, #16
 669              	.LCFI15:
 670              		.cfi_remember_state
 671              		.cfi_def_cfa_offset 8
 672              		@ sp needed
 673 0040 10BD     		pop	{r4, pc}
 674              	.LVL49:
 675              	.L46:
 676              	.LCFI16:
 677              		.cfi_restore_state
 330:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pxTimerBuffer; /*lint !e740 !e9087 StaticTimer_t is a pointer to a Tim
 678              		.loc 1 330 3 is_stmt 1 discriminator 1 view .LVU164
 679              	.LBB54:
 680              	.LBI54:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 681              		.loc 2 191 30 view .LVU165
 682              	.LBB55:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 683              		.loc 2 193 1 view .LVU166
 684              		.loc 2 195 2 view .LVU167
 685              		.syntax unified
 686              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 687 0042 4FF05003 			mov r3, #80												
 688 0046 83F31188 		msr basepri, r3											
 689 004a BFF36F8F 		isb														
 690 004e BFF34F8F 		dsb														
 691              	
 692              	@ 0 "" 2
 693              	.LVL50:
 694              		.thumb
 695              		.syntax unified
 696              	.L44:
 697              		.loc 2 195 2 is_stmt 0 view .LVU168
 698              	.LBE55:
ARM GAS  /tmp/cc39sGFU.s 			page 36


 699              	.LBE54:
 330:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pxTimerBuffer; /*lint !e740 !e9087 StaticTimer_t is a pointer to a Tim
 700              		.loc 1 330 3 is_stmt 1 discriminator 3 view .LVU169
 330:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxNewTimer = ( Timer_t * ) pxTimerBuffer; /*lint !e740 !e9087 StaticTimer_t is a pointer to a Tim
 701              		.loc 1 330 3 discriminator 3 view .LVU170
 702 0052 FEE7     		b	.L44
 703              		.cfi_endproc
 704              	.LFE6:
 706              		.section	.text.xTimerGenericCommand,"ax",%progbits
 707              		.align	1
 708              		.global	xTimerGenericCommand
 709              		.syntax unified
 710              		.thumb
 711              		.thumb_func
 713              	xTimerGenericCommand:
 714              	.LVL51:
 715              	.LFB8:
 382:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn = pdFAIL;
 716              		.loc 1 382 1 view -0
 717              		.cfi_startproc
 718              		@ args = 4, pretend = 0, frame = 16
 719              		@ frame_needed = 0, uses_anonymous_args = 0
 383:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** DaemonTaskMessage_t xMessage;
 720              		.loc 1 383 1 view .LVU172
 384:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 721              		.loc 1 384 1 view .LVU173
 386:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 722              		.loc 1 386 2 view .LVU174
 723 0000 B8B1     		cbz	r0, .L58
 724 0002 9C46     		mov	ip, r3
 725 0004 0346     		mov	r3, r0
 726              	.LVL52:
 386:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 727              		.loc 1 386 24 discriminator 2 view .LVU175
 390:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 728              		.loc 1 390 2 view .LVU176
 390:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 729              		.loc 1 390 18 is_stmt 0 view .LVU177
 730 0006 1848     		ldr	r0, .L60
 731              	.LVL53:
 390:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 732              		.loc 1 390 18 view .LVU178
 733 0008 0068     		ldr	r0, [r0]
 390:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 734              		.loc 1 390 4 view .LVU179
 735 000a 58B3     		cbz	r0, .L53
 382:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn = pdFAIL;
 736              		.loc 1 382 1 view .LVU180
 737 000c 00B5     		push	{lr}
 738              	.LCFI17:
 739              		.cfi_def_cfa_offset 4
 740              		.cfi_offset 14, -4
 741 000e 85B0     		sub	sp, sp, #20
 742              	.LCFI18:
 743              		.cfi_def_cfa_offset 24
 393:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xTimerParameters.xMessageValue = xOptionalValue;
 744              		.loc 1 393 3 is_stmt 1 view .LVU181
ARM GAS  /tmp/cc39sGFU.s 			page 37


 393:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xTimerParameters.xMessageValue = xOptionalValue;
 745              		.loc 1 393 23 is_stmt 0 view .LVU182
 746 0010 0091     		str	r1, [sp]
 394:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xTimerParameters.pxTimer = xTimer;
 747              		.loc 1 394 3 is_stmt 1 view .LVU183
 394:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xTimerParameters.pxTimer = xTimer;
 748              		.loc 1 394 45 is_stmt 0 view .LVU184
 749 0012 0192     		str	r2, [sp, #4]
 395:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 750              		.loc 1 395 3 is_stmt 1 view .LVU185
 395:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 751              		.loc 1 395 39 is_stmt 0 view .LVU186
 752 0014 0293     		str	r3, [sp, #8]
 397:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 753              		.loc 1 397 3 is_stmt 1 view .LVU187
 397:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 754              		.loc 1 397 5 is_stmt 0 view .LVU188
 755 0016 0529     		cmp	r1, #5
 756 0018 1CDC     		bgt	.L51
 399:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 757              		.loc 1 399 4 is_stmt 1 view .LVU189
 399:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 758              		.loc 1 399 8 is_stmt 0 view .LVU190
 759 001a FFF7FEFF 		bl	xTaskGetSchedulerState
 760              	.LVL54:
 399:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 761              		.loc 1 399 6 discriminator 1 view .LVU191
 762 001e 0228     		cmp	r0, #2
 763 0020 10D0     		beq	.L59
 405:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 764              		.loc 1 405 5 is_stmt 1 view .LVU192
 405:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 765              		.loc 1 405 15 is_stmt 0 view .LVU193
 766 0022 0023     		movs	r3, #0
 767 0024 1A46     		mov	r2, r3
 768 0026 6946     		mov	r1, sp
 769 0028 0F48     		ldr	r0, .L60
 770 002a 0068     		ldr	r0, [r0]
 771 002c FFF7FEFF 		bl	xQueueGenericSend
 772              	.LVL55:
 405:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 773              		.loc 1 405 15 view .LVU194
 774 0030 15E0     		b	.L47
 775              	.LVL56:
 776              	.L58:
 777              	.LCFI19:
 778              		.cfi_def_cfa_offset 0
 779              		.cfi_restore 14
 386:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 780              		.loc 1 386 2 is_stmt 1 discriminator 1 view .LVU195
 781              	.LBB56:
 782              	.LBI56:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 783              		.loc 2 191 30 view .LVU196
 784              	.LBB57:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 785              		.loc 2 193 1 view .LVU197
ARM GAS  /tmp/cc39sGFU.s 			page 38


 786              		.loc 2 195 2 view .LVU198
 787              		.syntax unified
 788              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 789 0032 4FF05003 			mov r3, #80												
 790 0036 83F31188 		msr basepri, r3											
 791 003a BFF36F8F 		isb														
 792 003e BFF34F8F 		dsb														
 793              	
 794              	@ 0 "" 2
 795              	.LVL57:
 796              		.thumb
 797              		.syntax unified
 798              	.L49:
 799              		.loc 2 195 2 is_stmt 0 view .LVU199
 800              	.LBE57:
 801              	.LBE56:
 386:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 802              		.loc 1 386 2 is_stmt 1 discriminator 3 view .LVU200
 386:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 803              		.loc 1 386 2 discriminator 3 view .LVU201
 804 0042 FEE7     		b	.L49
 805              	.LVL58:
 806              	.L59:
 807              	.LCFI20:
 808              		.cfi_def_cfa_offset 24
 809              		.cfi_offset 14, -4
 401:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 810              		.loc 1 401 5 view .LVU202
 401:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 811              		.loc 1 401 15 is_stmt 0 view .LVU203
 812 0044 0023     		movs	r3, #0
 813 0046 069A     		ldr	r2, [sp, #24]
 814 0048 6946     		mov	r1, sp
 815 004a 0748     		ldr	r0, .L60
 816 004c 0068     		ldr	r0, [r0]
 817 004e FFF7FEFF 		bl	xQueueGenericSend
 818              	.LVL59:
 401:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 819              		.loc 1 401 15 view .LVU204
 820 0052 04E0     		b	.L47
 821              	.LVL60:
 822              	.L51:
 410:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 823              		.loc 1 410 4 is_stmt 1 view .LVU205
 410:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 824              		.loc 1 410 14 is_stmt 0 view .LVU206
 825 0054 0023     		movs	r3, #0
 826              	.LVL61:
 410:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 827              		.loc 1 410 14 view .LVU207
 828 0056 6246     		mov	r2, ip
 829              	.LVL62:
 410:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 830              		.loc 1 410 14 view .LVU208
 831 0058 6946     		mov	r1, sp
 832              	.LVL63:
 410:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
ARM GAS  /tmp/cc39sGFU.s 			page 39


 833              		.loc 1 410 14 view .LVU209
 834 005a FFF7FEFF 		bl	xQueueGenericSendFromISR
 835              	.LVL64:
 836              	.L47:
 421:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 837              		.loc 1 421 1 view .LVU210
 838 005e 05B0     		add	sp, sp, #20
 839              	.LCFI21:
 840              		.cfi_def_cfa_offset 4
 841              		@ sp needed
 842 0060 5DF804FB 		ldr	pc, [sp], #4
 843              	.LVL65:
 844              	.L53:
 845              	.LCFI22:
 846              		.cfi_def_cfa_offset 0
 847              		.cfi_restore 14
 383:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** DaemonTaskMessage_t xMessage;
 848              		.loc 1 383 12 view .LVU211
 849 0064 0020     		movs	r0, #0
 417:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 850              		.loc 1 417 27 is_stmt 1 view .LVU212
 420:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 851              		.loc 1 420 2 view .LVU213
 421:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 852              		.loc 1 421 1 is_stmt 0 view .LVU214
 853 0066 7047     		bx	lr
 854              	.L61:
 855              		.align	2
 856              	.L60:
 857 0068 00000000 		.word	xTimerQueue
 858              		.cfi_endproc
 859              	.LFE8:
 861              		.section	.text.prvSwitchTimerLists,"ax",%progbits
 862              		.align	1
 863              		.syntax unified
 864              		.thumb
 865              		.thumb_func
 867              	prvSwitchTimerLists:
 868              	.LFB22:
 883:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xNextExpireTime, xReloadTime;
 869              		.loc 1 883 1 is_stmt 1 view -0
 870              		.cfi_startproc
 871              		@ args = 0, pretend = 0, frame = 0
 872              		@ frame_needed = 0, uses_anonymous_args = 0
 873 0000 70B5     		push	{r4, r5, r6, lr}
 874              	.LCFI23:
 875              		.cfi_def_cfa_offset 16
 876              		.cfi_offset 4, -16
 877              		.cfi_offset 5, -12
 878              		.cfi_offset 6, -8
 879              		.cfi_offset 14, -4
 880 0002 82B0     		sub	sp, sp, #8
 881              	.LCFI24:
 882              		.cfi_def_cfa_offset 24
 884:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** List_t *pxTemp;
 883              		.loc 1 884 1 view .LVU216
 885:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer;
ARM GAS  /tmp/cc39sGFU.s 			page 40


 884              		.loc 1 885 1 view .LVU217
 886:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xResult;
 885              		.loc 1 886 1 view .LVU218
 887:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 886              		.loc 1 887 1 view .LVU219
 893:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 887              		.loc 1 893 2 view .LVU220
 888              	.L64:
 931:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 889              		.loc 1 931 28 view .LVU221
 893:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 890              		.loc 1 893 49 view .LVU222
 893:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 891              		.loc 1 893 9 is_stmt 0 view .LVU223
 892 0004 1B4B     		ldr	r3, .L71
 893 0006 1B68     		ldr	r3, [r3]
 894 0008 1A68     		ldr	r2, [r3]
 893:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 895              		.loc 1 893 49 view .LVU224
 896 000a 62B3     		cbz	r2, .L70
 895:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 897              		.loc 1 895 3 is_stmt 1 view .LVU225
 895:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 898              		.loc 1 895 21 is_stmt 0 view .LVU226
 899 000c DB68     		ldr	r3, [r3, #12]
 895:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 900              		.loc 1 895 19 view .LVU227
 901 000e 1E68     		ldr	r6, [r3]
 902              	.LVL66:
 898:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 903              		.loc 1 898 3 is_stmt 1 view .LVU228
 898:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 904              		.loc 1 898 11 is_stmt 0 view .LVU229
 905 0010 DC68     		ldr	r4, [r3, #12]
 906              	.LVL67:
 899:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		traceTIMER_EXPIRED( pxTimer );
 907              		.loc 1 899 3 is_stmt 1 view .LVU230
 899:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		traceTIMER_EXPIRED( pxTimer );
 908              		.loc 1 899 12 is_stmt 0 view .LVU231
 909 0012 251D     		adds	r5, r4, #4
 910 0014 2846     		mov	r0, r5
 911 0016 FFF7FEFF 		bl	uxListRemove
 912              	.LVL68:
 900:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 913              		.loc 1 900 32 is_stmt 1 view .LVU232
 905:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 914              		.loc 1 905 3 view .LVU233
 905:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 915              		.loc 1 905 10 is_stmt 0 view .LVU234
 916 001a 236A     		ldr	r3, [r4, #32]
 905:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 917              		.loc 1 905 3 view .LVU235
 918 001c 2046     		mov	r0, r4
 919 001e 9847     		blx	r3
 920              	.LVL69:
 907:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 921              		.loc 1 907 3 is_stmt 1 view .LVU236
ARM GAS  /tmp/cc39sGFU.s 			page 41


 907:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 922              		.loc 1 907 16 is_stmt 0 view .LVU237
 923 0020 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
 907:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 924              		.loc 1 907 5 view .LVU238
 925 0024 13F0040F 		tst	r3, #4
 926 0028 ECD0     		beq	.L64
 915:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xReloadTime > xNextExpireTime )
 927              		.loc 1 915 4 is_stmt 1 view .LVU239
 915:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xReloadTime > xNextExpireTime )
 928              		.loc 1 915 45 is_stmt 0 view .LVU240
 929 002a A369     		ldr	r3, [r4, #24]
 915:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			if( xReloadTime > xNextExpireTime )
 930              		.loc 1 915 16 view .LVU241
 931 002c 3344     		add	r3, r3, r6
 932              	.LVL70:
 916:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 933              		.loc 1 916 4 is_stmt 1 view .LVU242
 916:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 934              		.loc 1 916 6 is_stmt 0 view .LVU243
 935 002e 9E42     		cmp	r6, r3
 936 0030 07D2     		bcs	.L65
 918:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
 937              		.loc 1 918 5 is_stmt 1 view .LVU244
 938 0032 6360     		str	r3, [r4, #4]
 919:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 939              		.loc 1 919 5 view .LVU245
 940 0034 2461     		str	r4, [r4, #16]
 920:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 941              		.loc 1 920 5 view .LVU246
 942 0036 2946     		mov	r1, r5
 943 0038 0E4B     		ldr	r3, .L71
 944              	.LVL71:
 920:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 945              		.loc 1 920 5 is_stmt 0 view .LVU247
 946 003a 1868     		ldr	r0, [r3]
 947 003c FFF7FEFF 		bl	vListInsert
 948              	.LVL72:
 920:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 949              		.loc 1 920 5 view .LVU248
 950 0040 E0E7     		b	.L64
 951              	.LVL73:
 952              	.L65:
 924:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				configASSERT( xResult );
 953              		.loc 1 924 5 is_stmt 1 view .LVU249
 924:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				configASSERT( xResult );
 954              		.loc 1 924 15 is_stmt 0 view .LVU250
 955 0042 0021     		movs	r1, #0
 956 0044 0091     		str	r1, [sp]
 957 0046 0B46     		mov	r3, r1
 958              	.LVL74:
 924:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				configASSERT( xResult );
 959              		.loc 1 924 15 view .LVU251
 960 0048 3246     		mov	r2, r6
 961 004a 2046     		mov	r0, r4
 962 004c FFF7FEFF 		bl	xTimerGenericCommand
 963              	.LVL75:
ARM GAS  /tmp/cc39sGFU.s 			page 42


 925:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) xResult;
 964              		.loc 1 925 5 is_stmt 1 view .LVU252
 965 0050 0028     		cmp	r0, #0
 966 0052 D7D1     		bne	.L64
 925:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) xResult;
 967              		.loc 1 925 5 discriminator 1 view .LVU253
 968              	.LBB58:
 969              	.LBI58:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 970              		.loc 2 191 30 view .LVU254
 971              	.LBB59:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 972              		.loc 2 193 1 view .LVU255
 973              		.loc 2 195 2 view .LVU256
 974              		.syntax unified
 975              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 976 0054 4FF05003 			mov r3, #80												
 977 0058 83F31188 		msr basepri, r3											
 978 005c BFF36F8F 		isb														
 979 0060 BFF34F8F 		dsb														
 980              	
 981              	@ 0 "" 2
 982              		.thumb
 983              		.syntax unified
 984              	.L67:
 985              	.LBE59:
 986              	.LBE58:
 925:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) xResult;
 987              		.loc 1 925 5 discriminator 3 view .LVU257
 925:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				( void ) xResult;
 988              		.loc 1 925 5 discriminator 3 view .LVU258
 989 0064 FEE7     		b	.L67
 990              	.LVL76:
 991              	.L70:
 935:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxCurrentTimerList = pxOverflowTimerList;
 992              		.loc 1 935 2 view .LVU259
 936:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxOverflowTimerList = pxTemp;
 993              		.loc 1 936 2 view .LVU260
 936:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	pxOverflowTimerList = pxTemp;
 994              		.loc 1 936 21 is_stmt 0 view .LVU261
 995 0066 044A     		ldr	r2, .L71+4
 996 0068 1068     		ldr	r0, [r2]
 997 006a 0249     		ldr	r1, .L71
 998 006c 0860     		str	r0, [r1]
 937:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 999              		.loc 1 937 2 is_stmt 1 view .LVU262
 937:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1000              		.loc 1 937 22 is_stmt 0 view .LVU263
 1001 006e 1360     		str	r3, [r2]
 938:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1002              		.loc 1 938 1 view .LVU264
 1003 0070 02B0     		add	sp, sp, #8
 1004              	.LCFI25:
 1005              		.cfi_def_cfa_offset 16
 1006              		@ sp needed
 1007 0072 70BD     		pop	{r4, r5, r6, pc}
 1008              	.L72:
ARM GAS  /tmp/cc39sGFU.s 			page 43


 1009              		.align	2
 1010              	.L71:
 1011 0074 00000000 		.word	pxCurrentTimerList
 1012 0078 00000000 		.word	pxOverflowTimerList
 1013              		.cfi_endproc
 1014              	.LFE22:
 1016              		.section	.text.prvSampleTimeNow,"ax",%progbits
 1017              		.align	1
 1018              		.syntax unified
 1019              		.thumb
 1020              		.thumb_func
 1022              	prvSampleTimeNow:
 1023              	.LVL77:
 1024              	.LFB19:
 671:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 1025              		.loc 1 671 1 is_stmt 1 view -0
 1026              		.cfi_startproc
 1027              		@ args = 0, pretend = 0, frame = 0
 1028              		@ frame_needed = 0, uses_anonymous_args = 0
 671:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 1029              		.loc 1 671 1 is_stmt 0 view .LVU266
 1030 0000 38B5     		push	{r3, r4, r5, lr}
 1031              	.LCFI26:
 1032              		.cfi_def_cfa_offset 16
 1033              		.cfi_offset 3, -16
 1034              		.cfi_offset 4, -12
 1035              		.cfi_offset 5, -8
 1036              		.cfi_offset 14, -4
 1037 0002 0546     		mov	r5, r0
 672:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** PRIVILEGED_DATA static TickType_t xLastTime = ( TickType_t ) 0U; /*lint !e956 Variable is only acce
 1038              		.loc 1 672 1 is_stmt 1 view .LVU267
 673:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1039              		.loc 1 673 17 view .LVU268
 675:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1040              		.loc 1 675 2 view .LVU269
 675:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1041              		.loc 1 675 13 is_stmt 0 view .LVU270
 1042 0004 FFF7FEFF 		bl	xTaskGetTickCount
 1043              	.LVL78:
 675:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1044              		.loc 1 675 13 view .LVU271
 1045 0008 0446     		mov	r4, r0
 1046              	.LVL79:
 677:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1047              		.loc 1 677 2 is_stmt 1 view .LVU272
 677:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1048              		.loc 1 677 15 is_stmt 0 view .LVU273
 1049 000a 074B     		ldr	r3, .L78
 1050 000c 1B68     		ldr	r3, [r3]
 677:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1051              		.loc 1 677 4 view .LVU274
 1052 000e 8342     		cmp	r3, r0
 1053 0010 05D8     		bhi	.L77
 684:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 1054              		.loc 1 684 3 is_stmt 1 view .LVU275
 684:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 1055              		.loc 1 684 29 is_stmt 0 view .LVU276
ARM GAS  /tmp/cc39sGFU.s 			page 44


 1056 0012 0023     		movs	r3, #0
 1057 0014 2B60     		str	r3, [r5]
 1058              	.LVL80:
 1059              	.L75:
 687:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1060              		.loc 1 687 2 is_stmt 1 view .LVU277
 687:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1061              		.loc 1 687 12 is_stmt 0 view .LVU278
 1062 0016 044B     		ldr	r3, .L78
 1063 0018 1C60     		str	r4, [r3]
 689:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1064              		.loc 1 689 2 is_stmt 1 view .LVU279
 690:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1065              		.loc 1 690 1 is_stmt 0 view .LVU280
 1066 001a 2046     		mov	r0, r4
 1067 001c 38BD     		pop	{r3, r4, r5, pc}
 1068              	.LVL81:
 1069              	.L77:
 679:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		*pxTimerListsWereSwitched = pdTRUE;
 1070              		.loc 1 679 3 is_stmt 1 view .LVU281
 1071 001e FFF7FEFF 		bl	prvSwitchTimerLists
 1072              	.LVL82:
 680:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 1073              		.loc 1 680 3 view .LVU282
 680:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 1074              		.loc 1 680 29 is_stmt 0 view .LVU283
 1075 0022 0123     		movs	r3, #1
 1076 0024 2B60     		str	r3, [r5]
 1077 0026 F6E7     		b	.L75
 1078              	.L79:
 1079              		.align	2
 1080              	.L78:
 1081 0028 00000000 		.word	xLastTime.2
 1082              		.cfi_endproc
 1083              	.LFE19:
 1085              		.section	.text.prvProcessExpiredTimer,"ax",%progbits
 1086              		.align	1
 1087              		.syntax unified
 1088              		.thumb
 1089              		.thumb_func
 1091              	prvProcessExpiredTimer:
 1092              	.LVL83:
 1093              	.LFB15:
 508:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xResult;
 1094              		.loc 1 508 1 is_stmt 1 view -0
 1095              		.cfi_startproc
 1096              		@ args = 0, pretend = 0, frame = 0
 1097              		@ frame_needed = 0, uses_anonymous_args = 0
 508:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xResult;
 1098              		.loc 1 508 1 is_stmt 0 view .LVU285
 1099 0000 70B5     		push	{r4, r5, r6, lr}
 1100              	.LCFI27:
 1101              		.cfi_def_cfa_offset 16
 1102              		.cfi_offset 4, -16
 1103              		.cfi_offset 5, -12
 1104              		.cfi_offset 6, -8
 1105              		.cfi_offset 14, -4
ARM GAS  /tmp/cc39sGFU.s 			page 45


 1106 0002 82B0     		sub	sp, sp, #8
 1107              	.LCFI28:
 1108              		.cfi_def_cfa_offset 24
 1109 0004 0646     		mov	r6, r0
 1110 0006 0D46     		mov	r5, r1
 509:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * const pxTimer = ( Timer_t * ) listGET_OWNER_OF_HEAD_ENTRY( pxCurrentTimerList ); /*lint !
 1111              		.loc 1 509 1 is_stmt 1 view .LVU286
 510:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1112              		.loc 1 510 1 view .LVU287
 510:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1113              		.loc 1 510 41 is_stmt 0 view .LVU288
 1114 0008 1749     		ldr	r1, .L86
 1115              	.LVL84:
 510:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1116              		.loc 1 510 41 view .LVU289
 1117 000a 0968     		ldr	r1, [r1]
 1118 000c C968     		ldr	r1, [r1, #12]
 510:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1119              		.loc 1 510 17 view .LVU290
 1120 000e CC68     		ldr	r4, [r1, #12]
 1121              	.LVL85:
 514:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	traceTIMER_EXPIRED( pxTimer );
 1122              		.loc 1 514 2 is_stmt 1 view .LVU291
 514:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	traceTIMER_EXPIRED( pxTimer );
 1123              		.loc 1 514 11 is_stmt 0 view .LVU292
 1124 0010 201D     		adds	r0, r4, #4
 1125              	.LVL86:
 514:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	traceTIMER_EXPIRED( pxTimer );
 1126              		.loc 1 514 11 view .LVU293
 1127 0012 FFF7FEFF 		bl	uxListRemove
 1128              	.LVL87:
 515:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1129              		.loc 1 515 31 is_stmt 1 view .LVU294
 519:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1130              		.loc 1 519 2 view .LVU295
 519:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1131              		.loc 1 519 15 is_stmt 0 view .LVU296
 1132 0016 94F828C0 		ldrb	ip, [r4, #40]	@ zero_extendqisi2
 519:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1133              		.loc 1 519 4 view .LVU297
 1134 001a 1CF0040F 		tst	ip, #4
 1135 001e 08D1     		bne	.L85
 539:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		mtCOVERAGE_TEST_MARKER();
 1136              		.loc 1 539 3 is_stmt 1 view .LVU298
 539:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		mtCOVERAGE_TEST_MARKER();
 1137              		.loc 1 539 21 is_stmt 0 view .LVU299
 1138 0020 2CF0010C 		bic	ip, ip, #1
 1139 0024 84F828C0 		strb	ip, [r4, #40]
 1140              	.L82:
 540:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 1141              		.loc 1 540 27 is_stmt 1 view .LVU300
 544:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1142              		.loc 1 544 2 view .LVU301
 544:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1143              		.loc 1 544 9 is_stmt 0 view .LVU302
 1144 0028 236A     		ldr	r3, [r4, #32]
 544:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
ARM GAS  /tmp/cc39sGFU.s 			page 46


 1145              		.loc 1 544 2 view .LVU303
 1146 002a 2046     		mov	r0, r4
 1147 002c 9847     		blx	r3
 1148              	.LVL88:
 545:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1149              		.loc 1 545 1 view .LVU304
 1150 002e 02B0     		add	sp, sp, #8
 1151              	.LCFI29:
 1152              		.cfi_remember_state
 1153              		.cfi_def_cfa_offset 16
 1154              		@ sp needed
 1155 0030 70BD     		pop	{r4, r5, r6, pc}
 1156              	.LVL89:
 1157              	.L85:
 1158              	.LCFI30:
 1159              		.cfi_restore_state
 524:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1160              		.loc 1 524 3 is_stmt 1 view .LVU305
 524:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1161              		.loc 1 524 71 is_stmt 0 view .LVU306
 1162 0032 A169     		ldr	r1, [r4, #24]
 524:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1163              		.loc 1 524 7 view .LVU307
 1164 0034 3346     		mov	r3, r6
 1165 0036 2A46     		mov	r2, r5
 1166 0038 3144     		add	r1, r1, r6
 1167 003a 2046     		mov	r0, r4
 1168 003c FFF7FEFF 		bl	prvInsertTimerInActiveList
 1169              	.LVL90:
 524:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1170              		.loc 1 524 5 discriminator 1 view .LVU308
 1171 0040 0028     		cmp	r0, #0
 1172 0042 F1D0     		beq	.L82
 528:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xResult );
 1173              		.loc 1 528 4 is_stmt 1 view .LVU309
 528:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			configASSERT( xResult );
 1174              		.loc 1 528 14 is_stmt 0 view .LVU310
 1175 0044 0021     		movs	r1, #0
 1176 0046 0091     		str	r1, [sp]
 1177 0048 0B46     		mov	r3, r1
 1178 004a 3246     		mov	r2, r6
 1179 004c 2046     		mov	r0, r4
 1180 004e FFF7FEFF 		bl	xTimerGenericCommand
 1181              	.LVL91:
 529:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xResult;
 1182              		.loc 1 529 4 is_stmt 1 view .LVU311
 1183 0052 0028     		cmp	r0, #0
 1184 0054 E8D1     		bne	.L82
 529:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xResult;
 1185              		.loc 1 529 4 discriminator 1 view .LVU312
 1186              	.LBB60:
 1187              	.LBI60:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1188              		.loc 2 191 30 view .LVU313
 1189              	.LBB61:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1190              		.loc 2 193 1 view .LVU314
ARM GAS  /tmp/cc39sGFU.s 			page 47


 1191              		.loc 2 195 2 view .LVU315
 1192              		.syntax unified
 1193              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1194 0056 4FF05003 			mov r3, #80												
 1195 005a 83F31188 		msr basepri, r3											
 1196 005e BFF36F8F 		isb														
 1197 0062 BFF34F8F 		dsb														
 1198              	
 1199              	@ 0 "" 2
 1200              		.thumb
 1201              		.syntax unified
 1202              	.L83:
 1203              	.LBE61:
 1204              	.LBE60:
 529:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xResult;
 1205              		.loc 1 529 4 discriminator 3 view .LVU316
 529:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			( void ) xResult;
 1206              		.loc 1 529 4 discriminator 3 view .LVU317
 1207 0066 FEE7     		b	.L83
 1208              	.L87:
 1209              		.align	2
 1210              	.L86:
 1211 0068 00000000 		.word	pxCurrentTimerList
 1212              		.cfi_endproc
 1213              	.LFE15:
 1215              		.section	.text.prvProcessTimerOrBlockTask,"ax",%progbits
 1216              		.align	1
 1217              		.syntax unified
 1218              		.thumb
 1219              		.thumb_func
 1221              	prvProcessTimerOrBlockTask:
 1222              	.LVL92:
 1223              	.LFB17:
 585:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 1224              		.loc 1 585 1 view -0
 1225              		.cfi_startproc
 1226              		@ args = 0, pretend = 0, frame = 8
 1227              		@ frame_needed = 0, uses_anonymous_args = 0
 585:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 1228              		.loc 1 585 1 is_stmt 0 view .LVU319
 1229 0000 70B5     		push	{r4, r5, r6, lr}
 1230              	.LCFI31:
 1231              		.cfi_def_cfa_offset 16
 1232              		.cfi_offset 4, -16
 1233              		.cfi_offset 5, -12
 1234              		.cfi_offset 6, -8
 1235              		.cfi_offset 14, -4
 1236 0002 82B0     		sub	sp, sp, #8
 1237              	.LCFI32:
 1238              		.cfi_def_cfa_offset 24
 1239 0004 0646     		mov	r6, r0
 1240 0006 0C46     		mov	r4, r1
 586:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerListsWereSwitched;
 1241              		.loc 1 586 1 is_stmt 1 view .LVU320
 587:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1242              		.loc 1 587 1 view .LVU321
 589:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
ARM GAS  /tmp/cc39sGFU.s 			page 48


 1243              		.loc 1 589 2 view .LVU322
 1244 0008 FFF7FEFF 		bl	vTaskSuspendAll
 1245              	.LVL93:
 596:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( xTimerListsWereSwitched == pdFALSE )
 1246              		.loc 1 596 3 view .LVU323
 596:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( xTimerListsWereSwitched == pdFALSE )
 1247              		.loc 1 596 14 is_stmt 0 view .LVU324
 1248 000c 01A8     		add	r0, sp, #4
 1249 000e FFF7FEFF 		bl	prvSampleTimeNow
 1250              	.LVL94:
 597:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1251              		.loc 1 597 3 is_stmt 1 view .LVU325
 597:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1252              		.loc 1 597 31 is_stmt 0 view .LVU326
 1253 0012 019B     		ldr	r3, [sp, #4]
 597:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1254              		.loc 1 597 5 view .LVU327
 1255 0014 3BBB     		cbnz	r3, .L89
 1256 0016 0546     		mov	r5, r0
 600:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1257              		.loc 1 600 4 is_stmt 1 view .LVU328
 600:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1258              		.loc 1 600 6 is_stmt 0 view .LVU329
 1259 0018 B042     		cmp	r0, r6
 1260 001a 28BF     		it	cs
 1261 001c 002C     		cmpcs	r4, #0
 1262 001e 19D0     		beq	.L95
 613:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 1263              		.loc 1 613 5 is_stmt 1 view .LVU330
 613:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 1264              		.loc 1 613 7 is_stmt 0 view .LVU331
 1265 0020 24B1     		cbz	r4, .L92
 617:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1266              		.loc 1 617 6 is_stmt 1 view .LVU332
 617:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1267              		.loc 1 617 22 is_stmt 0 view .LVU333
 1268 0022 134A     		ldr	r2, .L96
 1269 0024 1268     		ldr	r2, [r2]
 1270 0026 1268     		ldr	r2, [r2]
 1271 0028 DAB9     		cbnz	r2, .L93
 617:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1272              		.loc 1 617 22 discriminator 1 view .LVU334
 1273 002a 0124     		movs	r4, #1
 1274              	.LVL95:
 1275              	.L92:
 620:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1276              		.loc 1 620 5 is_stmt 1 view .LVU335
 1277 002c 2246     		mov	r2, r4
 1278 002e 711B     		subs	r1, r6, r5
 1279 0030 104B     		ldr	r3, .L96+4
 1280 0032 1868     		ldr	r0, [r3]
 1281              	.LVL96:
 620:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1282              		.loc 1 620 5 is_stmt 0 view .LVU336
 1283 0034 FFF7FEFF 		bl	vQueueWaitForMessageRestricted
 1284              	.LVL97:
 622:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
ARM GAS  /tmp/cc39sGFU.s 			page 49


 1285              		.loc 1 622 5 is_stmt 1 view .LVU337
 622:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 1286              		.loc 1 622 9 is_stmt 0 view .LVU338
 1287 0038 FFF7FEFF 		bl	xTaskResumeAll
 1288              	.LVL98:
 622:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				{
 1289              		.loc 1 622 7 discriminator 1 view .LVU339
 1290 003c A8B9     		cbnz	r0, .L88
 628:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1291              		.loc 1 628 6 is_stmt 1 view .LVU340
 1292 003e 4FF0E023 		mov	r3, #-*********
 1293 0042 4FF08052 		mov	r2, #*********
 1294 0046 C3F8042D 		str	r2, [r3, #3332]
 628:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1295              		.loc 1 628 6 view .LVU341
 1296              		.syntax unified
 1297              	@ 628 "Middlewares/Third_Party/FreeRTOS/Source/timers.c" 1
 1298 004a BFF34F8F 		dsb
 1299              	@ 0 "" 2
 628:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1300              		.loc 1 628 6 view .LVU342
 1301              	@ 628 "Middlewares/Third_Party/FreeRTOS/Source/timers.c" 1
 1302 004e BFF36F8F 		isb
 1303              	@ 0 "" 2
 628:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1304              		.loc 1 628 28 view .LVU343
 1305              		.thumb
 1306              		.syntax unified
 1307 0052 0AE0     		b	.L88
 1308              	.LVL99:
 1309              	.L95:
 602:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				prvProcessExpiredTimer( xNextExpireTime, xTimeNow );
 1310              		.loc 1 602 5 view .LVU344
 602:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				prvProcessExpiredTimer( xNextExpireTime, xTimeNow );
 1311              		.loc 1 602 14 is_stmt 0 view .LVU345
 1312 0054 FFF7FEFF 		bl	xTaskResumeAll
 1313              	.LVL100:
 603:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 1314              		.loc 1 603 5 is_stmt 1 view .LVU346
 1315 0058 2946     		mov	r1, r5
 1316 005a 3046     		mov	r0, r6
 1317 005c FFF7FEFF 		bl	prvProcessExpiredTimer
 1318              	.LVL101:
 1319 0060 03E0     		b	.L88
 1320              	.LVL102:
 1321              	.L93:
 617:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1322              		.loc 1 617 22 is_stmt 0 discriminator 2 view .LVU347
 1323 0062 1C46     		mov	r4, r3
 1324              	.LVL103:
 617:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 				}
 1325              		.loc 1 617 22 discriminator 2 view .LVU348
 1326 0064 E2E7     		b	.L92
 1327              	.LVL104:
 1328              	.L89:
 638:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1329              		.loc 1 638 4 is_stmt 1 view .LVU349
ARM GAS  /tmp/cc39sGFU.s 			page 50


 638:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1330              		.loc 1 638 13 is_stmt 0 view .LVU350
 1331 0066 FFF7FEFF 		bl	xTaskResumeAll
 1332              	.LVL105:
 1333              	.L88:
 641:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1334              		.loc 1 641 1 view .LVU351
 1335 006a 02B0     		add	sp, sp, #8
 1336              	.LCFI33:
 1337              		.cfi_def_cfa_offset 16
 1338              		@ sp needed
 1339 006c 70BD     		pop	{r4, r5, r6, pc}
 1340              	.LVL106:
 1341              	.L97:
 641:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1342              		.loc 1 641 1 view .LVU352
 1343 006e 00BF     		.align	2
 1344              	.L96:
 1345 0070 00000000 		.word	pxOverflowTimerList
 1346 0074 00000000 		.word	xTimerQueue
 1347              		.cfi_endproc
 1348              	.LFE17:
 1350              		.section	.text.prvProcessReceivedCommands,"ax",%progbits
 1351              		.align	1
 1352              		.syntax unified
 1353              		.thumb
 1354              		.thumb_func
 1356              	prvProcessReceivedCommands:
 1357              	.LFB21:
 735:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** DaemonTaskMessage_t xMessage;
 1358              		.loc 1 735 1 is_stmt 1 view -0
 1359              		.cfi_startproc
 1360              		@ args = 0, pretend = 0, frame = 24
 1361              		@ frame_needed = 0, uses_anonymous_args = 0
 1362 0000 10B5     		push	{r4, lr}
 1363              	.LCFI34:
 1364              		.cfi_def_cfa_offset 8
 1365              		.cfi_offset 4, -8
 1366              		.cfi_offset 14, -4
 1367 0002 88B0     		sub	sp, sp, #32
 1368              	.LCFI35:
 1369              		.cfi_def_cfa_offset 40
 736:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer;
 1370              		.loc 1 736 1 view .LVU354
 737:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerListsWereSwitched, xResult;
 1371              		.loc 1 737 1 view .LVU355
 738:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xTimeNow;
 1372              		.loc 1 738 1 view .LVU356
 739:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1373              		.loc 1 739 1 view .LVU357
 741:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1374              		.loc 1 741 2 view .LVU358
 741:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1375              		.loc 1 741 7 is_stmt 0 view .LVU359
 1376 0004 02E0     		b	.L101
 1377              	.L100:
 760:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
ARM GAS  /tmp/cc39sGFU.s 			page 51


 1378              		.loc 1 760 29 is_stmt 1 view .LVU360
 767:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1379              		.loc 1 767 3 view .LVU361
 767:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1380              		.loc 1 767 15 is_stmt 0 view .LVU362
 1381 0006 049B     		ldr	r3, [sp, #16]
 767:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1382              		.loc 1 767 5 view .LVU363
 1383 0008 002B     		cmp	r3, #0
 1384 000a 0FDA     		bge	.L117
 1385              	.L101:
 741:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1386              		.loc 1 741 62 is_stmt 1 view .LVU364
 741:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1387              		.loc 1 741 9 is_stmt 0 view .LVU365
 1388 000c 0022     		movs	r2, #0
 1389 000e 04A9     		add	r1, sp, #16
 1390 0010 3D4B     		ldr	r3, .L121
 1391 0012 1868     		ldr	r0, [r3]
 1392 0014 FFF7FEFF 		bl	xQueueReceive
 1393              	.LVL107:
 741:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1394              		.loc 1 741 62 discriminator 1 view .LVU366
 1395 0018 0028     		cmp	r0, #0
 1396 001a 73D0     		beq	.L118
 747:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1397              		.loc 1 747 4 is_stmt 1 view .LVU367
 747:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1398              		.loc 1 747 16 is_stmt 0 view .LVU368
 1399 001c 049B     		ldr	r3, [sp, #16]
 747:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1400              		.loc 1 747 6 view .LVU369
 1401 001e 002B     		cmp	r3, #0
 1402 0020 F1DA     		bge	.L100
 1403              	.LBB62:
 749:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1404              		.loc 1 749 5 is_stmt 1 view .LVU370
 1405              	.LVL108:
 753:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1406              		.loc 1 753 5 view .LVU371
 753:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1407              		.loc 1 753 31 discriminator 2 view .LVU372
 756:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 1408              		.loc 1 756 5 view .LVU373
 1409 0022 0799     		ldr	r1, [sp, #28]
 1410 0024 0698     		ldr	r0, [sp, #24]
 1411 0026 059B     		ldr	r3, [sp, #20]
 1412 0028 9847     		blx	r3
 1413              	.LVL109:
 1414              	.LBE62:
 1415 002a ECE7     		b	.L100
 1416              	.LVL110:
 1417              	.L117:
 771:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1418              		.loc 1 771 4 view .LVU374
 771:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1419              		.loc 1 771 12 is_stmt 0 view .LVU375
ARM GAS  /tmp/cc39sGFU.s 			page 52


 1420 002c 069C     		ldr	r4, [sp, #24]
 1421              	.LVL111:
 773:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1422              		.loc 1 773 4 is_stmt 1 view .LVU376
 773:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1423              		.loc 1 773 8 is_stmt 0 view .LVU377
 1424 002e 6369     		ldr	r3, [r4, #20]
 773:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1425              		.loc 1 773 6 view .LVU378
 1426 0030 13B1     		cbz	r3, .L102
 776:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 1427              		.loc 1 776 5 is_stmt 1 view .LVU379
 776:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 1428              		.loc 1 776 14 is_stmt 0 view .LVU380
 1429 0032 201D     		adds	r0, r4, #4
 1430 0034 FFF7FEFF 		bl	uxListRemove
 1431              	.LVL112:
 1432              	.L102:
 780:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			}
 1433              		.loc 1 780 29 is_stmt 1 view .LVU381
 783:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1434              		.loc 1 783 106 view .LVU382
 791:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1435              		.loc 1 791 4 view .LVU383
 791:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1436              		.loc 1 791 15 is_stmt 0 view .LVU384
 1437 0038 03A8     		add	r0, sp, #12
 1438 003a FFF7FEFF 		bl	prvSampleTimeNow
 1439              	.LVL113:
 793:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1440              		.loc 1 793 4 is_stmt 1 view .LVU385
 793:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1441              		.loc 1 793 20 is_stmt 0 view .LVU386
 1442 003e 049B     		ldr	r3, [sp, #16]
 793:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			{
 1443              		.loc 1 793 4 view .LVU387
 1444 0040 092B     		cmp	r3, #9
 1445 0042 E3D8     		bhi	.L101
 1446 0044 DFE803F0 		tbb	[pc, r3]
 1447              	.L105:
 1448 0048 05       		.byte	(.L107-.L105)/2
 1449 0049 05       		.byte	(.L107-.L105)/2
 1450 004a 05       		.byte	(.L107-.L105)/2
 1451 004b 30       		.byte	(.L106-.L105)/2
 1452 004c 37       		.byte	(.L104-.L105)/2
 1453 004d 50       		.byte	(.L108-.L105)/2
 1454 004e 05       		.byte	(.L107-.L105)/2
 1455 004f 05       		.byte	(.L107-.L105)/2
 1456 0050 30       		.byte	(.L106-.L105)/2
 1457 0051 37       		.byte	(.L104-.L105)/2
 1458              		.p2align 1
 1459              	.L107:
 801:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					if( prvInsertTimerInActiveList( pxTimer,  xMessage.u.xTimerParameters.xMessageValue + pxTimer-
 1460              		.loc 1 801 6 is_stmt 1 view .LVU388
 801:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					if( prvInsertTimerInActiveList( pxTimer,  xMessage.u.xTimerParameters.xMessageValue + pxTimer-
 1461              		.loc 1 801 13 is_stmt 0 view .LVU389
 1462 0052 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
ARM GAS  /tmp/cc39sGFU.s 			page 53


 801:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					if( prvInsertTimerInActiveList( pxTimer,  xMessage.u.xTimerParameters.xMessageValue + pxTimer-
 1463              		.loc 1 801 24 view .LVU390
 1464 0056 43F00103 		orr	r3, r3, #1
 1465 005a 84F82830 		strb	r3, [r4, #40]
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 1466              		.loc 1 802 6 is_stmt 1 view .LVU391
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 1467              		.loc 1 802 75 is_stmt 0 view .LVU392
 1468 005e 059B     		ldr	r3, [sp, #20]
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 1469              		.loc 1 802 99 view .LVU393
 1470 0060 A169     		ldr	r1, [r4, #24]
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 1471              		.loc 1 802 10 view .LVU394
 1472 0062 0246     		mov	r2, r0
 1473 0064 1944     		add	r1, r1, r3
 1474 0066 2046     		mov	r0, r4
 1475              	.LVL114:
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 1476              		.loc 1 802 10 view .LVU395
 1477 0068 FFF7FEFF 		bl	prvInsertTimerInActiveList
 1478              	.LVL115:
 802:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					{
 1479              		.loc 1 802 8 discriminator 1 view .LVU396
 1480 006c 0028     		cmp	r0, #0
 1481 006e CDD0     		beq	.L101
 806:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						traceTIMER_EXPIRED( pxTimer );
 1482              		.loc 1 806 7 is_stmt 1 view .LVU397
 806:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						traceTIMER_EXPIRED( pxTimer );
 1483              		.loc 1 806 14 is_stmt 0 view .LVU398
 1484 0070 236A     		ldr	r3, [r4, #32]
 806:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						traceTIMER_EXPIRED( pxTimer );
 1485              		.loc 1 806 7 view .LVU399
 1486 0072 2046     		mov	r0, r4
 1487 0074 9847     		blx	r3
 1488              	.LVL116:
 807:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1489              		.loc 1 807 36 is_stmt 1 view .LVU400
 809:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 1490              		.loc 1 809 7 view .LVU401
 809:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 1491              		.loc 1 809 20 is_stmt 0 view .LVU402
 1492 0076 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
 809:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 1493              		.loc 1 809 9 view .LVU403
 1494 007a 13F0040F 		tst	r3, #4
 1495 007e C5D0     		beq	.L101
 811:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							configASSERT( xResult );
 1496              		.loc 1 811 8 is_stmt 1 view .LVU404
 811:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							configASSERT( xResult );
 1497              		.loc 1 811 129 is_stmt 0 view .LVU405
 1498 0080 A269     		ldr	r2, [r4, #24]
 811:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							configASSERT( xResult );
 1499              		.loc 1 811 18 view .LVU406
 1500 0082 0021     		movs	r1, #0
 1501 0084 0091     		str	r1, [sp]
 1502 0086 0B46     		mov	r3, r1
ARM GAS  /tmp/cc39sGFU.s 			page 54


 1503 0088 0598     		ldr	r0, [sp, #20]
 1504 008a 0244     		add	r2, r2, r0
 1505 008c 2046     		mov	r0, r4
 1506 008e FFF7FEFF 		bl	xTimerGenericCommand
 1507              	.LVL117:
 812:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							( void ) xResult;
 1508              		.loc 1 812 8 is_stmt 1 view .LVU407
 1509 0092 0028     		cmp	r0, #0
 1510 0094 BAD1     		bne	.L101
 812:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							( void ) xResult;
 1511              		.loc 1 812 8 discriminator 1 view .LVU408
 1512              	.LBB63:
 1513              	.LBI63:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1514              		.loc 2 191 30 view .LVU409
 1515              	.LBB64:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1516              		.loc 2 193 1 view .LVU410
 1517              		.loc 2 195 2 view .LVU411
 1518              		.syntax unified
 1519              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1520 0096 4FF05003 			mov r3, #80												
 1521 009a 83F31188 		msr basepri, r3											
 1522 009e BFF36F8F 		isb														
 1523 00a2 BFF34F8F 		dsb														
 1524              	
 1525              	@ 0 "" 2
 1526              		.thumb
 1527              		.syntax unified
 1528              	.L110:
 1529              	.LBE64:
 1530              	.LBE63:
 812:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							( void ) xResult;
 1531              		.loc 1 812 8 discriminator 3 view .LVU412
 812:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 							( void ) xResult;
 1532              		.loc 1 812 8 discriminator 3 view .LVU413
 1533 00a6 FEE7     		b	.L110
 1534              	.LVL118:
 1535              	.L106:
 829:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 1536              		.loc 1 829 6 view .LVU414
 829:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 1537              		.loc 1 829 13 is_stmt 0 view .LVU415
 1538 00a8 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
 829:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 1539              		.loc 1 829 24 view .LVU416
 1540 00ac 23F00103 		bic	r3, r3, #1
 1541 00b0 84F82830 		strb	r3, [r4, #40]
 830:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1542              		.loc 1 830 6 is_stmt 1 view .LVU417
 1543 00b4 AAE7     		b	.L101
 1544              	.L104:
 834:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->xTimerPeriodInTicks = xMessage.u.xTimerParameters.xMessageValue;
 1545              		.loc 1 834 6 view .LVU418
 834:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->xTimerPeriodInTicks = xMessage.u.xTimerParameters.xMessageValue;
 1546              		.loc 1 834 13 is_stmt 0 view .LVU419
 1547 00b6 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
ARM GAS  /tmp/cc39sGFU.s 			page 55


 834:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					pxTimer->xTimerPeriodInTicks = xMessage.u.xTimerParameters.xMessageValue;
 1548              		.loc 1 834 24 view .LVU420
 1549 00ba 43F00103 		orr	r3, r3, #1
 1550 00be 84F82830 		strb	r3, [r4, #40]
 835:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					configASSERT( ( pxTimer->xTimerPeriodInTicks > 0 ) );
 1551              		.loc 1 835 6 is_stmt 1 view .LVU421
 835:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					configASSERT( ( pxTimer->xTimerPeriodInTicks > 0 ) );
 1552              		.loc 1 835 64 is_stmt 0 view .LVU422
 1553 00c2 0599     		ldr	r1, [sp, #20]
 835:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					configASSERT( ( pxTimer->xTimerPeriodInTicks > 0 ) );
 1554              		.loc 1 835 35 view .LVU423
 1555 00c4 A161     		str	r1, [r4, #24]
 836:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1556              		.loc 1 836 6 is_stmt 1 view .LVU424
 1557 00c6 31B1     		cbz	r1, .L119
 836:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1558              		.loc 1 836 58 discriminator 2 view .LVU425
 844:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 1559              		.loc 1 844 6 view .LVU426
 844:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 1560              		.loc 1 844 15 is_stmt 0 view .LVU427
 1561 00c8 0346     		mov	r3, r0
 1562 00ca 0246     		mov	r2, r0
 1563 00cc 0144     		add	r1, r1, r0
 1564 00ce 2046     		mov	r0, r4
 1565              	.LVL119:
 844:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 					break;
 1566              		.loc 1 844 15 view .LVU428
 1567 00d0 FFF7FEFF 		bl	prvInsertTimerInActiveList
 1568              	.LVL120:
 845:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1569              		.loc 1 845 6 is_stmt 1 view .LVU429
 1570 00d4 9AE7     		b	.L101
 1571              	.LVL121:
 1572              	.L119:
 836:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1573              		.loc 1 836 6 discriminator 1 view .LVU430
 1574              	.LBB65:
 1575              	.LBI65:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1576              		.loc 2 191 30 view .LVU431
 1577              	.LBB66:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1578              		.loc 2 193 1 view .LVU432
 1579              		.loc 2 195 2 view .LVU433
 1580              		.syntax unified
 1581              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1582 00d6 4FF05003 			mov r3, #80												
 1583 00da 83F31188 		msr basepri, r3											
 1584 00de BFF36F8F 		isb														
 1585 00e2 BFF34F8F 		dsb														
 1586              	
 1587              	@ 0 "" 2
 1588              		.thumb
 1589              		.syntax unified
 1590              	.L112:
 1591              	.LBE66:
ARM GAS  /tmp/cc39sGFU.s 			page 56


 1592              	.LBE65:
 836:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1593              		.loc 1 836 6 discriminator 3 view .LVU434
 836:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1594              		.loc 1 836 6 discriminator 3 view .LVU435
 1595 00e6 FEE7     		b	.L112
 1596              	.L108:
 853:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 1597              		.loc 1 853 7 view .LVU436
 853:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 1598              		.loc 1 853 20 is_stmt 0 view .LVU437
 1599 00e8 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
 853:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						{
 1600              		.loc 1 853 9 view .LVU438
 1601 00ec 13F0020F 		tst	r3, #2
 1602 00f0 04D0     		beq	.L120
 859:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 1603              		.loc 1 859 8 is_stmt 1 view .LVU439
 859:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 1604              		.loc 1 859 26 is_stmt 0 view .LVU440
 1605 00f2 23F00103 		bic	r3, r3, #1
 1606 00f6 84F82830 		strb	r3, [r4, #40]
 1607 00fa 87E7     		b	.L101
 1608              	.L120:
 855:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 1609              		.loc 1 855 8 is_stmt 1 view .LVU441
 1610 00fc 2046     		mov	r0, r4
 1611              	.LVL122:
 855:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 						}
 1612              		.loc 1 855 8 is_stmt 0 view .LVU442
 1613 00fe FFF7FEFF 		bl	vPortFree
 1614              	.LVL123:
 1615 0102 83E7     		b	.L101
 1616              	.LVL124:
 1617              	.L118:
 879:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1618              		.loc 1 879 1 view .LVU443
 1619 0104 08B0     		add	sp, sp, #32
 1620              	.LCFI36:
 1621              		.cfi_def_cfa_offset 8
 1622              		@ sp needed
 1623 0106 10BD     		pop	{r4, pc}
 1624              	.L122:
 1625              		.align	2
 1626              	.L121:
 1627 0108 00000000 		.word	xTimerQueue
 1628              		.cfi_endproc
 1629              	.LFE21:
 1631              		.section	.text.prvTimerTask,"ax",%progbits
 1632              		.align	1
 1633              		.syntax unified
 1634              		.thumb
 1635              		.thumb_func
 1637              	prvTimerTask:
 1638              	.LFB16:
 549:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xNextExpireTime;
 1639              		.loc 1 549 1 is_stmt 1 view -0
ARM GAS  /tmp/cc39sGFU.s 			page 57


 1640              		.cfi_startproc
 1641              		@ args = 0, pretend = 0, frame = 8
 1642              		@ frame_needed = 0, uses_anonymous_args = 0
 1643              	.LVL125:
 549:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xNextExpireTime;
 1644              		.loc 1 549 1 is_stmt 0 view .LVU445
 1645 0000 00B5     		push	{lr}
 1646              	.LCFI37:
 1647              		.cfi_def_cfa_offset 4
 1648              		.cfi_offset 14, -4
 1649 0002 83B0     		sub	sp, sp, #12
 1650              	.LCFI38:
 1651              		.cfi_def_cfa_offset 16
 1652              	.LVL126:
 1653              	.L124:
 550:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xListWasEmpty;
 1654              		.loc 1 550 1 is_stmt 1 view .LVU446
 551:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1655              		.loc 1 551 1 view .LVU447
 554:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1656              		.loc 1 554 2 view .LVU448
 568:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1657              		.loc 1 568 2 view .LVU449
 572:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1658              		.loc 1 572 3 view .LVU450
 572:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1659              		.loc 1 572 21 is_stmt 0 view .LVU451
 1660 0004 01A8     		add	r0, sp, #4
 1661 0006 FFF7FEFF 		bl	prvGetNextExpireTime
 1662              	.LVL127:
 576:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1663              		.loc 1 576 3 is_stmt 1 view .LVU452
 1664 000a 0199     		ldr	r1, [sp, #4]
 1665 000c FFF7FEFF 		bl	prvProcessTimerOrBlockTask
 1666              	.LVL128:
 579:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 1667              		.loc 1 579 3 discriminator 1 view .LVU453
 1668 0010 FFF7FEFF 		bl	prvProcessReceivedCommands
 1669              	.LVL129:
 568:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1670              		.loc 1 568 2 view .LVU454
 1671 0014 F6E7     		b	.L124
 1672              		.cfi_endproc
 1673              	.LFE16:
 1675              		.section	.text.xTimerGetTimerDaemonTaskHandle,"ax",%progbits
 1676              		.align	1
 1677              		.global	xTimerGetTimerDaemonTaskHandle
 1678              		.syntax unified
 1679              		.thumb
 1680              		.thumb_func
 1682              	xTimerGetTimerDaemonTaskHandle:
 1683              	.LFB9:
 425:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* If xTimerGetTimerDaemonTaskHandle() is called before the scheduler has been
 1684              		.loc 1 425 1 view -0
 1685              		.cfi_startproc
 1686              		@ args = 0, pretend = 0, frame = 0
 1687              		@ frame_needed = 0, uses_anonymous_args = 0
ARM GAS  /tmp/cc39sGFU.s 			page 58


 1688              		@ link register save eliminated.
 428:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xTimerTaskHandle;
 1689              		.loc 1 428 2 view .LVU456
 1690 0000 064B     		ldr	r3, .L130
 1691 0002 1868     		ldr	r0, [r3]
 1692 0004 00B1     		cbz	r0, .L129
 430:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1693              		.loc 1 430 1 is_stmt 0 view .LVU457
 1694 0006 7047     		bx	lr
 1695              	.L129:
 428:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xTimerTaskHandle;
 1696              		.loc 1 428 2 is_stmt 1 discriminator 1 view .LVU458
 1697              	.LBB67:
 1698              	.LBI67:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1699              		.loc 2 191 30 view .LVU459
 1700              	.LBB68:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1701              		.loc 2 193 1 view .LVU460
 1702              		.loc 2 195 2 view .LVU461
 1703              		.syntax unified
 1704              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1705 0008 4FF05003 			mov r3, #80												
 1706 000c 83F31188 		msr basepri, r3											
 1707 0010 BFF36F8F 		isb														
 1708 0014 BFF34F8F 		dsb														
 1709              	
 1710              	@ 0 "" 2
 1711              		.thumb
 1712              		.syntax unified
 1713              	.L128:
 1714              	.LBE68:
 1715              	.LBE67:
 428:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xTimerTaskHandle;
 1716              		.loc 1 428 2 discriminator 3 view .LVU462
 428:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xTimerTaskHandle;
 1717              		.loc 1 428 2 discriminator 3 view .LVU463
 1718 0018 FEE7     		b	.L128
 1719              	.L131:
 1720 001a 00BF     		.align	2
 1721              	.L130:
 1722 001c 00000000 		.word	xTimerTaskHandle
 1723              		.cfi_endproc
 1724              	.LFE9:
 1726              		.section	.text.xTimerGetPeriod,"ax",%progbits
 1727              		.align	1
 1728              		.global	xTimerGetPeriod
 1729              		.syntax unified
 1730              		.thumb
 1731              		.thumb_func
 1733              	xTimerGetPeriod:
 1734              	.LVL130:
 1735              	.LFB10:
 434:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer = xTimer;
 1736              		.loc 1 434 1 view -0
 1737              		.cfi_startproc
 1738              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/cc39sGFU.s 			page 59


 1739              		@ frame_needed = 0, uses_anonymous_args = 0
 1740              		@ link register save eliminated.
 435:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1741              		.loc 1 435 1 view .LVU465
 437:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->xTimerPeriodInTicks;
 1742              		.loc 1 437 2 view .LVU466
 1743 0000 08B1     		cbz	r0, .L135
 437:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->xTimerPeriodInTicks;
 1744              		.loc 1 437 24 discriminator 2 view .LVU467
 438:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1745              		.loc 1 438 2 view .LVU468
 439:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1746              		.loc 1 439 1 is_stmt 0 view .LVU469
 1747 0002 8069     		ldr	r0, [r0, #24]
 1748              	.LVL131:
 439:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1749              		.loc 1 439 1 view .LVU470
 1750 0004 7047     		bx	lr
 1751              	.LVL132:
 1752              	.L135:
 437:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->xTimerPeriodInTicks;
 1753              		.loc 1 437 2 is_stmt 1 discriminator 1 view .LVU471
 1754              	.LBB69:
 1755              	.LBI69:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1756              		.loc 2 191 30 view .LVU472
 1757              	.LBB70:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1758              		.loc 2 193 1 view .LVU473
 1759              		.loc 2 195 2 view .LVU474
 1760              		.syntax unified
 1761              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1762 0006 4FF05003 			mov r3, #80												
 1763 000a 83F31188 		msr basepri, r3											
 1764 000e BFF36F8F 		isb														
 1765 0012 BFF34F8F 		dsb														
 1766              	
 1767              	@ 0 "" 2
 1768              		.thumb
 1769              		.syntax unified
 1770              	.L134:
 1771              	.LBE70:
 1772              	.LBE69:
 437:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->xTimerPeriodInTicks;
 1773              		.loc 1 437 2 discriminator 3 view .LVU475
 437:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->xTimerPeriodInTicks;
 1774              		.loc 1 437 2 discriminator 3 view .LVU476
 1775 0016 FEE7     		b	.L134
 1776              		.cfi_endproc
 1777              	.LFE10:
 1779              		.section	.text.vTimerSetReloadMode,"ax",%progbits
 1780              		.align	1
 1781              		.global	vTimerSetReloadMode
 1782              		.syntax unified
 1783              		.thumb
 1784              		.thumb_func
 1786              	vTimerSetReloadMode:
ARM GAS  /tmp/cc39sGFU.s 			page 60


 1787              	.LVL133:
 1788              	.LFB11:
 443:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 1789              		.loc 1 443 1 view -0
 1790              		.cfi_startproc
 1791              		@ args = 0, pretend = 0, frame = 0
 1792              		@ frame_needed = 0, uses_anonymous_args = 0
 443:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 1793              		.loc 1 443 1 is_stmt 0 view .LVU478
 1794 0000 38B5     		push	{r3, r4, r5, lr}
 1795              	.LCFI39:
 1796              		.cfi_def_cfa_offset 16
 1797              		.cfi_offset 3, -16
 1798              		.cfi_offset 4, -12
 1799              		.cfi_offset 5, -8
 1800              		.cfi_offset 14, -4
 444:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1801              		.loc 1 444 1 is_stmt 1 view .LVU479
 1802              	.LVL134:
 446:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1803              		.loc 1 446 2 view .LVU480
 1804 0002 68B1     		cbz	r0, .L142
 1805 0004 0C46     		mov	r4, r1
 1806 0006 0546     		mov	r5, r0
 446:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1807              		.loc 1 446 24 discriminator 2 view .LVU481
 447:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1808              		.loc 1 447 2 view .LVU482
 1809 0008 FFF7FEFF 		bl	vPortEnterCritical
 1810              	.LVL135:
 449:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1811              		.loc 1 449 3 view .LVU483
 449:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1812              		.loc 1 449 5 is_stmt 0 view .LVU484
 1813 000c 8CB1     		cbz	r4, .L139
 451:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1814              		.loc 1 451 4 is_stmt 1 view .LVU485
 451:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1815              		.loc 1 451 11 is_stmt 0 view .LVU486
 1816 000e 95F82830 		ldrb	r3, [r5, #40]	@ zero_extendqisi2
 451:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1817              		.loc 1 451 22 view .LVU487
 1818 0012 43F00403 		orr	r3, r3, #4
 1819 0016 85F82830 		strb	r3, [r5, #40]
 1820              	.L140:
 458:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1821              		.loc 1 458 2 is_stmt 1 view .LVU488
 1822 001a FFF7FEFF 		bl	vPortExitCritical
 1823              	.LVL136:
 459:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1824              		.loc 1 459 1 is_stmt 0 view .LVU489
 1825 001e 38BD     		pop	{r3, r4, r5, pc}
 1826              	.LVL137:
 1827              	.L142:
 446:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1828              		.loc 1 446 2 is_stmt 1 discriminator 1 view .LVU490
 1829              	.LBB71:
ARM GAS  /tmp/cc39sGFU.s 			page 61


 1830              	.LBI71:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1831              		.loc 2 191 30 view .LVU491
 1832              	.LBB72:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1833              		.loc 2 193 1 view .LVU492
 1834              		.loc 2 195 2 view .LVU493
 1835              		.syntax unified
 1836              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1837 0020 4FF05003 			mov r3, #80												
 1838 0024 83F31188 		msr basepri, r3											
 1839 0028 BFF36F8F 		isb														
 1840 002c BFF34F8F 		dsb														
 1841              	
 1842              	@ 0 "" 2
 1843              		.thumb
 1844              		.syntax unified
 1845              	.L138:
 1846              	.LBE72:
 1847              	.LBE71:
 446:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1848              		.loc 1 446 2 discriminator 3 view .LVU494
 446:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1849              		.loc 1 446 2 discriminator 3 view .LVU495
 1850 0030 FEE7     		b	.L138
 1851              	.LVL138:
 1852              	.L139:
 455:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1853              		.loc 1 455 4 view .LVU496
 455:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1854              		.loc 1 455 11 is_stmt 0 view .LVU497
 1855 0032 95F82830 		ldrb	r3, [r5, #40]	@ zero_extendqisi2
 455:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1856              		.loc 1 455 22 view .LVU498
 1857 0036 23F00403 		bic	r3, r3, #4
 1858 003a 85F82830 		strb	r3, [r5, #40]
 1859 003e ECE7     		b	.L140
 1860              		.cfi_endproc
 1861              	.LFE11:
 1863              		.section	.text.uxTimerGetReloadMode,"ax",%progbits
 1864              		.align	1
 1865              		.global	uxTimerGetReloadMode
 1866              		.syntax unified
 1867              		.thumb
 1868              		.thumb_func
 1870              	uxTimerGetReloadMode:
 1871              	.LVL139:
 1872              	.LFB12:
 463:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 1873              		.loc 1 463 1 is_stmt 1 view -0
 1874              		.cfi_startproc
 1875              		@ args = 0, pretend = 0, frame = 0
 1876              		@ frame_needed = 0, uses_anonymous_args = 0
 464:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** UBaseType_t uxReturn;
 1877              		.loc 1 464 1 view .LVU500
 465:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1878              		.loc 1 465 1 view .LVU501
ARM GAS  /tmp/cc39sGFU.s 			page 62


 467:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1879              		.loc 1 467 2 view .LVU502
 1880 0000 68B1     		cbz	r0, .L150
 463:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 1881              		.loc 1 463 1 is_stmt 0 view .LVU503
 1882 0002 10B5     		push	{r4, lr}
 1883              	.LCFI40:
 1884              		.cfi_def_cfa_offset 8
 1885              		.cfi_offset 4, -8
 1886              		.cfi_offset 14, -4
 1887 0004 0446     		mov	r4, r0
 467:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1888              		.loc 1 467 24 is_stmt 1 discriminator 2 view .LVU504
 468:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 1889              		.loc 1 468 2 view .LVU505
 1890 0006 FFF7FEFF 		bl	vPortEnterCritical
 1891              	.LVL140:
 470:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1892              		.loc 1 470 3 view .LVU506
 470:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1893              		.loc 1 470 16 is_stmt 0 view .LVU507
 1894 000a 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
 470:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
 1895              		.loc 1 470 5 view .LVU508
 1896 000e 13F0040F 		tst	r3, #4
 1897 0012 0DD1     		bne	.L147
 473:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1898              		.loc 1 473 13 view .LVU509
 1899 0014 0024     		movs	r4, #0
 1900              	.LVL141:
 1901              	.L146:
 481:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1902              		.loc 1 481 2 is_stmt 1 view .LVU510
 1903 0016 FFF7FEFF 		bl	vPortExitCritical
 1904              	.LVL142:
 483:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1905              		.loc 1 483 2 view .LVU511
 484:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1906              		.loc 1 484 1 is_stmt 0 view .LVU512
 1907 001a 2046     		mov	r0, r4
 1908 001c 10BD     		pop	{r4, pc}
 1909              	.LVL143:
 1910              	.L150:
 1911              	.LCFI41:
 1912              		.cfi_def_cfa_offset 0
 1913              		.cfi_restore 4
 1914              		.cfi_restore 14
 467:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1915              		.loc 1 467 2 is_stmt 1 discriminator 1 view .LVU513
 1916              	.LBB73:
 1917              	.LBI73:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1918              		.loc 2 191 30 view .LVU514
 1919              	.LBB74:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1920              		.loc 2 193 1 view .LVU515
 1921              		.loc 2 195 2 view .LVU516
ARM GAS  /tmp/cc39sGFU.s 			page 63


 1922              		.syntax unified
 1923              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1924 001e 4FF05003 			mov r3, #80												
 1925 0022 83F31188 		msr basepri, r3											
 1926 0026 BFF36F8F 		isb														
 1927 002a BFF34F8F 		dsb														
 1928              	
 1929              	@ 0 "" 2
 1930              		.thumb
 1931              		.syntax unified
 1932              	.L145:
 1933              	.LBE74:
 1934              	.LBE73:
 467:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1935              		.loc 1 467 2 discriminator 3 view .LVU517
 467:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 1936              		.loc 1 467 2 discriminator 3 view .LVU518
 1937 002e FEE7     		b	.L145
 1938              	.LVL144:
 1939              	.L147:
 1940              	.LCFI42:
 1941              		.cfi_def_cfa_offset 8
 1942              		.cfi_offset 4, -8
 1943              		.cfi_offset 14, -4
 478:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1944              		.loc 1 478 13 is_stmt 0 view .LVU519
 1945 0030 0124     		movs	r4, #1
 1946              	.LVL145:
 478:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 1947              		.loc 1 478 13 view .LVU520
 1948 0032 F0E7     		b	.L146
 1949              		.cfi_endproc
 1950              	.LFE12:
 1952              		.section	.text.xTimerGetExpiryTime,"ax",%progbits
 1953              		.align	1
 1954              		.global	xTimerGetExpiryTime
 1955              		.syntax unified
 1956              		.thumb
 1957              		.thumb_func
 1959              	xTimerGetExpiryTime:
 1960              	.LVL146:
 1961              	.LFB13:
 488:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * pxTimer =  xTimer;
 1962              		.loc 1 488 1 is_stmt 1 view -0
 1963              		.cfi_startproc
 1964              		@ args = 0, pretend = 0, frame = 0
 1965              		@ frame_needed = 0, uses_anonymous_args = 0
 1966              		@ link register save eliminated.
 489:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** TickType_t xReturn;
 1967              		.loc 1 489 1 view .LVU522
 490:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 1968              		.loc 1 490 1 view .LVU523
 492:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xReturn = listGET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ) );
 1969              		.loc 1 492 2 view .LVU524
 1970 0000 08B1     		cbz	r0, .L154
 492:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xReturn = listGET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ) );
 1971              		.loc 1 492 24 discriminator 2 view .LVU525
ARM GAS  /tmp/cc39sGFU.s 			page 64


 493:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 1972              		.loc 1 493 2 view .LVU526
 1973              	.LVL147:
 494:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 1974              		.loc 1 494 2 view .LVU527
 495:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1975              		.loc 1 495 1 is_stmt 0 view .LVU528
 1976 0002 4068     		ldr	r0, [r0, #4]
 1977              	.LVL148:
 495:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 1978              		.loc 1 495 1 view .LVU529
 1979 0004 7047     		bx	lr
 1980              	.LVL149:
 1981              	.L154:
 492:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xReturn = listGET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ) );
 1982              		.loc 1 492 2 is_stmt 1 discriminator 1 view .LVU530
 1983              	.LBB75:
 1984              	.LBI75:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 1985              		.loc 2 191 30 view .LVU531
 1986              	.LBB76:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 1987              		.loc 2 193 1 view .LVU532
 1988              		.loc 2 195 2 view .LVU533
 1989              		.syntax unified
 1990              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 1991 0006 4FF05003 			mov r3, #80												
 1992 000a 83F31188 		msr basepri, r3											
 1993 000e BFF36F8F 		isb														
 1994 0012 BFF34F8F 		dsb														
 1995              	
 1996              	@ 0 "" 2
 1997              		.thumb
 1998              		.syntax unified
 1999              	.L153:
 2000              	.LBE76:
 2001              	.LBE75:
 492:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xReturn = listGET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ) );
 2002              		.loc 1 492 2 discriminator 3 view .LVU534
 492:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	xReturn = listGET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ) );
 2003              		.loc 1 492 2 discriminator 3 view .LVU535
 2004 0016 FEE7     		b	.L153
 2005              		.cfi_endproc
 2006              	.LFE13:
 2008              		.section	.text.pcTimerGetName,"ax",%progbits
 2009              		.align	1
 2010              		.global	pcTimerGetName
 2011              		.syntax unified
 2012              		.thumb
 2013              		.thumb_func
 2015              	pcTimerGetName:
 2016              	.LVL150:
 2017              	.LFB14:
 499:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer = xTimer;
 2018              		.loc 1 499 1 view -0
 2019              		.cfi_startproc
 2020              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/cc39sGFU.s 			page 65


 2021              		@ frame_needed = 0, uses_anonymous_args = 0
 2022              		@ link register save eliminated.
 500:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2023              		.loc 1 500 1 view .LVU537
 502:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->pcTimerName;
 2024              		.loc 1 502 2 view .LVU538
 2025 0000 08B1     		cbz	r0, .L158
 502:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->pcTimerName;
 2026              		.loc 1 502 24 discriminator 2 view .LVU539
 503:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 2027              		.loc 1 503 2 view .LVU540
 504:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 2028              		.loc 1 504 1 is_stmt 0 view .LVU541
 2029 0002 0068     		ldr	r0, [r0]
 2030              	.LVL151:
 504:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 2031              		.loc 1 504 1 view .LVU542
 2032 0004 7047     		bx	lr
 2033              	.LVL152:
 2034              	.L158:
 502:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->pcTimerName;
 2035              		.loc 1 502 2 is_stmt 1 discriminator 1 view .LVU543
 2036              	.LBB77:
 2037              	.LBI77:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2038              		.loc 2 191 30 view .LVU544
 2039              	.LBB78:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2040              		.loc 2 193 1 view .LVU545
 2041              		.loc 2 195 2 view .LVU546
 2042              		.syntax unified
 2043              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2044 0006 4FF05003 			mov r3, #80												
 2045 000a 83F31188 		msr basepri, r3											
 2046 000e BFF36F8F 		isb														
 2047 0012 BFF34F8F 		dsb														
 2048              	
 2049              	@ 0 "" 2
 2050              		.thumb
 2051              		.syntax unified
 2052              	.L157:
 2053              	.LBE78:
 2054              	.LBE77:
 502:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->pcTimerName;
 2055              		.loc 1 502 2 discriminator 3 view .LVU547
 502:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pxTimer->pcTimerName;
 2056              		.loc 1 502 2 discriminator 3 view .LVU548
 2057 0016 FEE7     		b	.L157
 2058              		.cfi_endproc
 2059              	.LFE14:
 2061              		.section	.text.xTimerIsTimerActive,"ax",%progbits
 2062              		.align	1
 2063              		.global	xTimerIsTimerActive
 2064              		.syntax unified
 2065              		.thumb
 2066              		.thumb_func
 2068              	xTimerIsTimerActive:
ARM GAS  /tmp/cc39sGFU.s 			page 66


 2069              	.LVL153:
 2070              	.LFB24:
 990:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
 991:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 992:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xTimerIsTimerActive( TimerHandle_t xTimer )
 993:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 2071              		.loc 1 993 1 view -0
 2072              		.cfi_startproc
 2073              		@ args = 0, pretend = 0, frame = 0
 2074              		@ frame_needed = 0, uses_anonymous_args = 0
 994:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn;
 2075              		.loc 1 994 1 view .LVU550
 995:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t *pxTimer = xTimer;
 2076              		.loc 1 995 1 view .LVU551
 996:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 997:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 2077              		.loc 1 997 2 view .LVU552
 2078 0000 68B1     		cbz	r0, .L166
 993:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** BaseType_t xReturn;
 2079              		.loc 1 993 1 is_stmt 0 view .LVU553
 2080 0002 10B5     		push	{r4, lr}
 2081              	.LCFI43:
 2082              		.cfi_def_cfa_offset 8
 2083              		.cfi_offset 4, -8
 2084              		.cfi_offset 14, -4
 2085 0004 0446     		mov	r4, r0
 2086              		.loc 1 997 24 is_stmt 1 discriminator 2 view .LVU554
 998:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 999:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	/* Is the timer in the list of active timers? */
1000:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 2087              		.loc 1 1000 2 view .LVU555
 2088 0006 FFF7FEFF 		bl	vPortEnterCritical
 2089              	.LVL154:
1001:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
1002:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		if( ( pxTimer->ucStatus & tmrSTATUS_IS_ACTIVE ) == 0 )
 2090              		.loc 1 1002 3 view .LVU556
 2091              		.loc 1 1002 16 is_stmt 0 view .LVU557
 2092 000a 94F82830 		ldrb	r3, [r4, #40]	@ zero_extendqisi2
 2093              		.loc 1 1002 5 view .LVU558
 2094 000e 13F0010F 		tst	r3, #1
 2095 0012 0DD1     		bne	.L163
1003:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
1004:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xReturn = pdFALSE;
 2096              		.loc 1 1004 12 view .LVU559
 2097 0014 0024     		movs	r4, #0
 2098              	.LVL155:
 2099              	.L162:
1005:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
1006:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		else
1007:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		{
1008:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 			xReturn = pdTRUE;
1009:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
1010:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
1011:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskEXIT_CRITICAL();
 2100              		.loc 1 1011 2 is_stmt 1 view .LVU560
 2101 0016 FFF7FEFF 		bl	vPortExitCritical
 2102              	.LVL156:
ARM GAS  /tmp/cc39sGFU.s 			page 67


1012:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1013:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return xReturn;
 2103              		.loc 1 1013 2 view .LVU561
1014:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** } /*lint !e818 Can't be pointer to const due to the typedef. */
 2104              		.loc 1 1014 1 is_stmt 0 view .LVU562
 2105 001a 2046     		mov	r0, r4
 2106 001c 10BD     		pop	{r4, pc}
 2107              	.LVL157:
 2108              	.L166:
 2109              	.LCFI44:
 2110              		.cfi_def_cfa_offset 0
 2111              		.cfi_restore 4
 2112              		.cfi_restore 14
 997:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2113              		.loc 1 997 2 is_stmt 1 discriminator 1 view .LVU563
 2114              	.LBB79:
 2115              	.LBI79:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2116              		.loc 2 191 30 view .LVU564
 2117              	.LBB80:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2118              		.loc 2 193 1 view .LVU565
 2119              		.loc 2 195 2 view .LVU566
 2120              		.syntax unified
 2121              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2122 001e 4FF05003 			mov r3, #80												
 2123 0022 83F31188 		msr basepri, r3											
 2124 0026 BFF36F8F 		isb														
 2125 002a BFF34F8F 		dsb														
 2126              	
 2127              	@ 0 "" 2
 2128              		.thumb
 2129              		.syntax unified
 2130              	.L161:
 2131              	.LBE80:
 2132              	.LBE79:
 997:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2133              		.loc 1 997 2 discriminator 3 view .LVU567
 997:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2134              		.loc 1 997 2 discriminator 3 view .LVU568
 2135 002e FEE7     		b	.L161
 2136              	.LVL158:
 2137              	.L163:
 2138              	.LCFI45:
 2139              		.cfi_def_cfa_offset 8
 2140              		.cfi_offset 4, -8
 2141              		.cfi_offset 14, -4
1008:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 2142              		.loc 1 1008 12 is_stmt 0 view .LVU569
 2143 0030 0124     		movs	r4, #1
 2144              	.LVL159:
1008:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		}
 2145              		.loc 1 1008 12 view .LVU570
 2146 0032 F0E7     		b	.L162
 2147              		.cfi_endproc
 2148              	.LFE24:
 2150              		.section	.text.pvTimerGetTimerID,"ax",%progbits
ARM GAS  /tmp/cc39sGFU.s 			page 68


 2151              		.align	1
 2152              		.global	pvTimerGetTimerID
 2153              		.syntax unified
 2154              		.thumb
 2155              		.thumb_func
 2157              	pvTimerGetTimerID:
 2158              	.LVL160:
 2159              	.LFB25:
1015:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
1016:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1017:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** void *pvTimerGetTimerID( const TimerHandle_t xTimer )
1018:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 2160              		.loc 1 1018 1 is_stmt 1 view -0
 2161              		.cfi_startproc
 2162              		@ args = 0, pretend = 0, frame = 0
 2163              		@ frame_needed = 0, uses_anonymous_args = 0
1019:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * const pxTimer = xTimer;
 2164              		.loc 1 1019 1 view .LVU572
1020:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** void *pvReturn;
 2165              		.loc 1 1020 1 view .LVU573
1021:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1022:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 2166              		.loc 1 1022 2 view .LVU574
 2167 0000 40B1     		cbz	r0, .L172
1018:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * const pxTimer = xTimer;
 2168              		.loc 1 1018 1 is_stmt 0 view .LVU575
 2169 0002 10B5     		push	{r4, lr}
 2170              	.LCFI46:
 2171              		.cfi_def_cfa_offset 8
 2172              		.cfi_offset 4, -8
 2173              		.cfi_offset 14, -4
 2174 0004 0446     		mov	r4, r0
 2175              		.loc 1 1022 24 is_stmt 1 discriminator 2 view .LVU576
1023:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1024:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 2176              		.loc 1 1024 2 view .LVU577
 2177 0006 FFF7FEFF 		bl	vPortEnterCritical
 2178              	.LVL161:
1025:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
1026:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pvReturn = pxTimer->pvTimerID;
 2179              		.loc 1 1026 3 view .LVU578
 2180              		.loc 1 1026 12 is_stmt 0 view .LVU579
 2181 000a E469     		ldr	r4, [r4, #28]
 2182              	.LVL162:
1027:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
1028:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskEXIT_CRITICAL();
 2183              		.loc 1 1028 2 is_stmt 1 view .LVU580
 2184 000c FFF7FEFF 		bl	vPortExitCritical
 2185              	.LVL163:
1029:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1030:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	return pvReturn;
 2186              		.loc 1 1030 2 view .LVU581
1031:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 2187              		.loc 1 1031 1 is_stmt 0 view .LVU582
 2188 0010 2046     		mov	r0, r4
 2189 0012 10BD     		pop	{r4, pc}
 2190              	.LVL164:
ARM GAS  /tmp/cc39sGFU.s 			page 69


 2191              	.L172:
 2192              	.LCFI47:
 2193              		.cfi_def_cfa_offset 0
 2194              		.cfi_restore 4
 2195              		.cfi_restore 14
1022:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2196              		.loc 1 1022 2 is_stmt 1 discriminator 1 view .LVU583
 2197              	.LBB81:
 2198              	.LBI81:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2199              		.loc 2 191 30 view .LVU584
 2200              	.LBB82:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2201              		.loc 2 193 1 view .LVU585
 2202              		.loc 2 195 2 view .LVU586
 2203              		.syntax unified
 2204              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2205 0014 4FF05003 			mov r3, #80												
 2206 0018 83F31188 		msr basepri, r3											
 2207 001c BFF36F8F 		isb														
 2208 0020 BFF34F8F 		dsb														
 2209              	
 2210              	@ 0 "" 2
 2211              		.thumb
 2212              		.syntax unified
 2213              	.L169:
 2214              	.LBE82:
 2215              	.LBE81:
1022:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2216              		.loc 1 1022 2 discriminator 3 view .LVU587
1022:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2217              		.loc 1 1022 2 discriminator 3 view .LVU588
 2218 0024 FEE7     		b	.L169
 2219              		.cfi_endproc
 2220              	.LFE25:
 2222              		.section	.text.vTimerSetTimerID,"ax",%progbits
 2223              		.align	1
 2224              		.global	vTimerSetTimerID
 2225              		.syntax unified
 2226              		.thumb
 2227              		.thumb_func
 2229              	vTimerSetTimerID:
 2230              	.LVL165:
 2231              	.LFB26:
1032:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
1033:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1034:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** void vTimerSetTimerID( TimerHandle_t xTimer, void *pvNewID )
1035:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** {
 2232              		.loc 1 1035 1 view -0
 2233              		.cfi_startproc
 2234              		@ args = 0, pretend = 0, frame = 0
 2235              		@ frame_needed = 0, uses_anonymous_args = 0
 2236              		.loc 1 1035 1 is_stmt 0 view .LVU590
 2237 0000 38B5     		push	{r3, r4, r5, lr}
 2238              	.LCFI48:
 2239              		.cfi_def_cfa_offset 16
 2240              		.cfi_offset 3, -16
ARM GAS  /tmp/cc39sGFU.s 			page 70


 2241              		.cfi_offset 4, -12
 2242              		.cfi_offset 5, -8
 2243              		.cfi_offset 14, -4
1036:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** Timer_t * const pxTimer = xTimer;
 2244              		.loc 1 1036 1 is_stmt 1 view .LVU591
 2245              	.LVL166:
1037:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1038:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	configASSERT( xTimer );
 2246              		.loc 1 1038 2 view .LVU592
 2247 0002 38B1     		cbz	r0, .L177
 2248 0004 0C46     		mov	r4, r1
 2249 0006 0546     		mov	r5, r0
 2250              		.loc 1 1038 24 discriminator 2 view .LVU593
1039:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1040:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskENTER_CRITICAL();
 2251              		.loc 1 1040 2 view .LVU594
 2252 0008 FFF7FEFF 		bl	vPortEnterCritical
 2253              	.LVL167:
1041:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
1042:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		pxTimer->pvTimerID = pvNewID;
 2254              		.loc 1 1042 3 view .LVU595
 2255              		.loc 1 1042 22 is_stmt 0 view .LVU596
 2256 000c EC61     		str	r4, [r5, #28]
1043:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
1044:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	taskEXIT_CRITICAL();
 2257              		.loc 1 1044 2 is_stmt 1 view .LVU597
 2258 000e FFF7FEFF 		bl	vPortExitCritical
 2259              	.LVL168:
1045:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** }
 2260              		.loc 1 1045 1 is_stmt 0 view .LVU598
 2261 0012 38BD     		pop	{r3, r4, r5, pc}
 2262              	.LVL169:
 2263              	.L177:
1038:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2264              		.loc 1 1038 2 is_stmt 1 discriminator 1 view .LVU599
 2265              	.LBB83:
 2266              	.LBI83:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2267              		.loc 2 191 30 view .LVU600
 2268              	.LBB84:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2269              		.loc 2 193 1 view .LVU601
 2270              		.loc 2 195 2 view .LVU602
 2271              		.syntax unified
 2272              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2273 0014 4FF05003 			mov r3, #80												
 2274 0018 83F31188 		msr basepri, r3											
 2275 001c BFF36F8F 		isb														
 2276 0020 BFF34F8F 		dsb														
 2277              	
 2278              	@ 0 "" 2
 2279              		.thumb
 2280              		.syntax unified
 2281              	.L175:
 2282              	.LBE84:
 2283              	.LBE83:
1038:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
ARM GAS  /tmp/cc39sGFU.s 			page 71


 2284              		.loc 1 1038 2 discriminator 3 view .LVU603
1038:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2285              		.loc 1 1038 2 discriminator 3 view .LVU604
 2286 0024 FEE7     		b	.L175
 2287              		.cfi_endproc
 2288              	.LFE26:
 2290              		.section	.text.xTimerPendFunctionCallFromISR,"ax",%progbits
 2291              		.align	1
 2292              		.global	xTimerPendFunctionCallFromISR
 2293              		.syntax unified
 2294              		.thumb
 2295              		.thumb_func
 2297              	xTimerPendFunctionCallFromISR:
 2298              	.LVL170:
 2299              	.LFB27:
1046:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
1047:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1048:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if( INCLUDE_xTimerPendFunctionCall == 1 )
1049:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1050:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	BaseType_t xTimerPendFunctionCallFromISR( PendedFunction_t xFunctionToPend, void *pvParameter1, ui
1051:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 2300              		.loc 1 1051 2 view -0
 2301              		.cfi_startproc
 2302              		@ args = 0, pretend = 0, frame = 16
 2303              		@ frame_needed = 0, uses_anonymous_args = 0
 2304              		.loc 1 1051 2 is_stmt 0 view .LVU606
 2305 0000 10B5     		push	{r4, lr}
 2306              	.LCFI49:
 2307              		.cfi_def_cfa_offset 8
 2308              		.cfi_offset 4, -8
 2309              		.cfi_offset 14, -4
 2310 0002 84B0     		sub	sp, sp, #16
 2311              	.LCFI50:
 2312              		.cfi_def_cfa_offset 24
 2313 0004 1446     		mov	r4, r2
 2314 0006 1A46     		mov	r2, r3
 2315              	.LVL171:
1052:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	DaemonTaskMessage_t xMessage;
 2316              		.loc 1 1052 2 is_stmt 1 view .LVU607
1053:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	BaseType_t xReturn;
 2317              		.loc 1 1053 2 view .LVU608
1054:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1055:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Complete the message with the function parameters and post it to the
1056:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		daemon task. */
1057:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.xMessageID = tmrCOMMAND_EXECUTE_CALLBACK_FROM_ISR;
 2318              		.loc 1 1057 3 view .LVU609
 2319              		.loc 1 1057 23 is_stmt 0 view .LVU610
 2320 0008 6FF00103 		mvn	r3, #1
 2321              	.LVL172:
 2322              		.loc 1 1057 23 view .LVU611
 2323 000c 0093     		str	r3, [sp]
1058:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xCallbackParameters.pxCallbackFunction = xFunctionToPend;
 2324              		.loc 1 1058 3 is_stmt 1 view .LVU612
 2325              		.loc 1 1058 53 is_stmt 0 view .LVU613
 2326 000e 0190     		str	r0, [sp, #4]
1059:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xCallbackParameters.pvParameter1 = pvParameter1;
 2327              		.loc 1 1059 3 is_stmt 1 view .LVU614
ARM GAS  /tmp/cc39sGFU.s 			page 72


 2328              		.loc 1 1059 47 is_stmt 0 view .LVU615
 2329 0010 0291     		str	r1, [sp, #8]
1060:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xCallbackParameters.ulParameter2 = ulParameter2;
 2330              		.loc 1 1060 3 is_stmt 1 view .LVU616
 2331              		.loc 1 1060 47 is_stmt 0 view .LVU617
 2332 0012 0394     		str	r4, [sp, #12]
1061:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1062:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xReturn = xQueueSendFromISR( xTimerQueue, &xMessage, pxHigherPriorityTaskWoken );
 2333              		.loc 1 1062 3 is_stmt 1 view .LVU618
 2334              		.loc 1 1062 13 is_stmt 0 view .LVU619
 2335 0014 0023     		movs	r3, #0
 2336 0016 6946     		mov	r1, sp
 2337              	.LVL173:
 2338              		.loc 1 1062 13 view .LVU620
 2339 0018 0248     		ldr	r0, .L180
 2340              	.LVL174:
 2341              		.loc 1 1062 13 view .LVU621
 2342 001a 0068     		ldr	r0, [r0]
 2343 001c FFF7FEFF 		bl	xQueueGenericSendFromISR
 2344              	.LVL175:
1063:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1064:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		tracePEND_FUNC_CALL_FROM_ISR( xFunctionToPend, pvParameter1, ulParameter2, xReturn );
 2345              		.loc 1 1064 87 is_stmt 1 view .LVU622
1065:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1066:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		return xReturn;
 2346              		.loc 1 1066 3 view .LVU623
1067:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 2347              		.loc 1 1067 2 is_stmt 0 view .LVU624
 2348 0020 04B0     		add	sp, sp, #16
 2349              	.LCFI51:
 2350              		.cfi_def_cfa_offset 8
 2351              		@ sp needed
 2352 0022 10BD     		pop	{r4, pc}
 2353              	.LVL176:
 2354              	.L181:
 2355              		.loc 1 1067 2 view .LVU625
 2356              		.align	2
 2357              	.L180:
 2358 0024 00000000 		.word	xTimerQueue
 2359              		.cfi_endproc
 2360              	.LFE27:
 2362              		.section	.text.xTimerPendFunctionCall,"ax",%progbits
 2363              		.align	1
 2364              		.global	xTimerPendFunctionCall
 2365              		.syntax unified
 2366              		.thumb
 2367              		.thumb_func
 2369              	xTimerPendFunctionCall:
 2370              	.LVL177:
 2371              	.LFB28:
1068:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1069:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif /* INCLUDE_xTimerPendFunctionCall */
1070:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
1071:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1072:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if( INCLUDE_xTimerPendFunctionCall == 1 )
1073:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1074:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	BaseType_t xTimerPendFunctionCall( PendedFunction_t xFunctionToPend, void *pvParameter1, uint32_t 
ARM GAS  /tmp/cc39sGFU.s 			page 73


1075:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 2372              		.loc 1 1075 2 is_stmt 1 view -0
 2373              		.cfi_startproc
 2374              		@ args = 0, pretend = 0, frame = 16
 2375              		@ frame_needed = 0, uses_anonymous_args = 0
 2376              		.loc 1 1075 2 is_stmt 0 view .LVU627
 2377 0000 30B5     		push	{r4, r5, lr}
 2378              	.LCFI52:
 2379              		.cfi_def_cfa_offset 12
 2380              		.cfi_offset 4, -12
 2381              		.cfi_offset 5, -8
 2382              		.cfi_offset 14, -4
 2383 0002 85B0     		sub	sp, sp, #20
 2384              	.LCFI53:
 2385              		.cfi_def_cfa_offset 32
 2386 0004 1446     		mov	r4, r2
 2387 0006 1A46     		mov	r2, r3
 2388              	.LVL178:
1076:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	DaemonTaskMessage_t xMessage;
 2389              		.loc 1 1076 2 is_stmt 1 view .LVU628
1077:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	BaseType_t xReturn;
 2390              		.loc 1 1077 2 view .LVU629
1078:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1079:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* This function can only be called after a timer has been created or
1080:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		after the scheduler has been started because, until then, the timer
1081:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		queue does not exist. */
1082:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		configASSERT( xTimerQueue );
 2391              		.loc 1 1082 3 view .LVU630
 2392 0008 0C4B     		ldr	r3, .L187
 2393              	.LVL179:
 2394              		.loc 1 1082 3 is_stmt 0 view .LVU631
 2395 000a 1D68     		ldr	r5, [r3]
 2396 000c 65B1     		cbz	r5, .L186
 2397              		.loc 1 1082 30 is_stmt 1 discriminator 2 view .LVU632
1083:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1084:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		/* Complete the message with the function parameters and post it to the
1085:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		daemon task. */
1086:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.xMessageID = tmrCOMMAND_EXECUTE_CALLBACK;
 2398              		.loc 1 1086 3 view .LVU633
 2399              		.loc 1 1086 23 is_stmt 0 view .LVU634
 2400 000e 4FF0FF33 		mov	r3, #-1
 2401 0012 0093     		str	r3, [sp]
1087:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xCallbackParameters.pxCallbackFunction = xFunctionToPend;
 2402              		.loc 1 1087 3 is_stmt 1 view .LVU635
 2403              		.loc 1 1087 53 is_stmt 0 view .LVU636
 2404 0014 0190     		str	r0, [sp, #4]
1088:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xCallbackParameters.pvParameter1 = pvParameter1;
 2405              		.loc 1 1088 3 is_stmt 1 view .LVU637
 2406              		.loc 1 1088 47 is_stmt 0 view .LVU638
 2407 0016 0291     		str	r1, [sp, #8]
1089:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xMessage.u.xCallbackParameters.ulParameter2 = ulParameter2;
 2408              		.loc 1 1089 3 is_stmt 1 view .LVU639
 2409              		.loc 1 1089 47 is_stmt 0 view .LVU640
 2410 0018 0394     		str	r4, [sp, #12]
1090:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1091:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		xReturn = xQueueSendToBack( xTimerQueue, &xMessage, xTicksToWait );
 2411              		.loc 1 1091 3 is_stmt 1 view .LVU641
ARM GAS  /tmp/cc39sGFU.s 			page 74


 2412              		.loc 1 1091 13 is_stmt 0 view .LVU642
 2413 001a 0023     		movs	r3, #0
 2414 001c 6946     		mov	r1, sp
 2415              	.LVL180:
 2416              		.loc 1 1091 13 view .LVU643
 2417 001e 2846     		mov	r0, r5
 2418              	.LVL181:
 2419              		.loc 1 1091 13 view .LVU644
 2420 0020 FFF7FEFF 		bl	xQueueGenericSend
 2421              	.LVL182:
1092:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1093:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		tracePEND_FUNC_CALL( xFunctionToPend, pvParameter1, ulParameter2, xReturn );
 2422              		.loc 1 1093 78 is_stmt 1 view .LVU645
1094:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1095:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		return xReturn;
 2423              		.loc 1 1095 3 view .LVU646
1096:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 2424              		.loc 1 1096 2 is_stmt 0 view .LVU647
 2425 0024 05B0     		add	sp, sp, #20
 2426              	.LCFI54:
 2427              		.cfi_remember_state
 2428              		.cfi_def_cfa_offset 12
 2429              		@ sp needed
 2430 0026 30BD     		pop	{r4, r5, pc}
 2431              	.LVL183:
 2432              	.L186:
 2433              	.LCFI55:
 2434              		.cfi_restore_state
1082:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2435              		.loc 1 1082 3 is_stmt 1 discriminator 1 view .LVU648
 2436              	.LBB85:
 2437              	.LBI85:
 191:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** {
 2438              		.loc 2 191 30 view .LVU649
 2439              	.LBB86:
 193:Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h **** 
 2440              		.loc 2 193 1 view .LVU650
 2441              		.loc 2 195 2 view .LVU651
 2442              		.syntax unified
 2443              	@ 195 "Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h" 1
 2444 0028 4FF05003 			mov r3, #80												
 2445 002c 83F31188 		msr basepri, r3											
 2446 0030 BFF36F8F 		isb														
 2447 0034 BFF34F8F 		dsb														
 2448              	
 2449              	@ 0 "" 2
 2450              		.thumb
 2451              		.syntax unified
 2452              	.L184:
 2453              	.LBE86:
 2454              	.LBE85:
1082:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2455              		.loc 1 1082 3 discriminator 3 view .LVU652
1082:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
 2456              		.loc 1 1082 3 discriminator 3 view .LVU653
 2457 0038 FEE7     		b	.L184
 2458              	.L188:
ARM GAS  /tmp/cc39sGFU.s 			page 75


 2459 003a 00BF     		.align	2
 2460              	.L187:
 2461 003c 00000000 		.word	xTimerQueue
 2462              		.cfi_endproc
 2463              	.LFE28:
 2465              		.section	.text.uxTimerGetTimerNumber,"ax",%progbits
 2466              		.align	1
 2467              		.global	uxTimerGetTimerNumber
 2468              		.syntax unified
 2469              		.thumb
 2470              		.thumb_func
 2472              	uxTimerGetTimerNumber:
 2473              	.LVL184:
 2474              	.LFB29:
1097:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1098:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif /* INCLUDE_xTimerPendFunctionCall */
1099:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
1100:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1101:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if ( configUSE_TRACE_FACILITY == 1 )
1102:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1103:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	UBaseType_t uxTimerGetTimerNumber( TimerHandle_t xTimer )
1104:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 2475              		.loc 1 1104 2 view -0
 2476              		.cfi_startproc
 2477              		@ args = 0, pretend = 0, frame = 0
 2478              		@ frame_needed = 0, uses_anonymous_args = 0
 2479              		@ link register save eliminated.
1105:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		return ( ( Timer_t * ) xTimer )->uxTimerNumber;
 2480              		.loc 1 1105 3 view .LVU655
1106:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 2481              		.loc 1 1106 2 is_stmt 0 view .LVU656
 2482 0000 406A     		ldr	r0, [r0, #36]
 2483              	.LVL185:
 2484              		.loc 1 1106 2 view .LVU657
 2485 0002 7047     		bx	lr
 2486              		.cfi_endproc
 2487              	.LFE29:
 2489              		.section	.text.vTimerSetTimerNumber,"ax",%progbits
 2490              		.align	1
 2491              		.global	vTimerSetTimerNumber
 2492              		.syntax unified
 2493              		.thumb
 2494              		.thumb_func
 2496              	vTimerSetTimerNumber:
 2497              	.LVL186:
 2498              	.LFB30:
1107:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1108:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #endif /* configUSE_TRACE_FACILITY */
1109:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** /*-----------------------------------------------------------*/
1110:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1111:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** #if ( configUSE_TRACE_FACILITY == 1 )
1112:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 
1113:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	void vTimerSetTimerNumber( TimerHandle_t xTimer, UBaseType_t uxTimerNumber )
1114:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	{
 2499              		.loc 1 1114 2 is_stmt 1 view -0
 2500              		.cfi_startproc
 2501              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/cc39sGFU.s 			page 76


 2502              		@ frame_needed = 0, uses_anonymous_args = 0
 2503              		@ link register save eliminated.
1115:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 		( ( Timer_t * ) xTimer )->uxTimerNumber = uxTimerNumber;
 2504              		.loc 1 1115 3 view .LVU659
 2505              		.loc 1 1115 43 is_stmt 0 view .LVU660
 2506 0000 4162     		str	r1, [r0, #36]
1116:Middlewares/Third_Party/FreeRTOS/Source/timers.c **** 	}
 2507              		.loc 1 1116 2 view .LVU661
 2508 0002 7047     		bx	lr
 2509              		.cfi_endproc
 2510              	.LFE30:
 2512              		.section	.bss.xStaticTimerQueue.0,"aw",%nobits
 2513              		.align	2
 2516              	xStaticTimerQueue.0:
 2517 0000 00000000 		.space	80
 2517      00000000 
 2517      00000000 
 2517      00000000 
 2517      00000000 
 2518              		.section	.bss.ucStaticTimerQueueStorage.1,"aw",%nobits
 2519              		.align	2
 2522              	ucStaticTimerQueueStorage.1:
 2523 0000 00000000 		.space	160
 2523      00000000 
 2523      00000000 
 2523      00000000 
 2523      00000000 
 2524              		.section	.bss.xLastTime.2,"aw",%nobits
 2525              		.align	2
 2528              	xLastTime.2:
 2529 0000 00000000 		.space	4
 2530              		.section	.bss.xTimerTaskHandle,"aw",%nobits
 2531              		.align	2
 2534              	xTimerTaskHandle:
 2535 0000 00000000 		.space	4
 2536              		.section	.bss.xTimerQueue,"aw",%nobits
 2537              		.align	2
 2540              	xTimerQueue:
 2541 0000 00000000 		.space	4
 2542              		.section	.bss.pxOverflowTimerList,"aw",%nobits
 2543              		.align	2
 2546              	pxOverflowTimerList:
 2547 0000 00000000 		.space	4
 2548              		.section	.bss.pxCurrentTimerList,"aw",%nobits
 2549              		.align	2
 2552              	pxCurrentTimerList:
 2553 0000 00000000 		.space	4
 2554              		.section	.bss.xActiveTimerList2,"aw",%nobits
 2555              		.align	2
 2558              	xActiveTimerList2:
 2559 0000 00000000 		.space	20
 2559      00000000 
 2559      00000000 
 2559      00000000 
 2559      00000000 
 2560              		.section	.bss.xActiveTimerList1,"aw",%nobits
 2561              		.align	2
ARM GAS  /tmp/cc39sGFU.s 			page 77


 2564              	xActiveTimerList1:
 2565 0000 00000000 		.space	20
 2565      00000000 
 2565      00000000 
 2565      00000000 
 2565      00000000 
 2566              		.text
 2567              	.Letext0:
 2568              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2569              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2570              		.file 5 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2571              		.file 6 "Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h"
 2572              		.file 7 "Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h"
 2573              		.file 8 "Middlewares/Third_Party/FreeRTOS/Source/include/list.h"
 2574              		.file 9 "Middlewares/Third_Party/FreeRTOS/Source/include/task.h"
 2575              		.file 10 "Middlewares/Third_Party/FreeRTOS/Source/include/queue.h"
 2576              		.file 11 "Middlewares/Third_Party/FreeRTOS/Source/include/timers.h"
 2577              		.file 12 "Middlewares/Third_Party/FreeRTOS/Source/include/portable.h"
ARM GAS  /tmp/cc39sGFU.s 			page 78


DEFINED SYMBOLS
                            *ABS*:00000000 timers.c
     /tmp/cc39sGFU.s:20     .text.prvGetNextExpireTime:00000000 $t
     /tmp/cc39sGFU.s:25     .text.prvGetNextExpireTime:00000000 prvGetNextExpireTime
     /tmp/cc39sGFU.s:71     .text.prvGetNextExpireTime:0000001c $d
     /tmp/cc39sGFU.s:2552   .bss.pxCurrentTimerList:00000000 pxCurrentTimerList
     /tmp/cc39sGFU.s:76     .text.prvInsertTimerInActiveList:00000000 $t
     /tmp/cc39sGFU.s:81     .text.prvInsertTimerInActiveList:00000000 prvInsertTimerInActiveList
     /tmp/cc39sGFU.s:176    .text.prvInsertTimerInActiveList:00000048 $d
     /tmp/cc39sGFU.s:2546   .bss.pxOverflowTimerList:00000000 pxOverflowTimerList
     /tmp/cc39sGFU.s:182    .rodata.prvCheckForValidListAndQueue.str1.4:00000000 $d
     /tmp/cc39sGFU.s:186    .text.prvCheckForValidListAndQueue:00000000 $t
     /tmp/cc39sGFU.s:191    .text.prvCheckForValidListAndQueue:00000000 prvCheckForValidListAndQueue
     /tmp/cc39sGFU.s:277    .text.prvCheckForValidListAndQueue:00000050 $d
     /tmp/cc39sGFU.s:2540   .bss.xTimerQueue:00000000 xTimerQueue
     /tmp/cc39sGFU.s:2564   .bss.xActiveTimerList1:00000000 xActiveTimerList1
     /tmp/cc39sGFU.s:2558   .bss.xActiveTimerList2:00000000 xActiveTimerList2
     /tmp/cc39sGFU.s:2516   .bss.xStaticTimerQueue.0:00000000 xStaticTimerQueue.0
     /tmp/cc39sGFU.s:2522   .bss.ucStaticTimerQueueStorage.1:00000000 ucStaticTimerQueueStorage.1
     /tmp/cc39sGFU.s:289    .text.prvInitialiseNewTimer:00000000 $t
     /tmp/cc39sGFU.s:294    .text.prvInitialiseNewTimer:00000000 prvInitialiseNewTimer
     /tmp/cc39sGFU.s:389    .rodata.xTimerCreateTimerTask.str1.4:00000000 $d
     /tmp/cc39sGFU.s:393    .text.xTimerCreateTimerTask:00000000 $t
     /tmp/cc39sGFU.s:399    .text.xTimerCreateTimerTask:00000000 xTimerCreateTimerTask
     /tmp/cc39sGFU.s:498    .text.xTimerCreateTimerTask:00000054 $d
     /tmp/cc39sGFU.s:1637   .text.prvTimerTask:00000000 prvTimerTask
     /tmp/cc39sGFU.s:2534   .bss.xTimerTaskHandle:00000000 xTimerTaskHandle
     /tmp/cc39sGFU.s:506    .text.xTimerCreate:00000000 $t
     /tmp/cc39sGFU.s:512    .text.xTimerCreate:00000000 xTimerCreate
     /tmp/cc39sGFU.s:578    .text.xTimerCreateStatic:00000000 $t
     /tmp/cc39sGFU.s:584    .text.xTimerCreateStatic:00000000 xTimerCreateStatic
     /tmp/cc39sGFU.s:707    .text.xTimerGenericCommand:00000000 $t
     /tmp/cc39sGFU.s:713    .text.xTimerGenericCommand:00000000 xTimerGenericCommand
     /tmp/cc39sGFU.s:857    .text.xTimerGenericCommand:00000068 $d
     /tmp/cc39sGFU.s:862    .text.prvSwitchTimerLists:00000000 $t
     /tmp/cc39sGFU.s:867    .text.prvSwitchTimerLists:00000000 prvSwitchTimerLists
     /tmp/cc39sGFU.s:1011   .text.prvSwitchTimerLists:00000074 $d
     /tmp/cc39sGFU.s:1017   .text.prvSampleTimeNow:00000000 $t
     /tmp/cc39sGFU.s:1022   .text.prvSampleTimeNow:00000000 prvSampleTimeNow
     /tmp/cc39sGFU.s:1081   .text.prvSampleTimeNow:00000028 $d
     /tmp/cc39sGFU.s:2528   .bss.xLastTime.2:00000000 xLastTime.2
     /tmp/cc39sGFU.s:1086   .text.prvProcessExpiredTimer:00000000 $t
     /tmp/cc39sGFU.s:1091   .text.prvProcessExpiredTimer:00000000 prvProcessExpiredTimer
     /tmp/cc39sGFU.s:1211   .text.prvProcessExpiredTimer:00000068 $d
     /tmp/cc39sGFU.s:1216   .text.prvProcessTimerOrBlockTask:00000000 $t
     /tmp/cc39sGFU.s:1221   .text.prvProcessTimerOrBlockTask:00000000 prvProcessTimerOrBlockTask
     /tmp/cc39sGFU.s:1345   .text.prvProcessTimerOrBlockTask:00000070 $d
     /tmp/cc39sGFU.s:1351   .text.prvProcessReceivedCommands:00000000 $t
     /tmp/cc39sGFU.s:1356   .text.prvProcessReceivedCommands:00000000 prvProcessReceivedCommands
     /tmp/cc39sGFU.s:1448   .text.prvProcessReceivedCommands:00000048 $d
     /tmp/cc39sGFU.s:1458   .text.prvProcessReceivedCommands:00000052 $t
     /tmp/cc39sGFU.s:1627   .text.prvProcessReceivedCommands:00000108 $d
     /tmp/cc39sGFU.s:1632   .text.prvTimerTask:00000000 $t
     /tmp/cc39sGFU.s:1676   .text.xTimerGetTimerDaemonTaskHandle:00000000 $t
     /tmp/cc39sGFU.s:1682   .text.xTimerGetTimerDaemonTaskHandle:00000000 xTimerGetTimerDaemonTaskHandle
     /tmp/cc39sGFU.s:1722   .text.xTimerGetTimerDaemonTaskHandle:0000001c $d
     /tmp/cc39sGFU.s:1727   .text.xTimerGetPeriod:00000000 $t
ARM GAS  /tmp/cc39sGFU.s 			page 79


     /tmp/cc39sGFU.s:1733   .text.xTimerGetPeriod:00000000 xTimerGetPeriod
     /tmp/cc39sGFU.s:1780   .text.vTimerSetReloadMode:00000000 $t
     /tmp/cc39sGFU.s:1786   .text.vTimerSetReloadMode:00000000 vTimerSetReloadMode
     /tmp/cc39sGFU.s:1864   .text.uxTimerGetReloadMode:00000000 $t
     /tmp/cc39sGFU.s:1870   .text.uxTimerGetReloadMode:00000000 uxTimerGetReloadMode
     /tmp/cc39sGFU.s:1953   .text.xTimerGetExpiryTime:00000000 $t
     /tmp/cc39sGFU.s:1959   .text.xTimerGetExpiryTime:00000000 xTimerGetExpiryTime
     /tmp/cc39sGFU.s:2009   .text.pcTimerGetName:00000000 $t
     /tmp/cc39sGFU.s:2015   .text.pcTimerGetName:00000000 pcTimerGetName
     /tmp/cc39sGFU.s:2062   .text.xTimerIsTimerActive:00000000 $t
     /tmp/cc39sGFU.s:2068   .text.xTimerIsTimerActive:00000000 xTimerIsTimerActive
     /tmp/cc39sGFU.s:2151   .text.pvTimerGetTimerID:00000000 $t
     /tmp/cc39sGFU.s:2157   .text.pvTimerGetTimerID:00000000 pvTimerGetTimerID
     /tmp/cc39sGFU.s:2223   .text.vTimerSetTimerID:00000000 $t
     /tmp/cc39sGFU.s:2229   .text.vTimerSetTimerID:00000000 vTimerSetTimerID
     /tmp/cc39sGFU.s:2291   .text.xTimerPendFunctionCallFromISR:00000000 $t
     /tmp/cc39sGFU.s:2297   .text.xTimerPendFunctionCallFromISR:00000000 xTimerPendFunctionCallFromISR
     /tmp/cc39sGFU.s:2358   .text.xTimerPendFunctionCallFromISR:00000024 $d
     /tmp/cc39sGFU.s:2363   .text.xTimerPendFunctionCall:00000000 $t
     /tmp/cc39sGFU.s:2369   .text.xTimerPendFunctionCall:00000000 xTimerPendFunctionCall
     /tmp/cc39sGFU.s:2461   .text.xTimerPendFunctionCall:0000003c $d
     /tmp/cc39sGFU.s:2466   .text.uxTimerGetTimerNumber:00000000 $t
     /tmp/cc39sGFU.s:2472   .text.uxTimerGetTimerNumber:00000000 uxTimerGetTimerNumber
     /tmp/cc39sGFU.s:2490   .text.vTimerSetTimerNumber:00000000 $t
     /tmp/cc39sGFU.s:2496   .text.vTimerSetTimerNumber:00000000 vTimerSetTimerNumber
     /tmp/cc39sGFU.s:2513   .bss.xStaticTimerQueue.0:00000000 $d
     /tmp/cc39sGFU.s:2519   .bss.ucStaticTimerQueueStorage.1:00000000 $d
     /tmp/cc39sGFU.s:2525   .bss.xLastTime.2:00000000 $d
     /tmp/cc39sGFU.s:2531   .bss.xTimerTaskHandle:00000000 $d
     /tmp/cc39sGFU.s:2537   .bss.xTimerQueue:00000000 $d
     /tmp/cc39sGFU.s:2543   .bss.pxOverflowTimerList:00000000 $d
     /tmp/cc39sGFU.s:2549   .bss.pxCurrentTimerList:00000000 $d
     /tmp/cc39sGFU.s:2555   .bss.xActiveTimerList2:00000000 $d
     /tmp/cc39sGFU.s:2561   .bss.xActiveTimerList1:00000000 $d

UNDEFINED SYMBOLS
vListInsert
vPortEnterCritical
vPortExitCritical
vListInitialise
xQueueGenericCreateStatic
vQueueAddToRegistry
vListInitialiseItem
vApplicationGetTimerTaskMemory
xTaskCreateStatic
pvPortMalloc
xTaskGetSchedulerState
xQueueGenericSend
xQueueGenericSendFromISR
uxListRemove
xTaskGetTickCount
vTaskSuspendAll
vQueueWaitForMessageRestricted
xTaskResumeAll
xQueueReceive
vPortFree
