ARM GAS  /tmp/ccO3pRIZ.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"icmp.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c"
  19              		.section	.rodata.icmp_send_response.str1.4,"aMS",%progbits,1
  20              		.align	2
  21              	.LC0:
  22 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c\000"
  22      6C657761 
  22      7265732F 
  22      54686972 
  22      645F5061 
  23 0032 0000     		.align	2
  24              	.LC1:
  25 0034 63686563 		.ascii	"check that first pbuf can hold icmp message\000"
  25      6B207468 
  25      61742066 
  25      69727374 
  25      20706275 
  26              		.align	2
  27              	.LC2:
  28 0060 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  28      7274696F 
  28      6E202225 
  28      73222066 
  28      61696C65 
  29              		.section	.text.icmp_send_response,"ax",%progbits
  30              		.align	1
  31              		.syntax unified
  32              		.thumb
  33              		.thumb_func
  35              	icmp_send_response:
  36              	.LVL0:
  37              	.LFB173:
   1:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * ICMP - Internet Control Message Protocol
   4:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  */
   6:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
   7:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /*
   8:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
   9:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * All rights reserved.
ARM GAS  /tmp/ccO3pRIZ.s 			page 2


  10:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  11:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Redistribution and use in source and binary forms, with or without modification,
  12:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * are permitted provided that the following conditions are met:
  13:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  14:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  15:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *    this list of conditions and the following disclaimer.
  16:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  17:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *    this list of conditions and the following disclaimer in the documentation
  18:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *    and/or other materials provided with the distribution.
  19:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * 3. The name of the author may not be used to endorse or promote products
  20:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *    derived from this software without specific prior written permission.
  21:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  22:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
  23:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  24:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  25:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  26:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  27:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  28:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  29:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  30:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  31:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * OF SUCH DAMAGE.
  32:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  33:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * This file is part of the lwIP TCP/IP stack.
  34:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  35:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Author: Adam Dunkels <<EMAIL>>
  36:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  37:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  */
  38:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  39:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /* Some ICMP messages should be passed to the transport protocols. This
  40:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****    is not implemented. */
  41:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  42:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include "lwip/opt.h"
  43:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  44:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_IPV4 && LWIP_ICMP /* don't build if not configured for use in lwipopts.h */
  45:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  46:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include "lwip/icmp.h"
  47:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include "lwip/inet_chksum.h"
  48:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include "lwip/ip.h"
  49:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include "lwip/def.h"
  50:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include "lwip/stats.h"
  51:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  52:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include <string.h>
  53:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  54:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_HOOK_FILENAME
  55:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #include LWIP_HOOK_FILENAME
  56:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif
  57:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  58:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /** Small optimization: set to 0 if incoming PBUF_POOL pbuf always can be
  59:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * used to modify and send a response packet (and to 1 if this is not the case,
  60:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * e.g. when link header is stripped off when receiving) */
  61:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifndef LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN
  62:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #define LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN 1
  63:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN */
  64:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  65:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /* The amount of data from the original packet to return in a dest-unreachable */
  66:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #define ICMP_DEST_UNREACH_DATASIZE 8
ARM GAS  /tmp/ccO3pRIZ.s 			page 3


  67:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  68:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** static void icmp_send_response(struct pbuf *p, u8_t type, u8_t code);
  69:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  70:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /**
  71:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Processes ICMP input packets, called from ip_input().
  72:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  73:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Currently only processes icmp echo requests and sends
  74:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * out the echo response.
  75:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
  76:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param p the icmp echo request packet, p->payload pointing to the icmp header
  77:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param inp the netif on which this packet was received
  78:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  */
  79:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** void
  80:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** icmp_input(struct pbuf *p, struct netif *inp)
  81:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** {
  82:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   u8_t type;
  83:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_DEBUG
  84:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   u8_t code;
  85:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_DEBUG */
  86:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   struct icmp_echo_hdr *iecho;
  87:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   const struct ip_hdr *iphdr_in;
  88:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   u16_t hlen;
  89:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   const ip4_addr_t *src;
  90:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  91:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ICMP_STATS_INC(icmp.recv);
  92:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpinmsgs);
  93:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
  94:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   iphdr_in = ip4_current_header();
  95:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   hlen = IPH_HL_BYTES(iphdr_in);
  96:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   if (hlen < IP_HLEN) {
  97:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short IP header (%"S16_F" bytes) received\n", hlen));
  98:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     goto lenerr;
  99:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   if (p->len < sizeof(u16_t) * 2) {
 101:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short ICMP (%"U16_F" bytes) received\n", p->tot_len));
 102:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     goto lenerr;
 103:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 104:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   type = *((u8_t *)p->payload);
 106:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_DEBUG
 107:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   code = *(((u8_t *)p->payload) + 1);
 108:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   /* if debug is enabled but debug statement below is somehow disabled: */
 109:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   LWIP_UNUSED_ARG(code);
 110:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_DEBUG */
 111:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   switch (type) {
 112:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     case ICMP_ER:
 113:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       /* This is OK, echo reply might have been parsed by a raw PCB
 114:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****          (as obviously, an echo request has been sent, too). */
 115:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       MIB2_STATS_INC(mib2.icmpinechoreps);
 116:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       break;
 117:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     case ICMP_ECHO:
 118:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       MIB2_STATS_INC(mib2.icmpinechos);
 119:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       src = ip4_current_dest_addr();
 120:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       /* multicast destination address? */
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (ip4_addr_ismulticast(ip4_current_dest_addr())) {
 122:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_MULTICAST_PING
 123:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* For multicast, use address of receiving interface as source address */
ARM GAS  /tmp/ccO3pRIZ.s 			page 4


 124:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         src = netif_ip4_addr(inp);
 125:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #else /* LWIP_MULTICAST_PING */
 126:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: Not echoing to multicast pings\n"));
 127:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         goto icmperr;
 128:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_MULTICAST_PING */
 129:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 130:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       /* broadcast destination address? */
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (ip4_addr_isbroadcast(ip4_current_dest_addr(), ip_current_netif())) {
 132:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_BROADCAST_PING
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* For broadcast, use address of receiving interface as source address */
 134:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         src = netif_ip4_addr(inp);
 135:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #else /* LWIP_BROADCAST_PING */
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: Not echoing to broadcast pings\n"));
 137:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         goto icmperr;
 138:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_BROADCAST_PING */
 139:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: ping\n"));
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (p->tot_len < sizeof(struct icmp_echo_hdr)) {
 142:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: bad ICMP echo received\n"));
 143:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         goto lenerr;
 144:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 145:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if CHECKSUM_CHECK_ICMP
 146:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       IF__NETIF_CHECKSUM_ENABLED(inp, NETIF_CHECKSUM_CHECK_ICMP) {
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (inet_chksum_pbuf(p) != 0) {
 148:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: checksum failed for received ICMP echo\n"));
 149:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(p);
 150:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           ICMP_STATS_INC(icmp.chkerr);
 151:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           MIB2_STATS_INC(mib2.icmpinerrors);
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           return;
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 154:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 155:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif
 156:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (pbuf_add_header(p, hlen + PBUF_LINK_HLEN + PBUF_LINK_ENCAPSULATION_HLEN)) {
 158:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* p is not big enough to contain link headers
 159:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****          * allocate a new one and copy p into it
 160:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****          */
 161:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         struct pbuf *r;
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         u16_t alloc_len = (u16_t)(p->tot_len + hlen);
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (alloc_len < p->tot_len) {
 164:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed (tot_len overflow)\n"));
 165:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 166:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 167:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* allocate new packet buffer with space for link headers */
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         r = pbuf_alloc(PBUF_LINK, alloc_len, PBUF_RAM);
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (r == NULL) {
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed\n"));
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (r->len < hlen + sizeof(struct icmp_echo_hdr)) {
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("first pbuf cannot hold the ICMP header
 175:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 176:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 177:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* copy the ip header */
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MEMCPY(r->payload, iphdr_in, hlen);
 180:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* switch r->payload back to icmp header (cannot fail) */
ARM GAS  /tmp/ccO3pRIZ.s 			page 5


 181:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (pbuf_remove_header(r, hlen)) {
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: moving r->payload to icmp header failed\n", 0);
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 184:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 185:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 186:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* copy the rest of the packet without ip header */
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (pbuf_copy(r, p) != ERR_OK) {
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("icmp_input: copying to new pbuf failed
 189:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 191:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 192:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* free the original p */
 193:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         pbuf_free(p);
 194:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* we now have an identical copy of p that has room for link headers */
 195:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         p = r;
 196:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else {
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* restore p->payload to point to icmp header (cannot fail) */
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (pbuf_remove_header(p, hlen + PBUF_LINK_HLEN + PBUF_LINK_ENCAPSULATION_HLEN)) {
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: restoring original p->payload failed\n", 0);
 200:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 201:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN */
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       /* At this point, all checks are OK. */
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       /* We generate an answer by switching the dest and src ip addresses,
 206:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****        * setting the icmp type to ECHO_RESPONSE and updating the checksum. */
 207:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       iecho = (struct icmp_echo_hdr *)p->payload;
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (pbuf_add_header(p, hlen)) {
 209:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("Can't move over header in packet"));
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else {
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         err_t ret;
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         struct ip_hdr *iphdr = (struct ip_hdr *)p->payload;
 213:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ip4_addr_copy(iphdr->src, *src);
 214:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ip4_addr_copy(iphdr->dest, *ip4_current_src_addr());
 215:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ICMPH_TYPE_SET(iecho, ICMP_ER);
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if CHECKSUM_GEN_ICMP
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         IF__NETIF_CHECKSUM_ENABLED(inp, NETIF_CHECKSUM_GEN_ICMP) {
 218:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           /* adjust the checksum */
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           if (iecho->chksum > PP_HTONS(0xffffU - (ICMP_ECHO << 8))) {
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****             iecho->chksum = (u16_t)(iecho->chksum + PP_HTONS((u16_t)(ICMP_ECHO << 8)) + 1);
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           } else {
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****             iecho->chksum = (u16_t)(iecho->chksum + PP_HTONS(ICMP_ECHO << 8));
 223:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           }
 224:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_CHECKSUM_CTRL_PER_NETIF
 226:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         else {
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           iecho->chksum = 0;
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 229:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_CHECKSUM_CTRL_PER_NETIF */
 230:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #else /* CHECKSUM_GEN_ICMP */
 231:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         iecho->chksum = 0;
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* CHECKSUM_GEN_ICMP */
 233:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 234:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* Set the correct TTL and recalculate the header checksum. */
 235:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         IPH_TTL_SET(iphdr, ICMP_TTL);
 236:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         IPH_CHKSUM_SET(iphdr, 0);
 237:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if CHECKSUM_GEN_IP
ARM GAS  /tmp/ccO3pRIZ.s 			page 6


 238:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         IF__NETIF_CHECKSUM_ENABLED(inp, NETIF_CHECKSUM_GEN_IP) {
 239:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           IPH_CHKSUM_SET(iphdr, inet_chksum(iphdr, hlen));
 240:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 241:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* CHECKSUM_GEN_IP */
 242:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ICMP_STATS_INC(icmp.xmit);
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* increase number of messages attempted to send */
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpoutmsgs);
 246:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* increase number of echo replies attempted to send */
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpoutechoreps);
 248:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 249:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* send an ICMP packet */
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ret = ip4_output_if(p, src, LWIP_IP_HDRINCL,
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                             ICMP_TTL, 0, IP_PROTO_ICMP, inp);
 252:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (ret != ERR_OK) {
 253:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: ip_output_if returned an error: %s\n", lwip_strerr(
 254:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 255:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 256:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       break;
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     default:
 258:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (type == ICMP_DUR) {
 259:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpindestunreachs);
 260:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_TE) {
 261:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpintimeexcds);
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_PP) {
 263:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpinparmprobs);
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_SQ) {
 265:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpinsrcquenchs);
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_RD) {
 267:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpinredirects);
 268:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_TS) {
 269:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpintimestamps);
 270:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_TSR) {
 271:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpintimestampreps);
 272:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_AM) {
 273:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpinaddrmasks);
 274:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else if (type == ICMP_AMR) {
 275:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         MIB2_STATS_INC(mib2.icmpinaddrmaskreps);
 276:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 277:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: ICMP type %"S16_F" code %"S16_F" not supported.\n",
 278:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                                (s16_t)type, (s16_t)code));
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       ICMP_STATS_INC(icmp.proterr);
 280:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       ICMP_STATS_INC(icmp.drop);
 281:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 282:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   pbuf_free(p);
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   return;
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** lenerr:
 285:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   pbuf_free(p);
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ICMP_STATS_INC(icmp.lenerr);
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpinerrors);
 288:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   return;
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN || !LWIP_MULTICAST_PING || !LWIP_BROADCAST_PING
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** icmperr:
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   pbuf_free(p);
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ICMP_STATS_INC(icmp.err);
 293:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpinerrors);
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   return;
ARM GAS  /tmp/ccO3pRIZ.s 			page 7


 295:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN || !LWIP_MULTICAST_PING || !LWIP_BROADCAST_PING */
 296:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 297:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 298:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /**
 299:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Send an icmp 'destination unreachable' packet, called from ip_input() if
 300:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * the transport layer protocol is unknown and from udp_input() if the local
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * port is not bound.
 302:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
 303:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param p the input packet for which the 'unreachable' should be sent,
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *          p->payload pointing to the IP header
 305:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param t type of the 'unreachable' packet
 306:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  */
 307:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** void
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** icmp_dest_unreach(struct pbuf *p, enum icmp_dur_type t)
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** {
 310:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpoutdestunreachs);
 311:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmp_send_response(p, ICMP_DUR, t);
 312:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 313:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 314:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if IP_FORWARD || IP_REASSEMBLY
 315:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /**
 316:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Send a 'time exceeded' packet, called from ip_forward() if TTL is 0.
 317:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
 318:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param p the input packet for which the 'time exceeded' should be sent,
 319:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *          p->payload pointing to the IP header
 320:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param t type of the 'time exceeded' packet
 321:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  */
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** void
 323:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** icmp_time_exceeded(struct pbuf *p, enum icmp_te_type t)
 324:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** {
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpouttimeexcds);
 326:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmp_send_response(p, ICMP_TE, t);
 327:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 329:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* IP_FORWARD || IP_REASSEMBLY */
 330:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 331:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** /**
 332:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * Send an icmp packet in response to an incoming packet.
 333:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *
 334:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param p the input packet for which the 'unreachable' should be sent,
 335:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  *          p->payload pointing to the IP header
 336:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param type Type of the ICMP header
 337:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  * @param code Code of the ICMP header
 338:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****  */
 339:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** static void
 340:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** icmp_send_response(struct pbuf *p, u8_t type, u8_t code)
 341:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** {
  38              		.loc 1 341 1 view -0
  39              		.cfi_startproc
  40              		@ args = 0, pretend = 0, frame = 8
  41              		@ frame_needed = 0, uses_anonymous_args = 0
  42              		.loc 1 341 1 is_stmt 0 view .LVU1
  43 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
  44              	.LCFI0:
  45              		.cfi_def_cfa_offset 24
  46              		.cfi_offset 4, -24
  47              		.cfi_offset 5, -20
ARM GAS  /tmp/ccO3pRIZ.s 			page 8


  48              		.cfi_offset 6, -16
  49              		.cfi_offset 7, -12
  50              		.cfi_offset 8, -8
  51              		.cfi_offset 14, -4
  52 0004 86B0     		sub	sp, sp, #24
  53              	.LCFI1:
  54              		.cfi_def_cfa_offset 48
  55 0006 0646     		mov	r6, r0
  56 0008 8846     		mov	r8, r1
  57 000a 1746     		mov	r7, r2
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   struct pbuf *q;
  58              		.loc 1 342 3 is_stmt 1 view .LVU2
 343:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   struct ip_hdr *iphdr;
  59              		.loc 1 343 3 view .LVU3
 344:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   /* we can use the echo header here */
 345:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   struct icmp_echo_hdr *icmphdr;
  60              		.loc 1 345 3 view .LVU4
 346:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ip4_addr_t iphdr_src;
  61              		.loc 1 346 3 view .LVU5
 347:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   struct netif *netif;
  62              		.loc 1 347 3 view .LVU6
 348:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 349:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   /* increase number of messages attempted to send */
 350:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpoutmsgs);
  63              		.loc 1 350 35 view .LVU7
 351:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 352:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   /* ICMP header + IP header + 8 bytes of data */
 353:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   q = pbuf_alloc(PBUF_IP, sizeof(struct icmp_echo_hdr) + IP_HLEN + ICMP_DEST_UNREACH_DATASIZE,
  64              		.loc 1 353 3 view .LVU8
  65              		.loc 1 353 7 is_stmt 0 view .LVU9
  66 000c 4FF42072 		mov	r2, #640
  67              	.LVL1:
  68              		.loc 1 353 7 view .LVU10
  69 0010 2421     		movs	r1, #36
  70              	.LVL2:
  71              		.loc 1 353 7 view .LVU11
  72 0012 2220     		movs	r0, #34
  73              	.LVL3:
  74              		.loc 1 353 7 view .LVU12
  75 0014 FFF7FEFF 		bl	pbuf_alloc
  76              	.LVL4:
 354:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                  PBUF_RAM);
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   if (q == NULL) {
  77              		.loc 1 355 3 is_stmt 1 view .LVU13
  78              		.loc 1 355 6 is_stmt 0 view .LVU14
  79 0018 0028     		cmp	r0, #0
  80 001a 3BD0     		beq	.L1
  81 001c 0546     		mov	r5, r0
 356:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_time_exceeded: failed to allocate pbuf for ICMP packet.\n"));
 357:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     MIB2_STATS_INC(mib2.icmpouterrors);
 358:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     return;
 359:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   LWIP_ASSERT("check that first pbuf can hold icmp message",
  82              		.loc 1 360 3 is_stmt 1 view .LVU15
  83              		.loc 1 360 3 view .LVU16
  84 001e 4389     		ldrh	r3, [r0, #10]
  85 0020 232B     		cmp	r3, #35
ARM GAS  /tmp/ccO3pRIZ.s 			page 9


  86 0022 3AD9     		bls	.L7
  87              	.LVL5:
  88              	.L4:
  89              		.loc 1 360 3 discriminator 3 view .LVU17
  90              		.loc 1 360 3 discriminator 3 view .LVU18
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****               (q->len >= (sizeof(struct icmp_echo_hdr) + IP_HLEN + ICMP_DEST_UNREACH_DATASIZE)));
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 363:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   iphdr = (struct ip_hdr *)p->payload;
  91              		.loc 1 363 3 view .LVU19
  92              		.loc 1 363 9 is_stmt 0 view .LVU20
  93 0024 D6F804C0 		ldr	ip, [r6, #4]
  94              	.LVL6:
 364:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   LWIP_DEBUGF(ICMP_DEBUG, ("icmp_time_exceeded from "));
  95              		.loc 1 364 56 is_stmt 1 view .LVU21
 365:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ip4_addr_debug_print_val(ICMP_DEBUG, iphdr->src);
  96              		.loc 1 365 51 view .LVU22
 366:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   LWIP_DEBUGF(ICMP_DEBUG, (" to "));
  97              		.loc 1 366 36 view .LVU23
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ip4_addr_debug_print_val(ICMP_DEBUG, iphdr->dest);
  98              		.loc 1 367 52 view .LVU24
 368:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   LWIP_DEBUGF(ICMP_DEBUG, ("\n"));
  99              		.loc 1 368 34 view .LVU25
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 370:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmphdr = (struct icmp_echo_hdr *)q->payload;
 100              		.loc 1 370 3 view .LVU26
 101              		.loc 1 370 11 is_stmt 0 view .LVU27
 102 0028 6C68     		ldr	r4, [r5, #4]
 103              	.LVL7:
 371:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmphdr->type = type;
 104              		.loc 1 371 3 is_stmt 1 view .LVU28
 105              		.loc 1 371 17 is_stmt 0 view .LVU29
 106 002a 84F80080 		strb	r8, [r4]
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmphdr->code = code;
 107              		.loc 1 372 3 is_stmt 1 view .LVU30
 108              		.loc 1 372 17 is_stmt 0 view .LVU31
 109 002e 6770     		strb	r7, [r4, #1]
 373:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmphdr->id = 0;
 110              		.loc 1 373 3 is_stmt 1 view .LVU32
 111              		.loc 1 373 15 is_stmt 0 view .LVU33
 112 0030 0023     		movs	r3, #0
 113 0032 2371     		strb	r3, [r4, #4]
 114 0034 6371     		strb	r3, [r4, #5]
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmphdr->seqno = 0;
 115              		.loc 1 374 3 is_stmt 1 view .LVU34
 116              		.loc 1 374 18 is_stmt 0 view .LVU35
 117 0036 A371     		strb	r3, [r4, #6]
 118 0038 E371     		strb	r3, [r4, #7]
 375:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 376:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   /* copy fields from original packet */
 377:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   SMEMCPY((u8_t *)q->payload + sizeof(struct icmp_echo_hdr), (u8_t *)p->payload,
 119              		.loc 1 377 3 is_stmt 1 view .LVU36
 120 003a 6B68     		ldr	r3, [r5, #4]
 121 003c 7268     		ldr	r2, [r6, #4]
 122 003e 1668     		ldr	r6, [r2]	@ unaligned
 123              	.LVL8:
 124              		.loc 1 377 3 is_stmt 0 view .LVU37
 125 0040 5068     		ldr	r0, [r2, #4]	@ unaligned
ARM GAS  /tmp/ccO3pRIZ.s 			page 10


 126 0042 9168     		ldr	r1, [r2, #8]	@ unaligned
 127 0044 D768     		ldr	r7, [r2, #12]	@ unaligned
 128              	.LVL9:
 129              		.loc 1 377 3 view .LVU38
 130 0046 9E60     		str	r6, [r3, #8]	@ unaligned
 131 0048 D860     		str	r0, [r3, #12]	@ unaligned
 132 004a 1961     		str	r1, [r3, #16]	@ unaligned
 133 004c 5F61     		str	r7, [r3, #20]	@ unaligned
 134 004e 1669     		ldr	r6, [r2, #16]	@ unaligned
 135 0050 5069     		ldr	r0, [r2, #20]	@ unaligned
 136 0052 9169     		ldr	r1, [r2, #24]	@ unaligned
 137 0054 9E61     		str	r6, [r3, #24]	@ unaligned
 138 0056 D861     		str	r0, [r3, #28]	@ unaligned
 139 0058 1962     		str	r1, [r3, #32]	@ unaligned
 378:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           IP_HLEN + ICMP_DEST_UNREACH_DATASIZE);
 379:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ip4_addr_copy(iphdr_src, iphdr->src);
 140              		.loc 1 380 3 is_stmt 1 view .LVU39
 141 005a DCF80C30 		ldr	r3, [ip, #12]	@ unaligned
 142 005e 0593     		str	r3, [sp, #20]
 381:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_HOOK_IP4_ROUTE_SRC
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   {
 383:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     ip4_addr_t iphdr_dst;
 384:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     ip4_addr_copy(iphdr_dst, iphdr->dest);
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     netif = ip4_route_src(&iphdr_dst, &iphdr_src);
 386:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 387:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #else
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   netif = ip4_route(&iphdr_src);
 143              		.loc 1 388 3 view .LVU40
 144              		.loc 1 388 11 is_stmt 0 view .LVU41
 145 0060 05A8     		add	r0, sp, #20
 146 0062 FFF7FEFF 		bl	ip4_route
 147              	.LVL10:
 389:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif
 390:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   if (netif != NULL) {
 148              		.loc 1 390 3 is_stmt 1 view .LVU42
 149              		.loc 1 390 6 is_stmt 0 view .LVU43
 150 0066 0746     		mov	r7, r0
 151 0068 88B1     		cbz	r0, .L5
 391:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     /* calculate checksum */
 392:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     icmphdr->chksum = 0;
 152              		.loc 1 392 5 is_stmt 1 view .LVU44
 153              		.loc 1 392 21 is_stmt 0 view .LVU45
 154 006a 0026     		movs	r6, #0
 155 006c A670     		strb	r6, [r4, #2]
 156 006e E670     		strb	r6, [r4, #3]
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if CHECKSUM_GEN_ICMP
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     IF__NETIF_CHECKSUM_ENABLED(netif, NETIF_CHECKSUM_GEN_ICMP) {
 395:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       icmphdr->chksum = inet_chksum(icmphdr, q->len);
 157              		.loc 1 395 7 is_stmt 1 view .LVU46
 158              		.loc 1 395 25 is_stmt 0 view .LVU47
 159 0070 6989     		ldrh	r1, [r5, #10]
 160 0072 2046     		mov	r0, r4
 161              	.LVL11:
 162              		.loc 1 395 25 view .LVU48
 163 0074 FFF7FEFF 		bl	inet_chksum
 164              	.LVL12:
ARM GAS  /tmp/ccO3pRIZ.s 			page 11


 165              		.loc 1 395 23 discriminator 1 view .LVU49
 166 0078 6080     		strh	r0, [r4, #2]	@ unaligned
 396:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     }
 397:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif
 398:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     ICMP_STATS_INC(icmp.xmit);
 167              		.loc 1 398 30 is_stmt 1 view .LVU50
 399:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     ip4_output_if(q, NULL, &iphdr_src, ICMP_TTL, 0, IP_PROTO_ICMP, netif);
 168              		.loc 1 399 5 view .LVU51
 169 007a 0297     		str	r7, [sp, #8]
 170 007c 0123     		movs	r3, #1
 171 007e 0193     		str	r3, [sp, #4]
 172 0080 0096     		str	r6, [sp]
 173 0082 FF23     		movs	r3, #255
 174 0084 05AA     		add	r2, sp, #20
 175 0086 3146     		mov	r1, r6
 176 0088 2846     		mov	r0, r5
 177 008a FFF7FEFF 		bl	ip4_output_if
 178              	.LVL13:
 179              	.L5:
 400:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   pbuf_free(q);
 180              		.loc 1 401 3 view .LVU52
 181 008e 2846     		mov	r0, r5
 182 0090 FFF7FEFF 		bl	pbuf_free
 183              	.LVL14:
 184              	.L1:
 402:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 185              		.loc 1 402 1 is_stmt 0 view .LVU53
 186 0094 06B0     		add	sp, sp, #24
 187              	.LCFI2:
 188              		.cfi_remember_state
 189              		.cfi_def_cfa_offset 24
 190              		@ sp needed
 191 0096 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 192              	.LVL15:
 193              	.L7:
 194              	.LCFI3:
 195              		.cfi_restore_state
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****               (q->len >= (sizeof(struct icmp_echo_hdr) + IP_HLEN + ICMP_DEST_UNREACH_DATASIZE)));
 196              		.loc 1 360 3 is_stmt 1 discriminator 1 view .LVU54
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****               (q->len >= (sizeof(struct icmp_echo_hdr) + IP_HLEN + ICMP_DEST_UNREACH_DATASIZE)));
 197              		.loc 1 360 3 discriminator 1 view .LVU55
 198 009a 044B     		ldr	r3, .L8
 199 009c 4FF4B472 		mov	r2, #360
 200 00a0 0349     		ldr	r1, .L8+4
 201 00a2 0448     		ldr	r0, .L8+8
 202              	.LVL16:
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****               (q->len >= (sizeof(struct icmp_echo_hdr) + IP_HLEN + ICMP_DEST_UNREACH_DATASIZE)));
 203              		.loc 1 360 3 is_stmt 0 discriminator 1 view .LVU56
 204 00a4 FFF7FEFF 		bl	printf
 205              	.LVL17:
 206 00a8 BCE7     		b	.L4
 207              	.L9:
 208 00aa 00BF     		.align	2
 209              	.L8:
 210 00ac 00000000 		.word	.LC0
 211 00b0 34000000 		.word	.LC1
ARM GAS  /tmp/ccO3pRIZ.s 			page 12


 212 00b4 60000000 		.word	.LC2
 213              		.cfi_endproc
 214              	.LFE173:
 216              		.section	.rodata.icmp_input.str1.4,"aMS",%progbits,1
 217              		.align	2
 218              	.LC3:
 219 0000 69636D70 		.ascii	"icmp_input: moving r->payload to icmp header failed"
 219      5F696E70 
 219      75743A20 
 219      6D6F7669 
 219      6E672072 
 220 0033 0A00     		.ascii	"\012\000"
 221 0035 000000   		.align	2
 222              	.LC4:
 223 0038 69636D70 		.ascii	"icmp_input: restoring original p->payload failed\012"
 223      5F696E70 
 223      75743A20 
 223      72657374 
 223      6F72696E 
 224 0069 00       		.ascii	"\000"
 225              		.section	.text.icmp_input,"ax",%progbits
 226              		.align	1
 227              		.global	icmp_input
 228              		.syntax unified
 229              		.thumb
 230              		.thumb_func
 232              	icmp_input:
 233              	.LVL18:
 234              	.LFB170:
  81:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   u8_t type;
 235              		.loc 1 81 1 is_stmt 1 view -0
 236              		.cfi_startproc
 237              		@ args = 0, pretend = 0, frame = 0
 238              		@ frame_needed = 0, uses_anonymous_args = 0
  81:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   u8_t type;
 239              		.loc 1 81 1 is_stmt 0 view .LVU58
 240 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 241              	.LCFI4:
 242              		.cfi_def_cfa_offset 24
 243              		.cfi_offset 4, -24
 244              		.cfi_offset 5, -20
 245              		.cfi_offset 6, -16
 246              		.cfi_offset 7, -12
 247              		.cfi_offset 8, -8
 248              		.cfi_offset 14, -4
 249 0004 84B0     		sub	sp, sp, #16
 250              	.LCFI5:
 251              		.cfi_def_cfa_offset 40
 252 0006 0546     		mov	r5, r0
  82:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_DEBUG
 253              		.loc 1 82 3 is_stmt 1 view .LVU59
  86:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   const struct ip_hdr *iphdr_in;
 254              		.loc 1 86 3 view .LVU60
  87:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   u16_t hlen;
 255              		.loc 1 87 3 view .LVU61
  88:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   const ip4_addr_t *src;
 256              		.loc 1 88 3 view .LVU62
ARM GAS  /tmp/ccO3pRIZ.s 			page 13


  89:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 257              		.loc 1 89 3 view .LVU63
  91:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpinmsgs);
 258              		.loc 1 91 28 view .LVU64
  92:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 259              		.loc 1 92 34 view .LVU65
  94:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   hlen = IPH_HL_BYTES(iphdr_in);
 260              		.loc 1 94 3 view .LVU66
  94:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   hlen = IPH_HL_BYTES(iphdr_in);
 261              		.loc 1 94 12 is_stmt 0 view .LVU67
 262 0008 524B     		ldr	r3, .L31
 263 000a 9E68     		ldr	r6, [r3, #8]
 264              	.LVL19:
  95:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   if (hlen < IP_HLEN) {
 265              		.loc 1 95 3 is_stmt 1 view .LVU68
  95:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   if (hlen < IP_HLEN) {
 266              		.loc 1 95 10 is_stmt 0 view .LVU69
 267 000c 3478     		ldrb	r4, [r6]	@ zero_extendqisi2
 268 000e 04F00F04 		and	r4, r4, #15
 269 0012 A400     		lsls	r4, r4, #2
 270              	.LVL20:
  96:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short IP header (%"S16_F" bytes) received\n", hlen));
 271              		.loc 1 96 3 is_stmt 1 view .LVU70
  96:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short IP header (%"S16_F" bytes) received\n", hlen));
 272              		.loc 1 96 6 is_stmt 0 view .LVU71
 273 0014 132C     		cmp	r4, #19
 274 0016 16D9     		bls	.L11
 275 0018 0F46     		mov	r7, r1
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short ICMP (%"U16_F" bytes) received\n", p->tot_len));
 276              		.loc 1 100 3 is_stmt 1 view .LVU72
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short ICMP (%"U16_F" bytes) received\n", p->tot_len));
 277              		.loc 1 100 8 is_stmt 0 view .LVU73
 278 001a 4389     		ldrh	r3, [r0, #10]
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: short ICMP (%"U16_F" bytes) received\n", p->tot_len));
 279              		.loc 1 100 6 view .LVU74
 280 001c 032B     		cmp	r3, #3
 281 001e 12D9     		bls	.L11
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_DEBUG
 282              		.loc 1 105 3 is_stmt 1 view .LVU75
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_DEBUG
 283              		.loc 1 105 21 is_stmt 0 view .LVU76
 284 0020 4368     		ldr	r3, [r0, #4]
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #ifdef LWIP_DEBUG
 285              		.loc 1 105 8 view .LVU77
 286 0022 1B78     		ldrb	r3, [r3]	@ zero_extendqisi2
 287              	.LVL21:
 111:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****     case ICMP_ER:
 288              		.loc 1 111 3 is_stmt 1 view .LVU78
 289 0024 082B     		cmp	r3, #8
 290 0026 40F08680 		bne	.L12
 118:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       src = ip4_current_dest_addr();
 291              		.loc 1 118 39 view .LVU79
 119:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       /* multicast destination address? */
 292              		.loc 1 119 7 view .LVU80
 293              	.LVL22:
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_MULTICAST_PING
 294              		.loc 1 121 7 view .LVU81
ARM GAS  /tmp/ccO3pRIZ.s 			page 14


 121:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_MULTICAST_PING
 295              		.loc 1 121 11 is_stmt 0 view .LVU82
 296 002a 4A4B     		ldr	r3, .L31
 297 002c 5869     		ldr	r0, [r3, #20]
 298              	.LVL23:
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_MULTICAST_PING
 299              		.loc 1 121 11 view .LVU83
 300 002e 00F0F003 		and	r3, r0, #240
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_MULTICAST_PING
 301              		.loc 1 121 10 view .LVU84
 302 0032 E02B     		cmp	r3, #224
 303 0034 1ED0     		beq	.L13
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_BROADCAST_PING
 304              		.loc 1 131 7 is_stmt 1 view .LVU85
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_BROADCAST_PING
 305              		.loc 1 131 11 is_stmt 0 view .LVU86
 306 0036 474B     		ldr	r3, .L31
 307 0038 1968     		ldr	r1, [r3]
 308              	.LVL24:
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_BROADCAST_PING
 309              		.loc 1 131 11 view .LVU87
 310 003a FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 311              	.LVL25:
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_BROADCAST_PING
 312              		.loc 1 131 10 discriminator 1 view .LVU88
 313 003e C8B9     		cbnz	r0, .L13
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (p->tot_len < sizeof(struct icmp_echo_hdr)) {
 314              		.loc 1 140 54 is_stmt 1 view .LVU89
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: bad ICMP echo received\n"));
 315              		.loc 1 141 7 view .LVU90
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: bad ICMP echo received\n"));
 316              		.loc 1 141 12 is_stmt 0 view .LVU91
 317 0040 2B89     		ldrh	r3, [r5, #8]
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: bad ICMP echo received\n"));
 318              		.loc 1 141 10 view .LVU92
 319 0042 072B     		cmp	r3, #7
 320 0044 05D8     		bhi	.L24
 321              	.LVL26:
 322              	.L11:
 285:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ICMP_STATS_INC(icmp.lenerr);
 323              		.loc 1 285 3 is_stmt 1 view .LVU93
 324 0046 2846     		mov	r0, r5
 325 0048 FFF7FEFF 		bl	pbuf_free
 326              	.LVL27:
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpinerrors);
 327              		.loc 1 286 30 view .LVU94
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   return;
 328              		.loc 1 287 36 view .LVU95
 288:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN || !LWIP_MULTICAST_PING || !LWIP_BROADCAST_PING
 329              		.loc 1 288 3 view .LVU96
 330              	.L10:
 296:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 331              		.loc 1 296 1 is_stmt 0 view .LVU97
 332 004c 04B0     		add	sp, sp, #16
 333              	.LCFI6:
 334              		.cfi_remember_state
 335              		.cfi_def_cfa_offset 24
ARM GAS  /tmp/ccO3pRIZ.s 			page 15


 336              		@ sp needed
 337 004e BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 338              	.LVL28:
 339              	.L24:
 340              	.LCFI7:
 341              		.cfi_restore_state
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: checksum failed for received ICMP echo\n"));
 342              		.loc 1 147 9 is_stmt 1 view .LVU98
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: checksum failed for received ICMP echo\n"));
 343              		.loc 1 147 13 is_stmt 0 view .LVU99
 344 0052 2846     		mov	r0, r5
 345 0054 FFF7FEFF 		bl	inet_chksum_pbuf
 346              	.LVL29:
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: checksum failed for received ICMP echo\n"));
 347              		.loc 1 147 12 discriminator 1 view .LVU100
 348 0058 80B9     		cbnz	r0, .L25
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* p is not big enough to contain link headers
 349              		.loc 1 157 7 is_stmt 1 view .LVU101
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* p is not big enough to contain link headers
 350              		.loc 1 157 52 is_stmt 0 view .LVU102
 351 005a 04F10E08 		add	r8, r4, #14
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* p is not big enough to contain link headers
 352              		.loc 1 157 11 view .LVU103
 353 005e 4146     		mov	r1, r8
 354 0060 2846     		mov	r0, r5
 355 0062 FFF7FEFF 		bl	pbuf_add_header
 356              	.LVL30:
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* p is not big enough to contain link headers
 357              		.loc 1 157 10 discriminator 1 view .LVU104
 358 0066 0028     		cmp	r0, #0
 359 0068 3ED0     		beq	.L16
 360              	.LBB2:
 161:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         u16_t alloc_len = (u16_t)(p->tot_len + hlen);
 361              		.loc 1 161 9 is_stmt 1 view .LVU105
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (alloc_len < p->tot_len) {
 362              		.loc 1 162 9 view .LVU106
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (alloc_len < p->tot_len) {
 363              		.loc 1 162 36 is_stmt 0 view .LVU107
 364 006a 2B89     		ldrh	r3, [r5, #8]
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (alloc_len < p->tot_len) {
 365              		.loc 1 162 15 view .LVU108
 366 006c 1919     		adds	r1, r3, r4
 367 006e 89B2     		uxth	r1, r1
 368              	.LVL31:
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed (tot_len overflow)\n"));
 369              		.loc 1 163 9 is_stmt 1 view .LVU109
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed (tot_len overflow)\n"));
 370              		.loc 1 163 12 is_stmt 0 view .LVU110
 371 0070 8B42     		cmp	r3, r1
 372 0072 07D9     		bls	.L26
 373              	.LVL32:
 374              	.L13:
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed (tot_len overflow)\n"));
 375              		.loc 1 163 12 view .LVU111
 376              	.LBE2:
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   ICMP_STATS_INC(icmp.err);
 377              		.loc 1 291 3 is_stmt 1 view .LVU112
ARM GAS  /tmp/ccO3pRIZ.s 			page 16


 378 0074 2846     		mov	r0, r5
 379 0076 FFF7FEFF 		bl	pbuf_free
 380              	.LVL33:
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpinerrors);
 381              		.loc 1 292 27 view .LVU113
 293:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   return;
 382              		.loc 1 293 36 view .LVU114
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #endif /* LWIP_ICMP_ECHO_CHECK_INPUT_PBUF_LEN || !LWIP_MULTICAST_PING || !LWIP_BROADCAST_PING */
 383              		.loc 1 294 3 view .LVU115
 384 007a E7E7     		b	.L10
 385              	.L25:
 148:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(p);
 386              		.loc 1 148 92 view .LVU116
 149:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           ICMP_STATS_INC(icmp.chkerr);
 387              		.loc 1 149 11 view .LVU117
 388 007c 2846     		mov	r0, r5
 389 007e FFF7FEFF 		bl	pbuf_free
 390              	.LVL34:
 150:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           MIB2_STATS_INC(mib2.icmpinerrors);
 391              		.loc 1 150 38 view .LVU118
 151:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           return;
 392              		.loc 1 151 44 view .LVU119
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 393              		.loc 1 152 11 view .LVU120
 394 0082 E3E7     		b	.L10
 395              	.LVL35:
 396              	.L26:
 397              	.LBB3:
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (r == NULL) {
 398              		.loc 1 168 9 view .LVU121
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         if (r == NULL) {
 399              		.loc 1 168 13 is_stmt 0 view .LVU122
 400 0084 4FF42072 		mov	r2, #640
 401 0088 0E20     		movs	r0, #14
 402 008a FFF7FEFF 		bl	pbuf_alloc
 403              	.LVL36:
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed\n"));
 404              		.loc 1 169 9 is_stmt 1 view .LVU123
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: allocating new pbuf failed\n"));
 405              		.loc 1 169 12 is_stmt 0 view .LVU124
 406 008e 8046     		mov	r8, r0
 407 0090 0028     		cmp	r0, #0
 408 0092 EFD0     		beq	.L13
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("first pbuf cannot hold the ICMP header
 409              		.loc 1 173 9 is_stmt 1 view .LVU125
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("first pbuf cannot hold the ICMP header
 410              		.loc 1 173 14 is_stmt 0 view .LVU126
 411 0094 4289     		ldrh	r2, [r0, #10]
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("first pbuf cannot hold the ICMP header
 412              		.loc 1 173 27 view .LVU127
 413 0096 04F10803 		add	r3, r4, #8
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("first pbuf cannot hold the ICMP header
 414              		.loc 1 173 12 view .LVU128
 415 009a 9A42     		cmp	r2, r3
 416 009c 13D3     		bcc	.L27
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* switch r->payload back to icmp header (cannot fail) */
 417              		.loc 1 179 9 is_stmt 1 view .LVU129
ARM GAS  /tmp/ccO3pRIZ.s 			page 17


 418 009e 2246     		mov	r2, r4
 419 00a0 3146     		mov	r1, r6
 420 00a2 4068     		ldr	r0, [r0, #4]
 421              	.LVL37:
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* switch r->payload back to icmp header (cannot fail) */
 422              		.loc 1 179 9 is_stmt 0 view .LVU130
 423 00a4 FFF7FEFF 		bl	memcpy
 424              	.LVL38:
 181:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: moving r->payload to icmp header failed\n", 0);
 425              		.loc 1 181 9 is_stmt 1 view .LVU131
 181:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: moving r->payload to icmp header failed\n", 0);
 426              		.loc 1 181 13 is_stmt 0 view .LVU132
 427 00a8 2146     		mov	r1, r4
 428 00aa 4046     		mov	r0, r8
 429 00ac FFF7FEFF 		bl	pbuf_remove_header
 430              	.LVL39:
 181:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: moving r->payload to icmp header failed\n", 0);
 431              		.loc 1 181 12 discriminator 1 view .LVU133
 432 00b0 60B9     		cbnz	r0, .L28
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("icmp_input: copying to new pbuf failed
 433              		.loc 1 187 9 is_stmt 1 view .LVU134
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("icmp_input: copying to new pbuf failed
 434              		.loc 1 187 13 is_stmt 0 view .LVU135
 435 00b2 2946     		mov	r1, r5
 436 00b4 4046     		mov	r0, r8
 437 00b6 FFF7FEFF 		bl	pbuf_copy
 438              	.LVL40:
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("icmp_input: copying to new pbuf failed
 439              		.loc 1 187 12 discriminator 1 view .LVU136
 440 00ba 88B9     		cbnz	r0, .L29
 193:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* we now have an identical copy of p that has room for link headers */
 441              		.loc 1 193 9 is_stmt 1 view .LVU137
 442 00bc 2846     		mov	r0, r5
 443 00be FFF7FEFF 		bl	pbuf_free
 444              	.LVL41:
 195:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else {
 445              		.loc 1 195 9 view .LVU138
 195:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       } else {
 446              		.loc 1 195 11 is_stmt 0 view .LVU139
 447 00c2 4546     		mov	r5, r8
 448              	.LBE3:
 449 00c4 15E0     		b	.L20
 450              	.LVL42:
 451              	.L27:
 452              	.LBB4:
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 453              		.loc 1 174 103 is_stmt 1 view .LVU140
 175:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 454              		.loc 1 175 11 view .LVU141
 455 00c6 FFF7FEFF 		bl	pbuf_free
 456              	.LVL43:
 176:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 457              		.loc 1 176 11 view .LVU142
 458 00ca D3E7     		b	.L13
 459              	.L28:
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 460              		.loc 1 182 11 view .LVU143
ARM GAS  /tmp/ccO3pRIZ.s 			page 18


 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 461              		.loc 1 182 11 view .LVU144
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 462              		.loc 1 182 11 discriminator 1 view .LVU145
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 463              		.loc 1 182 11 discriminator 1 view .LVU146
 464 00cc 224B     		ldr	r3, .L31+4
 465 00ce B622     		movs	r2, #182
 466 00d0 2249     		ldr	r1, .L31+8
 467 00d2 2348     		ldr	r0, .L31+12
 468 00d4 FFF7FEFF 		bl	printf
 469              	.LVL44:
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 470              		.loc 1 182 11 discriminator 3 view .LVU147
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 471              		.loc 1 182 11 discriminator 3 view .LVU148
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 472              		.loc 1 183 11 view .LVU149
 473 00d8 4046     		mov	r0, r8
 474 00da FFF7FEFF 		bl	pbuf_free
 475              	.LVL45:
 184:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 476              		.loc 1 184 11 view .LVU150
 477 00de C9E7     		b	.L13
 478              	.L29:
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           pbuf_free(r);
 479              		.loc 1 188 103 view .LVU151
 189:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 480              		.loc 1 189 11 view .LVU152
 481 00e0 4046     		mov	r0, r8
 482 00e2 FFF7FEFF 		bl	pbuf_free
 483              	.LVL46:
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 484              		.loc 1 190 11 view .LVU153
 485 00e6 C5E7     		b	.L13
 486              	.LVL47:
 487              	.L16:
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 488              		.loc 1 190 11 is_stmt 0 view .LVU154
 489              	.LBE4:
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: restoring original p->payload failed\n", 0);
 490              		.loc 1 198 9 is_stmt 1 view .LVU155
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: restoring original p->payload failed\n", 0);
 491              		.loc 1 198 13 is_stmt 0 view .LVU156
 492 00e8 4146     		mov	r1, r8
 493 00ea 2846     		mov	r0, r5
 494 00ec FFF7FEFF 		bl	pbuf_remove_header
 495              	.LVL48:
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_ASSERT("icmp_input: restoring original p->payload failed\n", 0);
 496              		.loc 1 198 12 discriminator 1 view .LVU157
 497 00f0 28BB     		cbnz	r0, .L30
 498              	.L20:
 207:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (pbuf_add_header(p, hlen)) {
 499              		.loc 1 207 7 is_stmt 1 view .LVU158
 207:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       if (pbuf_add_header(p, hlen)) {
 500              		.loc 1 207 13 is_stmt 0 view .LVU159
 501 00f2 6E68     		ldr	r6, [r5, #4]
ARM GAS  /tmp/ccO3pRIZ.s 			page 19


 502              	.LVL49:
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("Can't move over header in packet"));
 503              		.loc 1 208 7 is_stmt 1 view .LVU160
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("Can't move over header in packet"));
 504              		.loc 1 208 11 is_stmt 0 view .LVU161
 505 00f4 2146     		mov	r1, r4
 506 00f6 2846     		mov	r0, r5
 507 00f8 FFF7FEFF 		bl	pbuf_add_header
 508              	.LVL50:
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         LWIP_DEBUGF(ICMP_DEBUG | LWIP_DBG_LEVEL_SERIOUS, ("Can't move over header in packet"));
 509              		.loc 1 208 10 discriminator 1 view .LVU162
 510 00fc D8B9     		cbnz	r0, .L12
 511              	.LBB5:
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         struct ip_hdr *iphdr = (struct ip_hdr *)p->payload;
 512              		.loc 1 211 9 is_stmt 1 view .LVU163
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ip4_addr_copy(iphdr->src, *src);
 513              		.loc 1 212 9 view .LVU164
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ip4_addr_copy(iphdr->src, *src);
 514              		.loc 1 212 24 is_stmt 0 view .LVU165
 515 00fe 6968     		ldr	r1, [r5, #4]
 516              	.LVL51:
 213:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ip4_addr_copy(iphdr->dest, *ip4_current_src_addr());
 517              		.loc 1 213 9 is_stmt 1 view .LVU166
 518 0100 144B     		ldr	r3, .L31
 519 0102 5A69     		ldr	r2, [r3, #20]
 520 0104 CA60     		str	r2, [r1, #12]	@ unaligned
 214:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         ICMPH_TYPE_SET(iecho, ICMP_ER);
 521              		.loc 1 214 9 view .LVU167
 522 0106 1B69     		ldr	r3, [r3, #16]
 523 0108 0B61     		str	r3, [r1, #16]	@ unaligned
 215:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if CHECKSUM_GEN_ICMP
 524              		.loc 1 215 9 view .LVU168
 525 010a 0023     		movs	r3, #0
 526 010c 3370     		strb	r3, [r6]
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****             iecho->chksum = (u16_t)(iecho->chksum + PP_HTONS((u16_t)(ICMP_ECHO << 8)) + 1);
 527              		.loc 1 219 11 view .LVU169
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****             iecho->chksum = (u16_t)(iecho->chksum + PP_HTONS((u16_t)(ICMP_ECHO << 8)) + 1);
 528              		.loc 1 219 20 is_stmt 0 view .LVU170
 529 010e 7388     		ldrh	r3, [r6, #2]	@ unaligned
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****             iecho->chksum = (u16_t)(iecho->chksum + PP_HTONS((u16_t)(ICMP_ECHO << 8)) + 1);
 530              		.loc 1 219 14 view .LVU171
 531 0110 4FF6F772 		movw	r2, #65527
 532 0114 9342     		cmp	r3, r2
 533 0116 19D9     		bls	.L21
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           } else {
 534              		.loc 1 220 13 is_stmt 1 view .LVU172
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           } else {
 535              		.loc 1 220 29 is_stmt 0 view .LVU173
 536 0118 0933     		adds	r3, r3, #9
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           } else {
 537              		.loc 1 220 27 view .LVU174
 538 011a 7380     		strh	r3, [r6, #2]	@ unaligned
 539              	.L22:
 235:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         IPH_CHKSUM_SET(iphdr, 0);
 540              		.loc 1 235 9 is_stmt 1 view .LVU175
 541 011c FF23     		movs	r3, #255
 542 011e 0B72     		strb	r3, [r1, #8]
ARM GAS  /tmp/ccO3pRIZ.s 			page 20


 236:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** #if CHECKSUM_GEN_IP
 543              		.loc 1 236 9 view .LVU176
 544 0120 0022     		movs	r2, #0
 545 0122 8A72     		strb	r2, [r1, #10]
 546 0124 CA72     		strb	r2, [r1, #11]
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* increase number of messages attempted to send */
 547              		.loc 1 243 34 view .LVU177
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         /* increase number of echo replies attempted to send */
 548              		.loc 1 245 41 view .LVU178
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 549              		.loc 1 247 45 view .LVU179
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                             ICMP_TTL, 0, IP_PROTO_ICMP, inp);
 550              		.loc 1 250 9 view .LVU180
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                             ICMP_TTL, 0, IP_PROTO_ICMP, inp);
 551              		.loc 1 250 15 is_stmt 0 view .LVU181
 552 0126 0297     		str	r7, [sp, #8]
 553 0128 0121     		movs	r1, #1
 554              	.LVL52:
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                             ICMP_TTL, 0, IP_PROTO_ICMP, inp);
 555              		.loc 1 250 15 view .LVU182
 556 012a 0191     		str	r1, [sp, #4]
 557 012c 0092     		str	r2, [sp]
 558 012e 0D49     		ldr	r1, .L31+16
 559              	.LVL53:
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****                             ICMP_TTL, 0, IP_PROTO_ICMP, inp);
 560              		.loc 1 250 15 view .LVU183
 561 0130 2846     		mov	r0, r5
 562 0132 FFF7FEFF 		bl	ip4_output_if
 563              	.LVL54:
 252:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: ip_output_if returned an error: %s\n", lwip_strerr(
 564              		.loc 1 252 9 is_stmt 1 view .LVU184
 565              	.L12:
 252:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           LWIP_DEBUGF(ICMP_DEBUG, ("icmp_input: ip_output_if returned an error: %s\n", lwip_strerr(
 566              		.loc 1 252 9 is_stmt 0 view .LVU185
 567              	.LBE5:
 275:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       }
 568              		.loc 1 275 48 is_stmt 1 view .LVU186
 278:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       ICMP_STATS_INC(icmp.proterr);
 569              		.loc 1 278 58 view .LVU187
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****       ICMP_STATS_INC(icmp.drop);
 570              		.loc 1 279 35 view .LVU188
 280:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   }
 571              		.loc 1 280 32 view .LVU189
 282:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   return;
 572              		.loc 1 282 3 view .LVU190
 573 0136 2846     		mov	r0, r5
 574 0138 FFF7FEFF 		bl	pbuf_free
 575              	.LVL55:
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** lenerr:
 576              		.loc 1 283 3 view .LVU191
 577 013c 86E7     		b	.L10
 578              	.LVL56:
 579              	.L30:
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 580              		.loc 1 199 11 view .LVU192
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 581              		.loc 1 199 11 view .LVU193
ARM GAS  /tmp/ccO3pRIZ.s 			page 21


 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 582              		.loc 1 199 11 discriminator 1 view .LVU194
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 583              		.loc 1 199 11 discriminator 1 view .LVU195
 584 013e 064B     		ldr	r3, .L31+4
 585 0140 C722     		movs	r2, #199
 586 0142 0949     		ldr	r1, .L31+20
 587 0144 0648     		ldr	r0, .L31+12
 588 0146 FFF7FEFF 		bl	printf
 589              	.LVL57:
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 590              		.loc 1 199 11 discriminator 3 view .LVU196
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           goto icmperr;
 591              		.loc 1 199 11 discriminator 3 view .LVU197
 200:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****         }
 592              		.loc 1 200 11 view .LVU198
 593 014a 93E7     		b	.L13
 594              	.LVL58:
 595              	.L21:
 596              	.LBB6:
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           }
 597              		.loc 1 222 13 view .LVU199
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           }
 598              		.loc 1 222 29 is_stmt 0 view .LVU200
 599 014c 0833     		adds	r3, r3, #8
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****           }
 600              		.loc 1 222 27 view .LVU201
 601 014e 7380     		strh	r3, [r6, #2]	@ unaligned
 602 0150 E4E7     		b	.L22
 603              	.L32:
 604 0152 00BF     		.align	2
 605              	.L31:
 606 0154 00000000 		.word	ip_data
 607 0158 00000000 		.word	.LC0
 608 015c 00000000 		.word	.LC3
 609 0160 60000000 		.word	.LC2
 610 0164 14000000 		.word	ip_data+20
 611 0168 38000000 		.word	.LC4
 612              	.LBE6:
 613              		.cfi_endproc
 614              	.LFE170:
 616              		.section	.text.icmp_dest_unreach,"ax",%progbits
 617              		.align	1
 618              		.global	icmp_dest_unreach
 619              		.syntax unified
 620              		.thumb
 621              		.thumb_func
 623              	icmp_dest_unreach:
 624              	.LVL59:
 625              	.LFB171:
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpoutdestunreachs);
 626              		.loc 1 309 1 is_stmt 1 view -0
 627              		.cfi_startproc
 628              		@ args = 0, pretend = 0, frame = 0
 629              		@ frame_needed = 0, uses_anonymous_args = 0
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpoutdestunreachs);
 630              		.loc 1 309 1 is_stmt 0 view .LVU203
ARM GAS  /tmp/ccO3pRIZ.s 			page 22


 631 0000 08B5     		push	{r3, lr}
 632              	.LCFI8:
 633              		.cfi_def_cfa_offset 8
 634              		.cfi_offset 3, -8
 635              		.cfi_offset 14, -4
 636 0002 0A46     		mov	r2, r1
 310:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmp_send_response(p, ICMP_DUR, t);
 637              		.loc 1 310 43 is_stmt 1 view .LVU204
 311:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 638              		.loc 1 311 3 view .LVU205
 639 0004 0321     		movs	r1, #3
 640              	.LVL60:
 311:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 641              		.loc 1 311 3 is_stmt 0 view .LVU206
 642 0006 FFF7FEFF 		bl	icmp_send_response
 643              	.LVL61:
 312:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 644              		.loc 1 312 1 view .LVU207
 645 000a 08BD     		pop	{r3, pc}
 646              		.cfi_endproc
 647              	.LFE171:
 649              		.section	.text.icmp_time_exceeded,"ax",%progbits
 650              		.align	1
 651              		.global	icmp_time_exceeded
 652              		.syntax unified
 653              		.thumb
 654              		.thumb_func
 656              	icmp_time_exceeded:
 657              	.LVL62:
 658              	.LFB172:
 324:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpouttimeexcds);
 659              		.loc 1 324 1 is_stmt 1 view -0
 660              		.cfi_startproc
 661              		@ args = 0, pretend = 0, frame = 0
 662              		@ frame_needed = 0, uses_anonymous_args = 0
 324:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   MIB2_STATS_INC(mib2.icmpouttimeexcds);
 663              		.loc 1 324 1 is_stmt 0 view .LVU209
 664 0000 08B5     		push	{r3, lr}
 665              	.LCFI9:
 666              		.cfi_def_cfa_offset 8
 667              		.cfi_offset 3, -8
 668              		.cfi_offset 14, -4
 669 0002 0A46     		mov	r2, r1
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c ****   icmp_send_response(p, ICMP_TE, t);
 670              		.loc 1 325 40 is_stmt 1 view .LVU210
 326:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 671              		.loc 1 326 3 view .LVU211
 672 0004 0B21     		movs	r1, #11
 673              	.LVL63:
 326:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** }
 674              		.loc 1 326 3 is_stmt 0 view .LVU212
 675 0006 FFF7FEFF 		bl	icmp_send_response
 676              	.LVL64:
 327:Middlewares/Third_Party/LwIP/src/core/ipv4/icmp.c **** 
 677              		.loc 1 327 1 view .LVU213
 678 000a 08BD     		pop	{r3, pc}
 679              		.cfi_endproc
ARM GAS  /tmp/ccO3pRIZ.s 			page 23


 680              	.LFE172:
 682              		.text
 683              	.Letext0:
 684              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 685              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 686              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 687              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 688              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 689              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 690              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 691              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 692              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 693              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/icmp.h"
 694              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/icmp.h"
 695              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ip4.h"
 696              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/ip.h"
 697              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/inet_chksum.h"
 698              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4.h"
 699              		.file 17 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 700              		.file 18 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
 701              		.file 19 "<built-in>"
ARM GAS  /tmp/ccO3pRIZ.s 			page 24


DEFINED SYMBOLS
                            *ABS*:00000000 icmp.c
     /tmp/ccO3pRIZ.s:20     .rodata.icmp_send_response.str1.4:00000000 $d
     /tmp/ccO3pRIZ.s:30     .text.icmp_send_response:00000000 $t
     /tmp/ccO3pRIZ.s:35     .text.icmp_send_response:00000000 icmp_send_response
     /tmp/ccO3pRIZ.s:210    .text.icmp_send_response:000000ac $d
     /tmp/ccO3pRIZ.s:217    .rodata.icmp_input.str1.4:00000000 $d
     /tmp/ccO3pRIZ.s:226    .text.icmp_input:00000000 $t
     /tmp/ccO3pRIZ.s:232    .text.icmp_input:00000000 icmp_input
     /tmp/ccO3pRIZ.s:606    .text.icmp_input:00000154 $d
     /tmp/ccO3pRIZ.s:617    .text.icmp_dest_unreach:00000000 $t
     /tmp/ccO3pRIZ.s:623    .text.icmp_dest_unreach:00000000 icmp_dest_unreach
     /tmp/ccO3pRIZ.s:650    .text.icmp_time_exceeded:00000000 $t
     /tmp/ccO3pRIZ.s:656    .text.icmp_time_exceeded:00000000 icmp_time_exceeded

UNDEFINED SYMBOLS
pbuf_alloc
ip4_route
inet_chksum
ip4_output_if
pbuf_free
printf
ip4_addr_isbroadcast_u32
inet_chksum_pbuf
pbuf_add_header
memcpy
pbuf_remove_header
pbuf_copy
ip_data
