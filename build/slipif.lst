ARM GAS  /tmp/cczEqG5I.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"slipif.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/netif/slipif.c"
  19              		.section	.rodata.slipif_rxbyte.str1.4,"aMS",%progbits,1
  20              		.align	2
  21              	.LC0:
  22 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/netif/slipif.c\000"
  22      6C657761 
  22      7265732F 
  22      54686972 
  22      645F5061 
  23              		.align	2
  24              	.LC1:
  25 0030 6E657469 		.ascii	"netif != NULL\000"
  25      6620213D 
  25      204E554C 
  25      4C00
  26 003e 0000     		.align	2
  27              	.LC2:
  28 0040 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  28      7274696F 
  28      6E202225 
  28      73222066 
  28      61696C65 
  29              		.align	2
  30              	.LC3:
  31 0068 6E657469 		.ascii	"netif->state != NULL\000"
  31      662D3E73 
  31      74617465 
  31      20213D20 
  31      4E554C4C 
  32              		.section	.text.slipif_rxbyte,"ax",%progbits
  33              		.align	1
  34              		.syntax unified
  35              		.thumb
  36              		.thumb_func
  38              	slipif_rxbyte:
  39              	.LVL0:
  40              	.LFB176:
   1:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
   2:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * SLIP Interface
ARM GAS  /tmp/cczEqG5I.s 			page 2


   4:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
   5:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
   6:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
   7:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /*
   8:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Copyright (c) 2001-2004 Swedish Institute of Computer Science.
   9:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * All rights reserved.
  10:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  11:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Redistribution and use in source and binary forms, with or without
  12:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * modification, are permitted provided that the following conditions
  13:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * are met:
  14:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * 1. Redistributions of source code must retain the above copyright
  15:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *    notice, this list of conditions and the following disclaimer.
  16:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * 2. Redistributions in binary form must reproduce the above copyright
  17:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *    notice, this list of conditions and the following disclaimer in the
  18:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *    documentation and/or other materials provided with the distribution.
  19:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * 3. Neither the name of the Institute nor the names of its contributors
  20:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *    may be used to endorse or promote products derived from this software
  21:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *    without specific prior written permission.
  22:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  23:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * THIS SOFTWARE IS PROVIDED BY THE INSTITUTE AND CONTRIBUTORS ``AS IS'' AND
  24:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  25:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
  26:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * ARE DISCLAIMED.  IN NO EVENT SHALL THE INSTITUTE OR CONTRIBUTORS BE LIABLE
  27:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  28:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
  29:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
  30:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
  31:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
  32:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
  33:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * SUCH DAMAGE.
  34:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  35:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * This file is built upon the file: src/arch/rtxc/netif/sioslip.c
  36:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  37:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Author: Magnus Ivarsson <magnus.ivarsson(at)volvo.com>
  38:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *         Simon Goldschmidt
  39:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
  40:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  41:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  42:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
  43:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @defgroup slipif SLIP
  44:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @ingroup netifs
  45:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  46:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * This is an arch independent SLIP netif. The specific serial hooks must be
  47:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * provided by another file. They are sio_open, sio_read/sio_tryread and sio_send
  48:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  49:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Usage: This netif can be used in three ways:\n
  50:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *        1) For NO_SYS==0, an RX thread can be used which blocks on sio_read()
  51:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *           until data is received.\n
  52:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *        2) In your main loop, call slipif_poll() to check for new RX bytes,
  53:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *           completed packets are fed into netif->input().\n
  54:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *        3) Call slipif_received_byte[s]() from your serial RX ISR and
  55:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *           slipif_process_rxqueue() from your main loop. ISR level decodes
  56:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *           packets and puts completed packets on a queue which is fed into
  57:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *           the stack from the main loop (needs SYS_LIGHTWEIGHT_PROT for
  58:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *           pbuf_alloc to work on ISR level!).
  59:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
  60:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
ARM GAS  /tmp/cczEqG5I.s 			page 3


  61:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  62:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "netif/slipif.h"
  63:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/opt.h"
  64:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  65:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/def.h"
  66:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/pbuf.h"
  67:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/stats.h"
  68:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/snmp.h"
  69:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/sys.h"
  70:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #include "lwip/sio.h"
  71:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  72:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #define SLIP_END     0xC0 /* 0300: start and end of every packet */
  73:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #define SLIP_ESC     0xDB /* 0333: escape start (one byte escaped data follows) */
  74:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #define SLIP_ESC_END 0xDC /* 0334: following escape: original byte is 0xC0 (END) */
  75:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #define SLIP_ESC_ESC 0xDD /* 0335: following escape: original byte is 0xDB (ESC) */
  76:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  77:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /** Maximum packet size that is received by this netif */
  78:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #ifndef SLIP_MAX_SIZE
  79:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #define SLIP_MAX_SIZE 1500
  80:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif
  81:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  82:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /** Define this to the interface speed for SNMP
  83:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * (sio_fd is the sio_fd_t returned by sio_open).
  84:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * The default value of zero means 'unknown'.
  85:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
  86:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #ifndef SLIP_SIO_SPEED
  87:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #define SLIP_SIO_SPEED(sio_fd) 0
  88:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif
  89:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  90:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** enum slipif_recv_state {
  91:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   SLIP_RECV_NORMAL,
  92:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   SLIP_RECV_ESCAPE
  93:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** };
  94:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
  95:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** struct slipif_priv {
  96:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   sio_fd_t sd;
  97:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* q is the whole pbuf chain for a packet, p is the current pbuf in the chain */
  98:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct pbuf *p, *q;
  99:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u8_t state;
 100:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u16_t i, recved;
 101:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if SLIP_RX_FROM_ISR
 102:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct pbuf *rxpackets;
 103:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif
 104:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** };
 105:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 106:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 107:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Send a pbuf doing the necessary SLIP encapsulation
 108:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 109:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Uses the serial layer's sio_send()
 110:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 111:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif the lwip network interface structure for this slipif
 112:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param p the pbuf chain packet to send
 113:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @return always returns ERR_OK since the serial layer does not provide return values
 114:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 115:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** static err_t
 116:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_output(struct netif *netif, struct pbuf *p)
 117:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
ARM GAS  /tmp/cczEqG5I.s 			page 4


 118:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv;
 119:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct pbuf *q;
 120:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u16_t i;
 121:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u8_t c;
 122:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif != NULL", (netif != NULL));
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
 126:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 127:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_DEBUGF(SLIP_DEBUG, ("slipif_output: sending %"U16_F" bytes\n", p->tot_len));
 128:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv = (struct slipif_priv *)netif->state;
 129:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 130:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* Send pbuf out on the serial I/O device. */
 131:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* Start with packet delimiter. */
 132:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   sio_send(SLIP_END, priv->sd);
 133:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 134:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   for (q = p; q != NULL; q = q->next) {
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     for (i = 0; i < q->len; i++) {
 136:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
 137:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       switch (c) {
 138:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_END:
 139:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           /* need to escape this byte (0xC0 -> 0xDB, 0xDC) */
 140:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC, priv->sd);
 141:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC_END, priv->sd);
 142:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 143:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_ESC:
 144:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           /* need to escape this byte (0xDB -> 0xDB, 0xDD) */
 145:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC, priv->sd);
 146:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC_ESC, priv->sd);
 147:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 148:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         default:
 149:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           /* normal byte - no need for escaping */
 150:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(c, priv->sd);
 151:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 152:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       }
 153:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 154:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 155:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* End with packet delimiter. */
 156:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   sio_send(SLIP_END, priv->sd);
 157:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return ERR_OK;
 158:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 159:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 160:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if LWIP_IPV4
 161:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 162:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Send a pbuf doing the necessary SLIP encapsulation
 163:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 164:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Uses the serial layer's sio_send()
 165:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 166:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif the lwip network interface structure for this slipif
 167:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param p the pbuf chain packet to send
 168:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param ipaddr the ip address to send the packet to (not used for slipif)
 169:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @return always returns ERR_OK since the serial layer does not provide return values
 170:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 171:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** static err_t
 172:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_output_v4(struct netif *netif, struct pbuf *p, const ip4_addr_t *ipaddr)
 173:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
 174:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_UNUSED_ARG(ipaddr);
ARM GAS  /tmp/cczEqG5I.s 			page 5


 175:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return slipif_output(netif, p);
 176:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 177:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* LWIP_IPV4 */
 178:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 179:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if LWIP_IPV6
 180:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 181:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Send a pbuf doing the necessary SLIP encapsulation
 182:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 183:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Uses the serial layer's sio_send()
 184:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 185:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif the lwip network interface structure for this slipif
 186:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param p the pbuf chain packet to send
 187:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param ipaddr the ip address to send the packet to (not used for slipif)
 188:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @return always returns ERR_OK since the serial layer does not provide return values
 189:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 190:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** static err_t
 191:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_output_v6(struct netif *netif, struct pbuf *p, const ip6_addr_t *ipaddr)
 192:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
 193:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_UNUSED_ARG(ipaddr);
 194:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return slipif_output(netif, p);
 195:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 196:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* LWIP_IPV6 */
 197:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 198:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 199:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Handle the incoming SLIP stream character by character
 200:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 201:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif the lwip network interface structure for this slipif
 202:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param c received character (multiple calls to this function will
 203:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *        return a complete packet, NULL is returned before - used for polling)
 204:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @return The IP packet when SLIP_END is received
 205:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 206:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** static struct pbuf *
 207:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_rxbyte(struct netif *netif, u8_t c)
 208:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
  41              		.loc 1 208 1 view -0
  42              		.cfi_startproc
  43              		@ args = 0, pretend = 0, frame = 0
  44              		@ frame_needed = 0, uses_anonymous_args = 0
  45              		.loc 1 208 1 is_stmt 0 view .LVU1
  46 0000 38B5     		push	{r3, r4, r5, lr}
  47              	.LCFI0:
  48              		.cfi_def_cfa_offset 16
  49              		.cfi_offset 3, -16
  50              		.cfi_offset 4, -12
  51              		.cfi_offset 5, -8
  52              		.cfi_offset 14, -4
  53 0002 0D46     		mov	r5, r1
 209:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv;
  54              		.loc 1 209 3 is_stmt 1 view .LVU2
 210:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct pbuf *t;
  55              		.loc 1 210 3 view .LVU3
 211:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 212:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif != NULL", (netif != NULL));
  56              		.loc 1 212 3 view .LVU4
  57              		.loc 1 212 3 view .LVU5
  58 0004 0446     		mov	r4, r0
  59 0006 60B1     		cbz	r0, .L19
ARM GAS  /tmp/cczEqG5I.s 			page 6


  60              	.LVL1:
  61              	.L2:
  62              		.loc 1 212 3 discriminator 3 view .LVU6
  63              		.loc 1 212 3 discriminator 3 view .LVU7
 213:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
  64              		.loc 1 213 3 view .LVU8
  65              		.loc 1 213 3 view .LVU9
  66 0008 236A     		ldr	r3, [r4, #32]
  67 000a 8BB1     		cbz	r3, .L20
  68              	.L3:
  69              		.loc 1 213 3 discriminator 3 view .LVU10
  70              		.loc 1 213 3 discriminator 3 view .LVU11
 214:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 215:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv = (struct slipif_priv *)netif->state;
  71              		.loc 1 215 3 view .LVU12
  72              		.loc 1 215 8 is_stmt 0 view .LVU13
  73 000c 246A     		ldr	r4, [r4, #32]
  74              	.LVL2:
 216:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 217:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   switch (priv->state) {
  75              		.loc 1 217 3 is_stmt 1 view .LVU14
  76              		.loc 1 217 15 is_stmt 0 view .LVU15
  77 000e 237B     		ldrb	r3, [r4, #12]	@ zero_extendqisi2
  78              		.loc 1 217 3 view .LVU16
  79 0010 ABB1     		cbz	r3, .L4
  80 0012 012B     		cmp	r3, #1
  81 0014 2CD1     		bne	.L6
 218:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     case SLIP_RECV_NORMAL:
 219:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       switch (c) {
 220:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_END:
 221:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           if (priv->recved > 0) {
 222:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             /* Received whole packet. */
 223:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             /* Trim the pbuf to the size of the received packet. */
 224:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             pbuf_realloc(priv->q, priv->recved);
 225:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 226:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             LINK_STATS_INC(link.recv);
 227:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 228:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             LWIP_DEBUGF(SLIP_DEBUG, ("slipif: Got packet (%"U16_F" bytes)\n", priv->recved));
 229:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             t = priv->q;
 230:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->p = priv->q = NULL;
 231:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->i = priv->recved = 0;
 232:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             return t;
 233:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           }
 234:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           return NULL;
 235:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_ESC:
 236:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           priv->state = SLIP_RECV_ESCAPE;
 237:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           return NULL;
 238:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         default:
 239:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 240:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       } /* end switch (c) */
 241:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       break;
 242:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     case SLIP_RECV_ESCAPE:
 243:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* un-escape END or ESC bytes, leave other bytes
 244:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****          (although that would be a protocol error) */
 245:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       switch (c) {
  82              		.loc 1 245 7 is_stmt 1 view .LVU17
  83 0016 DC2D     		cmp	r5, #220
ARM GAS  /tmp/cczEqG5I.s 			page 7


  84 0018 27D0     		beq	.L15
  85 001a DD2D     		cmp	r5, #221
  86 001c 26D1     		bne	.L10
 246:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_ESC_END:
 247:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           c = SLIP_END;
 248:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 249:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_ESC_ESC:
 250:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           c = SLIP_ESC;
  87              		.loc 1 250 13 is_stmt 0 view .LVU18
  88 001e DB25     		movs	r5, #219
  89              	.LVL3:
  90              		.loc 1 250 13 view .LVU19
  91 0020 24E0     		b	.L10
  92              	.LVL4:
  93              	.L19:
 212:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
  94              		.loc 1 212 3 is_stmt 1 discriminator 1 view .LVU20
 212:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
  95              		.loc 1 212 3 discriminator 1 view .LVU21
  96 0022 304B     		ldr	r3, .L23
  97 0024 D422     		movs	r2, #212
  98 0026 3049     		ldr	r1, .L23+4
  99              	.LVL5:
 212:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 100              		.loc 1 212 3 is_stmt 0 discriminator 1 view .LVU22
 101 0028 3048     		ldr	r0, .L23+8
 102              	.LVL6:
 212:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 103              		.loc 1 212 3 discriminator 1 view .LVU23
 104 002a FFF7FEFF 		bl	printf
 105              	.LVL7:
 106 002e EBE7     		b	.L2
 107              	.L20:
 213:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 108              		.loc 1 213 3 is_stmt 1 discriminator 1 view .LVU24
 213:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 109              		.loc 1 213 3 discriminator 1 view .LVU25
 110 0030 2C4B     		ldr	r3, .L23
 111 0032 D522     		movs	r2, #213
 112 0034 2E49     		ldr	r1, .L23+12
 113 0036 2D48     		ldr	r0, .L23+8
 114 0038 FFF7FEFF 		bl	printf
 115              	.LVL8:
 116 003c E6E7     		b	.L3
 117              	.LVL9:
 118              	.L4:
 219:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_END:
 119              		.loc 1 219 7 view .LVU26
 120 003e C02D     		cmp	r5, #192
 121 0040 05D0     		beq	.L7
 122 0042 DB2D     		cmp	r5, #219
 123 0044 14D1     		bne	.L6
 236:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           return NULL;
 124              		.loc 1 236 11 view .LVU27
 236:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           return NULL;
 125              		.loc 1 236 23 is_stmt 0 view .LVU28
 126 0046 0123     		movs	r3, #1
ARM GAS  /tmp/cczEqG5I.s 			page 8


 127 0048 2373     		strb	r3, [r4, #12]
 237:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         default:
 128              		.loc 1 237 11 is_stmt 1 view .LVU29
 237:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         default:
 129              		.loc 1 237 18 is_stmt 0 view .LVU30
 130 004a 0020     		movs	r0, #0
 131 004c 47E0     		b	.L1
 132              	.L7:
 221:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             /* Received whole packet. */
 133              		.loc 1 221 11 is_stmt 1 view .LVU31
 221:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             /* Received whole packet. */
 134              		.loc 1 221 19 is_stmt 0 view .LVU32
 135 004e 218A     		ldrh	r1, [r4, #16]
 221:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             /* Received whole packet. */
 136              		.loc 1 221 14 view .LVU33
 137 0050 09B9     		cbnz	r1, .L21
 234:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_ESC:
 138              		.loc 1 234 18 view .LVU34
 139 0052 0020     		movs	r0, #0
 140 0054 43E0     		b	.L1
 141              	.L21:
 224:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 142              		.loc 1 224 13 is_stmt 1 view .LVU35
 143 0056 A068     		ldr	r0, [r4, #8]
 144 0058 FFF7FEFF 		bl	pbuf_realloc
 145              	.LVL10:
 226:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 146              		.loc 1 226 38 view .LVU36
 228:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             t = priv->q;
 147              		.loc 1 228 93 view .LVU37
 229:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->p = priv->q = NULL;
 148              		.loc 1 229 13 view .LVU38
 229:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->p = priv->q = NULL;
 149              		.loc 1 229 15 is_stmt 0 view .LVU39
 150 005c A068     		ldr	r0, [r4, #8]
 151              	.LVL11:
 230:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->i = priv->recved = 0;
 152              		.loc 1 230 13 is_stmt 1 view .LVU40
 230:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->i = priv->recved = 0;
 153              		.loc 1 230 31 is_stmt 0 view .LVU41
 154 005e 0023     		movs	r3, #0
 155 0060 A360     		str	r3, [r4, #8]
 230:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             priv->i = priv->recved = 0;
 156              		.loc 1 230 21 view .LVU42
 157 0062 6360     		str	r3, [r4, #4]
 231:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             return t;
 158              		.loc 1 231 13 is_stmt 1 view .LVU43
 231:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             return t;
 159              		.loc 1 231 36 is_stmt 0 view .LVU44
 160 0064 2382     		strh	r3, [r4, #16]	@ movhi
 231:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****             return t;
 161              		.loc 1 231 21 view .LVU45
 162 0066 E381     		strh	r3, [r4, #14]	@ movhi
 232:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           }
 163              		.loc 1 232 13 is_stmt 1 view .LVU46
 232:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           }
 164              		.loc 1 232 20 is_stmt 0 view .LVU47
ARM GAS  /tmp/cczEqG5I.s 			page 9


 165 0068 39E0     		b	.L1
 166              	.LVL12:
 167              	.L15:
 247:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 168              		.loc 1 247 13 view .LVU48
 169 006a C025     		movs	r5, #192
 170              	.LVL13:
 171              	.L10:
 251:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 252:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         default:
 253:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 254:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       }
 255:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       priv->state = SLIP_RECV_NORMAL;
 172              		.loc 1 255 7 is_stmt 1 view .LVU49
 173              		.loc 1 255 19 is_stmt 0 view .LVU50
 174 006c 0023     		movs	r3, #0
 175 006e 2373     		strb	r3, [r4, #12]
 256:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       break;
 176              		.loc 1 256 7 is_stmt 1 view .LVU51
 177              	.LVL14:
 178              	.L6:
 257:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     default:
 258:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       break;
 259:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   } /* end switch (priv->state) */
 260:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 261:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* byte received, packet not yet completely received */
 262:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   if (priv->p == NULL) {
 179              		.loc 1 262 3 view .LVU52
 180              		.loc 1 262 11 is_stmt 0 view .LVU53
 181 0070 6368     		ldr	r3, [r4, #4]
 182              		.loc 1 262 6 view .LVU54
 183 0072 F3B1     		cbz	r3, .L22
 184              	.L11:
 263:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     /* allocate a new pbuf */
 264:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     LWIP_DEBUGF(SLIP_DEBUG, ("slipif_input: alloc\n"));
 265:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     priv->p = pbuf_alloc(PBUF_LINK, (PBUF_POOL_BUFSIZE - PBUF_LINK_HLEN - PBUF_LINK_ENCAPSULATION_H
 266:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 267:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     if (priv->p == NULL) {
 268:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       LINK_STATS_INC(link.drop);
 269:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       LWIP_DEBUGF(SLIP_DEBUG, ("slipif_input: no new pbuf! (DROP)\n"));
 270:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* don't process any further since we got no pbuf to receive to */
 271:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       return NULL;
 272:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 273:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 274:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     if (priv->q != NULL) {
 275:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* 'chain' the pbuf to the existing chain */
 276:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       pbuf_cat(priv->q, priv->p);
 277:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     } else {
 278:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* p is the first pbuf in the chain */
 279:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       priv->q = priv->p;
 280:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 281:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 282:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 283:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* this automatically drops bytes if > SLIP_MAX_SIZE */
 284:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   if ((priv->p != NULL) && (priv->recved <= SLIP_MAX_SIZE)) {
 185              		.loc 1 284 3 is_stmt 1 view .LVU55
 186              		.loc 1 284 12 is_stmt 0 view .LVU56
ARM GAS  /tmp/cczEqG5I.s 			page 10


 187 0074 6068     		ldr	r0, [r4, #4]
 188              		.loc 1 284 6 view .LVU57
 189 0076 0028     		cmp	r0, #0
 190 0078 31D0     		beq	.L1
 191              		.loc 1 284 33 discriminator 1 view .LVU58
 192 007a 228A     		ldrh	r2, [r4, #16]
 193              		.loc 1 284 25 discriminator 1 view .LVU59
 194 007c 40F2DC53 		movw	r3, #1500
 195 0080 9A42     		cmp	r2, r3
 196 0082 2BD8     		bhi	.L16
 285:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     ((u8_t *)priv->p->payload)[priv->i] = c;
 197              		.loc 1 285 5 is_stmt 1 view .LVU60
 198              		.loc 1 285 21 is_stmt 0 view .LVU61
 199 0084 4268     		ldr	r2, [r0, #4]
 200              		.loc 1 285 36 view .LVU62
 201 0086 E389     		ldrh	r3, [r4, #14]
 202              		.loc 1 285 41 view .LVU63
 203 0088 D554     		strb	r5, [r2, r3]
 286:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     priv->recved++;
 204              		.loc 1 286 5 is_stmt 1 view .LVU64
 205              		.loc 1 286 9 is_stmt 0 view .LVU65
 206 008a 238A     		ldrh	r3, [r4, #16]
 207              		.loc 1 286 17 view .LVU66
 208 008c 0133     		adds	r3, r3, #1
 209 008e 2382     		strh	r3, [r4, #16]	@ movhi
 287:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     priv->i++;
 210              		.loc 1 287 5 is_stmt 1 view .LVU67
 211              		.loc 1 287 9 is_stmt 0 view .LVU68
 212 0090 E389     		ldrh	r3, [r4, #14]
 213              		.loc 1 287 12 view .LVU69
 214 0092 0133     		adds	r3, r3, #1
 215 0094 9BB2     		uxth	r3, r3
 216 0096 E381     		strh	r3, [r4, #14]	@ movhi
 288:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     if (priv->i >= priv->p->len) {
 217              		.loc 1 288 5 is_stmt 1 view .LVU70
 218              		.loc 1 288 24 is_stmt 0 view .LVU71
 219 0098 6268     		ldr	r2, [r4, #4]
 220              		.loc 1 288 27 view .LVU72
 221 009a 5189     		ldrh	r1, [r2, #10]
 222              		.loc 1 288 8 view .LVU73
 223 009c 8B42     		cmp	r3, r1
 224 009e 1FD3     		bcc	.L17
 289:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* on to the next pbuf */
 290:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       priv->i = 0;
 225              		.loc 1 290 7 is_stmt 1 view .LVU74
 226              		.loc 1 290 15 is_stmt 0 view .LVU75
 227 00a0 0023     		movs	r3, #0
 228 00a2 E381     		strh	r3, [r4, #14]	@ movhi
 291:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       if (priv->p->next != NULL && priv->p->next->len > 0) {
 229              		.loc 1 291 7 is_stmt 1 view .LVU76
 230              		.loc 1 291 18 is_stmt 0 view .LVU77
 231 00a4 1368     		ldr	r3, [r2]
 232              		.loc 1 291 10 view .LVU78
 233 00a6 B3B1     		cbz	r3, .L13
 234              		.loc 1 291 49 discriminator 1 view .LVU79
 235 00a8 5A89     		ldrh	r2, [r3, #10]
 236              		.loc 1 291 33 discriminator 1 view .LVU80
ARM GAS  /tmp/cczEqG5I.s 			page 11


 237 00aa A2B1     		cbz	r2, .L13
 292:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         /* p is a chain, on to the next in the chain */
 293:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         priv->p = priv->p->next;
 238              		.loc 1 293 9 is_stmt 1 view .LVU81
 239              		.loc 1 293 17 is_stmt 0 view .LVU82
 240 00ac 6360     		str	r3, [r4, #4]
 294:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       } else {
 295:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         /* p is a single pbuf, set it to NULL so next time a new
 296:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****          * pbuf is allocated */
 297:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         priv->p = NULL;
 298:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       }
 299:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 300:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 301:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return NULL;
 241              		.loc 1 301 10 view .LVU83
 242 00ae 0020     		movs	r0, #0
 293:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       } else {
 243              		.loc 1 293 17 view .LVU84
 244 00b0 15E0     		b	.L1
 245              	.L22:
 264:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     priv->p = pbuf_alloc(PBUF_LINK, (PBUF_POOL_BUFSIZE - PBUF_LINK_HLEN - PBUF_LINK_ENCAPSULATION_H
 246              		.loc 1 264 55 is_stmt 1 view .LVU85
 265:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 247              		.loc 1 265 5 view .LVU86
 265:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 248              		.loc 1 265 15 is_stmt 0 view .LVU87
 249 00b2 4FF4C172 		mov	r2, #386
 250 00b6 40F24221 		movw	r1, #578
 251 00ba 0E20     		movs	r0, #14
 252 00bc FFF7FEFF 		bl	pbuf_alloc
 253              	.LVL15:
 265:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 254              		.loc 1 265 13 discriminator 1 view .LVU88
 255 00c0 6060     		str	r0, [r4, #4]
 267:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       LINK_STATS_INC(link.drop);
 256              		.loc 1 267 5 is_stmt 1 view .LVU89
 267:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       LINK_STATS_INC(link.drop);
 257              		.loc 1 267 8 is_stmt 0 view .LVU90
 258 00c2 60B1     		cbz	r0, .L1
 274:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* 'chain' the pbuf to the existing chain */
 259              		.loc 1 274 5 is_stmt 1 view .LVU91
 274:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* 'chain' the pbuf to the existing chain */
 260              		.loc 1 274 13 is_stmt 0 view .LVU92
 261 00c4 A368     		ldr	r3, [r4, #8]
 274:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       /* 'chain' the pbuf to the existing chain */
 262              		.loc 1 274 8 view .LVU93
 263 00c6 23B1     		cbz	r3, .L12
 276:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     } else {
 264              		.loc 1 276 7 is_stmt 1 view .LVU94
 265 00c8 0146     		mov	r1, r0
 266 00ca 1846     		mov	r0, r3
 267 00cc FFF7FEFF 		bl	pbuf_cat
 268              	.LVL16:
 269 00d0 D0E7     		b	.L11
 270              	.L12:
 279:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 271              		.loc 1 279 7 view .LVU95
ARM GAS  /tmp/cczEqG5I.s 			page 12


 279:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 272              		.loc 1 279 15 is_stmt 0 view .LVU96
 273 00d2 A060     		str	r0, [r4, #8]
 274 00d4 CEE7     		b	.L11
 275              	.L13:
 297:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       }
 276              		.loc 1 297 9 is_stmt 1 view .LVU97
 297:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       }
 277              		.loc 1 297 17 is_stmt 0 view .LVU98
 278 00d6 0020     		movs	r0, #0
 279 00d8 6060     		str	r0, [r4, #4]
 280 00da 00E0     		b	.L1
 281              	.L16:
 282              		.loc 1 301 10 view .LVU99
 283 00dc 0020     		movs	r0, #0
 284              	.LVL17:
 285              	.L1:
 302:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 286              		.loc 1 302 1 view .LVU100
 287 00de 38BD     		pop	{r3, r4, r5, pc}
 288              	.LVL18:
 289              	.L17:
 301:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 290              		.loc 1 301 10 view .LVU101
 291 00e0 0020     		movs	r0, #0
 292 00e2 FCE7     		b	.L1
 293              	.L24:
 294              		.align	2
 295              	.L23:
 296 00e4 00000000 		.word	.LC0
 297 00e8 30000000 		.word	.LC1
 298 00ec 40000000 		.word	.LC2
 299 00f0 68000000 		.word	.LC3
 300              		.cfi_endproc
 301              	.LFE176:
 303              		.section	.text.slipif_rxbyte_input,"ax",%progbits
 304              		.align	1
 305              		.syntax unified
 306              		.thumb
 307              		.thumb_func
 309              	slipif_rxbyte_input:
 310              	.LVL19:
 311              	.LFB177:
 303:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 304:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /** Like slipif_rxbyte, but passes completed packets to netif->input
 305:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 306:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif The lwip network interface structure for this slipif
 307:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param c received character
 308:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 309:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** static void
 310:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_rxbyte_input(struct netif *netif, u8_t c)
 311:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
 312              		.loc 1 311 1 is_stmt 1 view -0
 313              		.cfi_startproc
 314              		@ args = 0, pretend = 0, frame = 0
 315              		@ frame_needed = 0, uses_anonymous_args = 0
 316              		.loc 1 311 1 is_stmt 0 view .LVU103
ARM GAS  /tmp/cczEqG5I.s 			page 13


 317 0000 38B5     		push	{r3, r4, r5, lr}
 318              	.LCFI1:
 319              		.cfi_def_cfa_offset 16
 320              		.cfi_offset 3, -16
 321              		.cfi_offset 4, -12
 322              		.cfi_offset 5, -8
 323              		.cfi_offset 14, -4
 324 0002 0446     		mov	r4, r0
 312:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct pbuf *p;
 325              		.loc 1 312 3 is_stmt 1 view .LVU104
 313:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   p = slipif_rxbyte(netif, c);
 326              		.loc 1 313 3 view .LVU105
 327              		.loc 1 313 7 is_stmt 0 view .LVU106
 328 0004 FFF7FEFF 		bl	slipif_rxbyte
 329              	.LVL20:
 314:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   if (p != NULL) {
 330              		.loc 1 314 3 is_stmt 1 view .LVU107
 331              		.loc 1 314 6 is_stmt 0 view .LVU108
 332 0008 20B1     		cbz	r0, .L25
 333 000a 0546     		mov	r5, r0
 315:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     if (netif->input(p, netif) != ERR_OK) {
 334              		.loc 1 315 5 is_stmt 1 view .LVU109
 335              		.loc 1 315 14 is_stmt 0 view .LVU110
 336 000c 2369     		ldr	r3, [r4, #16]
 337              		.loc 1 315 9 view .LVU111
 338 000e 2146     		mov	r1, r4
 339 0010 9847     		blx	r3
 340              	.LVL21:
 341              		.loc 1 315 8 discriminator 1 view .LVU112
 342 0012 00B9     		cbnz	r0, .L28
 343              	.LVL22:
 344              	.L25:
 316:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       pbuf_free(p);
 317:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 318:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 319:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 345              		.loc 1 319 1 view .LVU113
 346 0014 38BD     		pop	{r3, r4, r5, pc}
 347              	.LVL23:
 348              	.L28:
 316:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       pbuf_free(p);
 349              		.loc 1 316 7 is_stmt 1 view .LVU114
 350 0016 2846     		mov	r0, r5
 351 0018 FFF7FEFF 		bl	pbuf_free
 352              	.LVL24:
 353              		.loc 1 319 1 is_stmt 0 view .LVU115
 354 001c FAE7     		b	.L25
 355              		.cfi_endproc
 356              	.LFE177:
 358              		.section	.text.slipif_loop_thread,"ax",%progbits
 359              		.align	1
 360              		.syntax unified
 361              		.thumb
 362              		.thumb_func
 364              	slipif_loop_thread:
 365              	.LVL25:
 366              	.LFB178:
ARM GAS  /tmp/cczEqG5I.s 			page 14


 320:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 321:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if SLIP_USE_RX_THREAD
 322:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 323:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * The SLIP input thread.
 324:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 325:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Feed the IP layer with incoming packets
 326:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 327:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param nf the lwip network interface structure for this slipif
 328:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 329:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** static void
 330:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_loop_thread(void *nf)
 331:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
 367              		.loc 1 331 1 is_stmt 1 view -0
 368              		.cfi_startproc
 369              		@ args = 0, pretend = 0, frame = 8
 370              		@ frame_needed = 0, uses_anonymous_args = 0
 371              		.loc 1 331 1 is_stmt 0 view .LVU117
 372 0000 30B5     		push	{r4, r5, lr}
 373              	.LCFI2:
 374              		.cfi_def_cfa_offset 12
 375              		.cfi_offset 4, -12
 376              		.cfi_offset 5, -8
 377              		.cfi_offset 14, -4
 378 0002 83B0     		sub	sp, sp, #12
 379              	.LCFI3:
 380              		.cfi_def_cfa_offset 24
 381 0004 0546     		mov	r5, r0
 332:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u8_t c;
 382              		.loc 1 332 3 is_stmt 1 view .LVU118
 333:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct netif *netif = (struct netif *)nf;
 383              		.loc 1 333 3 view .LVU119
 384              	.LVL26:
 334:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv = (struct slipif_priv *)netif->state;
 385              		.loc 1 334 3 view .LVU120
 386              		.loc 1 334 23 is_stmt 0 view .LVU121
 387 0006 046A     		ldr	r4, [r0, #32]
 388              	.LVL27:
 389              	.L30:
 335:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 336:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   while (1) {
 390              		.loc 1 336 3 is_stmt 1 view .LVU122
 337:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     if (sio_read(priv->sd, &c, 1) > 0) {
 391              		.loc 1 337 5 view .LVU123
 392              		.loc 1 337 9 is_stmt 0 view .LVU124
 393 0008 0122     		movs	r2, #1
 394 000a 0DF10701 		add	r1, sp, #7
 395 000e 2068     		ldr	r0, [r4]
 396 0010 FFF7FEFF 		bl	sio_read
 397              	.LVL28:
 398              		.loc 1 337 8 discriminator 1 view .LVU125
 399 0014 0028     		cmp	r0, #0
 400 0016 F7D0     		beq	.L30
 338:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       slipif_rxbyte_input(netif, c);
 401              		.loc 1 338 7 is_stmt 1 view .LVU126
 402 0018 9DF80710 		ldrb	r1, [sp, #7]	@ zero_extendqisi2
 403 001c 2846     		mov	r0, r5
 404 001e FFF7FEFF 		bl	slipif_rxbyte_input
ARM GAS  /tmp/cczEqG5I.s 			page 15


 405              	.LVL29:
 406 0022 F1E7     		b	.L30
 407              		.cfi_endproc
 408              	.LFE178:
 410              		.section	.rodata.slipif_output.str1.4,"aMS",%progbits,1
 411              		.align	2
 412              	.LC4:
 413 0000 7020213D 		.ascii	"p != NULL\000"
 413      204E554C 
 413      4C00
 414              		.section	.text.slipif_output,"ax",%progbits
 415              		.align	1
 416              		.syntax unified
 417              		.thumb
 418              		.thumb_func
 420              	slipif_output:
 421              	.LVL30:
 422              	.LFB174:
 117:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv;
 423              		.loc 1 117 1 view -0
 424              		.cfi_startproc
 425              		@ args = 0, pretend = 0, frame = 0
 426              		@ frame_needed = 0, uses_anonymous_args = 0
 117:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv;
 427              		.loc 1 117 1 is_stmt 0 view .LVU128
 428 0000 70B5     		push	{r4, r5, r6, lr}
 429              	.LCFI4:
 430              		.cfi_def_cfa_offset 16
 431              		.cfi_offset 4, -16
 432              		.cfi_offset 5, -12
 433              		.cfi_offset 6, -8
 434              		.cfi_offset 14, -4
 435 0002 0D46     		mov	r5, r1
 118:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct pbuf *q;
 436              		.loc 1 118 3 is_stmt 1 view .LVU129
 119:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u16_t i;
 437              		.loc 1 119 3 view .LVU130
 120:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u8_t c;
 438              		.loc 1 120 3 view .LVU131
 121:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 439              		.loc 1 121 3 view .LVU132
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 440              		.loc 1 123 3 view .LVU133
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 441              		.loc 1 123 3 view .LVU134
 442 0004 0446     		mov	r4, r0
 443 0006 40B1     		cbz	r0, .L47
 444              	.LVL31:
 445              	.L34:
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 446              		.loc 1 123 3 discriminator 3 view .LVU135
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 447              		.loc 1 123 3 discriminator 3 view .LVU136
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
 448              		.loc 1 124 3 view .LVU137
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
 449              		.loc 1 124 3 view .LVU138
ARM GAS  /tmp/cczEqG5I.s 			page 16


 450 0008 236A     		ldr	r3, [r4, #32]
 451 000a 6BB1     		cbz	r3, .L48
 452              	.L35:
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
 453              		.loc 1 124 3 discriminator 3 view .LVU139
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
 454              		.loc 1 124 3 discriminator 3 view .LVU140
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 455              		.loc 1 125 3 view .LVU141
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 456              		.loc 1 125 3 view .LVU142
 457 000c 9DB1     		cbz	r5, .L49
 458              	.L36:
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 459              		.loc 1 125 3 discriminator 3 view .LVU143
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 460              		.loc 1 125 3 discriminator 3 view .LVU144
 127:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv = (struct slipif_priv *)netif->state;
 461              		.loc 1 127 83 view .LVU145
 128:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 462              		.loc 1 128 3 view .LVU146
 128:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 463              		.loc 1 128 8 is_stmt 0 view .LVU147
 464 000e 266A     		ldr	r6, [r4, #32]
 465              	.LVL32:
 132:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 466              		.loc 1 132 3 is_stmt 1 view .LVU148
 467 0010 3168     		ldr	r1, [r6]
 468 0012 C020     		movs	r0, #192
 469 0014 FFF7FEFF 		bl	sio_send
 470              	.LVL33:
 134:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     for (i = 0; i < q->len; i++) {
 471              		.loc 1 134 3 view .LVU149
 134:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     for (i = 0; i < q->len; i++) {
 472              		.loc 1 134 3 is_stmt 0 view .LVU150
 473 0018 35E0     		b	.L37
 474              	.LVL34:
 475              	.L47:
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 476              		.loc 1 123 3 is_stmt 1 discriminator 1 view .LVU151
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 477              		.loc 1 123 3 discriminator 1 view .LVU152
 478 001a 1F4B     		ldr	r3, .L52
 479 001c 7B22     		movs	r2, #123
 480 001e 1F49     		ldr	r1, .L52+4
 481              	.LVL35:
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 482              		.loc 1 123 3 is_stmt 0 discriminator 1 view .LVU153
 483 0020 1F48     		ldr	r0, .L52+8
 484              	.LVL36:
 123:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 485              		.loc 1 123 3 discriminator 1 view .LVU154
 486 0022 FFF7FEFF 		bl	printf
 487              	.LVL37:
 488 0026 EFE7     		b	.L34
 489              	.L48:
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
ARM GAS  /tmp/cczEqG5I.s 			page 17


 490              		.loc 1 124 3 is_stmt 1 discriminator 1 view .LVU155
 124:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("p != NULL", (p != NULL));
 491              		.loc 1 124 3 discriminator 1 view .LVU156
 492 0028 1B4B     		ldr	r3, .L52
 493 002a 7C22     		movs	r2, #124
 494 002c 1D49     		ldr	r1, .L52+12
 495 002e 1C48     		ldr	r0, .L52+8
 496 0030 FFF7FEFF 		bl	printf
 497              	.LVL38:
 498 0034 EAE7     		b	.L35
 499              	.L49:
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 500              		.loc 1 125 3 discriminator 1 view .LVU157
 125:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 501              		.loc 1 125 3 discriminator 1 view .LVU158
 502 0036 184B     		ldr	r3, .L52
 503 0038 7D22     		movs	r2, #125
 504 003a 1B49     		ldr	r1, .L52+16
 505 003c 1848     		ldr	r0, .L52+8
 506 003e FFF7FEFF 		bl	printf
 507              	.LVL39:
 508 0042 E4E7     		b	.L36
 509              	.LVL40:
 510              	.L38:
 140:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC_END, priv->sd);
 511              		.loc 1 140 11 view .LVU159
 512 0044 3168     		ldr	r1, [r6]
 513 0046 DB20     		movs	r0, #219
 514              	.LVL41:
 140:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC_END, priv->sd);
 515              		.loc 1 140 11 is_stmt 0 view .LVU160
 516 0048 FFF7FEFF 		bl	sio_send
 517              	.LVL42:
 141:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 518              		.loc 1 141 11 is_stmt 1 view .LVU161
 519 004c 3168     		ldr	r1, [r6]
 520 004e DC20     		movs	r0, #220
 521 0050 FFF7FEFF 		bl	sio_send
 522              	.LVL43:
 142:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_ESC:
 523              		.loc 1 142 11 view .LVU162
 524              	.L41:
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
 525              		.loc 1 135 30 discriminator 2 view .LVU163
 526 0054 0134     		adds	r4, r4, #1
 527              	.LVL44:
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
 528              		.loc 1 135 30 is_stmt 0 discriminator 2 view .LVU164
 529 0056 A4B2     		uxth	r4, r4
 530              	.LVL45:
 531              	.L43:
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
 532              		.loc 1 135 19 is_stmt 1 discriminator 1 view .LVU165
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
 533              		.loc 1 135 22 is_stmt 0 discriminator 1 view .LVU166
 534 0058 6B89     		ldrh	r3, [r5, #10]
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
ARM GAS  /tmp/cczEqG5I.s 			page 18


 535              		.loc 1 135 19 discriminator 1 view .LVU167
 536 005a A342     		cmp	r3, r4
 537 005c 12D9     		bls	.L50
 136:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       switch (c) {
 538              		.loc 1 136 7 is_stmt 1 view .LVU168
 136:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       switch (c) {
 539              		.loc 1 136 21 is_stmt 0 view .LVU169
 540 005e 6B68     		ldr	r3, [r5, #4]
 136:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       switch (c) {
 541              		.loc 1 136 9 view .LVU170
 542 0060 185D     		ldrb	r0, [r3, r4]	@ zero_extendqisi2
 543              	.LVL46:
 137:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         case SLIP_END:
 544              		.loc 1 137 7 is_stmt 1 view .LVU171
 545 0062 C028     		cmp	r0, #192
 546 0064 EED0     		beq	.L38
 547 0066 DB28     		cmp	r0, #219
 548 0068 03D0     		beq	.L39
 150:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 549              		.loc 1 150 11 view .LVU172
 550 006a 3168     		ldr	r1, [r6]
 551 006c FFF7FEFF 		bl	sio_send
 552              	.LVL47:
 151:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       }
 553              		.loc 1 151 11 view .LVU173
 554 0070 F0E7     		b	.L41
 555              	.LVL48:
 556              	.L39:
 145:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC_ESC, priv->sd);
 557              		.loc 1 145 11 view .LVU174
 558 0072 3168     		ldr	r1, [r6]
 559 0074 DB20     		movs	r0, #219
 560              	.LVL49:
 145:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           sio_send(SLIP_ESC_ESC, priv->sd);
 561              		.loc 1 145 11 is_stmt 0 view .LVU175
 562 0076 FFF7FEFF 		bl	sio_send
 563              	.LVL50:
 146:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****           break;
 564              		.loc 1 146 11 is_stmt 1 view .LVU176
 565 007a 3168     		ldr	r1, [r6]
 566 007c DD20     		movs	r0, #221
 567 007e FFF7FEFF 		bl	sio_send
 568              	.LVL51:
 147:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****         default:
 569              		.loc 1 147 11 view .LVU177
 570 0082 E7E7     		b	.L41
 571              	.LVL52:
 572              	.L50:
 134:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     for (i = 0; i < q->len; i++) {
 573              		.loc 1 134 28 discriminator 2 view .LVU178
 574 0084 2D68     		ldr	r5, [r5]
 575              	.LVL53:
 576              	.L37:
 134:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     for (i = 0; i < q->len; i++) {
 577              		.loc 1 134 17 discriminator 1 view .LVU179
 578 0086 0DB1     		cbz	r5, .L51
 135:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****       c = ((u8_t *)q->payload)[i];
ARM GAS  /tmp/cczEqG5I.s 			page 19


 579              		.loc 1 135 12 is_stmt 0 view .LVU180
 580 0088 0024     		movs	r4, #0
 581 008a E5E7     		b	.L43
 582              	.L51:
 156:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return ERR_OK;
 583              		.loc 1 156 3 is_stmt 1 view .LVU181
 584 008c 3168     		ldr	r1, [r6]
 585 008e C020     		movs	r0, #192
 586 0090 FFF7FEFF 		bl	sio_send
 587              	.LVL54:
 157:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 588              		.loc 1 157 3 view .LVU182
 158:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 589              		.loc 1 158 1 is_stmt 0 view .LVU183
 590 0094 0020     		movs	r0, #0
 591 0096 70BD     		pop	{r4, r5, r6, pc}
 592              	.LVL55:
 593              	.L53:
 158:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 594              		.loc 1 158 1 view .LVU184
 595              		.align	2
 596              	.L52:
 597 0098 00000000 		.word	.LC0
 598 009c 30000000 		.word	.LC1
 599 00a0 40000000 		.word	.LC2
 600 00a4 68000000 		.word	.LC3
 601 00a8 00000000 		.word	.LC4
 602              		.cfi_endproc
 603              	.LFE174:
 605              		.section	.text.slipif_output_v4,"ax",%progbits
 606              		.align	1
 607              		.syntax unified
 608              		.thumb
 609              		.thumb_func
 611              	slipif_output_v4:
 612              	.LVL56:
 613              	.LFB175:
 173:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_UNUSED_ARG(ipaddr);
 614              		.loc 1 173 1 is_stmt 1 view -0
 615              		.cfi_startproc
 616              		@ args = 0, pretend = 0, frame = 0
 617              		@ frame_needed = 0, uses_anonymous_args = 0
 173:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_UNUSED_ARG(ipaddr);
 618              		.loc 1 173 1 is_stmt 0 view .LVU186
 619 0000 08B5     		push	{r3, lr}
 620              	.LCFI5:
 621              		.cfi_def_cfa_offset 8
 622              		.cfi_offset 3, -8
 623              		.cfi_offset 14, -4
 174:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return slipif_output(netif, p);
 624              		.loc 1 174 3 is_stmt 1 view .LVU187
 175:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 625              		.loc 1 175 3 view .LVU188
 175:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 626              		.loc 1 175 10 is_stmt 0 view .LVU189
 627 0002 FFF7FEFF 		bl	slipif_output
 628              	.LVL57:
ARM GAS  /tmp/cczEqG5I.s 			page 20


 176:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* LWIP_IPV4 */
 629              		.loc 1 176 1 view .LVU190
 630 0006 08BD     		pop	{r3, pc}
 631              		.cfi_endproc
 632              	.LFE175:
 634              		.section	.rodata.slipif_init.str1.4,"aMS",%progbits,1
 635              		.align	2
 636              	.LC5:
 637 0000 736C6970 		.ascii	"slipif needs an input callback\000"
 637      6966206E 
 637      65656473 
 637      20616E20 
 637      696E7075 
 638 001f 00       		.align	2
 639              	.LC6:
 640 0020 736C6970 		.ascii	"slipif_loop\000"
 640      69665F6C 
 640      6F6F7000 
 641              		.section	.text.slipif_init,"ax",%progbits
 642              		.align	1
 643              		.global	slipif_init
 644              		.syntax unified
 645              		.thumb
 646              		.thumb_func
 648              	slipif_init:
 649              	.LVL58:
 650              	.LFB179:
 339:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     }
 340:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 341:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 342:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* SLIP_USE_RX_THREAD */
 343:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 344:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 345:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @ingroup slipif
 346:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * SLIP netif initialization
 347:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 348:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Call the arch specific sio_open and remember
 349:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * the opened device in the state field of the netif.
 350:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 351:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif the lwip network interface structure for this slipif
 352:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @return ERR_OK if serial line could be opened,
 353:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *         ERR_MEM if no memory could be allocated,
 354:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *         ERR_IF is serial line couldn't be opened
 355:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 356:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @note If netif->state is interpreted as an u8_t serial port number.
 357:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 358:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 359:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** err_t
 360:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_init(struct netif *netif)
 361:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
 651              		.loc 1 361 1 is_stmt 1 view -0
 652              		.cfi_startproc
 653              		@ args = 0, pretend = 0, frame = 0
 654              		@ frame_needed = 0, uses_anonymous_args = 0
 655              		.loc 1 361 1 is_stmt 0 view .LVU192
 656 0000 70B5     		push	{r4, r5, r6, lr}
 657              	.LCFI6:
ARM GAS  /tmp/cczEqG5I.s 			page 21


 658              		.cfi_def_cfa_offset 16
 659              		.cfi_offset 4, -16
 660              		.cfi_offset 5, -12
 661              		.cfi_offset 6, -8
 662              		.cfi_offset 14, -4
 663 0002 82B0     		sub	sp, sp, #8
 664              	.LCFI7:
 665              		.cfi_def_cfa_offset 24
 666 0004 0446     		mov	r4, r0
 362:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv;
 667              		.loc 1 362 3 is_stmt 1 view .LVU193
 363:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u8_t sio_num;
 668              		.loc 1 363 3 view .LVU194
 364:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 365:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("slipif needs an input callback", netif->input != NULL);
 669              		.loc 1 365 3 view .LVU195
 670              		.loc 1 365 3 view .LVU196
 671 0006 0369     		ldr	r3, [r0, #16]
 672 0008 53B3     		cbz	r3, .L62
 673              	.LVL59:
 674              	.L57:
 675              		.loc 1 365 3 discriminator 3 view .LVU197
 676              		.loc 1 365 3 discriminator 3 view .LVU198
 366:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 367:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* netif->state contains serial port number */
 368:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   sio_num = LWIP_PTR_NUMERIC_CAST(u8_t, netif->state);
 677              		.loc 1 368 3 view .LVU199
 678              		.loc 1 368 11 is_stmt 0 view .LVU200
 679 000a 94F82050 		ldrb	r5, [r4, #32]	@ zero_extendqisi2
 680              	.LVL60:
 369:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 370:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_DEBUGF(SLIP_DEBUG, ("slipif_init: netif->num=%"U16_F"\n", (u16_t)sio_num));
 681              		.loc 1 370 82 is_stmt 1 view .LVU201
 371:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 372:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* Allocate private data */
 373:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv = (struct slipif_priv *)mem_malloc(sizeof(struct slipif_priv));
 682              		.loc 1 373 3 view .LVU202
 683              		.loc 1 373 32 is_stmt 0 view .LVU203
 684 000e 1420     		movs	r0, #20
 685 0010 FFF7FEFF 		bl	mem_malloc
 686              	.LVL61:
 374:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   if (!priv) {
 687              		.loc 1 374 3 is_stmt 1 view .LVU204
 688              		.loc 1 374 6 is_stmt 0 view .LVU205
 689 0014 0646     		mov	r6, r0
 690 0016 0028     		cmp	r0, #0
 691 0018 30D0     		beq	.L60
 375:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     return ERR_MEM;
 376:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 377:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 378:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   netif->name[0] = 's';
 692              		.loc 1 378 3 is_stmt 1 view .LVU206
 693              		.loc 1 378 18 is_stmt 0 view .LVU207
 694 001a 7323     		movs	r3, #115
 695 001c 84F82E30 		strb	r3, [r4, #46]
 379:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   netif->name[1] = 'l';
 696              		.loc 1 379 3 is_stmt 1 view .LVU208
ARM GAS  /tmp/cczEqG5I.s 			page 22


 697              		.loc 1 379 18 is_stmt 0 view .LVU209
 698 0020 6C23     		movs	r3, #108
 699 0022 84F82F30 		strb	r3, [r4, #47]
 380:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if LWIP_IPV4
 381:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   netif->output = slipif_output_v4;
 700              		.loc 1 381 3 is_stmt 1 view .LVU210
 701              		.loc 1 381 17 is_stmt 0 view .LVU211
 702 0026 174B     		ldr	r3, .L64
 703 0028 6361     		str	r3, [r4, #20]
 382:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* LWIP_IPV4 */
 383:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if LWIP_IPV6
 384:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   netif->output_ip6 = slipif_output_v6;
 385:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* LWIP_IPV6 */
 386:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   netif->mtu = SLIP_MAX_SIZE;
 704              		.loc 1 386 3 is_stmt 1 view .LVU212
 705              		.loc 1 386 14 is_stmt 0 view .LVU213
 706 002a 40F2DC53 		movw	r3, #1500
 707 002e A384     		strh	r3, [r4, #36]	@ movhi
 387:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 388:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* Try to open the serial port. */
 389:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->sd = sio_open(sio_num);
 708              		.loc 1 389 3 is_stmt 1 view .LVU214
 709              		.loc 1 389 14 is_stmt 0 view .LVU215
 710 0030 2846     		mov	r0, r5
 711              	.LVL62:
 712              		.loc 1 389 14 view .LVU216
 713 0032 FFF7FEFF 		bl	sio_open
 714              	.LVL63:
 715              		.loc 1 389 12 discriminator 1 view .LVU217
 716 0036 3060     		str	r0, [r6]
 390:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   if (!priv->sd) {
 717              		.loc 1 390 3 is_stmt 1 view .LVU218
 718              		.loc 1 390 6 is_stmt 0 view .LVU219
 719 0038 D0B1     		cbz	r0, .L63
 391:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     /* Opening the serial port failed. */
 392:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     mem_free(priv);
 393:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     return ERR_IF;
 394:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 395:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 396:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* Initialize private data */
 397:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->p = NULL;
 720              		.loc 1 397 3 is_stmt 1 view .LVU220
 721              		.loc 1 397 11 is_stmt 0 view .LVU221
 722 003a 0025     		movs	r5, #0
 723              	.LVL64:
 724              		.loc 1 397 11 view .LVU222
 725 003c 7560     		str	r5, [r6, #4]
 398:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->q = NULL;
 726              		.loc 1 398 3 is_stmt 1 view .LVU223
 727              		.loc 1 398 11 is_stmt 0 view .LVU224
 728 003e B560     		str	r5, [r6, #8]
 399:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->state = SLIP_RECV_NORMAL;
 729              		.loc 1 399 3 is_stmt 1 view .LVU225
 730              		.loc 1 399 15 is_stmt 0 view .LVU226
 731 0040 3573     		strb	r5, [r6, #12]
 400:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->i = 0;
 732              		.loc 1 400 3 is_stmt 1 view .LVU227
ARM GAS  /tmp/cczEqG5I.s 			page 23


 733              		.loc 1 400 11 is_stmt 0 view .LVU228
 734 0042 F581     		strh	r5, [r6, #14]	@ movhi
 401:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->recved = 0;
 735              		.loc 1 401 3 is_stmt 1 view .LVU229
 736              		.loc 1 401 16 is_stmt 0 view .LVU230
 737 0044 3582     		strh	r5, [r6, #16]	@ movhi
 402:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if SLIP_RX_FROM_ISR
 403:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv->rxpackets = NULL;
 404:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif
 405:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 406:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   netif->state = priv;
 738              		.loc 1 406 3 is_stmt 1 view .LVU231
 739              		.loc 1 406 16 is_stmt 0 view .LVU232
 740 0046 2662     		str	r6, [r4, #32]
 407:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 408:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* initialize the snmp variables and counters inside the struct netif */
 409:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   MIB2_INIT_NETIF(netif, snmp_ifType_slip, SLIP_SIO_SPEED(priv->sd));
 741              		.loc 1 409 69 is_stmt 1 view .LVU233
 410:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 411:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #if SLIP_USE_RX_THREAD
 412:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   /* Create a thread to poll the serial line. */
 413:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   sys_thread_new(SLIPIF_THREAD_NAME, slipif_loop_thread, netif,
 742              		.loc 1 413 3 view .LVU234
 743 0048 0323     		movs	r3, #3
 744 004a 0093     		str	r3, [sp]
 745 004c 4FF48063 		mov	r3, #1024
 746 0050 2246     		mov	r2, r4
 747 0052 0D49     		ldr	r1, .L64+4
 748 0054 0D48     		ldr	r0, .L64+8
 749 0056 FFF7FEFF 		bl	sys_thread_new
 750              	.LVL65:
 414:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****                  SLIPIF_THREAD_STACKSIZE, SLIPIF_THREAD_PRIO);
 415:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** #endif /* SLIP_USE_RX_THREAD */
 416:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   return ERR_OK;
 751              		.loc 1 416 3 view .LVU235
 752              		.loc 1 416 10 is_stmt 0 view .LVU236
 753 005a 2846     		mov	r0, r5
 754              	.L58:
 417:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 755              		.loc 1 417 1 view .LVU237
 756 005c 02B0     		add	sp, sp, #8
 757              	.LCFI8:
 758              		.cfi_remember_state
 759              		.cfi_def_cfa_offset 16
 760              		@ sp needed
 761 005e 70BD     		pop	{r4, r5, r6, pc}
 762              	.LVL66:
 763              	.L62:
 764              	.LCFI9:
 765              		.cfi_restore_state
 365:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 766              		.loc 1 365 3 is_stmt 1 discriminator 1 view .LVU238
 365:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 767              		.loc 1 365 3 discriminator 1 view .LVU239
 768 0060 0B4B     		ldr	r3, .L64+12
 769 0062 40F26D12 		movw	r2, #365
 770 0066 0B49     		ldr	r1, .L64+16
ARM GAS  /tmp/cczEqG5I.s 			page 24


 771 0068 0B48     		ldr	r0, .L64+20
 772              	.LVL67:
 365:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 773              		.loc 1 365 3 is_stmt 0 discriminator 1 view .LVU240
 774 006a FFF7FEFF 		bl	printf
 775              	.LVL68:
 776 006e CCE7     		b	.L57
 777              	.LVL69:
 778              	.L63:
 392:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     return ERR_IF;
 779              		.loc 1 392 5 is_stmt 1 view .LVU241
 780 0070 3046     		mov	r0, r6
 781 0072 FFF7FEFF 		bl	mem_free
 782              	.LVL70:
 393:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 783              		.loc 1 393 5 view .LVU242
 393:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 784              		.loc 1 393 12 is_stmt 0 view .LVU243
 785 0076 6FF00B00 		mvn	r0, #11
 786 007a EFE7     		b	.L58
 787              	.LVL71:
 788              	.L60:
 375:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 789              		.loc 1 375 12 view .LVU244
 790 007c 4FF0FF30 		mov	r0, #-1
 791              	.LVL72:
 375:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 792              		.loc 1 375 12 view .LVU245
 793 0080 ECE7     		b	.L58
 794              	.L65:
 795 0082 00BF     		.align	2
 796              	.L64:
 797 0084 00000000 		.word	slipif_output_v4
 798 0088 00000000 		.word	slipif_loop_thread
 799 008c 20000000 		.word	.LC6
 800 0090 00000000 		.word	.LC0
 801 0094 00000000 		.word	.LC5
 802 0098 40000000 		.word	.LC2
 803              		.cfi_endproc
 804              	.LFE179:
 806              		.section	.text.slipif_poll,"ax",%progbits
 807              		.align	1
 808              		.global	slipif_poll
 809              		.syntax unified
 810              		.thumb
 811              		.thumb_func
 813              	slipif_poll:
 814              	.LVL73:
 815              	.LFB180:
 418:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 419:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** /**
 420:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @ingroup slipif
 421:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * Polls the serial device and feeds the IP layer with incoming packets.
 422:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  *
 423:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  * @param netif The lwip network interface structure for this slipif
 424:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****  */
 425:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** void
ARM GAS  /tmp/cczEqG5I.s 			page 25


 426:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** slipif_poll(struct netif *netif)
 427:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** {
 816              		.loc 1 427 1 is_stmt 1 view -0
 817              		.cfi_startproc
 818              		@ args = 0, pretend = 0, frame = 8
 819              		@ frame_needed = 0, uses_anonymous_args = 0
 820              		.loc 1 427 1 is_stmt 0 view .LVU247
 821 0000 30B5     		push	{r4, r5, lr}
 822              	.LCFI10:
 823              		.cfi_def_cfa_offset 12
 824              		.cfi_offset 4, -12
 825              		.cfi_offset 5, -8
 826              		.cfi_offset 14, -4
 827 0002 83B0     		sub	sp, sp, #12
 828              	.LCFI11:
 829              		.cfi_def_cfa_offset 24
 428:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   u8_t c;
 830              		.loc 1 428 3 is_stmt 1 view .LVU248
 429:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   struct slipif_priv *priv;
 831              		.loc 1 429 3 view .LVU249
 430:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 431:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif != NULL", (netif != NULL));
 832              		.loc 1 431 3 view .LVU250
 833              		.loc 1 431 3 view .LVU251
 834 0004 0446     		mov	r4, r0
 835 0006 18B1     		cbz	r0, .L72
 836              	.LVL74:
 837              	.L67:
 838              		.loc 1 431 3 discriminator 3 view .LVU252
 839              		.loc 1 431 3 discriminator 3 view .LVU253
 432:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 840              		.loc 1 432 3 view .LVU254
 841              		.loc 1 432 3 view .LVU255
 842 0008 236A     		ldr	r3, [r4, #32]
 843 000a 4BB1     		cbz	r3, .L73
 844              	.L68:
 845              		.loc 1 432 3 discriminator 3 view .LVU256
 846              		.loc 1 432 3 discriminator 3 view .LVU257
 433:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 434:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   priv = (struct slipif_priv *)netif->state;
 847              		.loc 1 434 3 view .LVU258
 848              		.loc 1 434 8 is_stmt 0 view .LVU259
 849 000c 256A     		ldr	r5, [r4, #32]
 850              	.LVL75:
 435:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 436:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   while (sio_tryread(priv->sd, &c, 1) > 0) {
 851              		.loc 1 436 3 is_stmt 1 view .LVU260
 852              		.loc 1 436 9 is_stmt 0 view .LVU261
 853 000e 14E0     		b	.L69
 854              	.LVL76:
 855              	.L72:
 431:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 856              		.loc 1 431 3 is_stmt 1 discriminator 1 view .LVU262
 431:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 857              		.loc 1 431 3 discriminator 1 view .LVU263
 858 0010 0F4B     		ldr	r3, .L74
 859 0012 40F2AF12 		movw	r2, #431
ARM GAS  /tmp/cczEqG5I.s 			page 26


 860 0016 0F49     		ldr	r1, .L74+4
 861 0018 0F48     		ldr	r0, .L74+8
 862              	.LVL77:
 431:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   LWIP_ASSERT("netif->state != NULL", (netif->state != NULL));
 863              		.loc 1 431 3 is_stmt 0 discriminator 1 view .LVU264
 864 001a FFF7FEFF 		bl	printf
 865              	.LVL78:
 866 001e F3E7     		b	.L67
 867              	.L73:
 432:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 868              		.loc 1 432 3 is_stmt 1 discriminator 1 view .LVU265
 432:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** 
 869              		.loc 1 432 3 discriminator 1 view .LVU266
 870 0020 0B4B     		ldr	r3, .L74
 871 0022 4FF4D872 		mov	r2, #432
 872 0026 0D49     		ldr	r1, .L74+12
 873 0028 0B48     		ldr	r0, .L74+8
 874 002a FFF7FEFF 		bl	printf
 875              	.LVL79:
 876 002e EDE7     		b	.L68
 877              	.LVL80:
 878              	.L70:
 437:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     slipif_rxbyte_input(netif, c);
 879              		.loc 1 437 5 view .LVU267
 880 0030 9DF80710 		ldrb	r1, [sp, #7]	@ zero_extendqisi2
 881 0034 2046     		mov	r0, r4
 882 0036 FFF7FEFF 		bl	slipif_rxbyte_input
 883              	.LVL81:
 884              	.L69:
 436:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     slipif_rxbyte_input(netif, c);
 885              		.loc 1 436 39 view .LVU268
 436:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     slipif_rxbyte_input(netif, c);
 886              		.loc 1 436 10 is_stmt 0 view .LVU269
 887 003a 0122     		movs	r2, #1
 888 003c 0DF10701 		add	r1, sp, #7
 889 0040 2868     		ldr	r0, [r5]
 890 0042 FFF7FEFF 		bl	sio_tryread
 891              	.LVL82:
 436:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****     slipif_rxbyte_input(netif, c);
 892              		.loc 1 436 39 discriminator 1 view .LVU270
 893 0046 0028     		cmp	r0, #0
 894 0048 F2D1     		bne	.L70
 438:Middlewares/Third_Party/LwIP/src/netif/slipif.c ****   }
 439:Middlewares/Third_Party/LwIP/src/netif/slipif.c **** }
 895              		.loc 1 439 1 view .LVU271
 896 004a 03B0     		add	sp, sp, #12
 897              	.LCFI12:
 898              		.cfi_def_cfa_offset 12
 899              		@ sp needed
 900 004c 30BD     		pop	{r4, r5, pc}
 901              	.LVL83:
 902              	.L75:
 903              		.loc 1 439 1 view .LVU272
 904 004e 00BF     		.align	2
 905              	.L74:
 906 0050 00000000 		.word	.LC0
 907 0054 30000000 		.word	.LC1
ARM GAS  /tmp/cczEqG5I.s 			page 27


 908 0058 40000000 		.word	.LC2
 909 005c 68000000 		.word	.LC3
 910              		.cfi_endproc
 911              	.LFE180:
 913              		.text
 914              	.Letext0:
 915              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 916              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 917              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 918              		.file 5 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 919              		.file 6 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 920              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 921              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 922              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 923              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/mem.h"
 924              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 925              		.file 12 "Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h"
 926              		.file 13 "Middlewares/Third_Party/LwIP/system/arch/sys_arch.h"
 927              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/sys.h"
 928              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/sio.h"
 929              		.file 16 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
ARM GAS  /tmp/cczEqG5I.s 			page 28


DEFINED SYMBOLS
                            *ABS*:00000000 slipif.c
     /tmp/cczEqG5I.s:20     .rodata.slipif_rxbyte.str1.4:00000000 $d
     /tmp/cczEqG5I.s:33     .text.slipif_rxbyte:00000000 $t
     /tmp/cczEqG5I.s:38     .text.slipif_rxbyte:00000000 slipif_rxbyte
     /tmp/cczEqG5I.s:296    .text.slipif_rxbyte:000000e4 $d
     /tmp/cczEqG5I.s:304    .text.slipif_rxbyte_input:00000000 $t
     /tmp/cczEqG5I.s:309    .text.slipif_rxbyte_input:00000000 slipif_rxbyte_input
     /tmp/cczEqG5I.s:359    .text.slipif_loop_thread:00000000 $t
     /tmp/cczEqG5I.s:364    .text.slipif_loop_thread:00000000 slipif_loop_thread
     /tmp/cczEqG5I.s:411    .rodata.slipif_output.str1.4:00000000 $d
     /tmp/cczEqG5I.s:415    .text.slipif_output:00000000 $t
     /tmp/cczEqG5I.s:420    .text.slipif_output:00000000 slipif_output
     /tmp/cczEqG5I.s:597    .text.slipif_output:00000098 $d
     /tmp/cczEqG5I.s:606    .text.slipif_output_v4:00000000 $t
     /tmp/cczEqG5I.s:611    .text.slipif_output_v4:00000000 slipif_output_v4
     /tmp/cczEqG5I.s:635    .rodata.slipif_init.str1.4:00000000 $d
     /tmp/cczEqG5I.s:642    .text.slipif_init:00000000 $t
     /tmp/cczEqG5I.s:648    .text.slipif_init:00000000 slipif_init
     /tmp/cczEqG5I.s:797    .text.slipif_init:00000084 $d
     /tmp/cczEqG5I.s:807    .text.slipif_poll:00000000 $t
     /tmp/cczEqG5I.s:813    .text.slipif_poll:00000000 slipif_poll
     /tmp/cczEqG5I.s:906    .text.slipif_poll:00000050 $d

UNDEFINED SYMBOLS
printf
pbuf_realloc
pbuf_alloc
pbuf_cat
pbuf_free
sio_read
sio_send
mem_malloc
sio_open
sys_thread_new
mem_free
sio_tryread
