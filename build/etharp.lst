ARM GAS  /tmp/cchE5ny2.s 			page 1


   1              		.cpu cortex-m7
   2              		.arch armv7e-m
   3              		.fpu fpv5-d16
   4              		.eabi_attribute 28, 1
   5              		.eabi_attribute 20, 1
   6              		.eabi_attribute 21, 1
   7              		.eabi_attribute 23, 3
   8              		.eabi_attribute 24, 1
   9              		.eabi_attribute 25, 1
  10              		.eabi_attribute 26, 1
  11              		.eabi_attribute 30, 1
  12              		.eabi_attribute 34, 1
  13              		.eabi_attribute 18, 4
  14              		.file	"etharp.c"
  15              		.text
  16              	.Ltext0:
  17              		.cfi_sections	.debug_frame
  18              		.file 1 "Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c"
  19              		.section	.text.etharp_free_entry,"ax",%progbits
  20              		.align	1
  21              		.syntax unified
  22              		.thumb
  23              		.thumb_func
  25              	etharp_free_entry:
  26              	.LVL0:
  27              	.LFB170:
   1:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
   2:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @file
   3:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Address Resolution Protocol module for IP over Ethernet
   4:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
   5:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Functionally, ARP is divided into two parts. The first maps an IP address
   6:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * to a physical address when sending a packet, and the second part answers
   7:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * requests from other machines for our physical address.
   8:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
   9:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * This implementation complies with RFC 826 (Ethernet ARP). It supports
  10:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Gratuitious ARP from RFC3220 (IP Mobility Support for IPv4) section 4.6
  11:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * if an interface calls etharp_gratuitous(our_netif) upon address change.
  12:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
  13:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  14:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /*
  15:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Copyright (c) 2001-2003 Swedish Institute of Computer Science.
  16:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Copyright (c) 2003-2004 Leon Woestenberg <<EMAIL>>
  17:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Copyright (c) 2003-2004 Axon Digital Design B.V., The Netherlands.
  18:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * All rights reserved.
  19:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
  20:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Redistribution and use in source and binary forms, with or without modification,
  21:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * are permitted provided that the following conditions are met:
  22:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
  23:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * 1. Redistributions of source code must retain the above copyright notice,
  24:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *    this list of conditions and the following disclaimer.
  25:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * 2. Redistributions in binary form must reproduce the above copyright notice,
  26:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *    this list of conditions and the following disclaimer in the documentation
  27:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *    and/or other materials provided with the distribution.
  28:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * 3. The name of the author may not be used to endorse or promote products
  29:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *    derived from this software without specific prior written permission.
  30:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
  31:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
ARM GAS  /tmp/cchE5ny2.s 			page 2


  32:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
  33:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
  34:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  35:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
  36:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
  37:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
  38:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
  39:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
  40:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * OF SUCH DAMAGE.
  41:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
  42:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * This file is part of the lwIP TCP/IP stack.
  43:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
  44:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
  45:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  46:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/opt.h"
  47:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  48:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_IPV4 && LWIP_ARP /* don't build if not configured for use in lwipopts.h */
  49:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  50:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/etharp.h"
  51:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/stats.h"
  52:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/snmp.h"
  53:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/dhcp.h"
  54:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/autoip.h"
  55:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "lwip/prot/iana.h"
  56:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include "netif/ethernet.h"
  57:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  58:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include <string.h>
  59:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  60:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #ifdef LWIP_HOOK_FILENAME
  61:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #include LWIP_HOOK_FILENAME
  62:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
  63:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  64:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Re-request a used ARP entry 1 minute before it would expire to prevent
  65:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *  breaking a steadily used connection because the ARP entry timed out. */
  66:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ARP_AGE_REREQUEST_USED_UNICAST   (ARP_MAXAGE - 30)
  67:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ARP_AGE_REREQUEST_USED_BROADCAST (ARP_MAXAGE - 15)
  68:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  69:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** the time an ARP entry stays pending after first request,
  70:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *  for ARP_TMR_INTERVAL = 1000, this is
  71:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *  10 seconds.
  72:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
  73:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *  @internal Keep this number at least 2, otherwise it might
  74:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *  run out instantly if the timeout occurs directly after a request.
  75:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
  76:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ARP_MAXPENDING 5
  77:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  78:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** ARP states */
  79:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** enum etharp_state {
  80:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATE_EMPTY = 0,
  81:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATE_PENDING,
  82:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATE_STABLE,
  83:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATE_STABLE_REREQUESTING_1,
  84:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATE_STABLE_REREQUESTING_2
  85:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
  86:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   , ETHARP_STATE_STATIC
  87:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_SUPPORT_STATIC_ENTRIES */
  88:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** };
ARM GAS  /tmp/cchE5ny2.s 			page 3


  89:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
  90:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** struct etharp_entry {
  91:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ARP_QUEUEING
  92:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /** Pointer to queue of pending outgoing packets on this ARP entry. */
  93:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct etharp_q_entry *q;
  94:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #else /* ARP_QUEUEING */
  95:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /** Pointer to a single pending outgoing packet on this ARP entry. */
  96:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct pbuf *q;
  97:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ARP_QUEUEING */
  98:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ip4_addr_t ipaddr;
  99:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct netif *netif;
 100:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct eth_addr ethaddr;
 101:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   u16_t ctime;
 102:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   u8_t state;
 103:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** };
 104:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 105:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static struct etharp_entry arp_table[ARP_TABLE_SIZE];
 106:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 107:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if !LWIP_NETIF_HWADDRHINT
 108:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static netif_addr_idx_t etharp_cached_entry;
 109:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* !LWIP_NETIF_HWADDRHINT */
 110:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Try hard to create a new entry - we want the IP address to appear in
 112:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     the cache (even if this means removing an active entry or so). */
 113:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ETHARP_FLAG_TRY_HARD     1
 114:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ETHARP_FLAG_FIND_ONLY    2
 115:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 116:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ETHARP_FLAG_STATIC_ENTRY 4
 117:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_SUPPORT_STATIC_ENTRIES */
 118:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 119:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_NETIF_HWADDRHINT
 120:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ETHARP_SET_ADDRHINT(netif, addrhint)  do { if (((netif) != NULL) && ((netif)->hints != NULL
 121:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                                               (netif)->hints->addr_hint = (addrhint); }} while(0)
 122:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #else /* LWIP_NETIF_HWADDRHINT */
 123:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define ETHARP_SET_ADDRHINT(netif, addrhint)  (etharp_cached_entry = (addrhint))
 124:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_NETIF_HWADDRHINT */
 125:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 126:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 127:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /* Check for maximum ARP_TABLE_SIZE */
 128:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if (ARP_TABLE_SIZE > NETIF_ADDR_IDX_MAX)
 129:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #error "ARP_TABLE_SIZE must fit in an s16_t, you have to reduce it in your lwipopts.h"
 130:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 131:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 132:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 133:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static err_t etharp_request_dst(struct netif *netif, const ip4_addr_t *ipaddr, const struct eth_add
 134:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static err_t etharp_raw(struct netif *netif,
 135:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                         const struct eth_addr *ethsrc_addr, const struct eth_addr *ethdst_addr,
 136:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                         const struct eth_addr *hwsrc_addr, const ip4_addr_t *ipsrc_addr,
 137:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                         const struct eth_addr *hwdst_addr, const ip4_addr_t *ipdst_addr,
 138:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                         const u16_t opcode);
 139:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 140:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ARP_QUEUEING
 141:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 142:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Free a complete queue of etharp entries
 143:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 144:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param q a qeueue of etharp_q_entry's to free
 145:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
ARM GAS  /tmp/cchE5ny2.s 			page 4


 146:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static void
 147:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** free_etharp_q(struct etharp_q_entry *q)
 148:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 149:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct etharp_q_entry *r;
 150:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 151:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   while (q) {
 152:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     r = q;
 153:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     q = q->next;
 154:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_ASSERT("r->p != NULL", (r->p != NULL));
 155:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     pbuf_free(r->p);
 156:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     memp_free(MEMP_ARP_QUEUE, r);
 157:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 158:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 159:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #else /* ARP_QUEUEING */
 160:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 161:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Compatibility define: free the queued pbuf */
 162:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #define free_etharp_q(q) pbuf_free(q)
 163:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 164:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ARP_QUEUEING */
 165:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 166:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Clean up ARP table entries */
 167:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static void
 168:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_free_entry(int i)
 169:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
  28              		.loc 1 169 1 view -0
  29              		.cfi_startproc
  30              		@ args = 0, pretend = 0, frame = 0
  31              		@ frame_needed = 0, uses_anonymous_args = 0
  32              		.loc 1 169 1 is_stmt 0 view .LVU1
  33 0000 10B5     		push	{r4, lr}
  34              	.LCFI0:
  35              		.cfi_def_cfa_offset 8
  36              		.cfi_offset 4, -8
  37              		.cfi_offset 14, -4
  38 0002 0446     		mov	r4, r0
 170:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* remove from SNMP ARP index tree */
 171:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   mib2_remove_arp_entry(arp_table[i].netif, &arp_table[i].ipaddr);
  39              		.loc 1 171 66 is_stmt 1 view .LVU2
 172:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* and empty packet queue */
 173:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (arp_table[i].q != NULL) {
  40              		.loc 1 173 3 view .LVU3
  41              		.loc 1 173 19 is_stmt 0 view .LVU4
  42 0004 00EB4002 		add	r2, r0, r0, lsl #1
  43 0008 094B     		ldr	r3, .L4
  44 000a 53F83200 		ldr	r0, [r3, r2, lsl #3]
  45              	.LVL1:
  46              		.loc 1 173 6 view .LVU5
  47 000e 38B1     		cbz	r0, .L2
 174:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* remove all queued packets */
 175:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG, ("etharp_free_entry: freeing entry %"U16_F", packet queue %p.\n", (u1
  48              		.loc 1 175 133 is_stmt 1 view .LVU6
 176:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     free_etharp_q(arp_table[i].q);
  49              		.loc 1 176 5 view .LVU7
  50 0010 FFF7FEFF 		bl	pbuf_free
  51              	.LVL2:
 177:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].q = NULL;
  52              		.loc 1 177 5 view .LVU8
ARM GAS  /tmp/cchE5ny2.s 			page 5


  53              		.loc 1 177 20 is_stmt 0 view .LVU9
  54 0014 04EB4403 		add	r3, r4, r4, lsl #1
  55 0018 DB00     		lsls	r3, r3, #3
  56 001a 054A     		ldr	r2, .L4
  57 001c 0021     		movs	r1, #0
  58 001e D150     		str	r1, [r2, r3]
  59              	.L2:
 178:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 179:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* recycle entry for re-use */
 180:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].state = ETHARP_STATE_EMPTY;
  60              		.loc 1 180 3 is_stmt 1 view .LVU10
  61              		.loc 1 180 22 is_stmt 0 view .LVU11
  62 0020 04EB4404 		add	r4, r4, r4, lsl #1
  63              	.LVL3:
  64              		.loc 1 180 22 view .LVU12
  65 0024 024B     		ldr	r3, .L4
  66 0026 03EBC403 		add	r3, r3, r4, lsl #3
  67 002a 0022     		movs	r2, #0
  68 002c 1A75     		strb	r2, [r3, #20]
 181:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #ifdef LWIP_DEBUG
 182:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* for debugging, clean out the complete entry */
 183:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].ctime = 0;
 184:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].netif = NULL;
 185:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ip4_addr_set_zero(&arp_table[i].ipaddr);
 186:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].ethaddr = ethzero;
 187:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_DEBUG */
 188:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
  69              		.loc 1 188 1 view .LVU13
  70 002e 10BD     		pop	{r4, pc}
  71              	.L5:
  72              		.align	2
  73              	.L4:
  74 0030 00000000 		.word	arp_table
  75              		.cfi_endproc
  76              	.LFE170:
  78              		.section	.rodata.etharp_find_entry.str1.4,"aMS",%progbits,1
  79              		.align	2
  80              	.LC0:
  81 0000 4D696464 		.ascii	"Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c"
  81      6C657761 
  81      7265732F 
  81      54686972 
  81      645F5061 
  82 0033 00       		.ascii	"\000"
  83              		.align	2
  84              	.LC1:
  85 0034 6172705F 		.ascii	"arp_table[i].q == NULL\000"
  85      7461626C 
  85      655B695D 
  85      2E71203D 
  85      3D204E55 
  86 004b 00       		.align	2
  87              	.LC2:
  88 004c 41737365 		.ascii	"Assertion \"%s\" failed at line %d in %s\012\000"
  88      7274696F 
  88      6E202225 
  88      73222066 
ARM GAS  /tmp/cchE5ny2.s 			page 6


  88      61696C65 
  89              		.align	2
  90              	.LC3:
  91 0074 69203C20 		.ascii	"i < ARP_TABLE_SIZE\000"
  91      4152505F 
  91      5441424C 
  91      455F5349 
  91      5A4500
  92 0087 00       		.align	2
  93              	.LC4:
  94 0088 6172705F 		.ascii	"arp_table[i].state == ETHARP_STATE_EMPTY\000"
  94      7461626C 
  94      655B695D 
  94      2E737461 
  94      7465203D 
  95              		.section	.text.etharp_find_entry,"ax",%progbits
  96              		.align	1
  97              		.syntax unified
  98              		.thumb
  99              		.thumb_func
 101              	etharp_find_entry:
 102              	.LVL4:
 103              	.LFB172:
 189:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 190:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 191:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Clears expired entries in the ARP table.
 192:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 193:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * This function should be called every ARP_TMR_INTERVAL milliseconds (1 second),
 194:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * in order to expire entries in the ARP table.
 195:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 196:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** void
 197:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_tmr(void)
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int i;
 200:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 201:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG, ("etharp_timer\n"));
 202:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* remove expired entries from the ARP table */
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   for (i = 0; i < ARP_TABLE_SIZE; ++i) {
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (state != ETHARP_STATE_EMPTY
 206:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 207:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         && (state != ETHARP_STATE_STATIC)
 208:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_SUPPORT_STATIC_ENTRIES */
 209:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        ) {
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       arp_table[i].ctime++;
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].ctime >= ARP_MAXAGE) ||
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           ((arp_table[i].state == ETHARP_STATE_PENDING)  &&
 213:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            (arp_table[i].ctime >= ARP_MAXPENDING))) {
 214:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* pending or stable entry has become old! */
 215:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG, ("etharp_timer: expired %s entry %d.\n",
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                                    arp_table[i].state >= ETHARP_STATE_STABLE ? "stable" : "pending"
 217:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* clean up entries that have just been expired */
 218:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         etharp_free_entry(i);
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_1) {
 220:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* Don't send more than one request every 2 seconds. */
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[i].state = ETHARP_STATE_STABLE_REREQUESTING_2;
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_2) {
ARM GAS  /tmp/cchE5ny2.s 			page 7


 223:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* Reset state to stable, so that the next transmitted packet will
 224:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            re-send an ARP request. */
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[i].state = ETHARP_STATE_STABLE;
 226:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
 227:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* still pending, resend an ARP query */
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         etharp_request(arp_table[i].netif, &arp_table[i].ipaddr);
 229:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 230:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 231:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 233:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 234:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 235:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Search the ARP table for a matching or new entry.
 236:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 237:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If an IP address is given, return a pending or stable ARP entry that matches
 238:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * the address. If no match is found, create a new entry with this address set,
 239:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * but in state ETHARP_EMPTY. The caller must check and possibly change the
 240:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * state of the returned entry.
 241:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 242:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If ipaddr is NULL, return a initialized new entry in state ETHARP_EMPTY.
 243:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 244:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * In all cases, attempt to create new entries from an empty entry. If no
 245:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * empty entries are available and ETHARP_FLAG_TRY_HARD flag is set, recycle
 246:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * old entries. Heuristic choose the least important entry for recycling.
 247:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 248:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr IP address to find in ARP cache, or to add if not found.
 249:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param flags See @ref etharp_state
 250:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif netif related to this address (used for NETIF_HWADDRHINT)
 251:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 252:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return The ARP entry index that matched or is created, ERR_MEM if no
 253:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * entry is found or could be recycled.
 254:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 255:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static s16_t
 256:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_find_entry(const ip4_addr_t *ipaddr, u8_t flags, struct netif *netif)
 257:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 104              		.loc 1 257 1 is_stmt 1 view -0
 105              		.cfi_startproc
 106              		@ args = 0, pretend = 0, frame = 16
 107              		@ frame_needed = 0, uses_anonymous_args = 0
 108              		.loc 1 257 1 is_stmt 0 view .LVU15
 109 0000 2DE9F04F 		push	{r4, r5, r6, r7, r8, r9, r10, fp, lr}
 110              	.LCFI1:
 111              		.cfi_def_cfa_offset 36
 112              		.cfi_offset 4, -36
 113              		.cfi_offset 5, -32
 114              		.cfi_offset 6, -28
 115              		.cfi_offset 7, -24
 116              		.cfi_offset 8, -20
 117              		.cfi_offset 9, -16
 118              		.cfi_offset 10, -12
 119              		.cfi_offset 11, -8
 120              		.cfi_offset 14, -4
 121 0004 85B0     		sub	sp, sp, #20
 122              	.LCFI2:
 123              		.cfi_def_cfa_offset 56
 124 0006 0646     		mov	r6, r0
 125 0008 1746     		mov	r7, r2
ARM GAS  /tmp/cchE5ny2.s 			page 8


 258:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t old_pending = ARP_TABLE_SIZE, old_stable = ARP_TABLE_SIZE;
 126              		.loc 1 258 3 is_stmt 1 view .LVU16
 127              	.LVL5:
 259:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t empty = ARP_TABLE_SIZE;
 128              		.loc 1 259 3 view .LVU17
 260:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i = 0;
 129              		.loc 1 260 3 view .LVU18
 261:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* oldest entry with packets on queue */
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t old_queue = ARP_TABLE_SIZE;
 130              		.loc 1 262 3 view .LVU19
 263:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* its age */
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   u16_t age_queue = 0, age_pending = 0, age_stable = 0;
 131              		.loc 1 264 3 view .LVU20
 265:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 266:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_UNUSED_ARG(netif);
 132              		.loc 1 266 3 view .LVU21
 267:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 268:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /**
 269:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * a) do a search through the cache, remember candidates
 270:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * b) select candidate entry
 271:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * c) create new entry
 272:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    */
 273:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 274:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* a) in a single search sweep, do all of this
 275:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 1) remember the first empty entry (if any)
 276:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 2) remember the oldest stable entry (if any)
 277:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 3) remember the oldest pending entry without queued packets (if any)
 278:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 4) remember the oldest pending entry with queued packets (if any)
 279:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 5) search for a matching IP entry, either pending or stable
 280:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    *    until 5 matches, or all entries are searched for.
 281:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    */
 282:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   for (i = 0; i < ARP_TABLE_SIZE; ++i) {
 133              		.loc 1 283 3 view .LVU22
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 134              		.loc 1 264 41 is_stmt 0 view .LVU23
 135 000a 4FF00009 		mov	r9, #0
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 136              		.loc 1 264 24 view .LVU24
 137 000e CDF80C90 		str	r9, [sp, #12]
 264:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 138              		.loc 1 264 9 view .LVU25
 139 0012 CB46     		mov	fp, r9
 262:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* its age */
 140              		.loc 1 262 9 view .LVU26
 141 0014 4FF00A0A 		mov	r10, #10
 142              		.loc 1 283 10 view .LVU27
 143 0018 4B46     		mov	r3, r9
 259:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i = 0;
 144              		.loc 1 259 9 view .LVU28
 145 001a 5546     		mov	r5, r10
 258:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t empty = ARP_TABLE_SIZE;
 146              		.loc 1 258 39 view .LVU29
 147 001c D046     		mov	r8, r10
 258:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t empty = ARP_TABLE_SIZE;
 148              		.loc 1 258 9 view .LVU30
 149 001e CDF808A0 		str	r10, [sp, #8]
ARM GAS  /tmp/cchE5ny2.s 			page 9


 150 0022 0191     		str	r1, [sp, #4]
 151              		.loc 1 283 3 view .LVU31
 152 0024 29E0     		b	.L7
 153              	.LVL6:
 154              	.L33:
 155              	.LBB2:
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 285:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* no empty entry found yet and now we do find one? */
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if ((empty == ARP_TABLE_SIZE) && (state == ETHARP_STATE_EMPTY)) {
 287:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG, ("etharp_find_entry: found empty entry %d\n", (int)i));
 288:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* remember first empty entry */
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       empty = i;
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else if (state != ETHARP_STATE_EMPTY) {
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_ASSERT("state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE",
 292:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                   state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE);
 293:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* if given, does IP address match IP address in ARP entry? */
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (ipaddr && ip4_addr_cmp(ipaddr, &arp_table[i].ipaddr)
 295:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 296:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           && ((netif == NULL) || (netif == arp_table[i].netif))
 156              		.loc 1 296 11 view .LVU32
 157 0026 002F     		cmp	r7, #0
 158 0028 00F0AD80 		beq	.L21
 159              		.loc 1 296 56 discriminator 1 view .LVU33
 160 002c 5A4A     		ldr	r2, .L38
 161 002e 02EBCE02 		add	r2, r2, lr, lsl #3
 162 0032 9268     		ldr	r2, [r2, #8]
 163              		.loc 1 296 31 discriminator 1 view .LVU34
 164 0034 BA42     		cmp	r2, r7
 165 0036 3AD1     		bne	.L9
 297:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_TABLE_MATCH_NETIF */
 298:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          ) {
 299:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: found matching entry %d\n",
 300:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* found exact IP address match, simply bail out */
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         return i;
 166              		.loc 1 301 16 view .LVU35
 167 0038 1D46     		mov	r5, r3
 168              	.LVL7:
 169              		.loc 1 301 16 view .LVU36
 170 003a 7EE0     		b	.L10
 171              	.LVL8:
 172              	.L34:
 302:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 303:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* pending entry? */
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (state == ETHARP_STATE_PENDING) {
 305:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* pending with queued packets? */
 306:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         if (arp_table[i].q != NULL) {
 173              		.loc 1 306 9 is_stmt 1 view .LVU37
 174              		.loc 1 306 25 is_stmt 0 view .LVU38
 175 003c 04EB4402 		add	r2, r4, r4, lsl #1
 176 0040 5548     		ldr	r0, .L38
 177              	.LVL9:
 178              		.loc 1 306 25 view .LVU39
 179 0042 50F83220 		ldr	r2, [r0, r2, lsl #3]
 180              		.loc 1 306 12 view .LVU40
 181 0046 4AB1     		cbz	r2, .L12
 307:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           if (arp_table[i].ctime >= age_queue) {
 182              		.loc 1 307 11 is_stmt 1 view .LVU41
ARM GAS  /tmp/cchE5ny2.s 			page 10


 183              		.loc 1 307 27 is_stmt 0 view .LVU42
 184 0048 04EB4404 		add	r4, r4, r4, lsl #1
 185 004c 00EBC402 		add	r2, r0, r4, lsl #3
 186 0050 528A     		ldrh	r2, [r2, #18]
 187              		.loc 1 307 14 view .LVU43
 188 0052 5A45     		cmp	r2, fp
 189 0054 0FD3     		bcc	.L8
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_queue = i;
 309:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             age_queue = arp_table[i].ctime;
 190              		.loc 1 309 23 view .LVU44
 191 0056 9346     		mov	fp, r2
 192              	.LVL10:
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_queue = i;
 193              		.loc 1 308 23 view .LVU45
 194 0058 9A46     		mov	r10, r3
 195              	.LVL11:
 308:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_queue = i;
 196              		.loc 1 308 23 view .LVU46
 197 005a 0CE0     		b	.L8
 198              	.LVL12:
 199              	.L12:
 310:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           }
 311:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         } else
 312:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* pending without queued packets? */
 313:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         {
 314:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           if (arp_table[i].ctime >= age_pending) {
 200              		.loc 1 314 11 is_stmt 1 view .LVU47
 201              		.loc 1 314 27 is_stmt 0 view .LVU48
 202 005c 04EB4404 		add	r4, r4, r4, lsl #1
 203 0060 4D4A     		ldr	r2, .L38
 204 0062 02EBC402 		add	r2, r2, r4, lsl #3
 205 0066 528A     		ldrh	r2, [r2, #18]
 206              		.loc 1 314 14 view .LVU49
 207 0068 0399     		ldr	r1, [sp, #12]
 208 006a 8A42     		cmp	r2, r1
 209 006c 03D3     		bcc	.L8
 315:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_pending = i;
 316:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             age_pending = arp_table[i].ctime;
 210              		.loc 1 316 25 view .LVU50
 211 006e 0392     		str	r2, [sp, #12]
 212              	.LVL13:
 315:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_pending = i;
 213              		.loc 1 315 25 view .LVU51
 214 0070 0293     		str	r3, [sp, #8]
 215              	.LVL14:
 315:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_pending = i;
 216              		.loc 1 315 25 view .LVU52
 217 0072 00E0     		b	.L8
 218              	.LVL15:
 219              	.L20:
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else if (state != ETHARP_STATE_EMPTY) {
 220              		.loc 1 289 13 view .LVU53
 221 0074 1D46     		mov	r5, r3
 222              	.LVL16:
 223              	.L8:
 289:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else if (state != ETHARP_STATE_EMPTY) {
 224              		.loc 1 289 13 view .LVU54
ARM GAS  /tmp/cchE5ny2.s 			page 11


 225              	.LBE2:
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 226              		.loc 1 283 35 is_stmt 1 discriminator 2 view .LVU55
 227 0076 0133     		adds	r3, r3, #1
 228              	.LVL17:
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 229              		.loc 1 283 35 is_stmt 0 discriminator 2 view .LVU56
 230 0078 1BB2     		sxth	r3, r3
 231              	.LVL18:
 232              	.L7:
 283:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 233              		.loc 1 283 17 is_stmt 1 discriminator 1 view .LVU57
 234 007a 092B     		cmp	r3, #9
 235 007c 26DC     		bgt	.L32
 236              	.LBB3:
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* no empty entry found yet and now we do find one? */
 237              		.loc 1 284 5 view .LVU58
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* no empty entry found yet and now we do find one? */
 238              		.loc 1 284 30 is_stmt 0 view .LVU59
 239 007e 1C46     		mov	r4, r3
 284:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* no empty entry found yet and now we do find one? */
 240              		.loc 1 284 10 view .LVU60
 241 0080 03EB4300 		add	r0, r3, r3, lsl #1
 242 0084 444A     		ldr	r2, .L38
 243 0086 02EBC002 		add	r2, r2, r0, lsl #3
 244 008a 107D     		ldrb	r0, [r2, #20]	@ zero_extendqisi2
 245              	.LVL19:
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG, ("etharp_find_entry: found empty entry %d\n", (int)i));
 246              		.loc 1 286 5 is_stmt 1 view .LVU61
 286:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG, ("etharp_find_entry: found empty entry %d\n", (int)i));
 247              		.loc 1 286 8 is_stmt 0 view .LVU62
 248 008c 0A2D     		cmp	r5, #10
 249 008e 08BF     		it	eq
 250 0090 0028     		cmpeq	r0, #0
 251 0092 EFD0     		beq	.L20
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_ASSERT("state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE",
 252              		.loc 1 290 12 is_stmt 1 view .LVU63
 290:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_ASSERT("state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE",
 253              		.loc 1 290 15 is_stmt 0 view .LVU64
 254 0094 0028     		cmp	r0, #0
 255 0096 EED0     		beq	.L8
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                   state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE);
 256              		.loc 1 291 7 is_stmt 1 view .LVU65
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                   state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE);
 257              		.loc 1 291 7 view .LVU66
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                   state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE);
 258              		.loc 1 291 7 discriminator 3 view .LVU67
 291:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                   state == ETHARP_STATE_PENDING || state >= ETHARP_STATE_STABLE);
 259              		.loc 1 291 7 discriminator 3 view .LVU68
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 260              		.loc 1 294 7 view .LVU69
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 261              		.loc 1 294 10 is_stmt 0 view .LVU70
 262 0098 4EB1     		cbz	r6, .L9
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 263              		.loc 1 294 21 discriminator 1 view .LVU71
 264 009a 03EB430E 		add	lr, r3, r3, lsl #1
ARM GAS  /tmp/cchE5ny2.s 			page 12


 265 009e 3E4A     		ldr	r2, .L38
 266 00a0 02EBCE0C 		add	ip, r2, lr, lsl #3
 267 00a4 DCF80420 		ldr	r2, [ip, #4]
 294:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 268              		.loc 1 294 18 discriminator 1 view .LVU72
 269 00a8 3168     		ldr	r1, [r6]
 270 00aa 9142     		cmp	r1, r2
 271 00ac BBD0     		beq	.L33
 272              	.L9:
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* pending with queued packets? */
 273              		.loc 1 304 7 is_stmt 1 view .LVU73
 304:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* pending with queued packets? */
 274              		.loc 1 304 10 is_stmt 0 view .LVU74
 275 00ae 0128     		cmp	r0, #1
 276 00b0 C4D0     		beq	.L34
 317:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           }
 318:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 319:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* stable entry? */
 320:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (state >= ETHARP_STATE_STABLE) {
 277              		.loc 1 320 14 is_stmt 1 view .LVU75
 278              		.loc 1 320 17 is_stmt 0 view .LVU76
 279 00b2 0128     		cmp	r0, #1
 280 00b4 DFD9     		bls	.L8
 321:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 322:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* don't record old_stable for static entries since they never expire */
 323:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         if (state < ETHARP_STATE_STATIC)
 324:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_SUPPORT_STATIC_ENTRIES */
 325:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         {
 326:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* remember entry with oldest stable entry in oldest, its age in maxtime */
 327:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           if (arp_table[i].ctime >= age_stable) {
 281              		.loc 1 327 11 is_stmt 1 view .LVU77
 282              		.loc 1 327 27 is_stmt 0 view .LVU78
 283 00b6 04EB4404 		add	r4, r4, r4, lsl #1
 284 00ba 374A     		ldr	r2, .L38
 285 00bc 02EBC402 		add	r2, r2, r4, lsl #3
 286 00c0 528A     		ldrh	r2, [r2, #18]
 287              		.loc 1 327 14 view .LVU79
 288 00c2 4A45     		cmp	r2, r9
 289 00c4 D7D3     		bcc	.L8
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_stable = i;
 329:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             age_stable = arp_table[i].ctime;
 290              		.loc 1 329 24 view .LVU80
 291 00c6 9146     		mov	r9, r2
 292              	.LVL20:
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_stable = i;
 293              		.loc 1 328 24 view .LVU81
 294 00c8 9846     		mov	r8, r3
 295              	.LVL21:
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_stable = i;
 296              		.loc 1 328 24 view .LVU82
 297 00ca D4E7     		b	.L8
 298              	.LVL22:
 299              	.L32:
 328:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             old_stable = i;
 300              		.loc 1 328 24 view .LVU83
 301              	.LBE3:
 330:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           }
ARM GAS  /tmp/cchE5ny2.s 			page 13


 331:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 332:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 333:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 334:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 335:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* { we have no match } => try to create a new entry */
 336:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 337:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* don't create new entry, only search? */
 338:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (((flags & ETHARP_FLAG_FIND_ONLY) != 0) ||
 302              		.loc 1 338 6 view .LVU84
 303 00cc 0199     		ldr	r1, [sp, #4]
 304              		.loc 1 338 3 is_stmt 1 view .LVU85
 305              		.loc 1 338 6 is_stmt 0 view .LVU86
 306 00ce 11F0020F 		tst	r1, #2
 307 00d2 5AD1     		bne	.L26
 308              		.loc 1 338 46 discriminator 1 view .LVU87
 309 00d4 0A2D     		cmp	r5, #10
 310 00d6 34D0     		beq	.L35
 311              	.L14:
 339:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* or no empty entry found and not allowed to recycle? */
 340:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ((empty == ARP_TABLE_SIZE) && ((flags & ETHARP_FLAG_TRY_HARD) == 0))) {
 341:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: no empty entry found and not al
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return (s16_t)ERR_MEM;
 343:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 344:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 345:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* b) choose the least destructive entry to recycle:
 346:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 1) empty entry
 347:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 2) oldest stable entry
 348:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 3) oldest pending entry without queued packets
 349:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 4) oldest pending entry with queued packets
 350:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    *
 351:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * { ETHARP_FLAG_TRY_HARD is set at this point }
 352:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    */
 353:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 354:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* 1) empty entry available? */
 355:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (empty < ARP_TABLE_SIZE) {
 312              		.loc 1 355 3 is_stmt 1 view .LVU88
 313              		.loc 1 355 6 is_stmt 0 view .LVU89
 314 00d8 092D     		cmp	r5, #9
 315 00da 16DD     		ble	.L15
 356:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     i = empty;
 357:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting empty entry %d\n", (i
 358:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else {
 359:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* 2) found recyclable stable entry? */
 360:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (old_stable < ARP_TABLE_SIZE) {
 316              		.loc 1 360 5 is_stmt 1 view .LVU90
 317              		.loc 1 360 8 is_stmt 0 view .LVU91
 318 00dc B8F1090F 		cmp	r8, #9
 319 00e0 35DC     		bgt	.L16
 361:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest stable*/
 362:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       i = old_stable;
 320              		.loc 1 362 7 is_stmt 1 view .LVU92
 321              	.LVL23:
 363:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest stable entry
 322              		.loc 1 363 116 view .LVU93
 364:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* no queued packets should exist on stable entries */
 365:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_ASSERT("arp_table[i].q == NULL", arp_table[i].q == NULL);
 323              		.loc 1 365 7 view .LVU94
ARM GAS  /tmp/cchE5ny2.s 			page 14


 324              		.loc 1 365 7 view .LVU95
 325 00e2 08EB4803 		add	r3, r8, r8, lsl #1
 326 00e6 2C4A     		ldr	r2, .L38
 327 00e8 52F83330 		ldr	r3, [r2, r3, lsl #3]
 328 00ec 002B     		cmp	r3, #0
 329 00ee 36D0     		beq	.L28
 330              		.loc 1 365 7 discriminator 1 view .LVU96
 331              		.loc 1 365 7 discriminator 1 view .LVU97
 332 00f0 2A4B     		ldr	r3, .L38+4
 333 00f2 40F26D12 		movw	r2, #365
 334 00f6 2A49     		ldr	r1, .L38+8
 335 00f8 2A48     		ldr	r0, .L38+12
 336 00fa FFF7FEFF 		bl	printf
 337              	.LVL24:
 338 00fe 4546     		mov	r5, r8
 339              	.LVL25:
 340              	.L17:
 366:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* 3) found recyclable pending entry without queued packets? */
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else if (old_pending < ARP_TABLE_SIZE) {
 368:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending */
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       i = old_pending;
 370:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 371:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* 4) found recyclable pending entry with queued packets? */
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else if (old_queue < ARP_TABLE_SIZE) {
 373:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending (queued packets are free in etharp_free_entry) */
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       i = old_queue;
 375:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 376:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* no empty or recyclable entries found */
 377:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else {
 378:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: no empty or recyclable entrie
 379:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       return (s16_t)ERR_MEM;
 380:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 381:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 382:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* { empty or recyclable entry found } */
 383:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_ASSERT("i < ARP_TABLE_SIZE", i < ARP_TABLE_SIZE);
 341              		.loc 1 383 5 discriminator 3 view .LVU98
 342              		.loc 1 383 5 discriminator 3 view .LVU99
 384:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     etharp_free_entry(i);
 343              		.loc 1 384 5 view .LVU100
 344 0100 2846     		mov	r0, r5
 345 0102 FFF7FEFF 		bl	etharp_free_entry
 346              	.LVL26:
 385:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 386:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 387:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("i < ARP_TABLE_SIZE", i < ARP_TABLE_SIZE);
 347              		.loc 1 387 3 view .LVU101
 348              		.loc 1 387 3 view .LVU102
 349 0106 092D     		cmp	r5, #9
 350 0108 2DDC     		bgt	.L36
 351              	.LVL27:
 352              	.L15:
 353              		.loc 1 387 3 discriminator 3 view .LVU103
 354              		.loc 1 387 3 discriminator 3 view .LVU104
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[i].state == ETHARP_STATE_EMPTY",
 355              		.loc 1 388 3 view .LVU105
 356              		.loc 1 388 3 view .LVU106
 357 010a 2C46     		mov	r4, r5
ARM GAS  /tmp/cchE5ny2.s 			page 15


 358 010c 05EB4502 		add	r2, r5, r5, lsl #1
 359 0110 214B     		ldr	r3, .L38
 360 0112 03EBC203 		add	r3, r3, r2, lsl #3
 361 0116 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 362 0118 6BBB     		cbnz	r3, .L37
 363              	.L18:
 364              		.loc 1 388 3 discriminator 3 view .LVU107
 365              		.loc 1 388 3 discriminator 3 view .LVU108
 389:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[i].state == ETHARP_STATE_EMPTY);
 390:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 391:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* IP address given? */
 392:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (ipaddr != NULL) {
 366              		.loc 1 392 3 view .LVU109
 367              		.loc 1 392 6 is_stmt 0 view .LVU110
 368 011a 36B1     		cbz	r6, .L19
 393:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* set IP address */
 394:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ip4_addr_copy(arp_table[i].ipaddr, *ipaddr);
 369              		.loc 1 394 5 is_stmt 1 view .LVU111
 370 011c 3168     		ldr	r1, [r6]
 371 011e 04EB4402 		add	r2, r4, r4, lsl #1
 372 0122 1D4B     		ldr	r3, .L38
 373 0124 03EBC203 		add	r3, r3, r2, lsl #3
 374 0128 5960     		str	r1, [r3, #4]
 375              	.L19:
 395:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 396:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].ctime = 0;
 376              		.loc 1 396 3 view .LVU112
 377              		.loc 1 396 22 is_stmt 0 view .LVU113
 378 012a 1B4B     		ldr	r3, .L38
 379 012c 04EB4402 		add	r2, r4, r4, lsl #1
 380 0130 03EBC202 		add	r2, r3, r2, lsl #3
 381 0134 0021     		movs	r1, #0
 382 0136 5182     		strh	r1, [r2, #18]	@ movhi
 397:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 398:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].netif = netif;
 383              		.loc 1 398 3 is_stmt 1 view .LVU114
 384              		.loc 1 398 22 is_stmt 0 view .LVU115
 385 0138 9760     		str	r7, [r2, #8]
 399:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_TABLE_MATCH_NETIF */
 400:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return (s16_t)i;
 386              		.loc 1 400 3 is_stmt 1 view .LVU116
 387              	.L10:
 401:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 388              		.loc 1 401 1 is_stmt 0 view .LVU117
 389 013a 2846     		mov	r0, r5
 390 013c 05B0     		add	sp, sp, #20
 391              	.LCFI3:
 392              		.cfi_remember_state
 393              		.cfi_def_cfa_offset 36
 394              		@ sp needed
 395 013e BDE8F08F 		pop	{r4, r5, r6, r7, r8, r9, r10, fp, pc}
 396              	.LVL28:
 397              	.L35:
 398              	.LCFI4:
 399              		.cfi_restore_state
 340:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: no empty entry found and not al
 400              		.loc 1 340 34 view .LVU118
ARM GAS  /tmp/cchE5ny2.s 			page 16


 401 0142 11F0010F 		tst	r1, #1
 402 0146 C7D1     		bne	.L14
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 403              		.loc 1 342 12 view .LVU119
 404 0148 4FF0FF35 		mov	r5, #-1
 405              	.LVL29:
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 406              		.loc 1 342 12 view .LVU120
 407 014c F5E7     		b	.L10
 408              	.LVL30:
 409              	.L16:
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending */
 410              		.loc 1 367 12 is_stmt 1 view .LVU121
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending */
 411              		.loc 1 367 15 is_stmt 0 view .LVU122
 412 014e 029B     		ldr	r3, [sp, #8]
 413              	.LVL31:
 367:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending */
 414              		.loc 1 367 15 view .LVU123
 415 0150 092B     		cmp	r3, #9
 416 0152 06DD     		ble	.L29
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending (queued packets are free in etharp_free_entry) */
 417              		.loc 1 372 12 is_stmt 1 view .LVU124
 372:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* recycle oldest pending (queued packets are free in etharp_free_entry) */
 418              		.loc 1 372 15 is_stmt 0 view .LVU125
 419 0154 BAF1090F 		cmp	r10, #9
 420 0158 1ADC     		bgt	.L30
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 421              		.loc 1 374 9 view .LVU126
 422 015a 5546     		mov	r5, r10
 423              	.LVL32:
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 424              		.loc 1 374 9 view .LVU127
 425 015c D0E7     		b	.L17
 426              	.LVL33:
 427              	.L28:
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 428              		.loc 1 374 9 view .LVU128
 429 015e 4546     		mov	r5, r8
 430              	.LVL34:
 374:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 431              		.loc 1 374 9 view .LVU129
 432 0160 CEE7     		b	.L17
 433              	.LVL35:
 434              	.L29:
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 435              		.loc 1 369 9 view .LVU130
 436 0162 029D     		ldr	r5, [sp, #8]
 437              	.LVL36:
 369:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_find_entry: selecting oldest pending entr
 438              		.loc 1 369 9 view .LVU131
 439 0164 CCE7     		b	.L17
 440              	.LVL37:
 441              	.L36:
 387:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[i].state == ETHARP_STATE_EMPTY",
 442              		.loc 1 387 3 is_stmt 1 discriminator 1 view .LVU132
 387:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[i].state == ETHARP_STATE_EMPTY",
ARM GAS  /tmp/cchE5ny2.s 			page 17


 443              		.loc 1 387 3 discriminator 1 view .LVU133
 444 0166 0D4B     		ldr	r3, .L38+4
 445 0168 40F28312 		movw	r2, #387
 446 016c 0E49     		ldr	r1, .L38+16
 447 016e 0D48     		ldr	r0, .L38+12
 448 0170 FFF7FEFF 		bl	printf
 449              	.LVL38:
 450 0174 C9E7     		b	.L15
 451              	.LVL39:
 452              	.L37:
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[i].state == ETHARP_STATE_EMPTY);
 453              		.loc 1 388 3 discriminator 1 view .LVU134
 388:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[i].state == ETHARP_STATE_EMPTY);
 454              		.loc 1 388 3 discriminator 1 view .LVU135
 455 0176 094B     		ldr	r3, .L38+4
 456 0178 4FF4C272 		mov	r2, #388
 457 017c 0B49     		ldr	r1, .L38+20
 458 017e 0948     		ldr	r0, .L38+12
 459 0180 FFF7FEFF 		bl	printf
 460              	.LVL40:
 461 0184 C9E7     		b	.L18
 462              	.LVL41:
 463              	.L21:
 464              	.LBB4:
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 465              		.loc 1 301 16 is_stmt 0 view .LVU136
 466 0186 1D46     		mov	r5, r3
 467              	.LVL42:
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 468              		.loc 1 301 16 view .LVU137
 469 0188 D7E7     		b	.L10
 470              	.LVL43:
 471              	.L26:
 301:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 472              		.loc 1 301 16 view .LVU138
 473              	.LBE4:
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 474              		.loc 1 342 12 view .LVU139
 475 018a 4FF0FF35 		mov	r5, #-1
 476              	.LVL44:
 342:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 477              		.loc 1 342 12 view .LVU140
 478 018e D4E7     		b	.L10
 479              	.LVL45:
 480              	.L30:
 379:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 481              		.loc 1 379 14 view .LVU141
 482 0190 4FF0FF35 		mov	r5, #-1
 483              	.LVL46:
 379:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 484              		.loc 1 379 14 view .LVU142
 485 0194 D1E7     		b	.L10
 486              	.L39:
 487 0196 00BF     		.align	2
 488              	.L38:
 489 0198 00000000 		.word	arp_table
 490 019c 00000000 		.word	.LC0
ARM GAS  /tmp/cchE5ny2.s 			page 18


 491 01a0 34000000 		.word	.LC1
 492 01a4 4C000000 		.word	.LC2
 493 01a8 74000000 		.word	.LC3
 494 01ac 88000000 		.word	.LC4
 495              		.cfi_endproc
 496              	.LFE172:
 498              		.section	.rodata.etharp_update_arp_entry.str1.4,"aMS",%progbits,1
 499              		.align	2
 500              	.LC5:
 501 0000 6E657469 		.ascii	"netif->hwaddr_len == ETH_HWADDR_LEN\000"
 501      662D3E68 
 501      77616464 
 501      725F6C65 
 501      6E203D3D 
 502              		.section	.text.etharp_update_arp_entry,"ax",%progbits
 503              		.align	1
 504              		.syntax unified
 505              		.thumb
 506              		.thumb_func
 508              	etharp_update_arp_entry:
 509              	.LVL47:
 510              	.LFB173:
 402:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 403:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 404:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Update (or insert) a IP/MAC address pair in the ARP cache.
 405:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 406:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If a pending entry is resolved, any queued packets will be sent
 407:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * at this point.
 408:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 409:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif netif related to this entry (used for NETIF_ADDRHINT)
 410:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr IP address of the inserted ARP entry.
 411:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ethaddr Ethernet address of the inserted ARP entry.
 412:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param flags See @ref etharp_state
 413:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 414:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return
 415:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_OK Successfully updated ARP cache.
 416:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_MEM If we could not add a new ARP entry when ETHARP_FLAG_TRY_HARD was set.
 417:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_ARG Non-unicast address given, those will not appear in ARP cache.
 418:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 419:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @see pbuf_free()
 420:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 421:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static err_t
 422:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_update_arp_entry(struct netif *netif, const ip4_addr_t *ipaddr, struct eth_addr *ethaddr, u8
 423:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 511              		.loc 1 423 1 is_stmt 1 view -0
 512              		.cfi_startproc
 513              		@ args = 0, pretend = 0, frame = 0
 514              		@ frame_needed = 0, uses_anonymous_args = 0
 515              		.loc 1 423 1 is_stmt 0 view .LVU144
 516 0000 F0B5     		push	{r4, r5, r6, r7, lr}
 517              	.LCFI5:
 518              		.cfi_def_cfa_offset 20
 519              		.cfi_offset 4, -20
 520              		.cfi_offset 5, -16
 521              		.cfi_offset 6, -12
 522              		.cfi_offset 7, -8
 523              		.cfi_offset 14, -4
ARM GAS  /tmp/cchE5ny2.s 			page 19


 524 0002 83B0     		sub	sp, sp, #12
 525              	.LCFI6:
 526              		.cfi_def_cfa_offset 32
 527 0004 0546     		mov	r5, r0
 528 0006 0C46     		mov	r4, r1
 529 0008 1646     		mov	r6, r2
 530 000a 1F46     		mov	r7, r3
 424:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i;
 531              		.loc 1 424 3 is_stmt 1 view .LVU145
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif->hwaddr_len == ETH_HWADDR_LEN", netif->hwaddr_len == ETH_HWADDR_LEN);
 532              		.loc 1 425 3 view .LVU146
 533              		.loc 1 425 3 view .LVU147
 534 000c 90F82C20 		ldrb	r2, [r0, #44]	@ zero_extendqisi2
 535              	.LVL48:
 536              		.loc 1 425 3 is_stmt 0 view .LVU148
 537 0010 062A     		cmp	r2, #6
 538 0012 3DD1     		bne	.L48
 539              	.LVL49:
 540              	.L41:
 541              		.loc 1 425 3 is_stmt 1 discriminator 3 view .LVU149
 542              		.loc 1 425 3 discriminator 3 view .LVU150
 426:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: %"U16_F".%"U16_F".%"U16_F".
 427:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ip4_addr1_16(ipaddr), ip4_addr2_16(ipaddr), ip4_addr3_16(ipaddr), ip4_addr4_16(ipaddr
 428:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (u16_t)ethaddr->addr[0], (u16_t)ethaddr->addr[1], (u16_t)ethaddr->addr[2],
 429:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (u16_t)ethaddr->addr[3], (u16_t)ethaddr->addr[4], (u16_t)ethaddr->addr[5]));
 543              		.loc 1 429 90 view .LVU151
 430:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* non-unicast address? */
 431:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (ip4_addr_isany(ipaddr) ||
 544              		.loc 1 431 3 view .LVU152
 545              		.loc 1 431 6 is_stmt 0 view .LVU153
 546 0014 002C     		cmp	r4, #0
 547 0016 43D0     		beq	.L42
 548              		.loc 1 431 7 discriminator 1 view .LVU154
 549 0018 2068     		ldr	r0, [r4]
 550 001a 0028     		cmp	r0, #0
 551 001c 40D0     		beq	.L42
 432:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_isbroadcast(ipaddr, netif) ||
 552              		.loc 1 432 7 view .LVU155
 553 001e 2946     		mov	r1, r5
 554 0020 FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 555              	.LVL50:
 431:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_isbroadcast(ipaddr, netif) ||
 556              		.loc 1 431 30 discriminator 2 view .LVU156
 557 0024 0028     		cmp	r0, #0
 558 0026 3BD1     		bne	.L42
 433:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr)) {
 559              		.loc 1 433 7 view .LVU157
 560 0028 2268     		ldr	r2, [r4]
 561 002a 02F0F002 		and	r2, r2, #240
 432:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_isbroadcast(ipaddr, netif) ||
 562              		.loc 1 432 43 view .LVU158
 563 002e E02A     		cmp	r2, #224
 564 0030 36D0     		beq	.L42
 434:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: will not add non-unicast 
 435:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_ARG;
 436:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 437:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* find or create ARP entry */
ARM GAS  /tmp/cchE5ny2.s 			page 20


 438:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = etharp_find_entry(ipaddr, flags, netif);
 565              		.loc 1 438 3 is_stmt 1 view .LVU159
 566              		.loc 1 438 7 is_stmt 0 view .LVU160
 567 0032 2A46     		mov	r2, r5
 568 0034 3946     		mov	r1, r7
 569 0036 2046     		mov	r0, r4
 570 0038 FFF7FEFF 		bl	etharp_find_entry
 571              	.LVL51:
 439:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* bail out if no entry could be found */
 440:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (i < 0) {
 572              		.loc 1 440 3 is_stmt 1 view .LVU161
 573              		.loc 1 440 6 is_stmt 0 view .LVU162
 574 003c B0F1000C 		subs	ip, r0, #0
 575 0040 31DB     		blt	.L49
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return (err_t)i;
 442:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 443:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 444:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 445:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (flags & ETHARP_FLAG_STATIC_ENTRY) {
 446:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* record static type */
 447:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].state = ETHARP_STATE_STATIC;
 448:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else if (arp_table[i].state == ETHARP_STATE_STATIC) {
 449:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* found entry is a static type, don't overwrite it */
 450:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_VAL;
 451:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else
 452:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_SUPPORT_STATIC_ENTRIES */
 453:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   {
 454:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* mark it stable */
 455:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].state = ETHARP_STATE_STABLE;
 576              		.loc 1 455 5 is_stmt 1 view .LVU163
 577              		.loc 1 455 24 is_stmt 0 view .LVU164
 578 0042 1C4B     		ldr	r3, .L50
 579 0044 0CEB4C00 		add	r0, ip, ip, lsl #1
 580              	.LVL52:
 581              		.loc 1 455 24 view .LVU165
 582 0048 03EBC002 		add	r2, r3, r0, lsl #3
 583 004c 0221     		movs	r1, #2
 584 004e 1175     		strb	r1, [r2, #20]
 456:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 457:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 458:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* record network interface */
 459:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].netif = netif;
 585              		.loc 1 459 3 is_stmt 1 view .LVU166
 586              		.loc 1 459 22 is_stmt 0 view .LVU167
 587 0050 9560     		str	r5, [r2, #8]
 460:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* insert in SNMP ARP index tree */
 461:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   mib2_add_arp_entry(netif, &arp_table[i].ipaddr);
 588              		.loc 1 461 50 is_stmt 1 view .LVU168
 462:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 463:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: updating stable entry %"S16
 589              		.loc 1 463 111 view .LVU169
 464:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* update address */
 465:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   SMEMCPY(&arp_table[i].ethaddr, ethaddr, ETH_HWADDR_LEN);
 590              		.loc 1 465 3 view .LVU170
 591 0052 C100     		lsls	r1, r0, #3
 592 0054 0831     		adds	r1, r1, #8
 593 0056 1944     		add	r1, r1, r3
ARM GAS  /tmp/cchE5ny2.s 			page 21


 594 0058 3468     		ldr	r4, [r6]	@ unaligned
 595              	.LVL53:
 596              		.loc 1 465 3 is_stmt 0 view .LVU171
 597 005a 4C60     		str	r4, [r1, #4]	@ unaligned
 598 005c B488     		ldrh	r4, [r6, #4]	@ unaligned
 599 005e 0C81     		strh	r4, [r1, #8]	@ unaligned
 466:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* reset time stamp */
 467:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   arp_table[i].ctime = 0;
 600              		.loc 1 467 3 is_stmt 1 view .LVU172
 601              		.loc 1 467 22 is_stmt 0 view .LVU173
 602 0060 0021     		movs	r1, #0
 603 0062 5182     		strh	r1, [r2, #18]	@ movhi
 468:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* this is where we will send out queued packets! */
 469:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ARP_QUEUEING
 470:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   while (arp_table[i].q != NULL) {
 471:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     struct pbuf *p;
 472:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* remember remainder of queue */
 473:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     struct etharp_q_entry *q = arp_table[i].q;
 474:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* pop first item off the queue */
 475:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].q = q->next;
 476:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* get the packet pointer */
 477:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     p = q->p;
 478:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* now queue entry can be freed */
 479:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     memp_free(MEMP_ARP_QUEUE, q);
 480:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #else /* ARP_QUEUEING */
 481:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (arp_table[i].q != NULL) {
 604              		.loc 1 481 3 is_stmt 1 view .LVU174
 605              		.loc 1 481 19 is_stmt 0 view .LVU175
 606 0064 53F83070 		ldr	r7, [r3, r0, lsl #3]
 607              	.LVL54:
 608              		.loc 1 481 6 view .LVU176
 609 0068 0FB3     		cbz	r7, .L46
 610              	.LBB5:
 482:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     struct pbuf *p = arp_table[i].q;
 611              		.loc 1 482 5 is_stmt 1 view .LVU177
 612              	.LVL55:
 483:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].q = NULL;
 613              		.loc 1 483 5 view .LVU178
 614              		.loc 1 483 20 is_stmt 0 view .LVU179
 615 006a C300     		lsls	r3, r0, #3
 616 006c 0C46     		mov	r4, r1
 617 006e 114A     		ldr	r2, .L50
 618 0070 D150     		str	r1, [r2, r3]
 484:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ARP_QUEUEING */
 485:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* send the queued IP packet */
 486:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ethernet_output(netif, p, (struct eth_addr *)(netif->hwaddr), ethaddr, ETHTYPE_IP);
 619              		.loc 1 486 5 is_stmt 1 view .LVU180
 620 0072 4FF40063 		mov	r3, #2048
 621 0076 0093     		str	r3, [sp]
 622 0078 3346     		mov	r3, r6
 623 007a 05F12602 		add	r2, r5, #38
 624 007e 3946     		mov	r1, r7
 625 0080 2846     		mov	r0, r5
 626 0082 FFF7FEFF 		bl	ethernet_output
 627              	.LVL56:
 487:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* free the queued IP packet */
 488:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     pbuf_free(p);
ARM GAS  /tmp/cchE5ny2.s 			page 22


 628              		.loc 1 488 5 view .LVU181
 629 0086 3846     		mov	r0, r7
 630 0088 FFF7FEFF 		bl	pbuf_free
 631              	.LVL57:
 632              	.LBE5:
 489:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return ERR_OK;
 633              		.loc 1 490 10 is_stmt 0 view .LVU182
 634 008c 2046     		mov	r0, r4
 635 008e 0CE0     		b	.L44
 636              	.LVL58:
 637              	.L48:
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: %"U16_F".%"U16_F".%"U16_F".
 638              		.loc 1 425 3 is_stmt 1 discriminator 1 view .LVU183
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: %"U16_F".%"U16_F".%"U16_F".
 639              		.loc 1 425 3 discriminator 1 view .LVU184
 640 0090 094B     		ldr	r3, .L50+4
 641              	.LVL59:
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: %"U16_F".%"U16_F".%"U16_F".
 642              		.loc 1 425 3 is_stmt 0 discriminator 1 view .LVU185
 643 0092 40F2A912 		movw	r2, #425
 644 0096 0949     		ldr	r1, .L50+8
 645              	.LVL60:
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: %"U16_F".%"U16_F".%"U16_F".
 646              		.loc 1 425 3 discriminator 1 view .LVU186
 647 0098 0948     		ldr	r0, .L50+12
 648              	.LVL61:
 425:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_update_arp_entry: %"U16_F".%"U16_F".%"U16_F".
 649              		.loc 1 425 3 discriminator 1 view .LVU187
 650 009a FFF7FEFF 		bl	printf
 651              	.LVL62:
 652 009e B9E7     		b	.L41
 653              	.L42:
 434:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_ARG;
 654              		.loc 1 434 128 is_stmt 1 view .LVU188
 435:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 655              		.loc 1 435 5 view .LVU189
 435:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 656              		.loc 1 435 12 is_stmt 0 view .LVU190
 657 00a0 6FF00F00 		mvn	r0, #15
 658 00a4 01E0     		b	.L44
 659              	.LVL63:
 660              	.L49:
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 661              		.loc 1 441 5 is_stmt 1 view .LVU191
 441:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 662              		.loc 1 441 12 is_stmt 0 view .LVU192
 663 00a6 4FFA8CF0 		sxtb	r0, ip
 664              	.LVL64:
 665              	.L44:
 491:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 666              		.loc 1 491 1 view .LVU193
 667 00aa 03B0     		add	sp, sp, #12
 668              	.LCFI7:
 669              		.cfi_remember_state
 670              		.cfi_def_cfa_offset 20
 671              		@ sp needed
ARM GAS  /tmp/cchE5ny2.s 			page 23


 672 00ac F0BD     		pop	{r4, r5, r6, r7, pc}
 673              	.LVL65:
 674              	.L46:
 675              	.LCFI8:
 676              		.cfi_restore_state
 490:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 677              		.loc 1 490 10 view .LVU194
 678 00ae 0020     		movs	r0, #0
 679 00b0 FBE7     		b	.L44
 680              	.L51:
 681 00b2 00BF     		.align	2
 682              	.L50:
 683 00b4 00000000 		.word	arp_table
 684 00b8 00000000 		.word	.LC0
 685 00bc 00000000 		.word	.LC5
 686 00c0 4C000000 		.word	.LC2
 687              		.cfi_endproc
 688              	.LFE173:
 690              		.section	.rodata.etharp_raw.str1.4,"aMS",%progbits,1
 691              		.align	2
 692              	.LC6:
 693 0000 6E657469 		.ascii	"netif != NULL\000"
 693      6620213D 
 693      204E554C 
 693      4C00
 694 000e 0000     		.align	2
 695              	.LC7:
 696 0010 63686563 		.ascii	"check that first pbuf can hold struct etharp_hdr\000"
 696      6B207468 
 696      61742066 
 696      69727374 
 696      20706275 
 697 0041 000000   		.align	2
 698              	.LC8:
 699 0044 6E657469 		.ascii	"netif->hwaddr_len must be the same as ETH_HWADDR_LE"
 699      662D3E68 
 699      77616464 
 699      725F6C65 
 699      6E206D75 
 700 0077 4E20666F 		.ascii	"N for etharp!\000"
 700      72206574 
 700      68617270 
 700      2100
 701              		.section	.text.etharp_raw,"ax",%progbits
 702              		.align	1
 703              		.syntax unified
 704              		.thumb
 705              		.thumb_func
 707              	etharp_raw:
 708              	.LVL66:
 709              	.LFB181:
 492:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 493:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 494:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Add a new static entry to the ARP table. If an entry exists for the
 495:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * specified IP address, this entry is overwritten.
 496:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If packets are queued for the specified IP address, they are sent out.
 497:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
ARM GAS  /tmp/cchE5ny2.s 			page 24


 498:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr IP address for the new static entry
 499:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ethaddr ethernet address for the new static entry
 500:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return See return values of etharp_add_static_entry
 501:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 502:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** err_t
 503:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_add_static_entry(const ip4_addr_t *ipaddr, struct eth_addr *ethaddr)
 504:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 505:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct netif *netif;
 506:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT_CORE_LOCKED();
 507:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_add_static_entry: %"U16_F".%"U16_F".%"U16_F".
 508:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ip4_addr1_16(ipaddr), ip4_addr2_16(ipaddr), ip4_addr3_16(ipaddr), ip4_addr4_16(ipaddr
 509:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (u16_t)ethaddr->addr[0], (u16_t)ethaddr->addr[1], (u16_t)ethaddr->addr[2],
 510:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (u16_t)ethaddr->addr[3], (u16_t)ethaddr->addr[4], (u16_t)ethaddr->addr[5]));
 511:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 512:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   netif = ip4_route(ipaddr);
 513:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (netif == NULL) {
 514:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_RTE;
 515:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 516:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 517:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return etharp_update_arp_entry(netif, ipaddr, ethaddr, ETHARP_FLAG_TRY_HARD | ETHARP_FLAG_STATIC_
 518:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 519:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 520:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Remove a static entry from the ARP table previously added with a call to
 521:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * etharp_add_static_entry.
 522:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 523:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr IP address of the static entry to remove
 524:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return ERR_OK: entry removed
 525:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         ERR_MEM: entry wasn't found
 526:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         ERR_ARG: entry wasn't a static entry but a dynamic one
 527:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 528:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** err_t
 529:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_remove_static_entry(const ip4_addr_t *ipaddr)
 530:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 531:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i;
 532:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT_CORE_LOCKED();
 533:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_remove_static_entry: %"U16_F".%"U16_F".%"U16_
 534:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ip4_addr1_16(ipaddr), ip4_addr2_16(ipaddr), ip4_addr3_16(ipaddr), ip4_addr4_16(ipaddr
 535:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 536:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* find or create ARP entry */
 537:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = etharp_find_entry(ipaddr, ETHARP_FLAG_FIND_ONLY, NULL);
 538:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* bail out if no entry could be found */
 539:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (i < 0) {
 540:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return (err_t)i;
 541:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 542:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 543:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (arp_table[i].state != ETHARP_STATE_STATIC) {
 544:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* entry wasn't a static entry, cannot remove it */
 545:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_ARG;
 546:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 547:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* entry found, free it */
 548:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   etharp_free_entry(i);
 549:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return ERR_OK;
 550:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 551:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ETHARP_SUPPORT_STATIC_ENTRIES */
 552:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 553:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 554:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Remove all ARP table entries of the specified netif.
ARM GAS  /tmp/cchE5ny2.s 			page 25


 555:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 556:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif points to a network interface
 557:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 558:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** void
 559:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_cleanup_netif(struct netif *netif)
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 561:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int i;
 562:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   for (i = 0; i < ARP_TABLE_SIZE; ++i) {
 564:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if ((state != ETHARP_STATE_EMPTY) && (arp_table[i].netif == netif)) {
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 567:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 568:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 569:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 570:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 571:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 572:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Finds (stable) ethernet/IP address pair from ARP table
 573:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * using interface and IP address index.
 574:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @note the addresses in the ARP table are in network order!
 575:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 576:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif points to interface index
 577:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr points to the (network order) IP address index
 578:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param eth_ret points to return pointer
 579:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ip_ret points to return pointer
 580:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return table index if found, -1 otherwise
 581:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 582:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** ssize_t
 583:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_find_addr(struct netif *netif, const ip4_addr_t *ipaddr,
 584:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                  struct eth_addr **eth_ret, const ip4_addr_t **ip_ret)
 585:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 586:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i;
 587:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL && ip_ret != NULL",
 589:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 590:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 591:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_UNUSED_ARG(netif);
 592:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 593:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = etharp_find_entry(ipaddr, ETHARP_FLAG_FIND_ONLY, netif);
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if ((i >= 0) && (arp_table[i].state >= ETHARP_STATE_STABLE)) {
 595:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 596:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ip_ret = &arp_table[i].ipaddr;
 597:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return i;
 598:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return -1;
 600:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 601:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 602:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 603:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Possibility to iterate over stable ARP table entries
 604:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 605:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param i entry number, 0 to ARP_TABLE_SIZE
 606:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr return value: IP address
 607:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif return value: points to interface
 608:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param eth_ret return value: ETH address
 609:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return 1 on valid index, 0 otherwise
 610:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 611:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** int
ARM GAS  /tmp/cchE5ny2.s 			page 26


 612:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_get_entry(size_t i, ip4_addr_t **ipaddr, struct netif **netif, struct eth_addr **eth_ret)
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 617:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if ((i < ARP_TABLE_SIZE) && (arp_table[i].state >= ETHARP_STATE_STABLE)) {
 619:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ipaddr  = &arp_table[i].ipaddr;
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *netif   = arp_table[i].netif;
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 622:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return 1;
 623:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else {
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return 0;
 625:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 626:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 627:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 628:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 629:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Responds to ARP requests to us. Upon ARP replies to us, add entry to cache
 630:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * send out queued IP packets. Updates cache with snooped address pairs.
 631:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 632:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Should be called for incoming ARP packets. The pbuf in the argument
 633:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * is freed by this function.
 634:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 635:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param p The ARP packet that arrived on netif. Is freed by this function.
 636:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif The lwIP network interface on which the ARP packet pbuf arrived.
 637:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 638:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @see pbuf_free()
 639:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 640:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** void
 641:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_input(struct pbuf *p, struct netif *netif)
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 643:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct etharp_hdr *hdr;
 644:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* these are aligned properly, whereas the ARP header fields might not be */
 645:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ip4_addr_t sipaddr, dipaddr;
 646:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   u8_t for_us;
 647:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 648:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT_CORE_LOCKED();
 649:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ERROR("netif != NULL", (netif != NULL), return;);
 651:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr = (struct etharp_hdr *)p->payload;
 653:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 654:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* RFC 826 "Packet Reception": */
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if ((hdr->hwtype != PP_HTONS(LWIP_IANA_HWTYPE_ETHERNET)) ||
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->hwlen != ETH_HWADDR_LEN) ||
 657:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->protolen != sizeof(ip4_addr_t)) ||
 658:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->proto != PP_HTONS(ETHTYPE_IP)))  {
 659:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 660:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                 ("etharp_input: packet dropped, wrong hw type, hwlen, proto, protolen or ethernet t
 661:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                  hdr->hwtype, (u16_t)hdr->hwlen, hdr->proto, (u16_t)hdr->protolen));
 662:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ETHARP_STATS_INC(etharp.proterr);
 663:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ETHARP_STATS_INC(etharp.drop);
 664:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     pbuf_free(p);
 665:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return;
 666:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATS_INC(etharp.recv);
 668:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
ARM GAS  /tmp/cchE5ny2.s 			page 27


 669:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_AUTOIP
 670:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* We have to check if a host already has configured our random
 671:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * created link local address and continuously check if there is
 672:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * a host with this IP-address so we can detect collisions */
 673:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   autoip_arp_reply(netif, hdr);
 674:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_AUTOIP */
 675:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 676:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* Copy struct ip4_addr_wordaligned to aligned ip4_addr, to support compilers without
 677:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * structure packing (not using structure copy which breaks strict-aliasing rules). */
 678:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   IPADDR_WORDALIGNED_COPY_TO_IP4_ADDR_T(&sipaddr, &hdr->sipaddr);
 679:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   IPADDR_WORDALIGNED_COPY_TO_IP4_ADDR_T(&dipaddr, &hdr->dipaddr);
 680:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 681:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* this interface is not configured? */
 682:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 683:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     for_us = 0;
 684:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else {
 685:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* ARP packet directed to us? */
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     for_us = (u8_t)ip4_addr_cmp(&dipaddr, netif_ip4_addr(netif));
 687:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 688:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 689:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* ARP message directed to us?
 690:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       -> add IP address in ARP cache; assume requester wants to talk to us,
 691:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          can result in directly sending the queued packets for this host.
 692:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****      ARP message not directed to us?
 693:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ->  update the source IP address in the cache, if present */
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   etharp_update_arp_entry(netif, &sipaddr, &(hdr->shwaddr),
 695:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 696:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 697:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* now act on the message itself */
 698:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   switch (hdr->opcode) {
 699:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* ARP request? */
 700:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     case PP_HTONS(ARP_REQUEST):
 701:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* ARP request. If it asked for our address, we send out a
 702:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        * reply. In any case, we time-stamp any existing ARP entry,
 703:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        * and possibly send out an IP packet that was queued on it. */
 704:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 705:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF (ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_input: incoming ARP request\n"));
 706:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* ARP request for our address? */
 707:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (for_us) {
 708:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* send ARP response */
 709:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         etharp_raw(netif,
 710:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    (struct eth_addr *)netif->hwaddr, &hdr->shwaddr,
 711:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    (struct eth_addr *)netif->hwaddr, netif_ip4_addr(netif),
 712:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    &hdr->shwaddr, &sipaddr,
 713:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    ARP_REPLY);
 714:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* we are not configured? */
 715:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (ip4_addr_isany_val(*netif_ip4_addr(netif))) {
 716:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* { for_us == 0 and netif->ip_addr.addr == 0 } */
 717:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_input: we are unconfigured, ARP request
 718:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* request was not directed to us */
 719:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else {
 720:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* { for_us == 0 and netif->ip_addr.addr != 0 } */
 721:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_input: ARP request was not for us.\n"))
 722:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 723:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       break;
 724:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     case PP_HTONS(ARP_REPLY):
 725:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* ARP reply. We already updated the ARP cache earlier. */
ARM GAS  /tmp/cchE5ny2.s 			page 28


 726:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_input: incoming ARP reply\n"));
 727:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if (LWIP_DHCP && DHCP_DOES_ARP_CHECK)
 728:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* DHCP wants to know about ARP replies from any host with an
 729:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        * IP address also offered to us by the DHCP server. We do not
 730:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        * want to take a duplicate IP address on a single network.
 731:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        * @todo How should we handle redundant (fail-over) interfaces? */
 732:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       dhcp_arp_reply(netif, &sipaddr);
 733:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* (LWIP_DHCP && DHCP_DOES_ARP_CHECK) */
 734:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       break;
 735:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     default:
 736:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_input: ARP unknown opcode type %"S16_F"\n
 737:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ETHARP_STATS_INC(etharp.err);
 738:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       break;
 739:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 740:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* free ARP packet */
 741:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   pbuf_free(p);
 742:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 743:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 744:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /** Just a small helper function that sends a pbuf to an ethernet address
 745:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * in the arp_table specified by the index 'arp_idx'.
 746:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 747:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static err_t
 748:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_output_to_arp_index(struct netif *netif, struct pbuf *q, netif_addr_idx_t arp_idx)
 749:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[arp_idx].state >= ETHARP_STATE_STABLE",
 751:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 752:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* if arp table entry is about to expire: re-request it,
 753:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****      but only if its state is ETHARP_STATE_STABLE to prevent flooding the
 754:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****      network with ARP requests if this address is used frequently. */
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (arp_table[arp_idx].state == ETHARP_STATE_STABLE) {
 756:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (arp_table[arp_idx].ctime >= ARP_AGE_REREQUEST_USED_BROADCAST) {
 757:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a standard request using broadcast */
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (etharp_request(netif, &arp_table[arp_idx].ipaddr) == ERR_OK) {
 759:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 760:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 761:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else if (arp_table[arp_idx].ctime >= ARP_AGE_REREQUEST_USED_UNICAST) {
 762:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a unicast request (for 15 seconds) to prevent unnecessary broadcast */
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (etharp_request_dst(netif, &arp_table[arp_idx].ipaddr, &arp_table[arp_idx].ethaddr) == ERR
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 765:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 766:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 767:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 768:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 769:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return ethernet_output(netif, q, (struct eth_addr *)(netif->hwaddr), &arp_table[arp_idx].ethaddr,
 770:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 771:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 772:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 773:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Resolve and fill-in Ethernet address header for outgoing IP packet.
 774:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 775:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * For IP multicast and broadcast, corresponding Ethernet addresses
 776:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * are selected and the packet is transmitted on the link.
 777:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 778:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * For unicast addresses, the packet is submitted to etharp_query(). In
 779:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * case the IP address is outside the local network, the IP address of
 780:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * the gateway is used.
 781:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 782:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif The lwIP network interface which the IP packet will be sent on.
ARM GAS  /tmp/cchE5ny2.s 			page 29


 783:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param q The pbuf(s) containing the IP packet to be sent.
 784:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr The IP address of the packet destination.
 785:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 786:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return
 787:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_RTE No route to destination (no gateway to external networks),
 788:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * or the return type of either etharp_query() or ethernet_output().
 789:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 790:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** err_t
 791:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_output(struct netif *netif, struct pbuf *q, const ip4_addr_t *ipaddr)
 792:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 793:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   const struct eth_addr *dest;
 794:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct eth_addr mcastaddr;
 795:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   const ip4_addr_t *dst_addr = ipaddr;
 796:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 797:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT_CORE_LOCKED();
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 801:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 802:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* Determine on destination hardware address. Broadcasts and multicasts
 803:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * are special, other IP addresses are looked up in the ARP table. */
 804:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 805:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* broadcast destination IP address? */
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (ip4_addr_isbroadcast(ipaddr, netif)) {
 807:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* broadcast on Ethernet also */
 808:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     dest = (const struct eth_addr *)&ethbroadcast;
 809:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* multicast destination IP address? */
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else if (ip4_addr_ismulticast(ipaddr)) {
 811:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* Hash IP multicast address to MAC address.*/
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[0] = LL_IP4_MULTICAST_ADDR_0;
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[1] = LL_IP4_MULTICAST_ADDR_1;
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[2] = LL_IP4_MULTICAST_ADDR_2;
 815:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[3] = ip4_addr2(ipaddr) & 0x7f;
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[4] = ip4_addr3(ipaddr);
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[5] = ip4_addr4(ipaddr);
 818:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* destination Ethernet address is multicast */
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     dest = &mcastaddr;
 820:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* unicast destination IP address? */
 821:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else {
 822:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     netif_addr_idx_t i;
 823:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* outside local network? if so, this can neither be a global broadcast nor
 824:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        a subnet broadcast. */
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (!ip4_addr_netcmp(ipaddr, netif_ip4_addr(netif), netif_ip4_netmask(netif)) &&
 826:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         !ip4_addr_islinklocal(ipaddr)) {
 827:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_AUTOIP
 828:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       struct ip_hdr *iphdr = LWIP_ALIGNMENT_CAST(struct ip_hdr *, q->payload);
 829:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* According to RFC 3297, chapter 2.6.2 (Forwarding Rules), a packet with
 830:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          a link-local source address must always be "directly to its destination
 831:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          on the same physical link. The host MUST NOT send the packet to any
 832:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          router for forwarding". */
 833:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (!ip4_addr_islinklocal(&iphdr->src))
 834:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_AUTOIP */
 835:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       {
 836:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #ifdef LWIP_HOOK_ETHARP_GET_GW
 837:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* For advanced routing, a single default gateway might not be enough, so get
 838:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            the IP address of the gateway to handle the current destination address. */
 839:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         dst_addr = LWIP_HOOK_ETHARP_GET_GW(netif, ipaddr);
ARM GAS  /tmp/cchE5ny2.s 			page 30


 840:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         if (dst_addr == NULL)
 841:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_HOOK_ETHARP_GET_GW */
 842:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         {
 843:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* interface has default gateway? */
 844:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           if (!ip4_addr_isany_val(*netif_ip4_gw(netif))) {
 845:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* send to hardware address of default gateway IP address */
 846:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             dst_addr = netif_ip4_gw(netif);
 847:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* no default gateway available */
 848:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           } else {
 849:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* no route to destination error (default gateway missing) */
 850:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             return ERR_RTE;
 851:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           }
 852:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 853:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 854:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 855:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_NETIF_HWADDRHINT
 856:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (netif->hints != NULL) {
 857:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* per-pcb cached entry was given */
 858:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       netif_addr_idx_t etharp_cached_entry = netif->hints->addr_hint;
 859:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (etharp_cached_entry < ARP_TABLE_SIZE) {
 860:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_NETIF_HWADDRHINT */
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         if ((arp_table[etharp_cached_entry].state >= ETHARP_STATE_STABLE) &&
 862:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 863:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             (arp_table[etharp_cached_entry].netif == netif) &&
 864:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 865:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             (ip4_addr_cmp(dst_addr, &arp_table[etharp_cached_entry].ipaddr))) {
 866:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* the per-pcb-cached entry is stable and the right one! */
 867:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           ETHARP_STATS_INC(etharp.cachehit);
 868:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           return etharp_output_to_arp_index(netif, q, etharp_cached_entry);
 869:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 870:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_NETIF_HWADDRHINT
 871:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 872:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 873:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_NETIF_HWADDRHINT */
 874:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 875:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* find stable entry: do this here since this is a critical path for
 876:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        throughput and etharp_find_entry() is kind of slow */
 877:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     for (i = 0; i < ARP_TABLE_SIZE; i++) {
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].state >= ETHARP_STATE_STABLE) &&
 879:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 880:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           (arp_table[i].netif == netif) &&
 881:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 882:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           (ip4_addr_cmp(dst_addr, &arp_table[i].ipaddr))) {
 883:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* found an existing, stable entry */
 884:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         ETHARP_SET_ADDRHINT(netif, i);
 885:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         return etharp_output_to_arp_index(netif, q, i);
 886:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 887:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 888:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* no stable entry found, use the (slower) query function:
 889:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****        queue on destination Ethernet address belonging to ipaddr */
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return etharp_query(netif, dst_addr, q);
 891:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 892:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 893:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* continuation for multicast/broadcast destinations */
 894:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* obtain source Ethernet address of the given interface */
 895:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* send packet directly on the link */
 896:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return ethernet_output(netif, q, (struct eth_addr *)(netif->hwaddr), dest, ETHTYPE_IP);
ARM GAS  /tmp/cchE5ny2.s 			page 31


 897:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 898:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 899:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
 900:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Send an ARP request for the given IP address and/or queue a packet.
 901:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 902:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If the IP address was not yet in the cache, a pending ARP cache entry
 903:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * is added and an ARP request is sent for the given address. The packet
 904:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * is queued on this entry.
 905:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 906:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If the IP address was already pending in the cache, a new ARP request
 907:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * is sent for the given address. The packet is queued on this entry.
 908:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 909:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If the IP address was already stable in the cache, and a packet is
 910:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * given, it is directly sent and no ARP request is sent out.
 911:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 912:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * If the IP address was already stable in the cache, and no packet is
 913:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * given, an ARP request is sent out.
 914:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 915:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif The lwIP network interface on which ipaddr
 916:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * must be queried for.
 917:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr The IP address to be resolved.
 918:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param q If non-NULL, a pbuf that must be delivered to the IP address.
 919:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * q is not freed by this function.
 920:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 921:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @note q must only be ONE packet, not a packet queue!
 922:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 923:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return
 924:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_BUF Could not make room for Ethernet header.
 925:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_MEM Hardware address unknown, and no more ARP entries available
 926:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *   to query for address or queue the packet.
 927:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_MEM Could not queue packet due to memory shortage.
 928:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_RTE No route to destination (no gateway to external networks).
 929:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * - ERR_ARG Non-unicast address given, those will not appear in ARP cache.
 930:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
 931:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
 932:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** err_t
 933:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_query(struct netif *netif, const ip4_addr_t *ipaddr, struct pbuf *q)
 934:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 935:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct eth_addr *srcaddr = (struct eth_addr *)netif->hwaddr;
 936:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   err_t result = ERR_MEM;
 937:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int is_new_entry = 0;
 938:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i_err;
 939:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   netif_addr_idx_t i;
 940:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 941:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* non-unicast address? */
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (ip4_addr_isbroadcast(ipaddr, netif) ||
 943:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 944:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_isany(ipaddr)) {
 945:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: will not add non-unicast IP address 
 946:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_ARG;
 947:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 948:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 949:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* find entry in ARP cache, ask to create entry if queueing packet */
 950:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i_err = etharp_find_entry(ipaddr, ETHARP_FLAG_TRY_HARD, netif);
 951:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 952:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* could not find or create entry? */
 953:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (i_err < 0) {
ARM GAS  /tmp/cchE5ny2.s 			page 32


 954:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: could not create ARP entry\n"));
 955:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (q) {
 956:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: packet dropped\n"));
 957:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ETHARP_STATS_INC(etharp.memerr);
 958:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 959:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return (err_t)i_err;
 960:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("type overflow", (size_t)i_err < NETIF_ADDR_IDX_MAX);
 962:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 963:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 964:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* mark a fresh entry as pending (we just sent a request) */
 965:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (arp_table[i].state == ETHARP_STATE_EMPTY) {
 966:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     is_new_entry = 1;
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].state = ETHARP_STATE_PENDING;
 968:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* record network interface for re-sending arp request in etharp_tmr */
 969:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].netif = netif;
 970:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 971:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 972:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* { i is either a STABLE or (new or existing) PENDING entry } */
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[i].state == PENDING or STABLE",
 974:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 975:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                (arp_table[i].state >= ETHARP_STATE_STABLE)));
 976:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 977:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* do we have a new entry? or an implicit query request? */
 978:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (is_new_entry || (q == NULL)) {
 979:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* try to resolve it; send out ARP request */
 980:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     result = etharp_request(netif, ipaddr);
 981:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (result != ERR_OK) {
 982:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* ARP request couldn't be sent */
 983:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* We don't re-send arp request in etharp_tmr, but we still queue packets,
 984:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          since this failure could be temporary, and the next packet calling
 985:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****          etharp_query again could lead to sending the queued packets. */
 986:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 987:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (q == NULL) {
 988:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       return result;
 989:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 990:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 991:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 992:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* packet given? */
 993:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 994:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* stable entry? */
 995:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (arp_table[i].state >= ETHARP_STATE_STABLE) {
 996:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* we have a valid IP->Ethernet address mapping */
 997:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ETHARP_SET_ADDRHINT(netif, i);
 998:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* send the packet */
 999:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     result = ethernet_output(netif, q, srcaddr, &(arp_table[i].ethaddr), ETHTYPE_IP);
1000:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* pending entry? (either just created or already pending */
1001:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
1002:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* entry is still pending, queue the given packet 'q' */
1003:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     struct pbuf *p;
1004:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     int copy_needed = 0;
1005:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* IF q includes a pbuf that must be copied, copy the whole chain into a
1006:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****      * new PBUF_RAM. See the definition of PBUF_NEEDS_COPY for details. */
1007:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     p = q;
1008:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     while (p) {
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_ASSERT("no packet queues allowed!", (p->len != p->tot_len) || (p->next == 0));
1010:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
ARM GAS  /tmp/cchE5ny2.s 			page 33


1011:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         copy_needed = 1;
1012:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         break;
1013:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
1014:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       p = p->next;
1015:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
1016:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (copy_needed) {
1017:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* copy the whole packet into new pbufs */
1018:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       p = pbuf_clone(PBUF_LINK, PBUF_RAM, q);
1019:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else {
1020:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* referencing the old pbuf is enough */
1021:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       p = q;
1022:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       pbuf_ref(p);
1023:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
1024:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* packet could be taken over? */
1025:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (p != NULL) {
1026:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* queue packet ... */
1027:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ARP_QUEUEING
1028:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       struct etharp_q_entry *new_entry;
1029:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* allocate a new arp queue entry */
1030:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       new_entry = (struct etharp_q_entry *)memp_malloc(MEMP_ARP_QUEUE);
1031:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (new_entry != NULL) {
1032:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         unsigned int qlen = 0;
1033:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         new_entry->next = 0;
1034:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         new_entry->p = p;
1035:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         if (arp_table[i].q != NULL) {
1036:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* queue was already existent, append the new entry to the end */
1037:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           struct etharp_q_entry *r;
1038:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           r = arp_table[i].q;
1039:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           qlen++;
1040:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           while (r->next != NULL) {
1041:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             r = r->next;
1042:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             qlen++;
1043:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           }
1044:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           r->next = new_entry;
1045:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         } else {
1046:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* queue did not exist, first item in queue */
1047:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           arp_table[i].q = new_entry;
1048:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
1049:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ARP_QUEUE_LEN
1050:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         if (qlen >= ARP_QUEUE_LEN) {
1051:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           struct etharp_q_entry *old;
1052:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           old = arp_table[i].q;
1053:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           arp_table[i].q = arp_table[i].q->next;
1054:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           pbuf_free(old->p);
1055:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           memp_free(MEMP_ARP_QUEUE, old);
1056:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
1057:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
1058:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: queued packet %p on ARP entry %"
1059:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         result = ERR_OK;
1060:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else {
1061:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* the pool MEMP_ARP_QUEUE is empty */
1062:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         pbuf_free(p);
1063:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: could not queue a copy of PBUF_R
1064:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         result = ERR_MEM;
1065:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
1066:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #else /* ARP_QUEUEING */
1067:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* always queue one packet per ARP request only, freeing a previously queued packet */
ARM GAS  /tmp/cchE5ny2.s 			page 34


1068:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (arp_table[i].q != NULL) {
1069:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: dropped previously queued packet
1070:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         pbuf_free(arp_table[i].q);
1071:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
1072:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       arp_table[i].q = p;
1073:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       result = ERR_OK;
1074:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: queued packet %p on ARP entry %"U1
1075:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ARP_QUEUEING */
1076:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else {
1077:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ETHARP_STATS_INC(etharp.memerr);
1078:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: could not queue a copy of PBUF_REF
1079:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       result = ERR_MEM;
1080:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
1081:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
1082:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return result;
1083:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
1084:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1085:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
1086:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Send a raw ARP packet (opcode and all addresses can be modified)
1087:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
1088:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif the lwip network interface on which to send the ARP packet
1089:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ethsrc_addr the source MAC address for the ethernet header
1090:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ethdst_addr the destination MAC address for the ethernet header
1091:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param hwsrc_addr the source MAC address for the ARP protocol header
1092:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipsrc_addr the source IP address for the ARP protocol header
1093:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param hwdst_addr the destination MAC address for the ARP protocol header
1094:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipdst_addr the destination IP address for the ARP protocol header
1095:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param opcode the type of the ARP packet
1096:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return ERR_OK if the ARP packet has been sent
1097:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         ERR_MEM if the ARP packet couldn't be allocated
1098:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         any other err_t on failure
1099:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
1100:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static err_t
1101:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_raw(struct netif *netif, const struct eth_addr *ethsrc_addr,
1102:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            const struct eth_addr *ethdst_addr,
1103:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            const struct eth_addr *hwsrc_addr, const ip4_addr_t *ipsrc_addr,
1104:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            const struct eth_addr *hwdst_addr, const ip4_addr_t *ipdst_addr,
1105:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            const u16_t opcode)
1106:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 710              		.loc 1 1106 1 is_stmt 1 view -0
 711              		.cfi_startproc
 712              		@ args = 16, pretend = 0, frame = 0
 713              		@ frame_needed = 0, uses_anonymous_args = 0
 714              		.loc 1 1106 1 is_stmt 0 view .LVU196
 715 0000 2DE9F047 		push	{r4, r5, r6, r7, r8, r9, r10, lr}
 716              	.LCFI9:
 717              		.cfi_def_cfa_offset 32
 718              		.cfi_offset 4, -32
 719              		.cfi_offset 5, -28
 720              		.cfi_offset 6, -24
 721              		.cfi_offset 7, -20
 722              		.cfi_offset 8, -16
 723              		.cfi_offset 9, -12
 724              		.cfi_offset 10, -8
 725              		.cfi_offset 14, -4
 726 0004 82B0     		sub	sp, sp, #8
 727              	.LCFI10:
ARM GAS  /tmp/cchE5ny2.s 			page 35


 728              		.cfi_def_cfa_offset 40
 729 0006 0F46     		mov	r7, r1
 730 0008 9046     		mov	r8, r2
 731 000a 1D46     		mov	r5, r3
 732 000c DDF82CA0 		ldr	r10, [sp, #44]
1107:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct pbuf *p;
 733              		.loc 1 1107 3 is_stmt 1 view .LVU197
1108:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   err_t result = ERR_OK;
 734              		.loc 1 1108 3 view .LVU198
 735              	.LVL67:
1109:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct etharp_hdr *hdr;
 736              		.loc 1 1109 3 view .LVU199
1110:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 737              		.loc 1 1111 3 view .LVU200
 738              		.loc 1 1111 3 view .LVU201
 739 0010 8146     		mov	r9, r0
 740 0012 0028     		cmp	r0, #0
 741 0014 42D0     		beq	.L59
 742              	.LVL68:
 743              	.L53:
 744              		.loc 1 1111 3 discriminator 3 view .LVU202
 745              		.loc 1 1111 3 discriminator 3 view .LVU203
1112:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1113:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* allocate a pbuf for the outgoing ARP request packet */
1114:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   p = pbuf_alloc(PBUF_LINK, SIZEOF_ETHARP_HDR, PBUF_RAM);
 746              		.loc 1 1114 3 view .LVU204
 747              		.loc 1 1114 7 is_stmt 0 view .LVU205
 748 0016 4FF42072 		mov	r2, #640
 749 001a 1C21     		movs	r1, #28
 750 001c 0E20     		movs	r0, #14
 751 001e FFF7FEFF 		bl	pbuf_alloc
 752              	.LVL69:
1115:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* could allocate a pbuf for an ARP request? */
1116:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (p == NULL) {
 753              		.loc 1 1116 3 is_stmt 1 view .LVU206
 754              		.loc 1 1116 6 is_stmt 0 view .LVU207
 755 0022 0646     		mov	r6, r0
 756 0024 0028     		cmp	r0, #0
 757 0026 51D0     		beq	.L57
1117:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_SERIOUS,
1118:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                 ("etharp_raw: could not allocate pbuf for ARP request.\n"));
1119:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ETHARP_STATS_INC(etharp.memerr);
1120:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return ERR_MEM;
1121:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
1122:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("check that first pbuf can hold struct etharp_hdr",
 758              		.loc 1 1122 3 is_stmt 1 view .LVU208
 759              		.loc 1 1122 3 view .LVU209
 760 0028 4389     		ldrh	r3, [r0, #10]
 761 002a 1B2B     		cmp	r3, #27
 762 002c 3ED9     		bls	.L60
 763              	.LVL70:
 764              	.L55:
 765              		.loc 1 1122 3 discriminator 3 view .LVU210
 766              		.loc 1 1122 3 discriminator 3 view .LVU211
1123:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (p->len >= SIZEOF_ETHARP_HDR));
1124:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
ARM GAS  /tmp/cchE5ny2.s 			page 36


1125:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr = (struct etharp_hdr *)p->payload;
 767              		.loc 1 1125 3 view .LVU212
 768              		.loc 1 1125 7 is_stmt 0 view .LVU213
 769 002e 7468     		ldr	r4, [r6, #4]
 770              	.LVL71:
1126:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_raw: sending raw ARP packet.\n"));
 771              		.loc 1 1126 88 is_stmt 1 view .LVU214
1127:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr->opcode = lwip_htons(opcode);
 772              		.loc 1 1127 3 view .LVU215
 773              		.loc 1 1127 17 is_stmt 0 view .LVU216
 774 0030 BDF83400 		ldrh	r0, [sp, #52]
 775 0034 FFF7FEFF 		bl	lwip_htons
 776              	.LVL72:
 777              		.loc 1 1127 15 discriminator 1 view .LVU217
 778 0038 E080     		strh	r0, [r4, #6]	@ unaligned
1128:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1129:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif->hwaddr_len must be the same as ETH_HWADDR_LEN for etharp!",
 779              		.loc 1 1129 3 is_stmt 1 view .LVU218
 780              		.loc 1 1129 3 view .LVU219
 781 003a 99F82C30 		ldrb	r3, [r9, #44]	@ zero_extendqisi2
 782 003e 062B     		cmp	r3, #6
 783 0040 3CD1     		bne	.L61
 784              	.L56:
 785              		.loc 1 1129 3 discriminator 3 view .LVU220
 786              		.loc 1 1129 3 discriminator 3 view .LVU221
1130:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
1131:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1132:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* Write the ARP MAC-Addresses */
1133:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   SMEMCPY(&hdr->shwaddr, hwsrc_addr, ETH_HWADDR_LEN);
 787              		.loc 1 1133 3 view .LVU222
 788 0042 2B68     		ldr	r3, [r5]	@ unaligned
 789 0044 A360     		str	r3, [r4, #8]	@ unaligned
 790 0046 AB88     		ldrh	r3, [r5, #4]	@ unaligned
 791 0048 A381     		strh	r3, [r4, #12]	@ unaligned
1134:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   SMEMCPY(&hdr->dhwaddr, hwdst_addr, ETH_HWADDR_LEN);
 792              		.loc 1 1134 3 view .LVU223
 793 004a DAF80030 		ldr	r3, [r10]	@ unaligned
 794 004e C4F81230 		str	r3, [r4, #18]	@ unaligned
 795 0052 BAF80430 		ldrh	r3, [r10, #4]	@ unaligned
 796 0056 E382     		strh	r3, [r4, #22]	@ unaligned
1135:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* Copy struct ip4_addr_wordaligned to aligned ip4_addr, to support compilers without
1136:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * structure packing. */
1137:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   IPADDR_WORDALIGNED_COPY_FROM_IP4_ADDR_T(&hdr->sipaddr, ipsrc_addr);
 797              		.loc 1 1137 3 view .LVU224
 798 0058 0A9B     		ldr	r3, [sp, #40]
 799 005a 1B68     		ldr	r3, [r3]	@ unaligned
 800 005c C4F80E30 		str	r3, [r4, #14]	@ unaligned
1138:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   IPADDR_WORDALIGNED_COPY_FROM_IP4_ADDR_T(&hdr->dipaddr, ipdst_addr);
 801              		.loc 1 1138 3 view .LVU225
 802 0060 0C9B     		ldr	r3, [sp, #48]
 803 0062 1B68     		ldr	r3, [r3]	@ unaligned
 804 0064 A361     		str	r3, [r4, #24]	@ unaligned
1139:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1140:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr->hwtype = PP_HTONS(LWIP_IANA_HWTYPE_ETHERNET);
 805              		.loc 1 1140 3 view .LVU226
 806              		.loc 1 1140 15 is_stmt 0 view .LVU227
 807 0066 0025     		movs	r5, #0
ARM GAS  /tmp/cchE5ny2.s 			page 37


 808              	.LVL73:
 809              		.loc 1 1140 15 view .LVU228
 810 0068 2570     		strb	r5, [r4]
 811 006a 0123     		movs	r3, #1
 812 006c 6370     		strb	r3, [r4, #1]
1141:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr->proto = PP_HTONS(ETHTYPE_IP);
 813              		.loc 1 1141 3 is_stmt 1 view .LVU229
 814              		.loc 1 1141 14 is_stmt 0 view .LVU230
 815 006e 0823     		movs	r3, #8
 816 0070 A370     		strb	r3, [r4, #2]
 817 0072 E570     		strb	r5, [r4, #3]
1142:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* set hwlen and protolen */
1143:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr->hwlen = ETH_HWADDR_LEN;
 818              		.loc 1 1143 3 is_stmt 1 view .LVU231
 819              		.loc 1 1143 14 is_stmt 0 view .LVU232
 820 0074 0623     		movs	r3, #6
 821 0076 2371     		strb	r3, [r4, #4]
1144:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   hdr->protolen = sizeof(ip4_addr_t);
 822              		.loc 1 1144 3 is_stmt 1 view .LVU233
 823              		.loc 1 1144 17 is_stmt 0 view .LVU234
 824 0078 0423     		movs	r3, #4
 825 007a 6371     		strb	r3, [r4, #5]
1145:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1146:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* send ARP query */
1147:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_AUTOIP
1148:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* If we are using Link-Local, all ARP packets that contain a Link-Local
1149:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * 'sender IP address' MUST be sent using link-layer broadcast instead of
1150:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****    * link-layer unicast. (See RFC3927 Section 2.5, last paragraph) */
1151:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if (ip4_addr_islinklocal(ipsrc_addr)) {
1152:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ethernet_output(netif, p, ethsrc_addr, &ethbroadcast, ETHTYPE_ARP);
1153:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else
1154:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* LWIP_AUTOIP */
1155:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   {
1156:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ethernet_output(netif, p, ethsrc_addr, ethdst_addr, ETHTYPE_ARP);
 826              		.loc 1 1156 5 is_stmt 1 view .LVU235
 827 007c 40F60603 		movw	r3, #2054
 828 0080 0093     		str	r3, [sp]
 829 0082 4346     		mov	r3, r8
 830 0084 3A46     		mov	r2, r7
 831 0086 3146     		mov	r1, r6
 832 0088 4846     		mov	r0, r9
 833 008a FFF7FEFF 		bl	ethernet_output
 834              	.LVL74:
1157:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
1158:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1159:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   ETHARP_STATS_INC(etharp.xmit);
 835              		.loc 1 1159 32 view .LVU236
1160:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* free ARP query packet */
1161:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   pbuf_free(p);
 836              		.loc 1 1161 3 view .LVU237
 837 008e 3046     		mov	r0, r6
 838 0090 FFF7FEFF 		bl	pbuf_free
 839              	.LVL75:
1162:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   p = NULL;
 840              		.loc 1 1162 3 view .LVU238
1163:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* could not allocate pbuf for ARP request */
1164:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
ARM GAS  /tmp/cchE5ny2.s 			page 38


1165:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return result;
 841              		.loc 1 1165 3 view .LVU239
 842              		.loc 1 1165 10 is_stmt 0 view .LVU240
 843 0094 2846     		mov	r0, r5
 844              	.LVL76:
 845              	.L54:
1166:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 846              		.loc 1 1166 1 view .LVU241
 847 0096 02B0     		add	sp, sp, #8
 848              	.LCFI11:
 849              		.cfi_remember_state
 850              		.cfi_def_cfa_offset 32
 851              		@ sp needed
 852 0098 BDE8F087 		pop	{r4, r5, r6, r7, r8, r9, r10, pc}
 853              	.LVL77:
 854              	.L59:
 855              	.LCFI12:
 856              		.cfi_restore_state
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 857              		.loc 1 1111 3 is_stmt 1 discriminator 1 view .LVU242
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 858              		.loc 1 1111 3 discriminator 1 view .LVU243
 859 009c 0D4B     		ldr	r3, .L62
 860              	.LVL78:
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 861              		.loc 1 1111 3 is_stmt 0 discriminator 1 view .LVU244
 862 009e 40F25742 		movw	r2, #1111
 863              	.LVL79:
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 864              		.loc 1 1111 3 discriminator 1 view .LVU245
 865 00a2 0D49     		ldr	r1, .L62+4
 866              	.LVL80:
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 867              		.loc 1 1111 3 discriminator 1 view .LVU246
 868 00a4 0D48     		ldr	r0, .L62+8
 869              	.LVL81:
1111:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 870              		.loc 1 1111 3 discriminator 1 view .LVU247
 871 00a6 FFF7FEFF 		bl	printf
 872              	.LVL82:
 873 00aa B4E7     		b	.L53
 874              	.LVL83:
 875              	.L60:
1122:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (p->len >= SIZEOF_ETHARP_HDR));
 876              		.loc 1 1122 3 is_stmt 1 discriminator 1 view .LVU248
1122:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (p->len >= SIZEOF_ETHARP_HDR));
 877              		.loc 1 1122 3 discriminator 1 view .LVU249
 878 00ac 094B     		ldr	r3, .L62
 879 00ae 40F26242 		movw	r2, #1122
 880 00b2 0B49     		ldr	r1, .L62+12
 881 00b4 0948     		ldr	r0, .L62+8
 882              	.LVL84:
1122:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (p->len >= SIZEOF_ETHARP_HDR));
 883              		.loc 1 1122 3 is_stmt 0 discriminator 1 view .LVU250
 884 00b6 FFF7FEFF 		bl	printf
 885              	.LVL85:
 886 00ba B8E7     		b	.L55
ARM GAS  /tmp/cchE5ny2.s 			page 39


 887              	.LVL86:
 888              	.L61:
1129:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
 889              		.loc 1 1129 3 is_stmt 1 discriminator 1 view .LVU251
1129:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               (netif->hwaddr_len == ETH_HWADDR_LEN));
 890              		.loc 1 1129 3 discriminator 1 view .LVU252
 891 00bc 054B     		ldr	r3, .L62
 892 00be 40F26942 		movw	r2, #1129
 893 00c2 0849     		ldr	r1, .L62+16
 894 00c4 0548     		ldr	r0, .L62+8
 895 00c6 FFF7FEFF 		bl	printf
 896              	.LVL87:
 897 00ca BAE7     		b	.L56
 898              	.LVL88:
 899              	.L57:
1120:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 900              		.loc 1 1120 12 is_stmt 0 view .LVU253
 901 00cc 4FF0FF30 		mov	r0, #-1
 902              	.LVL89:
1120:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 903              		.loc 1 1120 12 view .LVU254
 904 00d0 E1E7     		b	.L54
 905              	.L63:
 906 00d2 00BF     		.align	2
 907              	.L62:
 908 00d4 00000000 		.word	.LC0
 909 00d8 00000000 		.word	.LC6
 910 00dc 4C000000 		.word	.LC2
 911 00e0 10000000 		.word	.LC7
 912 00e4 44000000 		.word	.LC8
 913              		.cfi_endproc
 914              	.LFE181:
 916              		.section	.text.etharp_request_dst,"ax",%progbits
 917              		.align	1
 918              		.syntax unified
 919              		.thumb
 920              		.thumb_func
 922              	etharp_request_dst:
 923              	.LVL90:
 924              	.LFB182:
1167:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1168:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
1169:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Send an ARP request packet asking for ipaddr to a specific eth address.
1170:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Used to send unicast request to refresh the ARP table just before an entry
1171:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * times out
1172:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
1173:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif the lwip network interface on which to send the request
1174:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr the IP address for which to ask
1175:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param hw_dst_addr the ethernet address to send this packet to
1176:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return ERR_OK if the request has been sent
1177:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         ERR_MEM if the ARP packet couldn't be allocated
1178:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         any other err_t on failure
1179:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
1180:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** static err_t
1181:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_request_dst(struct netif *netif, const ip4_addr_t *ipaddr, const struct eth_addr *hw_dst_add
1182:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 925              		.loc 1 1182 1 is_stmt 1 view -0
ARM GAS  /tmp/cchE5ny2.s 			page 40


 926              		.cfi_startproc
 927              		@ args = 0, pretend = 0, frame = 0
 928              		@ frame_needed = 0, uses_anonymous_args = 0
 929              		.loc 1 1182 1 is_stmt 0 view .LVU256
 930 0000 30B5     		push	{r4, r5, lr}
 931              	.LCFI13:
 932              		.cfi_def_cfa_offset 12
 933              		.cfi_offset 4, -12
 934              		.cfi_offset 5, -8
 935              		.cfi_offset 14, -4
 936 0002 85B0     		sub	sp, sp, #20
 937              	.LCFI14:
 938              		.cfi_def_cfa_offset 32
1183:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return etharp_raw(netif, (struct eth_addr *)netif->hwaddr, hw_dst_addr,
 939              		.loc 1 1183 3 is_stmt 1 view .LVU257
 940              		.loc 1 1183 47 is_stmt 0 view .LVU258
 941 0004 00F12603 		add	r3, r0, #38
1184:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                     (struct eth_addr *)netif->hwaddr, netif_ip4_addr(netif), &ethzero,
 942              		.loc 1 1184 55 view .LVU259
 943 0008 041D     		adds	r4, r0, #4
1183:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return etharp_raw(netif, (struct eth_addr *)netif->hwaddr, hw_dst_addr,
 944              		.loc 1 1183 10 view .LVU260
 945 000a 0125     		movs	r5, #1
 946 000c 0395     		str	r5, [sp, #12]
 947 000e 0291     		str	r1, [sp, #8]
 948 0010 0349     		ldr	r1, .L66
 949              	.LVL91:
1183:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return etharp_raw(netif, (struct eth_addr *)netif->hwaddr, hw_dst_addr,
 950              		.loc 1 1183 10 view .LVU261
 951 0012 0191     		str	r1, [sp, #4]
 952 0014 0094     		str	r4, [sp]
 953 0016 1946     		mov	r1, r3
 954 0018 FFF7FEFF 		bl	etharp_raw
 955              	.LVL92:
1185:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                     ipaddr, ARP_REQUEST);
1186:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 956              		.loc 1 1186 1 view .LVU262
 957 001c 05B0     		add	sp, sp, #20
 958              	.LCFI15:
 959              		.cfi_def_cfa_offset 12
 960              		@ sp needed
 961 001e 30BD     		pop	{r4, r5, pc}
 962              	.LVL93:
 963              	.L67:
 964              		.loc 1 1186 1 view .LVU263
 965              		.align	2
 966              	.L66:
 967 0020 00000000 		.word	ethzero
 968              		.cfi_endproc
 969              	.LFE182:
 971              		.section	.text.etharp_cleanup_netif,"ax",%progbits
 972              		.align	1
 973              		.global	etharp_cleanup_netif
 974              		.syntax unified
 975              		.thumb
 976              		.thumb_func
 978              	etharp_cleanup_netif:
ARM GAS  /tmp/cchE5ny2.s 			page 41


 979              	.LVL94:
 980              	.LFB174:
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int i;
 981              		.loc 1 560 1 is_stmt 1 view -0
 982              		.cfi_startproc
 983              		@ args = 0, pretend = 0, frame = 0
 984              		@ frame_needed = 0, uses_anonymous_args = 0
 560:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int i;
 985              		.loc 1 560 1 is_stmt 0 view .LVU265
 986 0000 38B5     		push	{r3, r4, r5, lr}
 987              	.LCFI16:
 988              		.cfi_def_cfa_offset 16
 989              		.cfi_offset 3, -16
 990              		.cfi_offset 4, -12
 991              		.cfi_offset 5, -8
 992              		.cfi_offset 14, -4
 993 0002 0546     		mov	r5, r0
 561:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 994              		.loc 1 561 3 is_stmt 1 view .LVU266
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 995              		.loc 1 563 3 view .LVU267
 996              	.LVL95:
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 997              		.loc 1 563 10 is_stmt 0 view .LVU268
 998 0004 0024     		movs	r4, #0
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 999              		.loc 1 563 3 view .LVU269
 1000 0006 00E0     		b	.L69
 1001              	.LVL96:
 1002              	.L70:
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1003              		.loc 1 563 35 is_stmt 1 discriminator 2 view .LVU270
 1004 0008 0134     		adds	r4, r4, #1
 1005              	.LVL97:
 1006              	.L69:
 563:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1007              		.loc 1 563 17 discriminator 1 view .LVU271
 1008 000a 092C     		cmp	r4, #9
 1009 000c 11DC     		bgt	.L73
 1010              	.LBB6:
 564:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if ((state != ETHARP_STATE_EMPTY) && (arp_table[i].netif == netif)) {
 1011              		.loc 1 564 5 view .LVU272
 564:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if ((state != ETHARP_STATE_EMPTY) && (arp_table[i].netif == netif)) {
 1012              		.loc 1 564 10 is_stmt 0 view .LVU273
 1013 000e 04EB4402 		add	r2, r4, r4, lsl #1
 1014 0012 084B     		ldr	r3, .L74
 1015 0014 03EBC203 		add	r3, r3, r2, lsl #3
 1016 0018 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 1017              	.LVL98:
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 1018              		.loc 1 565 5 is_stmt 1 view .LVU274
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 1019              		.loc 1 565 8 is_stmt 0 view .LVU275
 1020 001a 002B     		cmp	r3, #0
 1021 001c F4D0     		beq	.L70
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 1022              		.loc 1 565 55 discriminator 1 view .LVU276
ARM GAS  /tmp/cchE5ny2.s 			page 42


 1023 001e 054B     		ldr	r3, .L74
 1024              	.LVL99:
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 1025              		.loc 1 565 55 discriminator 1 view .LVU277
 1026 0020 03EBC203 		add	r3, r3, r2, lsl #3
 1027              	.LVL100:
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 1028              		.loc 1 565 55 discriminator 1 view .LVU278
 1029 0024 9B68     		ldr	r3, [r3, #8]
 1030              	.LVL101:
 565:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       etharp_free_entry(i);
 1031              		.loc 1 565 39 discriminator 1 view .LVU279
 1032 0026 AB42     		cmp	r3, r5
 1033 0028 EED1     		bne	.L70
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 1034              		.loc 1 566 7 is_stmt 1 view .LVU280
 1035 002a 2046     		mov	r0, r4
 1036 002c FFF7FEFF 		bl	etharp_free_entry
 1037              	.LVL102:
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 1038              		.loc 1 566 7 is_stmt 0 view .LVU281
 1039 0030 EAE7     		b	.L70
 1040              	.LVL103:
 1041              	.L73:
 566:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 1042              		.loc 1 566 7 view .LVU282
 1043              	.LBE6:
 569:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1044              		.loc 1 569 1 view .LVU283
 1045 0032 38BD     		pop	{r3, r4, r5, pc}
 1046              	.LVL104:
 1047              	.L75:
 569:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1048              		.loc 1 569 1 view .LVU284
 1049              		.align	2
 1050              	.L74:
 1051 0034 00000000 		.word	arp_table
 1052              		.cfi_endproc
 1053              	.LFE174:
 1055              		.section	.rodata.etharp_find_addr.str1.4,"aMS",%progbits,1
 1056              		.align	2
 1057              	.LC9:
 1058 0000 6574685F 		.ascii	"eth_ret != NULL && ip_ret != NULL\000"
 1058      72657420 
 1058      213D204E 
 1058      554C4C20 
 1058      26262069 
 1059              		.section	.text.etharp_find_addr,"ax",%progbits
 1060              		.align	1
 1061              		.global	etharp_find_addr
 1062              		.syntax unified
 1063              		.thumb
 1064              		.thumb_func
 1066              	etharp_find_addr:
 1067              	.LVL105:
 1068              	.LFB175:
 585:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i;
ARM GAS  /tmp/cchE5ny2.s 			page 43


 1069              		.loc 1 585 1 is_stmt 1 view -0
 1070              		.cfi_startproc
 1071              		@ args = 0, pretend = 0, frame = 0
 1072              		@ frame_needed = 0, uses_anonymous_args = 0
 585:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i;
 1073              		.loc 1 585 1 is_stmt 0 view .LVU286
 1074 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 1075              	.LCFI17:
 1076              		.cfi_def_cfa_offset 24
 1077              		.cfi_offset 3, -24
 1078              		.cfi_offset 4, -20
 1079              		.cfi_offset 5, -16
 1080              		.cfi_offset 6, -12
 1081              		.cfi_offset 7, -8
 1082              		.cfi_offset 14, -4
 1083 0002 0746     		mov	r7, r0
 1084 0004 0E46     		mov	r6, r1
 586:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1085              		.loc 1 586 3 is_stmt 1 view .LVU287
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1086              		.loc 1 588 3 view .LVU288
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1087              		.loc 1 588 3 view .LVU289
 1088 0006 1546     		mov	r5, r2
 1089 0008 1C46     		mov	r4, r3
 1090 000a 002B     		cmp	r3, #0
 1091 000c 18BF     		it	ne
 1092 000e 002A     		cmpne	r2, #0
 1093 0010 1CD0     		beq	.L82
 1094              	.LVL106:
 1095              	.L77:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1096              		.loc 1 588 3 discriminator 3 view .LVU290
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1097              		.loc 1 588 3 discriminator 3 view .LVU291
 591:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1098              		.loc 1 591 3 view .LVU292
 593:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if ((i >= 0) && (arp_table[i].state >= ETHARP_STATE_STABLE)) {
 1099              		.loc 1 593 3 view .LVU293
 593:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   if ((i >= 0) && (arp_table[i].state >= ETHARP_STATE_STABLE)) {
 1100              		.loc 1 593 7 is_stmt 0 view .LVU294
 1101 0012 3A46     		mov	r2, r7
 1102 0014 0221     		movs	r1, #2
 1103 0016 3046     		mov	r0, r6
 1104 0018 FFF7FEFF 		bl	etharp_find_entry
 1105              	.LVL107:
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1106              		.loc 1 594 3 is_stmt 1 view .LVU295
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1107              		.loc 1 594 6 is_stmt 0 view .LVU296
 1108 001c 031E     		subs	r3, r0, #0
 1109 001e 1DDB     		blt	.L79
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1110              		.loc 1 594 32 discriminator 1 view .LVU297
 1111 0020 1846     		mov	r0, r3
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1112              		.loc 1 594 32 discriminator 1 view .LVU298
ARM GAS  /tmp/cchE5ny2.s 			page 44


 1113 0022 03EB4301 		add	r1, r3, r3, lsl #1
 1114 0026 104A     		ldr	r2, .L83
 1115 0028 02EBC102 		add	r2, r2, r1, lsl #3
 1116 002c 127D     		ldrb	r2, [r2, #20]	@ zero_extendqisi2
 594:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1117              		.loc 1 594 16 discriminator 1 view .LVU299
 1118 002e 012A     		cmp	r2, #1
 1119 0030 17D9     		bls	.L80
 595:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ip_ret = &arp_table[i].ipaddr;
 1120              		.loc 1 595 5 is_stmt 1 view .LVU300
 595:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ip_ret = &arp_table[i].ipaddr;
 1121              		.loc 1 595 16 is_stmt 0 view .LVU301
 1122 0032 CA00     		lsls	r2, r1, #3
 1123 0034 0832     		adds	r2, r2, #8
 1124 0036 0C49     		ldr	r1, .L83
 1125 0038 0A44     		add	r2, r2, r1
 1126 003a 0432     		adds	r2, r2, #4
 595:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ip_ret = &arp_table[i].ipaddr;
 1127              		.loc 1 595 14 view .LVU302
 1128 003c 2A60     		str	r2, [r5]
 596:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return i;
 1129              		.loc 1 596 5 is_stmt 1 view .LVU303
 596:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return i;
 1130              		.loc 1 596 15 is_stmt 0 view .LVU304
 1131 003e 03EB4303 		add	r3, r3, r3, lsl #1
 1132 0042 01EBC301 		add	r1, r1, r3, lsl #3
 1133 0046 0431     		adds	r1, r1, #4
 596:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return i;
 1134              		.loc 1 596 13 view .LVU305
 1135 0048 2160     		str	r1, [r4]
 597:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1136              		.loc 1 597 5 is_stmt 1 view .LVU306
 1137              	.LVL108:
 1138              	.L76:
 600:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1139              		.loc 1 600 1 is_stmt 0 view .LVU307
 1140 004a F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 1141              	.LVL109:
 1142              	.L82:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1143              		.loc 1 588 3 is_stmt 1 discriminator 1 view .LVU308
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1144              		.loc 1 588 3 discriminator 1 view .LVU309
 1145 004c 074B     		ldr	r3, .L83+4
 1146              	.LVL110:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1147              		.loc 1 588 3 is_stmt 0 discriminator 1 view .LVU310
 1148 004e 4FF41372 		mov	r2, #588
 1149              	.LVL111:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1150              		.loc 1 588 3 discriminator 1 view .LVU311
 1151 0052 0749     		ldr	r1, .L83+8
 1152              	.LVL112:
 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1153              		.loc 1 588 3 discriminator 1 view .LVU312
 1154 0054 0748     		ldr	r0, .L83+12
 1155              	.LVL113:
ARM GAS  /tmp/cchE5ny2.s 			page 45


 588:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               eth_ret != NULL && ip_ret != NULL);
 1156              		.loc 1 588 3 discriminator 1 view .LVU313
 1157 0056 FFF7FEFF 		bl	printf
 1158              	.LVL114:
 1159 005a DAE7     		b	.L77
 1160              	.LVL115:
 1161              	.L79:
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1162              		.loc 1 599 10 view .LVU314
 1163 005c 4FF0FF30 		mov	r0, #-1
 1164              	.LVL116:
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1165              		.loc 1 599 10 view .LVU315
 1166 0060 F3E7     		b	.L76
 1167              	.LVL117:
 1168              	.L80:
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1169              		.loc 1 599 10 view .LVU316
 1170 0062 4FF0FF30 		mov	r0, #-1
 1171              	.LVL118:
 599:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1172              		.loc 1 599 10 view .LVU317
 1173 0066 F0E7     		b	.L76
 1174              	.L84:
 1175              		.align	2
 1176              	.L83:
 1177 0068 00000000 		.word	arp_table
 1178 006c 00000000 		.word	.LC0
 1179 0070 00000000 		.word	.LC9
 1180 0074 4C000000 		.word	.LC2
 1181              		.cfi_endproc
 1182              	.LFE175:
 1184              		.section	.rodata.etharp_get_entry.str1.4,"aMS",%progbits,1
 1185              		.align	2
 1186              	.LC10:
 1187 0000 69706164 		.ascii	"ipaddr != NULL\000"
 1187      64722021 
 1187      3D204E55 
 1187      4C4C00
 1188 000f 00       		.align	2
 1189              	.LC11:
 1190 0010 6574685F 		.ascii	"eth_ret != NULL\000"
 1190      72657420 
 1190      213D204E 
 1190      554C4C00 
 1191              		.section	.text.etharp_get_entry,"ax",%progbits
 1192              		.align	1
 1193              		.global	etharp_get_entry
 1194              		.syntax unified
 1195              		.thumb
 1196              		.thumb_func
 1198              	etharp_get_entry:
 1199              	.LVL119:
 1200              	.LFB176:
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 1201              		.loc 1 613 1 is_stmt 1 view -0
 1202              		.cfi_startproc
ARM GAS  /tmp/cchE5ny2.s 			page 46


 1203              		@ args = 0, pretend = 0, frame = 0
 1204              		@ frame_needed = 0, uses_anonymous_args = 0
 613:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 1205              		.loc 1 613 1 is_stmt 0 view .LVU319
 1206 0000 F8B5     		push	{r3, r4, r5, r6, r7, lr}
 1207              	.LCFI18:
 1208              		.cfi_def_cfa_offset 24
 1209              		.cfi_offset 3, -24
 1210              		.cfi_offset 4, -20
 1211              		.cfi_offset 5, -16
 1212              		.cfi_offset 6, -12
 1213              		.cfi_offset 7, -8
 1214              		.cfi_offset 14, -4
 1215 0002 0446     		mov	r4, r0
 1216 0004 1646     		mov	r6, r2
 1217 0006 1D46     		mov	r5, r3
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1218              		.loc 1 614 3 is_stmt 1 view .LVU320
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1219              		.loc 1 614 3 view .LVU321
 1220 0008 0F46     		mov	r7, r1
 1221 000a D9B1     		cbz	r1, .L93
 1222              	.LVL120:
 1223              	.L86:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1224              		.loc 1 614 3 discriminator 3 view .LVU322
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1225              		.loc 1 614 3 discriminator 3 view .LVU323
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 1226              		.loc 1 615 3 view .LVU324
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 1227              		.loc 1 615 3 view .LVU325
 1228 000c 16B3     		cbz	r6, .L94
 1229              	.L87:
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 1230              		.loc 1 615 3 discriminator 3 view .LVU326
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 1231              		.loc 1 615 3 discriminator 3 view .LVU327
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1232              		.loc 1 616 3 view .LVU328
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1233              		.loc 1 616 3 view .LVU329
 1234 000e 4DB3     		cbz	r5, .L95
 1235              	.L88:
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1236              		.loc 1 616 3 discriminator 3 view .LVU330
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1237              		.loc 1 616 3 discriminator 3 view .LVU331
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ipaddr  = &arp_table[i].ipaddr;
 1238              		.loc 1 618 3 view .LVU332
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ipaddr  = &arp_table[i].ipaddr;
 1239              		.loc 1 618 6 is_stmt 0 view .LVU333
 1240 0010 092C     		cmp	r4, #9
 1241 0012 2FD8     		bhi	.L90
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ipaddr  = &arp_table[i].ipaddr;
 1242              		.loc 1 618 44 discriminator 1 view .LVU334
 1243 0014 04EB4402 		add	r2, r4, r4, lsl #1
ARM GAS  /tmp/cchE5ny2.s 			page 47


 1244 0018 184B     		ldr	r3, .L96
 1245 001a 03EBC203 		add	r3, r3, r2, lsl #3
 1246 001e 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 618:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *ipaddr  = &arp_table[i].ipaddr;
 1247              		.loc 1 618 28 discriminator 1 view .LVU335
 1248 0020 012B     		cmp	r3, #1
 1249 0022 29D9     		bls	.L91
 619:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *netif   = arp_table[i].netif;
 1250              		.loc 1 619 5 is_stmt 1 view .LVU336
 619:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *netif   = arp_table[i].netif;
 1251              		.loc 1 619 16 is_stmt 0 view .LVU337
 1252 0024 1549     		ldr	r1, .L96
 1253 0026 01EBC203 		add	r3, r1, r2, lsl #3
 1254 002a 1A1D     		adds	r2, r3, #4
 619:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *netif   = arp_table[i].netif;
 1255              		.loc 1 619 14 view .LVU338
 1256 002c 3A60     		str	r2, [r7]
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1257              		.loc 1 620 5 is_stmt 1 view .LVU339
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1258              		.loc 1 620 28 is_stmt 0 view .LVU340
 1259 002e 9B68     		ldr	r3, [r3, #8]
 620:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     *eth_ret = &arp_table[i].ethaddr;
 1260              		.loc 1 620 14 view .LVU341
 1261 0030 3360     		str	r3, [r6]
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return 1;
 1262              		.loc 1 621 5 is_stmt 1 view .LVU342
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return 1;
 1263              		.loc 1 621 16 is_stmt 0 view .LVU343
 1264 0032 04EB4402 		add	r2, r4, r4, lsl #1
 1265 0036 D200     		lsls	r2, r2, #3
 1266 0038 0832     		adds	r2, r2, #8
 1267 003a 0A44     		add	r2, r2, r1
 1268 003c 0432     		adds	r2, r2, #4
 621:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return 1;
 1269              		.loc 1 621 14 view .LVU344
 1270 003e 2A60     		str	r2, [r5]
 622:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else {
 1271              		.loc 1 622 5 is_stmt 1 view .LVU345
 622:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   } else {
 1272              		.loc 1 622 12 is_stmt 0 view .LVU346
 1273 0040 0120     		movs	r0, #1
 1274              	.L85:
 626:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1275              		.loc 1 626 1 view .LVU347
 1276 0042 F8BD     		pop	{r3, r4, r5, r6, r7, pc}
 1277              	.LVL121:
 1278              	.L93:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1279              		.loc 1 614 3 is_stmt 1 discriminator 1 view .LVU348
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1280              		.loc 1 614 3 discriminator 1 view .LVU349
 1281 0044 0E4B     		ldr	r3, .L96+4
 1282              	.LVL122:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1283              		.loc 1 614 3 is_stmt 0 discriminator 1 view .LVU350
 1284 0046 40F26622 		movw	r2, #614
ARM GAS  /tmp/cchE5ny2.s 			page 48


 1285              	.LVL123:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1286              		.loc 1 614 3 discriminator 1 view .LVU351
 1287 004a 0E49     		ldr	r1, .L96+8
 1288              	.LVL124:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1289              		.loc 1 614 3 discriminator 1 view .LVU352
 1290 004c 0E48     		ldr	r0, .L96+12
 1291              	.LVL125:
 614:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 1292              		.loc 1 614 3 discriminator 1 view .LVU353
 1293 004e FFF7FEFF 		bl	printf
 1294              	.LVL126:
 1295 0052 DBE7     		b	.L86
 1296              	.L94:
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 1297              		.loc 1 615 3 is_stmt 1 discriminator 1 view .LVU354
 615:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("eth_ret != NULL", eth_ret != NULL);
 1298              		.loc 1 615 3 discriminator 1 view .LVU355
 1299 0054 0A4B     		ldr	r3, .L96+4
 1300 0056 40F26722 		movw	r2, #615
 1301 005a 0C49     		ldr	r1, .L96+16
 1302 005c 0A48     		ldr	r0, .L96+12
 1303 005e FFF7FEFF 		bl	printf
 1304              	.LVL127:
 1305 0062 D4E7     		b	.L87
 1306              	.L95:
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1307              		.loc 1 616 3 discriminator 1 view .LVU356
 616:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1308              		.loc 1 616 3 discriminator 1 view .LVU357
 1309 0064 064B     		ldr	r3, .L96+4
 1310 0066 4FF41A72 		mov	r2, #616
 1311 006a 0949     		ldr	r1, .L96+20
 1312 006c 0648     		ldr	r0, .L96+12
 1313 006e FFF7FEFF 		bl	printf
 1314              	.LVL128:
 1315 0072 CDE7     		b	.L88
 1316              	.L90:
 624:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1317              		.loc 1 624 12 is_stmt 0 view .LVU358
 1318 0074 0020     		movs	r0, #0
 1319 0076 E4E7     		b	.L85
 1320              	.L91:
 1321 0078 0020     		movs	r0, #0
 1322 007a E2E7     		b	.L85
 1323              	.L97:
 1324              		.align	2
 1325              	.L96:
 1326 007c 00000000 		.word	arp_table
 1327 0080 00000000 		.word	.LC0
 1328 0084 00000000 		.word	.LC10
 1329 0088 4C000000 		.word	.LC2
 1330 008c 00000000 		.word	.LC6
 1331 0090 10000000 		.word	.LC11
 1332              		.cfi_endproc
 1333              	.LFE176:
ARM GAS  /tmp/cchE5ny2.s 			page 49


 1335              		.section	.text.etharp_input,"ax",%progbits
 1336              		.align	1
 1337              		.global	etharp_input
 1338              		.syntax unified
 1339              		.thumb
 1340              		.thumb_func
 1342              	etharp_input:
 1343              	.LVL129:
 1344              	.LFB177:
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct etharp_hdr *hdr;
 1345              		.loc 1 642 1 is_stmt 1 view -0
 1346              		.cfi_startproc
 1347              		@ args = 0, pretend = 0, frame = 8
 1348              		@ frame_needed = 0, uses_anonymous_args = 0
 642:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct etharp_hdr *hdr;
 1349              		.loc 1 642 1 is_stmt 0 view .LVU360
 1350 0000 2DE9F041 		push	{r4, r5, r6, r7, r8, lr}
 1351              	.LCFI19:
 1352              		.cfi_def_cfa_offset 24
 1353              		.cfi_offset 4, -24
 1354              		.cfi_offset 5, -20
 1355              		.cfi_offset 6, -16
 1356              		.cfi_offset 7, -12
 1357              		.cfi_offset 8, -8
 1358              		.cfi_offset 14, -4
 1359 0004 86B0     		sub	sp, sp, #24
 1360              	.LCFI20:
 1361              		.cfi_def_cfa_offset 48
 643:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* these are aligned properly, whereas the ARP header fields might not be */
 1362              		.loc 1 643 3 is_stmt 1 view .LVU361
 645:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   u8_t for_us;
 1363              		.loc 1 645 3 view .LVU362
 646:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1364              		.loc 1 646 3 view .LVU363
 648:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1365              		.loc 1 648 28 view .LVU364
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1366              		.loc 1 650 3 view .LVU365
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1367              		.loc 1 650 3 view .LVU366
 1368 0006 A9B1     		cbz	r1, .L109
 1369 0008 0446     		mov	r4, r0
 1370 000a 0E46     		mov	r6, r1
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1371              		.loc 1 650 3 discriminator 2 view .LVU367
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1372              		.loc 1 650 3 discriminator 2 view .LVU368
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1373              		.loc 1 652 3 view .LVU369
 652:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1374              		.loc 1 652 7 is_stmt 0 view .LVU370
 1375 000c 4568     		ldr	r5, [r0, #4]
 1376              	.LVL130:
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->hwlen != ETH_HWADDR_LEN) ||
 1377              		.loc 1 655 3 is_stmt 1 view .LVU371
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->hwlen != ETH_HWADDR_LEN) ||
 1378              		.loc 1 655 11 is_stmt 0 view .LVU372
ARM GAS  /tmp/cchE5ny2.s 			page 50


 1379 000e 2B88     		ldrh	r3, [r5]	@ unaligned
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->hwlen != ETH_HWADDR_LEN) ||
 1380              		.loc 1 655 6 view .LVU373
 1381 0010 B3F5807F 		cmp	r3, #256
 1382 0014 08D1     		bne	.L101
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->protolen != sizeof(ip4_addr_t)) ||
 1383              		.loc 1 656 11 view .LVU374
 1384 0016 2B79     		ldrb	r3, [r5, #4]	@ zero_extendqisi2
 655:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->hwlen != ETH_HWADDR_LEN) ||
 1385              		.loc 1 655 60 discriminator 1 view .LVU375
 1386 0018 062B     		cmp	r3, #6
 1387 001a 05D1     		bne	.L101
 657:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->proto != PP_HTONS(ETHTYPE_IP)))  {
 1388              		.loc 1 657 11 view .LVU376
 1389 001c 6B79     		ldrb	r3, [r5, #5]	@ zero_extendqisi2
 656:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->protolen != sizeof(ip4_addr_t)) ||
 1390              		.loc 1 656 38 view .LVU377
 1391 001e 042B     		cmp	r3, #4
 1392 0020 02D1     		bne	.L101
 658:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE | LWIP_DBG_LEVEL_WARNING,
 1393              		.loc 1 658 11 view .LVU378
 1394 0022 6B88     		ldrh	r3, [r5, #2]	@ unaligned
 657:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       (hdr->proto != PP_HTONS(ETHTYPE_IP)))  {
 1395              		.loc 1 657 45 view .LVU379
 1396 0024 082B     		cmp	r3, #8
 1397 0026 0DD0     		beq	.L102
 1398              	.L101:
 661:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ETHARP_STATS_INC(etharp.proterr);
 1399              		.loc 1 661 84 is_stmt 1 view .LVU380
 662:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     ETHARP_STATS_INC(etharp.drop);
 1400              		.loc 1 662 37 view .LVU381
 663:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     pbuf_free(p);
 1401              		.loc 1 663 34 view .LVU382
 664:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return;
 1402              		.loc 1 664 5 view .LVU383
 1403 0028 2046     		mov	r0, r4
 1404              	.LVL131:
 664:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     return;
 1405              		.loc 1 664 5 is_stmt 0 view .LVU384
 1406 002a FFF7FEFF 		bl	pbuf_free
 1407              	.LVL132:
 665:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1408              		.loc 1 665 5 is_stmt 1 view .LVU385
 1409              	.L98:
 742:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1410              		.loc 1 742 1 is_stmt 0 view .LVU386
 1411 002e 06B0     		add	sp, sp, #24
 1412              	.LCFI21:
 1413              		.cfi_remember_state
 1414              		.cfi_def_cfa_offset 24
 1415              		@ sp needed
 1416 0030 BDE8F081 		pop	{r4, r5, r6, r7, r8, pc}
 1417              	.LVL133:
 1418              	.L109:
 1419              	.LCFI22:
 1420              		.cfi_restore_state
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
ARM GAS  /tmp/cchE5ny2.s 			page 51


 1421              		.loc 1 650 3 is_stmt 1 discriminator 1 view .LVU387
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1422              		.loc 1 650 3 discriminator 1 view .LVU388
 1423 0034 1D4B     		ldr	r3, .L111
 1424 0036 40F28A22 		movw	r2, #650
 1425 003a 1D49     		ldr	r1, .L111+4
 1426              	.LVL134:
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1427              		.loc 1 650 3 is_stmt 0 discriminator 1 view .LVU389
 1428 003c 1D48     		ldr	r0, .L111+8
 1429              	.LVL135:
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1430              		.loc 1 650 3 discriminator 1 view .LVU390
 1431 003e FFF7FEFF 		bl	printf
 1432              	.LVL136:
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1433              		.loc 1 650 3 is_stmt 1 discriminator 1 view .LVU391
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1434              		.loc 1 650 3 discriminator 1 view .LVU392
 650:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1435              		.loc 1 650 3 is_stmt 0 view .LVU393
 1436 0042 F4E7     		b	.L98
 1437              	.LVL137:
 1438              	.L102:
 667:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1439              		.loc 1 667 32 is_stmt 1 view .LVU394
 678:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   IPADDR_WORDALIGNED_COPY_TO_IP4_ADDR_T(&dipaddr, &hdr->dipaddr);
 1440              		.loc 1 678 3 view .LVU395
 1441 0044 D5F80E30 		ldr	r3, [r5, #14]	@ unaligned
 1442 0048 0593     		str	r3, [sp, #20]
 679:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1443              		.loc 1 679 3 view .LVU396
 1444 004a AA69     		ldr	r2, [r5, #24]	@ unaligned
 682:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     for_us = 0;
 1445              		.loc 1 682 3 view .LVU397
 682:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     for_us = 0;
 1446              		.loc 1 682 7 is_stmt 0 view .LVU398
 1447 004c 4F68     		ldr	r7, [r1, #4]
 682:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     for_us = 0;
 1448              		.loc 1 682 6 view .LVU399
 1449 004e 7FB9     		cbnz	r7, .L103
 1450              	.LVL138:
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1451              		.loc 1 694 3 is_stmt 1 view .LVU400
 1452 0050 05F10808 		add	r8, r5, #8
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1453              		.loc 1 694 3 is_stmt 0 discriminator 2 view .LVU401
 1454 0054 0223     		movs	r3, #2
 1455              	.LVL139:
 1456              	.L104:
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1457              		.loc 1 694 3 discriminator 4 view .LVU402
 1458 0056 4246     		mov	r2, r8
 1459 0058 05A9     		add	r1, sp, #20
 1460              	.LVL140:
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1461              		.loc 1 694 3 discriminator 4 view .LVU403
ARM GAS  /tmp/cchE5ny2.s 			page 52


 1462 005a 3046     		mov	r0, r6
 1463              	.LVL141:
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1464              		.loc 1 694 3 discriminator 4 view .LVU404
 1465 005c FFF7FEFF 		bl	etharp_update_arp_entry
 1466              	.LVL142:
 698:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* ARP request? */
 1467              		.loc 1 698 3 is_stmt 1 view .LVU405
 698:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* ARP request? */
 1468              		.loc 1 698 14 is_stmt 0 view .LVU406
 1469 0060 EB88     		ldrh	r3, [r5, #6]	@ unaligned
 698:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* ARP request? */
 1470              		.loc 1 698 3 view .LVU407
 1471 0062 B3F5807F 		cmp	r3, #256
 1472 0066 0ED0     		beq	.L110
 1473              	.L105:
 741:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1474              		.loc 1 741 3 is_stmt 1 view .LVU408
 1475 0068 2046     		mov	r0, r4
 1476 006a FFF7FEFF 		bl	pbuf_free
 1477              	.LVL143:
 1478 006e DEE7     		b	.L98
 1479              	.LVL144:
 1480              	.L103:
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1481              		.loc 1 686 5 view .LVU409
 686:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1482              		.loc 1 686 14 is_stmt 0 view .LVU410
 1483 0070 9742     		cmp	r7, r2
 1484 0072 14BF     		ite	ne
 1485 0074 0027     		movne	r7, #0
 1486 0076 0127     		moveq	r7, #1
 1487              	.LVL145:
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1488              		.loc 1 694 3 is_stmt 1 view .LVU411
 1489 0078 05F10808 		add	r8, r5, #8
 1490 007c 0FB1     		cbz	r7, .L107
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1491              		.loc 1 694 3 is_stmt 0 discriminator 1 view .LVU412
 1492 007e 0123     		movs	r3, #1
 1493 0080 E9E7     		b	.L104
 1494              	.L107:
 694:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                           for_us ? ETHARP_FLAG_TRY_HARD : ETHARP_FLAG_FIND_ONLY);
 1495              		.loc 1 694 3 discriminator 2 view .LVU413
 1496 0082 0223     		movs	r3, #2
 1497 0084 E7E7     		b	.L104
 1498              	.LVL146:
 1499              	.L110:
 705:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* ARP request for our address? */
 1500              		.loc 1 705 92 is_stmt 1 view .LVU414
 707:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* send ARP response */
 1501              		.loc 1 707 7 view .LVU415
 707:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* send ARP response */
 1502              		.loc 1 707 10 is_stmt 0 view .LVU416
 1503 0086 002F     		cmp	r7, #0
 1504 0088 EED0     		beq	.L105
 709:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    (struct eth_addr *)netif->hwaddr, &hdr->shwaddr,
ARM GAS  /tmp/cchE5ny2.s 			page 53


 1505              		.loc 1 709 9 is_stmt 1 view .LVU417
 710:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    (struct eth_addr *)netif->hwaddr, netif_ip4_addr(netif),
 1506              		.loc 1 710 39 is_stmt 0 view .LVU418
 1507 008a 06F12601 		add	r1, r6, #38
 711:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    &hdr->shwaddr, &sipaddr,
 1508              		.loc 1 711 54 view .LVU419
 1509 008e 331D     		adds	r3, r6, #4
 709:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****                    (struct eth_addr *)netif->hwaddr, &hdr->shwaddr,
 1510              		.loc 1 709 9 view .LVU420
 1511 0090 0222     		movs	r2, #2
 1512 0092 0392     		str	r2, [sp, #12]
 1513 0094 05AA     		add	r2, sp, #20
 1514 0096 0292     		str	r2, [sp, #8]
 1515 0098 CDF80480 		str	r8, [sp, #4]
 1516 009c 0093     		str	r3, [sp]
 1517 009e 0B46     		mov	r3, r1
 1518 00a0 4246     		mov	r2, r8
 1519 00a2 3046     		mov	r0, r6
 1520 00a4 FFF7FEFF 		bl	etharp_raw
 1521              	.LVL147:
 1522 00a8 DEE7     		b	.L105
 1523              	.L112:
 1524 00aa 00BF     		.align	2
 1525              	.L111:
 1526 00ac 00000000 		.word	.LC0
 1527 00b0 00000000 		.word	.LC6
 1528 00b4 4C000000 		.word	.LC2
 1529              		.cfi_endproc
 1530              	.LFE177:
 1532              		.section	.text.etharp_request,"ax",%progbits
 1533              		.align	1
 1534              		.global	etharp_request
 1535              		.syntax unified
 1536              		.thumb
 1537              		.thumb_func
 1539              	etharp_request:
 1540              	.LVL148:
 1541              	.LFB183:
1187:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
1188:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** /**
1189:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * Send an ARP request packet asking for ipaddr.
1190:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *
1191:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param netif the lwip network interface on which to send the request
1192:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @param ipaddr the IP address for which to ask
1193:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  * @return ERR_OK if the request has been sent
1194:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         ERR_MEM if the ARP packet couldn't be allocated
1195:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  *         any other err_t on failure
1196:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****  */
1197:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** err_t
1198:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** etharp_request(struct netif *netif, const ip4_addr_t *ipaddr)
1199:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** {
 1542              		.loc 1 1199 1 is_stmt 1 view -0
 1543              		.cfi_startproc
 1544              		@ args = 0, pretend = 0, frame = 0
 1545              		@ frame_needed = 0, uses_anonymous_args = 0
 1546              		.loc 1 1199 1 is_stmt 0 view .LVU422
 1547 0000 08B5     		push	{r3, lr}
ARM GAS  /tmp/cchE5ny2.s 			page 54


 1548              	.LCFI23:
 1549              		.cfi_def_cfa_offset 8
 1550              		.cfi_offset 3, -8
 1551              		.cfi_offset 14, -4
1200:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_request: sending ARP request.\n"));
 1552              		.loc 1 1200 89 is_stmt 1 view .LVU423
1201:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   return etharp_request_dst(netif, ipaddr, &ethbroadcast);
 1553              		.loc 1 1201 3 view .LVU424
 1554              		.loc 1 1201 10 is_stmt 0 view .LVU425
 1555 0002 024A     		ldr	r2, .L115
 1556 0004 FFF7FEFF 		bl	etharp_request_dst
 1557              	.LVL149:
1202:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1558              		.loc 1 1202 1 view .LVU426
 1559 0008 08BD     		pop	{r3, pc}
 1560              	.L116:
 1561 000a 00BF     		.align	2
 1562              	.L115:
 1563 000c 00000000 		.word	ethbroadcast
 1564              		.cfi_endproc
 1565              	.LFE183:
 1567              		.section	.text.etharp_tmr,"ax",%progbits
 1568              		.align	1
 1569              		.global	etharp_tmr
 1570              		.syntax unified
 1571              		.thumb
 1572              		.thumb_func
 1574              	etharp_tmr:
 1575              	.LFB171:
 198:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int i;
 1576              		.loc 1 198 1 is_stmt 1 view -0
 1577              		.cfi_startproc
 1578              		@ args = 0, pretend = 0, frame = 0
 1579              		@ frame_needed = 0, uses_anonymous_args = 0
 1580 0000 10B5     		push	{r4, lr}
 1581              	.LCFI24:
 1582              		.cfi_def_cfa_offset 8
 1583              		.cfi_offset 4, -8
 1584              		.cfi_offset 14, -4
 199:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1585              		.loc 1 199 3 view .LVU428
 201:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* remove expired entries from the ARP table */
 1586              		.loc 1 201 48 view .LVU429
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1587              		.loc 1 203 3 view .LVU430
 1588              	.LVL150:
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1589              		.loc 1 203 10 is_stmt 0 view .LVU431
 1590 0002 0024     		movs	r4, #0
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1591              		.loc 1 203 3 view .LVU432
 1592 0004 05E0     		b	.L118
 1593              	.LVL151:
 1594              	.L127:
 1595              	.LBB7:
 212:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****            (arp_table[i].ctime >= ARP_MAXPENDING))) {
 1596              		.loc 1 212 58 view .LVU433
ARM GAS  /tmp/cchE5ny2.s 			page 55


 1597 0006 042B     		cmp	r3, #4
 1598 0008 1BD9     		bls	.L121
 1599              	.L120:
 216:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* clean up entries that have just been expired */
 1600              		.loc 1 216 105 is_stmt 1 view .LVU434
 218:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_1) {
 1601              		.loc 1 218 9 view .LVU435
 1602 000a 2046     		mov	r0, r4
 1603 000c FFF7FEFF 		bl	etharp_free_entry
 1604              	.LVL152:
 1605              	.L119:
 218:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_1) {
 1606              		.loc 1 218 9 is_stmt 0 view .LVU436
 1607              	.LBE7:
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1608              		.loc 1 203 35 is_stmt 1 discriminator 2 view .LVU437
 1609 0010 0134     		adds	r4, r4, #1
 1610              	.LVL153:
 1611              	.L118:
 203:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     u8_t state = arp_table[i].state;
 1612              		.loc 1 203 17 discriminator 1 view .LVU438
 1613 0012 092C     		cmp	r4, #9
 1614 0014 35DC     		bgt	.L126
 1615              	.LBB8:
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (state != ETHARP_STATE_EMPTY
 1616              		.loc 1 204 5 view .LVU439
 204:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (state != ETHARP_STATE_EMPTY
 1617              		.loc 1 204 10 is_stmt 0 view .LVU440
 1618 0016 04EB4403 		add	r3, r4, r4, lsl #1
 1619 001a DA00     		lsls	r2, r3, #3
 1620 001c 194B     		ldr	r3, .L130
 1621 001e 1344     		add	r3, r3, r2
 1622 0020 1A7D     		ldrb	r2, [r3, #20]	@ zero_extendqisi2
 1623              	.LVL154:
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 1624              		.loc 1 205 5 is_stmt 1 view .LVU441
 205:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_SUPPORT_STATIC_ENTRIES
 1625              		.loc 1 205 8 is_stmt 0 view .LVU442
 1626 0022 002A     		cmp	r2, #0
 1627 0024 F4D0     		beq	.L119
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].ctime >= ARP_MAXAGE) ||
 1628              		.loc 1 210 7 is_stmt 1 view .LVU443
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].ctime >= ARP_MAXAGE) ||
 1629              		.loc 1 210 19 is_stmt 0 view .LVU444
 1630 0026 04EB4401 		add	r1, r4, r4, lsl #1
 1631 002a CB00     		lsls	r3, r1, #3
 1632 002c 1549     		ldr	r1, .L130
 1633 002e 1944     		add	r1, r1, r3
 1634 0030 4B8A     		ldrh	r3, [r1, #18]
 210:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].ctime >= ARP_MAXAGE) ||
 1635              		.loc 1 210 25 view .LVU445
 1636 0032 0133     		adds	r3, r3, #1
 1637 0034 9BB2     		uxth	r3, r3
 1638 0036 4B82     		strh	r3, [r1, #18]	@ movhi
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           ((arp_table[i].state == ETHARP_STATE_PENDING)  &&
 1639              		.loc 1 211 7 is_stmt 1 view .LVU446
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           ((arp_table[i].state == ETHARP_STATE_PENDING)  &&
ARM GAS  /tmp/cchE5ny2.s 			page 56


 1640              		.loc 1 211 10 is_stmt 0 view .LVU447
 1641 0038 B3F5967F 		cmp	r3, #300
 1642 003c E5D2     		bcs	.L120
 211:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           ((arp_table[i].state == ETHARP_STATE_PENDING)  &&
 1643              		.loc 1 211 46 discriminator 1 view .LVU448
 1644 003e 012A     		cmp	r2, #1
 1645 0040 E1D0     		beq	.L127
 1646              	.L121:
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* Don't send more than one request every 2 seconds. */
 1647              		.loc 1 219 14 is_stmt 1 view .LVU449
 219:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* Don't send more than one request every 2 seconds. */
 1648              		.loc 1 219 17 is_stmt 0 view .LVU450
 1649 0042 032A     		cmp	r2, #3
 1650 0044 0DD0     		beq	.L128
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* Reset state to stable, so that the next transmitted packet will
 1651              		.loc 1 222 14 is_stmt 1 view .LVU451
 222:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* Reset state to stable, so that the next transmitted packet will
 1652              		.loc 1 222 17 is_stmt 0 view .LVU452
 1653 0046 042A     		cmp	r2, #4
 1654 0048 13D0     		beq	.L129
 226:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* still pending, resend an ARP query */
 1655              		.loc 1 226 14 is_stmt 1 view .LVU453
 226:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* still pending, resend an ARP query */
 1656              		.loc 1 226 17 is_stmt 0 view .LVU454
 1657 004a 012A     		cmp	r2, #1
 1658 004c E0D1     		bne	.L119
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1659              		.loc 1 228 9 is_stmt 1 view .LVU455
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1660              		.loc 1 228 44 is_stmt 0 view .LVU456
 1661 004e 04EB4402 		add	r2, r4, r4, lsl #1
 1662              	.LVL155:
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1663              		.loc 1 228 44 view .LVU457
 1664 0052 D300     		lsls	r3, r2, #3
 1665 0054 0B4A     		ldr	r2, .L130
 1666 0056 1344     		add	r3, r3, r2
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1667              		.loc 1 228 9 view .LVU458
 1668 0058 191D     		adds	r1, r3, #4
 1669              	.LVL156:
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1670              		.loc 1 228 9 view .LVU459
 1671 005a 9868     		ldr	r0, [r3, #8]
 1672 005c FFF7FEFF 		bl	etharp_request
 1673              	.LVL157:
 228:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1674              		.loc 1 228 9 view .LVU460
 1675 0060 D6E7     		b	.L119
 1676              	.LVL158:
 1677              	.L128:
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_2) {
 1678              		.loc 1 221 9 is_stmt 1 view .LVU461
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_2) {
 1679              		.loc 1 221 28 is_stmt 0 view .LVU462
 1680 0062 04EB4403 		add	r3, r4, r4, lsl #1
 1681 0066 DA00     		lsls	r2, r3, #3
ARM GAS  /tmp/cchE5ny2.s 			page 57


 1682              	.LVL159:
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_2) {
 1683              		.loc 1 221 28 view .LVU463
 1684 0068 064B     		ldr	r3, .L130
 1685 006a 1344     		add	r3, r3, r2
 1686 006c 0422     		movs	r2, #4
 1687 006e 1A75     		strb	r2, [r3, #20]
 1688              	.LVL160:
 221:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_STABLE_REREQUESTING_2) {
 1689              		.loc 1 221 28 view .LVU464
 1690 0070 CEE7     		b	.L119
 1691              	.LVL161:
 1692              	.L129:
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
 1693              		.loc 1 225 9 is_stmt 1 view .LVU465
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
 1694              		.loc 1 225 28 is_stmt 0 view .LVU466
 1695 0072 04EB4403 		add	r3, r4, r4, lsl #1
 1696 0076 DA00     		lsls	r2, r3, #3
 1697              	.LVL162:
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
 1698              		.loc 1 225 28 view .LVU467
 1699 0078 024B     		ldr	r3, .L130
 1700 007a 1344     		add	r3, r3, r2
 1701 007c 0222     		movs	r2, #2
 1702 007e 1A75     		strb	r2, [r3, #20]
 1703              	.LVL163:
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
 1704              		.loc 1 225 28 view .LVU468
 1705 0080 C6E7     		b	.L119
 1706              	.LVL164:
 1707              	.L126:
 225:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       } else if (arp_table[i].state == ETHARP_STATE_PENDING) {
 1708              		.loc 1 225 28 view .LVU469
 1709              	.LBE8:
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1710              		.loc 1 232 1 view .LVU470
 1711 0082 10BD     		pop	{r4, pc}
 1712              	.LVL165:
 1713              	.L131:
 232:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1714              		.loc 1 232 1 view .LVU471
 1715              		.align	2
 1716              	.L130:
 1717 0084 00000000 		.word	arp_table
 1718              		.cfi_endproc
 1719              	.LFE171:
 1721              		.section	.rodata.etharp_output_to_arp_index.str1.4,"aMS",%progbits,1
 1722              		.align	2
 1723              	.LC12:
 1724 0000 6172705F 		.ascii	"arp_table[arp_idx].state >= ETHARP_STATE_STABLE\000"
 1724      7461626C 
 1724      655B6172 
 1724      705F6964 
 1724      785D2E73 
 1725              		.section	.text.etharp_output_to_arp_index,"ax",%progbits
 1726              		.align	1
ARM GAS  /tmp/cchE5ny2.s 			page 58


 1727              		.syntax unified
 1728              		.thumb
 1729              		.thumb_func
 1731              	etharp_output_to_arp_index:
 1732              	.LVL166:
 1733              	.LFB178:
 749:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[arp_idx].state >= ETHARP_STATE_STABLE",
 1734              		.loc 1 749 1 is_stmt 1 view -0
 1735              		.cfi_startproc
 1736              		@ args = 0, pretend = 0, frame = 0
 1737              		@ frame_needed = 0, uses_anonymous_args = 0
 749:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("arp_table[arp_idx].state >= ETHARP_STATE_STABLE",
 1738              		.loc 1 749 1 is_stmt 0 view .LVU473
 1739 0000 70B5     		push	{r4, r5, r6, lr}
 1740              	.LCFI25:
 1741              		.cfi_def_cfa_offset 16
 1742              		.cfi_offset 4, -16
 1743              		.cfi_offset 5, -12
 1744              		.cfi_offset 6, -8
 1745              		.cfi_offset 14, -4
 1746 0002 82B0     		sub	sp, sp, #8
 1747              	.LCFI26:
 1748              		.cfi_def_cfa_offset 24
 1749 0004 0546     		mov	r5, r0
 1750 0006 0E46     		mov	r6, r1
 1751 0008 1446     		mov	r4, r2
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1752              		.loc 1 750 3 is_stmt 1 view .LVU474
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1753              		.loc 1 750 3 view .LVU475
 1754 000a 02EB4202 		add	r2, r2, r2, lsl #1
 1755              	.LVL167:
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1756              		.loc 1 750 3 is_stmt 0 view .LVU476
 1757 000e 2E4B     		ldr	r3, .L140
 1758 0010 03EBC203 		add	r3, r3, r2, lsl #3
 1759 0014 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 1760 0016 012B     		cmp	r3, #1
 1761 0018 19D9     		bls	.L137
 1762              	.LVL168:
 1763              	.L133:
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1764              		.loc 1 750 3 is_stmt 1 discriminator 3 view .LVU477
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1765              		.loc 1 750 3 discriminator 3 view .LVU478
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (arp_table[arp_idx].ctime >= ARP_AGE_REREQUEST_USED_BROADCAST) {
 1766              		.loc 1 755 3 view .LVU479
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (arp_table[arp_idx].ctime >= ARP_AGE_REREQUEST_USED_BROADCAST) {
 1767              		.loc 1 755 25 is_stmt 0 view .LVU480
 1768 001a 04EB4402 		add	r2, r4, r4, lsl #1
 1769 001e 2A4B     		ldr	r3, .L140
 1770 0020 03EBC203 		add	r3, r3, r2, lsl #3
 1771 0024 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 755:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (arp_table[arp_idx].ctime >= ARP_AGE_REREQUEST_USED_BROADCAST) {
 1772              		.loc 1 755 6 view .LVU481
 1773 0026 022B     		cmp	r3, #2
 1774 0028 19D0     		beq	.L138
ARM GAS  /tmp/cchE5ny2.s 			page 59


 1775              	.L134:
 769:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1776              		.loc 1 769 3 is_stmt 1 view .LVU482
 769:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1777              		.loc 1 769 72 is_stmt 0 view .LVU483
 1778 002a 04EB4403 		add	r3, r4, r4, lsl #1
 1779 002e DB00     		lsls	r3, r3, #3
 1780 0030 0833     		adds	r3, r3, #8
 1781 0032 254A     		ldr	r2, .L140
 1782 0034 1344     		add	r3, r3, r2
 769:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 1783              		.loc 1 769 10 view .LVU484
 1784 0036 4FF40062 		mov	r2, #2048
 1785 003a 0092     		str	r2, [sp]
 1786 003c 0433     		adds	r3, r3, #4
 1787 003e 05F12602 		add	r2, r5, #38
 1788 0042 3146     		mov	r1, r6
 1789 0044 2846     		mov	r0, r5
 1790 0046 FFF7FEFF 		bl	ethernet_output
 1791              	.LVL169:
 770:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1792              		.loc 1 770 1 view .LVU485
 1793 004a 02B0     		add	sp, sp, #8
 1794              	.LCFI27:
 1795              		.cfi_remember_state
 1796              		.cfi_def_cfa_offset 16
 1797              		@ sp needed
 1798 004c 70BD     		pop	{r4, r5, r6, pc}
 1799              	.LVL170:
 1800              	.L137:
 1801              	.LCFI28:
 1802              		.cfi_restore_state
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1803              		.loc 1 750 3 is_stmt 1 discriminator 1 view .LVU486
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1804              		.loc 1 750 3 discriminator 1 view .LVU487
 1805 004e 1F4B     		ldr	r3, .L140+4
 1806 0050 40F2EE22 		movw	r2, #750
 1807 0054 1E49     		ldr	r1, .L140+8
 1808              	.LVL171:
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1809              		.loc 1 750 3 is_stmt 0 discriminator 1 view .LVU488
 1810 0056 1F48     		ldr	r0, .L140+12
 1811              	.LVL172:
 750:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               arp_table[arp_idx].state >= ETHARP_STATE_STABLE);
 1812              		.loc 1 750 3 discriminator 1 view .LVU489
 1813 0058 FFF7FEFF 		bl	printf
 1814              	.LVL173:
 1815 005c DDE7     		b	.L133
 1816              	.L138:
 756:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a standard request using broadcast */
 1817              		.loc 1 756 5 is_stmt 1 view .LVU490
 756:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a standard request using broadcast */
 1818              		.loc 1 756 27 is_stmt 0 view .LVU491
 1819 005e 1A4B     		ldr	r3, .L140
 1820 0060 03EBC203 		add	r3, r3, r2, lsl #3
 1821 0064 5B8A     		ldrh	r3, [r3, #18]
ARM GAS  /tmp/cchE5ny2.s 			page 60


 756:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a standard request using broadcast */
 1822              		.loc 1 756 8 view .LVU492
 1823 0066 B3F58E7F 		cmp	r3, #284
 1824 006a 1BD8     		bhi	.L139
 761:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a unicast request (for 15 seconds) to prevent unnecessary broadcast */
 1825              		.loc 1 761 12 is_stmt 1 view .LVU493
 761:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* issue a unicast request (for 15 seconds) to prevent unnecessary broadcast */
 1826              		.loc 1 761 15 is_stmt 0 view .LVU494
 1827 006c B3F5877F 		cmp	r3, #270
 1828 0070 DBD3     		bcc	.L134
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1829              		.loc 1 763 7 is_stmt 1 view .LVU495
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1830              		.loc 1 763 37 is_stmt 0 view .LVU496
 1831 0072 04EB4401 		add	r1, r4, r4, lsl #1
 1832 0076 144B     		ldr	r3, .L140
 1833 0078 03EBC101 		add	r1, r3, r1, lsl #3
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1834              		.loc 1 763 65 view .LVU497
 1835 007c 04EB4402 		add	r2, r4, r4, lsl #1
 1836 0080 D200     		lsls	r2, r2, #3
 1837 0082 0832     		adds	r2, r2, #8
 1838 0084 1A44     		add	r2, r2, r3
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1839              		.loc 1 763 11 view .LVU498
 1840 0086 0432     		adds	r2, r2, #4
 1841 0088 0431     		adds	r1, r1, #4
 1842 008a 2846     		mov	r0, r5
 1843 008c FFF7FEFF 		bl	etharp_request_dst
 1844              	.LVL174:
 763:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1845              		.loc 1 763 10 discriminator 1 view .LVU499
 1846 0090 0028     		cmp	r0, #0
 1847 0092 CAD1     		bne	.L134
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1848              		.loc 1 764 9 is_stmt 1 view .LVU500
 764:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1849              		.loc 1 764 34 is_stmt 0 view .LVU501
 1850 0094 04EB4402 		add	r2, r4, r4, lsl #1
 1851 0098 0B4B     		ldr	r3, .L140
 1852 009a 03EBC203 		add	r3, r3, r2, lsl #3
 1853 009e 0322     		movs	r2, #3
 1854 00a0 1A75     		strb	r2, [r3, #20]
 1855 00a2 C2E7     		b	.L134
 1856              	.L139:
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1857              		.loc 1 758 7 is_stmt 1 view .LVU502
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1858              		.loc 1 758 33 is_stmt 0 view .LVU503
 1859 00a4 0849     		ldr	r1, .L140
 1860 00a6 01EBC201 		add	r1, r1, r2, lsl #3
 758:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1861              		.loc 1 758 11 view .LVU504
 1862 00aa 0431     		adds	r1, r1, #4
 1863 00ac 2846     		mov	r0, r5
 1864 00ae FFF7FEFF 		bl	etharp_request
 1865              	.LVL175:
ARM GAS  /tmp/cchE5ny2.s 			page 61


 758:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         arp_table[arp_idx].state = ETHARP_STATE_STABLE_REREQUESTING_1;
 1866              		.loc 1 758 10 discriminator 1 view .LVU505
 1867 00b2 0028     		cmp	r0, #0
 1868 00b4 B9D1     		bne	.L134
 759:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1869              		.loc 1 759 9 is_stmt 1 view .LVU506
 759:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 1870              		.loc 1 759 34 is_stmt 0 view .LVU507
 1871 00b6 04EB4402 		add	r2, r4, r4, lsl #1
 1872 00ba 034B     		ldr	r3, .L140
 1873 00bc 03EBC203 		add	r3, r3, r2, lsl #3
 1874 00c0 0322     		movs	r2, #3
 1875 00c2 1A75     		strb	r2, [r3, #20]
 1876 00c4 B1E7     		b	.L134
 1877              	.L141:
 1878 00c6 00BF     		.align	2
 1879              	.L140:
 1880 00c8 00000000 		.word	arp_table
 1881 00cc 00000000 		.word	.LC0
 1882 00d0 00000000 		.word	.LC12
 1883 00d4 4C000000 		.word	.LC2
 1884              		.cfi_endproc
 1885              	.LFE178:
 1887              		.section	.rodata.etharp_query.str1.4,"aMS",%progbits,1
 1888              		.align	2
 1889              	.LC13:
 1890 0000 74797065 		.ascii	"type overflow\000"
 1890      206F7665 
 1890      72666C6F 
 1890      7700
 1891 000e 0000     		.align	2
 1892              	.LC14:
 1893 0010 6172705F 		.ascii	"arp_table[i].state == PENDING or STABLE\000"
 1893      7461626C 
 1893      655B695D 
 1893      2E737461 
 1893      7465203D 
 1894              		.align	2
 1895              	.LC15:
 1896 0038 6E6F2070 		.ascii	"no packet queues allowed!\000"
 1896      61636B65 
 1896      74207175 
 1896      65756573 
 1896      20616C6C 
 1897              		.section	.text.etharp_query,"ax",%progbits
 1898              		.align	1
 1899              		.global	etharp_query
 1900              		.syntax unified
 1901              		.thumb
 1902              		.thumb_func
 1904              	etharp_query:
 1905              	.LVL176:
 1906              	.LFB180:
 934:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct eth_addr *srcaddr = (struct eth_addr *)netif->hwaddr;
 1907              		.loc 1 934 1 is_stmt 1 view -0
 1908              		.cfi_startproc
 1909              		@ args = 0, pretend = 0, frame = 0
ARM GAS  /tmp/cchE5ny2.s 			page 62


 1910              		@ frame_needed = 0, uses_anonymous_args = 0
 934:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct eth_addr *srcaddr = (struct eth_addr *)netif->hwaddr;
 1911              		.loc 1 934 1 is_stmt 0 view .LVU509
 1912 0000 2DE9F043 		push	{r4, r5, r6, r7, r8, r9, lr}
 1913              	.LCFI29:
 1914              		.cfi_def_cfa_offset 28
 1915              		.cfi_offset 4, -28
 1916              		.cfi_offset 5, -24
 1917              		.cfi_offset 6, -20
 1918              		.cfi_offset 7, -16
 1919              		.cfi_offset 8, -12
 1920              		.cfi_offset 9, -8
 1921              		.cfi_offset 14, -4
 1922 0004 83B0     		sub	sp, sp, #12
 1923              	.LCFI30:
 1924              		.cfi_def_cfa_offset 40
 1925 0006 0546     		mov	r5, r0
 1926 0008 8846     		mov	r8, r1
 1927 000a 1646     		mov	r6, r2
 935:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   err_t result = ERR_MEM;
 1928              		.loc 1 935 3 is_stmt 1 view .LVU510
 935:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   err_t result = ERR_MEM;
 1929              		.loc 1 935 20 is_stmt 0 view .LVU511
 1930 000c 00F12607 		add	r7, r0, #38
 1931              	.LVL177:
 936:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int is_new_entry = 0;
 1932              		.loc 1 936 3 is_stmt 1 view .LVU512
 937:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i_err;
 1933              		.loc 1 937 3 view .LVU513
 938:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   netif_addr_idx_t i;
 1934              		.loc 1 938 3 view .LVU514
 939:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1935              		.loc 1 939 3 view .LVU515
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 1936              		.loc 1 942 3 view .LVU516
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 1937              		.loc 1 942 7 is_stmt 0 view .LVU517
 1938 0010 0146     		mov	r1, r0
 1939              	.LVL178:
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 1940              		.loc 1 942 7 view .LVU518
 1941 0012 D8F80000 		ldr	r0, [r8]
 1942              	.LVL179:
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 1943              		.loc 1 942 7 view .LVU519
 1944 0016 FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 1945              	.LVL180:
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 1946              		.loc 1 942 6 discriminator 1 view .LVU520
 1947 001a 0028     		cmp	r0, #0
 1948 001c 40F0A180 		bne	.L157
 943:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_isany(ipaddr)) {
 1949              		.loc 1 943 7 view .LVU521
 1950 0020 D8F80030 		ldr	r3, [r8]
 1951 0024 03F0F002 		and	r2, r3, #240
 942:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ip4_addr_ismulticast(ipaddr) ||
 1952              		.loc 1 942 43 discriminator 1 view .LVU522
ARM GAS  /tmp/cchE5ny2.s 			page 63


 1953 0028 E02A     		cmp	r2, #224
 1954 002a 00F09D80 		beq	.L158
 944:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: will not add non-unicast IP address 
 1955              		.loc 1 944 7 view .LVU523
 1956 002e 002B     		cmp	r3, #0
 1957 0030 00F09D80 		beq	.L159
 950:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1958              		.loc 1 950 3 is_stmt 1 view .LVU524
 950:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1959              		.loc 1 950 11 is_stmt 0 view .LVU525
 1960 0034 2A46     		mov	r2, r5
 1961 0036 0121     		movs	r1, #1
 1962 0038 4046     		mov	r0, r8
 1963 003a FFF7FEFF 		bl	etharp_find_entry
 1964              	.LVL181:
 953:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: could not create ARP entry\n"));
 1965              		.loc 1 953 3 is_stmt 1 view .LVU526
 953:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: could not create ARP entry\n"));
 1966              		.loc 1 953 6 is_stmt 0 view .LVU527
 1967 003e 041E     		subs	r4, r0, #0
 1968 0040 13DB     		blt	.L166
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 1969              		.loc 1 961 3 is_stmt 1 view .LVU528
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 1970              		.loc 1 961 3 view .LVU529
 1971 0042 A3B2     		uxth	r3, r4
 1972 0044 7E2B     		cmp	r3, #126
 1973 0046 12D8     		bhi	.L167
 1974              	.LVL182:
 1975              	.L145:
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 1976              		.loc 1 961 3 discriminator 3 view .LVU530
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 1977              		.loc 1 961 3 discriminator 3 view .LVU531
 962:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1978              		.loc 1 962 3 view .LVU532
 962:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 1979              		.loc 1 962 5 is_stmt 0 view .LVU533
 1980 0048 E4B2     		uxtb	r4, r4
 1981              	.LVL183:
 965:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     is_new_entry = 1;
 1982              		.loc 1 965 3 is_stmt 1 view .LVU534
 965:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     is_new_entry = 1;
 1983              		.loc 1 965 19 is_stmt 0 view .LVU535
 1984 004a 04EB4402 		add	r2, r4, r4, lsl #1
 1985 004e 4B4B     		ldr	r3, .L172
 1986 0050 03EBC203 		add	r3, r3, r2, lsl #3
 1987 0054 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 965:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     is_new_entry = 1;
 1988              		.loc 1 965 6 view .LVU536
 1989 0056 93B9     		cbnz	r3, .L160
 966:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     arp_table[i].state = ETHARP_STATE_PENDING;
 1990              		.loc 1 966 5 is_stmt 1 view .LVU537
 1991              	.LVL184:
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* record network interface for re-sending arp request in etharp_tmr */
 1992              		.loc 1 967 5 view .LVU538
 967:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* record network interface for re-sending arp request in etharp_tmr */
ARM GAS  /tmp/cchE5ny2.s 			page 64


 1993              		.loc 1 967 24 is_stmt 0 view .LVU539
 1994 0058 484B     		ldr	r3, .L172
 1995 005a 03EBC203 		add	r3, r3, r2, lsl #3
 1996 005e 4FF00109 		mov	r9, #1
 1997 0062 83F81490 		strb	r9, [r3, #20]
 969:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1998              		.loc 1 969 5 is_stmt 1 view .LVU540
 969:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 1999              		.loc 1 969 24 is_stmt 0 view .LVU541
 2000 0066 9D60     		str	r5, [r3, #8]
 2001 0068 0BE0     		b	.L146
 2002              	.LVL185:
 2003              	.L166:
 954:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (q) {
 2004              		.loc 1 954 95 is_stmt 1 view .LVU542
 955:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: packet dropped\n"));
 2005              		.loc 1 955 5 view .LVU543
 956:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       ETHARP_STATS_INC(etharp.memerr);
 2006              		.loc 1 956 85 view .LVU544
 957:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2007              		.loc 1 957 38 view .LVU545
 959:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2008              		.loc 1 959 5 view .LVU546
 959:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2009              		.loc 1 959 12 is_stmt 0 view .LVU547
 2010 006a 60B2     		sxtb	r0, r4
 2011              	.LVL186:
 959:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2012              		.loc 1 959 12 view .LVU548
 2013 006c 42E0     		b	.L143
 2014              	.LVL187:
 2015              	.L167:
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 2016              		.loc 1 961 3 is_stmt 1 discriminator 1 view .LVU549
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 2017              		.loc 1 961 3 discriminator 1 view .LVU550
 2018 006e 444B     		ldr	r3, .L172+4
 2019 0070 40F2C132 		movw	r2, #961
 2020 0074 4349     		ldr	r1, .L172+8
 2021 0076 4448     		ldr	r0, .L172+12
 2022              	.LVL188:
 961:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   i = (netif_addr_idx_t)i_err;
 2023              		.loc 1 961 3 is_stmt 0 discriminator 1 view .LVU551
 2024 0078 FFF7FEFF 		bl	printf
 2025              	.LVL189:
 2026 007c E4E7     		b	.L145
 2027              	.LVL190:
 2028              	.L160:
 937:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   s16_t i_err;
 2029              		.loc 1 937 7 view .LVU552
 2030 007e 4FF00009 		mov	r9, #0
 2031              	.LVL191:
 2032              	.L146:
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 2033              		.loc 1 973 3 is_stmt 1 view .LVU553
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 2034              		.loc 1 973 3 view .LVU554
ARM GAS  /tmp/cchE5ny2.s 			page 65


 2035 0082 04EB4402 		add	r2, r4, r4, lsl #1
 2036 0086 3D4B     		ldr	r3, .L172
 2037 0088 03EBC203 		add	r3, r3, r2, lsl #3
 2038 008c 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 2039 008e 9BB1     		cbz	r3, .L168
 2040              	.L147:
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 2041              		.loc 1 973 3 discriminator 3 view .LVU555
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 2042              		.loc 1 973 3 discriminator 3 view .LVU556
 978:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* try to resolve it; send out ARP request */
 2043              		.loc 1 978 3 view .LVU557
 978:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* try to resolve it; send out ARP request */
 2044              		.loc 1 978 20 is_stmt 0 view .LVU558
 2045 0090 002E     		cmp	r6, #0
 2046 0092 14BF     		ite	ne
 2047 0094 4B46     		movne	r3, r9
 2048 0096 49F00103 		orreq	r3, r9, #1
 978:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* try to resolve it; send out ARP request */
 2049              		.loc 1 978 6 view .LVU559
 2050 009a ABB9     		cbnz	r3, .L169
 936:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   int is_new_entry = 0;
 2051              		.loc 1 936 9 view .LVU560
 2052 009c 4FF0FF30 		mov	r0, #-1
 2053              	.LVL192:
 2054              	.L148:
 993:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* stable entry? */
 2055              		.loc 1 993 3 is_stmt 1 discriminator 3 view .LVU561
 993:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   /* stable entry? */
 2056              		.loc 1 993 3 discriminator 3 view .LVU562
 995:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* we have a valid IP->Ethernet address mapping */
 2057              		.loc 1 995 3 view .LVU563
 995:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* we have a valid IP->Ethernet address mapping */
 2058              		.loc 1 995 19 is_stmt 0 view .LVU564
 2059 00a0 04EB4402 		add	r2, r4, r4, lsl #1
 2060 00a4 354B     		ldr	r3, .L172
 2061 00a6 03EBC203 		add	r3, r3, r2, lsl #3
 2062 00aa 1B7D     		ldrb	r3, [r3, #20]	@ zero_extendqisi2
 995:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* we have a valid IP->Ethernet address mapping */
 2063              		.loc 1 995 6 view .LVU565
 2064 00ac 012B     		cmp	r3, #1
 2065 00ae 12D8     		bhi	.L170
1001:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* entry is still pending, queue the given packet 'q' */
 2066              		.loc 1 1001 10 is_stmt 1 view .LVU566
1001:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* entry is still pending, queue the given packet 'q' */
 2067              		.loc 1 1001 13 is_stmt 0 view .LVU567
 2068 00b0 012B     		cmp	r3, #1
 2069 00b2 1FD1     		bne	.L143
 2070              	.LBB9:
1007:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     while (p) {
 2071              		.loc 1 1007 7 view .LVU568
 2072 00b4 3546     		mov	r5, r6
 2073              	.LVL193:
1007:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     while (p) {
 2074              		.loc 1 1007 7 view .LVU569
 2075 00b6 25E0     		b	.L150
 2076              	.LVL194:
ARM GAS  /tmp/cchE5ny2.s 			page 66


 2077              	.L168:
1007:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     while (p) {
 2078              		.loc 1 1007 7 view .LVU570
 2079              	.LBE9:
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 2080              		.loc 1 973 3 is_stmt 1 discriminator 1 view .LVU571
 973:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****               ((arp_table[i].state == ETHARP_STATE_PENDING) ||
 2081              		.loc 1 973 3 discriminator 1 view .LVU572
 2082 00b8 314B     		ldr	r3, .L172+4
 2083 00ba 40F2CD32 		movw	r2, #973
 2084 00be 3349     		ldr	r1, .L172+16
 2085 00c0 3148     		ldr	r0, .L172+12
 2086 00c2 FFF7FEFF 		bl	printf
 2087              	.LVL195:
 2088 00c6 E3E7     		b	.L147
 2089              	.L169:
 980:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (result != ERR_OK) {
 2090              		.loc 1 980 5 view .LVU573
 980:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (result != ERR_OK) {
 2091              		.loc 1 980 14 is_stmt 0 view .LVU574
 2092 00c8 4146     		mov	r1, r8
 2093 00ca 2846     		mov	r0, r5
 2094 00cc FFF7FEFF 		bl	etharp_request
 2095              	.LVL196:
 981:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* ARP request couldn't be sent */
 2096              		.loc 1 981 5 is_stmt 1 view .LVU575
 986:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     if (q == NULL) {
 2097              		.loc 1 986 5 view .LVU576
 987:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       return result;
 2098              		.loc 1 987 5 view .LVU577
 987:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       return result;
 2099              		.loc 1 987 8 is_stmt 0 view .LVU578
 2100 00d0 002E     		cmp	r6, #0
 2101 00d2 E5D1     		bne	.L148
 2102 00d4 0EE0     		b	.L143
 2103              	.LVL197:
 2104              	.L170:
 997:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* send the packet */
 2105              		.loc 1 997 5 is_stmt 1 view .LVU579
 2106 00d6 2E4B     		ldr	r3, .L172+20
 2107 00d8 1C70     		strb	r4, [r3]
 999:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* pending entry? (either just created or already pending */
 2108              		.loc 1 999 5 view .LVU580
 999:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* pending entry? (either just created or already pending */
 2109              		.loc 1 999 49 is_stmt 0 view .LVU581
 2110 00da D300     		lsls	r3, r2, #3
 2111 00dc 0833     		adds	r3, r3, #8
 2112 00de 274A     		ldr	r2, .L172
 2113 00e0 1344     		add	r3, r3, r2
 999:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* pending entry? (either just created or already pending */
 2114              		.loc 1 999 14 view .LVU582
 2115 00e2 4FF40062 		mov	r2, #2048
 2116 00e6 0092     		str	r2, [sp]
 2117 00e8 0433     		adds	r3, r3, #4
 2118 00ea 3A46     		mov	r2, r7
 2119 00ec 3146     		mov	r1, r6
 2120 00ee 2846     		mov	r0, r5
ARM GAS  /tmp/cchE5ny2.s 			page 67


 2121 00f0 FFF7FEFF 		bl	ethernet_output
 2122              	.LVL198:
 2123              	.L143:
1083:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2124              		.loc 1 1083 1 view .LVU583
 2125 00f4 03B0     		add	sp, sp, #12
 2126              	.LCFI31:
 2127              		.cfi_remember_state
 2128              		.cfi_def_cfa_offset 28
 2129              		@ sp needed
 2130 00f6 BDE8F083 		pop	{r4, r5, r6, r7, r8, r9, pc}
 2131              	.LVL199:
 2132              	.L151:
 2133              	.LCFI32:
 2134              		.cfi_restore_state
 2135              	.LBB10:
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2136              		.loc 1 1009 7 is_stmt 1 discriminator 5 view .LVU584
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2137              		.loc 1 1009 7 discriminator 5 view .LVU585
1010:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         copy_needed = 1;
 2138              		.loc 1 1010 7 view .LVU586
1010:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         copy_needed = 1;
 2139              		.loc 1 1010 11 is_stmt 0 view .LVU587
 2140 00fa 2B7B     		ldrb	r3, [r5, #12]	@ zero_extendqisi2
1010:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         copy_needed = 1;
 2141              		.loc 1 1010 10 view .LVU588
 2142 00fc 13F0400F 		tst	r3, #64
 2143 0100 29D1     		bne	.L163
1014:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2144              		.loc 1 1014 7 is_stmt 1 view .LVU589
1014:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2145              		.loc 1 1014 9 is_stmt 0 view .LVU590
 2146 0102 2D68     		ldr	r5, [r5]
 2147              	.LVL200:
 2148              	.L150:
1008:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_ASSERT("no packet queues allowed!", (p->len != p->tot_len) || (p->next == 0));
 2149              		.loc 1 1008 12 is_stmt 1 view .LVU591
 2150 0104 75B1     		cbz	r5, .L171
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2151              		.loc 1 1009 7 view .LVU592
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2152              		.loc 1 1009 7 view .LVU593
 2153 0106 6A89     		ldrh	r2, [r5, #10]
 2154 0108 2B89     		ldrh	r3, [r5, #8]
 2155 010a 9A42     		cmp	r2, r3
 2156 010c F5D1     		bne	.L151
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2157              		.loc 1 1009 7 is_stmt 0 discriminator 1 view .LVU594
 2158 010e 2B68     		ldr	r3, [r5]
 2159 0110 002B     		cmp	r3, #0
 2160 0112 F2D0     		beq	.L151
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2161              		.loc 1 1009 7 is_stmt 1 discriminator 3 view .LVU595
1009:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if (PBUF_NEEDS_COPY(p)) {
 2162              		.loc 1 1009 7 discriminator 3 view .LVU596
 2163 0114 1A4B     		ldr	r3, .L172+4
ARM GAS  /tmp/cchE5ny2.s 			page 68


 2164 0116 40F2F132 		movw	r2, #1009
 2165 011a 1E49     		ldr	r1, .L172+24
 2166 011c 1A48     		ldr	r0, .L172+12
 2167 011e FFF7FEFF 		bl	printf
 2168              	.LVL201:
 2169 0122 EAE7     		b	.L151
 2170              	.L171:
1004:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* IF q includes a pbuf that must be copied, copy the whole chain into a
 2171              		.loc 1 1004 9 is_stmt 0 view .LVU597
 2172 0124 0023     		movs	r3, #0
 2173              	.L152:
 2174              	.LVL202:
1016:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* copy the whole packet into new pbufs */
 2175              		.loc 1 1016 5 is_stmt 1 view .LVU598
1016:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* copy the whole packet into new pbufs */
 2176              		.loc 1 1016 8 is_stmt 0 view .LVU599
 2177 0126 C3B1     		cbz	r3, .L154
1018:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else {
 2178              		.loc 1 1018 7 is_stmt 1 view .LVU600
1018:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else {
 2179              		.loc 1 1018 11 is_stmt 0 view .LVU601
 2180 0128 3246     		mov	r2, r6
 2181 012a 4FF42071 		mov	r1, #640
 2182 012e 0E20     		movs	r0, #14
 2183 0130 FFF7FEFF 		bl	pbuf_clone
 2184              	.LVL203:
1018:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     } else {
 2185              		.loc 1 1018 11 view .LVU602
 2186 0134 0646     		mov	r6, r0
 2187              	.LVL204:
 2188              	.L155:
1025:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* queue packet ... */
 2189              		.loc 1 1025 5 is_stmt 1 view .LVU603
1025:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       /* queue packet ... */
 2190              		.loc 1 1025 8 is_stmt 0 view .LVU604
 2191 0136 EEB1     		cbz	r6, .L164
1068:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: dropped previously queued packet
 2192              		.loc 1 1068 7 is_stmt 1 view .LVU605
1068:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: dropped previously queued packet
 2193              		.loc 1 1068 23 is_stmt 0 view .LVU606
 2194 0138 04EB4403 		add	r3, r4, r4, lsl #1
 2195 013c 0F4A     		ldr	r2, .L172
 2196 013e 52F83300 		ldr	r0, [r2, r3, lsl #3]
1068:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: dropped previously queued packet
 2197              		.loc 1 1068 10 view .LVU607
 2198 0142 08B1     		cbz	r0, .L156
1069:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         pbuf_free(arp_table[i].q);
 2199              		.loc 1 1069 152 is_stmt 1 view .LVU608
1070:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 2200              		.loc 1 1070 9 view .LVU609
 2201 0144 FFF7FEFF 		bl	pbuf_free
 2202              	.LVL205:
 2203              	.L156:
1072:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       result = ERR_OK;
 2204              		.loc 1 1072 7 view .LVU610
1072:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       result = ERR_OK;
 2205              		.loc 1 1072 22 is_stmt 0 view .LVU611
ARM GAS  /tmp/cchE5ny2.s 			page 69


 2206 0148 04EB4404 		add	r4, r4, r4, lsl #1
 2207              	.LVL206:
1072:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       result = ERR_OK;
 2208              		.loc 1 1072 22 view .LVU612
 2209 014c 0B4B     		ldr	r3, .L172
 2210 014e 43F83460 		str	r6, [r3, r4, lsl #3]
1073:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: queued packet %p on ARP entry %"U1
 2211              		.loc 1 1073 7 is_stmt 1 view .LVU613
 2212              	.LVL207:
1074:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif /* ARP_QUEUEING */
 2213              		.loc 1 1074 130 view .LVU614
1073:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       LWIP_DEBUGF(ETHARP_DEBUG | LWIP_DBG_TRACE, ("etharp_query: queued packet %p on ARP entry %"U1
 2214              		.loc 1 1073 14 is_stmt 0 view .LVU615
 2215 0152 0020     		movs	r0, #0
 2216 0154 CEE7     		b	.L143
 2217              	.LVL208:
 2218              	.L163:
1011:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         break;
 2219              		.loc 1 1011 21 view .LVU616
 2220 0156 0123     		movs	r3, #1
 2221 0158 E5E7     		b	.L152
 2222              	.LVL209:
 2223              	.L154:
1021:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       pbuf_ref(p);
 2224              		.loc 1 1021 7 is_stmt 1 view .LVU617
1022:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2225              		.loc 1 1022 7 view .LVU618
 2226 015a 3046     		mov	r0, r6
 2227 015c FFF7FEFF 		bl	pbuf_ref
 2228              	.LVL210:
1022:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2229              		.loc 1 1022 7 is_stmt 0 view .LVU619
 2230 0160 E9E7     		b	.L155
 2231              	.LVL211:
 2232              	.L157:
1022:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2233              		.loc 1 1022 7 view .LVU620
 2234              	.LBE10:
 946:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2235              		.loc 1 946 12 view .LVU621
 2236 0162 6FF00F00 		mvn	r0, #15
 2237 0166 C5E7     		b	.L143
 2238              	.L158:
 2239 0168 6FF00F00 		mvn	r0, #15
 2240 016c C2E7     		b	.L143
 2241              	.L159:
 2242 016e 6FF00F00 		mvn	r0, #15
 2243 0172 BFE7     		b	.L143
 2244              	.LVL212:
 2245              	.L164:
 2246              	.LBB11:
1079:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     }
 2247              		.loc 1 1079 14 view .LVU622
 2248 0174 4FF0FF30 		mov	r0, #-1
 2249 0178 BCE7     		b	.L143
 2250              	.L173:
 2251 017a 00BF     		.align	2
ARM GAS  /tmp/cchE5ny2.s 			page 70


 2252              	.L172:
 2253 017c 00000000 		.word	arp_table
 2254 0180 00000000 		.word	.LC0
 2255 0184 00000000 		.word	.LC13
 2256 0188 4C000000 		.word	.LC2
 2257 018c 10000000 		.word	.LC14
 2258 0190 00000000 		.word	etharp_cached_entry
 2259 0194 38000000 		.word	.LC15
 2260              	.LBE11:
 2261              		.cfi_endproc
 2262              	.LFE180:
 2264              		.section	.rodata.etharp_output.str1.4,"aMS",%progbits,1
 2265              		.align	2
 2266              	.LC16:
 2267 0000 7120213D 		.ascii	"q != NULL\000"
 2267      204E554C 
 2267      4C00
 2268              		.section	.text.etharp_output,"ax",%progbits
 2269              		.align	1
 2270              		.global	etharp_output
 2271              		.syntax unified
 2272              		.thumb
 2273              		.thumb_func
 2275              	etharp_output:
 2276              	.LVL213:
 2277              	.LFB179:
 792:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   const struct eth_addr *dest;
 2278              		.loc 1 792 1 is_stmt 1 view -0
 2279              		.cfi_startproc
 2280              		@ args = 0, pretend = 0, frame = 8
 2281              		@ frame_needed = 0, uses_anonymous_args = 0
 792:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   const struct eth_addr *dest;
 2282              		.loc 1 792 1 is_stmt 0 view .LVU624
 2283 0000 70B5     		push	{r4, r5, r6, lr}
 2284              	.LCFI33:
 2285              		.cfi_def_cfa_offset 16
 2286              		.cfi_offset 4, -16
 2287              		.cfi_offset 5, -12
 2288              		.cfi_offset 6, -8
 2289              		.cfi_offset 14, -4
 2290 0002 84B0     		sub	sp, sp, #16
 2291              	.LCFI34:
 2292              		.cfi_def_cfa_offset 32
 2293 0004 0E46     		mov	r6, r1
 2294 0006 1446     		mov	r4, r2
 793:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   struct eth_addr mcastaddr;
 2295              		.loc 1 793 3 is_stmt 1 view .LVU625
 794:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   const ip4_addr_t *dst_addr = ipaddr;
 2296              		.loc 1 794 3 view .LVU626
 795:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2297              		.loc 1 795 3 view .LVU627
 2298              	.LVL214:
 797:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("netif != NULL", netif != NULL);
 2299              		.loc 1 797 28 view .LVU628
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2300              		.loc 1 798 3 view .LVU629
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
ARM GAS  /tmp/cchE5ny2.s 			page 71


 2301              		.loc 1 798 3 view .LVU630
 2302 0008 0546     		mov	r5, r0
 2303 000a 0028     		cmp	r0, #0
 2304 000c 3DD0     		beq	.L189
 2305              	.LVL215:
 2306              	.L175:
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2307              		.loc 1 798 3 discriminator 3 view .LVU631
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2308              		.loc 1 798 3 discriminator 3 view .LVU632
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 2309              		.loc 1 799 3 view .LVU633
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 2310              		.loc 1 799 3 view .LVU634
 2311 000e 002E     		cmp	r6, #0
 2312 0010 43D0     		beq	.L190
 2313              	.L176:
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 2314              		.loc 1 799 3 discriminator 3 view .LVU635
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 2315              		.loc 1 799 3 discriminator 3 view .LVU636
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2316              		.loc 1 800 3 view .LVU637
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2317              		.loc 1 800 3 view .LVU638
 2318 0012 002C     		cmp	r4, #0
 2319 0014 49D0     		beq	.L191
 2320              	.L177:
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2321              		.loc 1 800 3 discriminator 3 view .LVU639
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2322              		.loc 1 800 3 discriminator 3 view .LVU640
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* broadcast on Ethernet also */
 2323              		.loc 1 806 3 view .LVU641
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* broadcast on Ethernet also */
 2324              		.loc 1 806 7 is_stmt 0 view .LVU642
 2325 0016 2946     		mov	r1, r5
 2326 0018 2068     		ldr	r0, [r4]
 2327 001a FFF7FEFF 		bl	ip4_addr_isbroadcast_u32
 2328              	.LVL216:
 806:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* broadcast on Ethernet also */
 2329              		.loc 1 806 6 discriminator 1 view .LVU643
 2330 001e 0346     		mov	r3, r0
 2331 0020 0028     		cmp	r0, #0
 2332 0022 40F08180 		bne	.L186
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* Hash IP multicast address to MAC address.*/
 2333              		.loc 1 810 10 is_stmt 1 view .LVU644
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* Hash IP multicast address to MAC address.*/
 2334              		.loc 1 810 14 is_stmt 0 view .LVU645
 2335 0026 2268     		ldr	r2, [r4]
 2336 0028 02F0F001 		and	r1, r2, #240
 810:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* Hash IP multicast address to MAC address.*/
 2337              		.loc 1 810 13 view .LVU646
 2338 002c E029     		cmp	r1, #224
 2339 002e 44D0     		beq	.L192
 2340              	.LBB12:
 822:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* outside local network? if so, this can neither be a global broadcast nor
ARM GAS  /tmp/cchE5ny2.s 			page 72


 2341              		.loc 1 822 5 is_stmt 1 view .LVU647
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         !ip4_addr_islinklocal(ipaddr)) {
 2342              		.loc 1 825 5 view .LVU648
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         !ip4_addr_islinklocal(ipaddr)) {
 2343              		.loc 1 825 10 is_stmt 0 view .LVU649
 2344 0030 6968     		ldr	r1, [r5, #4]
 2345 0032 A868     		ldr	r0, [r5, #8]
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         !ip4_addr_islinklocal(ipaddr)) {
 2346              		.loc 1 825 9 view .LVU650
 2347 0034 5140     		eors	r1, r1, r2
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         !ip4_addr_islinklocal(ipaddr)) {
 2348              		.loc 1 825 8 view .LVU651
 2349 0036 0142     		tst	r1, r0
 2350 0038 09D0     		beq	.L180
 826:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if LWIP_AUTOIP
 2351              		.loc 1 826 10 view .LVU652
 2352 003a 92B2     		uxth	r2, r2
 825:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         !ip4_addr_islinklocal(ipaddr)) {
 2353              		.loc 1 825 83 discriminator 1 view .LVU653
 2354 003c 4FF6A961 		movw	r1, #65193
 2355 0040 8A42     		cmp	r2, r1
 2356 0042 04D0     		beq	.L180
 844:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* send to hardware address of default gateway IP address */
 2357              		.loc 1 844 11 is_stmt 1 view .LVU654
 844:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* send to hardware address of default gateway IP address */
 2358              		.loc 1 844 16 is_stmt 0 view .LVU655
 2359 0044 EA68     		ldr	r2, [r5, #12]
 844:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* send to hardware address of default gateway IP address */
 2360              		.loc 1 844 14 view .LVU656
 2361 0046 002A     		cmp	r2, #0
 2362 0048 7AD0     		beq	.L187
 846:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* no default gateway available */
 2363              		.loc 1 846 13 is_stmt 1 view .LVU657
 846:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****             /* no default gateway available */
 2364              		.loc 1 846 22 is_stmt 0 view .LVU658
 2365 004a 05F10C04 		add	r4, r5, #12
 2366              	.LVL217:
 2367              	.L180:
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2368              		.loc 1 861 9 is_stmt 1 view .LVU659
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2369              		.loc 1 861 44 is_stmt 0 view .LVU660
 2370 004e 3E4A     		ldr	r2, .L194
 2371 0050 1278     		ldrb	r2, [r2]	@ zero_extendqisi2
 2372 0052 02EB4200 		add	r0, r2, r2, lsl #1
 2373 0056 3D49     		ldr	r1, .L194+4
 2374 0058 01EBC001 		add	r1, r1, r0, lsl #3
 2375 005c 097D     		ldrb	r1, [r1, #20]	@ zero_extendqisi2
 861:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2376              		.loc 1 861 12 view .LVU661
 2377 005e 0129     		cmp	r1, #1
 2378 0060 43D9     		bls	.L184
 863:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 2379              		.loc 1 863 44 view .LVU662
 2380 0062 3A49     		ldr	r1, .L194+4
 2381 0064 01EBC001 		add	r1, r1, r0, lsl #3
 2382 0068 8968     		ldr	r1, [r1, #8]
ARM GAS  /tmp/cchE5ny2.s 			page 73


 861:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2383              		.loc 1 861 75 discriminator 1 view .LVU663
 2384 006a A942     		cmp	r1, r5
 2385 006c 3DD1     		bne	.L184
 865:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           /* the per-pcb-cached entry is stable and the right one! */
 2386              		.loc 1 865 14 view .LVU664
 2387 006e 2068     		ldr	r0, [r4]
 2388 0070 02EB420C 		add	ip, r2, r2, lsl #1
 2389 0074 3549     		ldr	r1, .L194+4
 2390 0076 01EBCC01 		add	r1, r1, ip, lsl #3
 2391 007a 4968     		ldr	r1, [r1, #4]
 863:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 2392              		.loc 1 863 61 view .LVU665
 2393 007c 8842     		cmp	r0, r1
 2394 007e 34D1     		bne	.L184
 867:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           return etharp_output_to_arp_index(netif, q, etharp_cached_entry);
 2395              		.loc 1 867 44 is_stmt 1 view .LVU666
 868:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 2396              		.loc 1 868 11 view .LVU667
 868:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 2397              		.loc 1 868 18 is_stmt 0 view .LVU668
 2398 0080 3146     		mov	r1, r6
 2399 0082 2846     		mov	r0, r5
 2400 0084 FFF7FEFF 		bl	etharp_output_to_arp_index
 2401              	.LVL218:
 2402 0088 58E0     		b	.L181
 2403              	.LVL219:
 2404              	.L189:
 868:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         }
 2405              		.loc 1 868 18 view .LVU669
 2406              	.LBE12:
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2407              		.loc 1 798 3 is_stmt 1 discriminator 1 view .LVU670
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2408              		.loc 1 798 3 discriminator 1 view .LVU671
 2409 008a 314B     		ldr	r3, .L194+8
 2410 008c 40F21E32 		movw	r2, #798
 2411              	.LVL220:
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2412              		.loc 1 798 3 is_stmt 0 discriminator 1 view .LVU672
 2413 0090 3049     		ldr	r1, .L194+12
 2414              	.LVL221:
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2415              		.loc 1 798 3 discriminator 1 view .LVU673
 2416 0092 3148     		ldr	r0, .L194+16
 2417              	.LVL222:
 798:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("q != NULL", q != NULL);
 2418              		.loc 1 798 3 discriminator 1 view .LVU674
 2419 0094 FFF7FEFF 		bl	printf
 2420              	.LVL223:
 2421 0098 B9E7     		b	.L175
 2422              	.L190:
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 2423              		.loc 1 799 3 is_stmt 1 discriminator 1 view .LVU675
 799:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   LWIP_ASSERT("ipaddr != NULL", ipaddr != NULL);
 2424              		.loc 1 799 3 discriminator 1 view .LVU676
 2425 009a 2D4B     		ldr	r3, .L194+8
ARM GAS  /tmp/cchE5ny2.s 			page 74


 2426 009c 40F21F32 		movw	r2, #799
 2427 00a0 2E49     		ldr	r1, .L194+20
 2428 00a2 2D48     		ldr	r0, .L194+16
 2429 00a4 FFF7FEFF 		bl	printf
 2430              	.LVL224:
 2431 00a8 B3E7     		b	.L176
 2432              	.L191:
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2433              		.loc 1 800 3 discriminator 1 view .LVU677
 800:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2434              		.loc 1 800 3 discriminator 1 view .LVU678
 2435 00aa 294B     		ldr	r3, .L194+8
 2436 00ac 4FF44872 		mov	r2, #800
 2437 00b0 2B49     		ldr	r1, .L194+24
 2438 00b2 2948     		ldr	r0, .L194+16
 2439 00b4 FFF7FEFF 		bl	printf
 2440              	.LVL225:
 2441 00b8 ADE7     		b	.L177
 2442              	.L192:
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[1] = LL_IP4_MULTICAST_ADDR_1;
 2443              		.loc 1 812 5 view .LVU679
 812:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[1] = LL_IP4_MULTICAST_ADDR_1;
 2444              		.loc 1 812 23 is_stmt 0 view .LVU680
 2445 00ba 0123     		movs	r3, #1
 2446 00bc 8DF80830 		strb	r3, [sp, #8]
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[2] = LL_IP4_MULTICAST_ADDR_2;
 2447              		.loc 1 813 5 is_stmt 1 view .LVU681
 813:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[2] = LL_IP4_MULTICAST_ADDR_2;
 2448              		.loc 1 813 23 is_stmt 0 view .LVU682
 2449 00c0 0023     		movs	r3, #0
 2450 00c2 8DF80930 		strb	r3, [sp, #9]
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[3] = ip4_addr2(ipaddr) & 0x7f;
 2451              		.loc 1 814 5 is_stmt 1 view .LVU683
 814:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[3] = ip4_addr2(ipaddr) & 0x7f;
 2452              		.loc 1 814 23 is_stmt 0 view .LVU684
 2453 00c6 5E23     		movs	r3, #94
 2454 00c8 8DF80A30 		strb	r3, [sp, #10]
 815:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[4] = ip4_addr3(ipaddr);
 2455              		.loc 1 815 5 is_stmt 1 view .LVU685
 815:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[4] = ip4_addr3(ipaddr);
 2456              		.loc 1 815 43 is_stmt 0 view .LVU686
 2457 00cc 6378     		ldrb	r3, [r4, #1]	@ zero_extendqisi2
 2458 00ce 03F07F03 		and	r3, r3, #127
 815:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[4] = ip4_addr3(ipaddr);
 2459              		.loc 1 815 23 view .LVU687
 2460 00d2 8DF80B30 		strb	r3, [sp, #11]
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[5] = ip4_addr4(ipaddr);
 2461              		.loc 1 816 5 is_stmt 1 view .LVU688
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[5] = ip4_addr4(ipaddr);
 2462              		.loc 1 816 25 is_stmt 0 view .LVU689
 2463 00d6 A378     		ldrb	r3, [r4, #2]	@ zero_extendqisi2
 816:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     mcastaddr.addr[5] = ip4_addr4(ipaddr);
 2464              		.loc 1 816 23 view .LVU690
 2465 00d8 8DF80C30 		strb	r3, [sp, #12]
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* destination Ethernet address is multicast */
 2466              		.loc 1 817 5 is_stmt 1 view .LVU691
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* destination Ethernet address is multicast */
ARM GAS  /tmp/cchE5ny2.s 			page 75


 2467              		.loc 1 817 25 is_stmt 0 view .LVU692
 2468 00dc E378     		ldrb	r3, [r4, #3]	@ zero_extendqisi2
 817:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* destination Ethernet address is multicast */
 2469              		.loc 1 817 23 view .LVU693
 2470 00de 8DF80D30 		strb	r3, [sp, #13]
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* unicast destination IP address? */
 2471              		.loc 1 819 5 is_stmt 1 view .LVU694
 2472              	.LVL226:
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* unicast destination IP address? */
 2473              		.loc 1 819 10 is_stmt 0 view .LVU695
 2474 00e2 02AB     		add	r3, sp, #8
 2475              	.LVL227:
 819:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* unicast destination IP address? */
 2476              		.loc 1 819 10 view .LVU696
 2477 00e4 21E0     		b	.L178
 2478              	.LVL228:
 2479              	.L183:
 2480              	.LBB13:
 877:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].state >= ETHARP_STATE_STABLE) &&
 2481              		.loc 1 877 38 is_stmt 1 discriminator 2 view .LVU697
 2482 00e6 0133     		adds	r3, r3, #1
 2483              	.LVL229:
 877:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].state >= ETHARP_STATE_STABLE) &&
 2484              		.loc 1 877 38 is_stmt 0 discriminator 2 view .LVU698
 2485 00e8 DBB2     		uxtb	r3, r3
 2486              	.LVL230:
 2487              	.L184:
 877:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       if ((arp_table[i].state >= ETHARP_STATE_STABLE) &&
 2488              		.loc 1 877 19 is_stmt 1 discriminator 1 view .LVU699
 2489 00ea 092B     		cmp	r3, #9
 2490 00ec 16D8     		bhi	.L193
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2491              		.loc 1 878 7 view .LVU700
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2492              		.loc 1 878 24 is_stmt 0 view .LVU701
 2493 00ee 03EB430C 		add	ip, r3, r3, lsl #1
 2494 00f2 1648     		ldr	r0, .L194+4
 2495 00f4 00EBCC00 		add	r0, r0, ip, lsl #3
 2496 00f8 027D     		ldrb	r2, [r0, #20]	@ zero_extendqisi2
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2497              		.loc 1 878 10 view .LVU702
 2498 00fa 012A     		cmp	r2, #1
 2499 00fc F3D9     		bls	.L183
 880:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 2500              		.loc 1 880 24 view .LVU703
 2501 00fe 8268     		ldr	r2, [r0, #8]
 878:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #if ETHARP_TABLE_MATCH_NETIF
 2502              		.loc 1 878 55 discriminator 1 view .LVU704
 2503 0100 AA42     		cmp	r2, r5
 2504 0102 F0D1     		bne	.L183
 882:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         /* found an existing, stable entry */
 2505              		.loc 1 882 12 view .LVU705
 2506 0104 2168     		ldr	r1, [r4]
 2507 0106 4268     		ldr	r2, [r0, #4]
 880:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** #endif
 2508              		.loc 1 880 41 view .LVU706
 2509 0108 9142     		cmp	r1, r2
ARM GAS  /tmp/cchE5ny2.s 			page 76


 2510 010a ECD1     		bne	.L183
 884:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****         return etharp_output_to_arp_index(netif, q, i);
 2511              		.loc 1 884 9 is_stmt 1 view .LVU707
 2512 010c 0E4A     		ldr	r2, .L194
 2513 010e 1370     		strb	r3, [r2]
 885:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 2514              		.loc 1 885 9 view .LVU708
 885:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 2515              		.loc 1 885 16 is_stmt 0 view .LVU709
 2516 0110 1A46     		mov	r2, r3
 2517 0112 3146     		mov	r1, r6
 2518 0114 2846     		mov	r0, r5
 2519 0116 FFF7FEFF 		bl	etharp_output_to_arp_index
 2520              	.LVL231:
 885:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****       }
 2521              		.loc 1 885 16 view .LVU710
 2522 011a 0FE0     		b	.L181
 2523              	.LVL232:
 2524              	.L193:
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2525              		.loc 1 890 5 is_stmt 1 view .LVU711
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2526              		.loc 1 890 12 is_stmt 0 view .LVU712
 2527 011c 3246     		mov	r2, r6
 2528 011e 2146     		mov	r1, r4
 2529 0120 2846     		mov	r0, r5
 2530 0122 FFF7FEFF 		bl	etharp_query
 2531              	.LVL233:
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2532              		.loc 1 890 12 view .LVU713
 2533 0126 09E0     		b	.L181
 2534              	.LVL234:
 2535              	.L186:
 890:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****   }
 2536              		.loc 1 890 12 view .LVU714
 2537              	.LBE13:
 808:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****     /* multicast destination IP address? */
 2538              		.loc 1 808 10 view .LVU715
 2539 0128 0E4B     		ldr	r3, .L194+28
 2540              	.L178:
 2541              	.LVL235:
 896:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 2542              		.loc 1 896 3 is_stmt 1 view .LVU716
 896:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** }
 2543              		.loc 1 896 10 is_stmt 0 view .LVU717
 2544 012a 4FF40062 		mov	r2, #2048
 2545 012e 0092     		str	r2, [sp]
 2546 0130 05F12602 		add	r2, r5, #38
 2547 0134 3146     		mov	r1, r6
 2548 0136 2846     		mov	r0, r5
 2549 0138 FFF7FEFF 		bl	ethernet_output
 2550              	.LVL236:
 2551              	.L181:
 897:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c **** 
 2552              		.loc 1 897 1 view .LVU718
 2553 013c 04B0     		add	sp, sp, #16
 2554              	.LCFI35:
ARM GAS  /tmp/cchE5ny2.s 			page 77


 2555              		.cfi_remember_state
 2556              		.cfi_def_cfa_offset 16
 2557              		@ sp needed
 2558 013e 70BD     		pop	{r4, r5, r6, pc}
 2559              	.LVL237:
 2560              	.L187:
 2561              	.LCFI36:
 2562              		.cfi_restore_state
 2563              	.LBB14:
 850:Middlewares/Third_Party/LwIP/src/core/ipv4/etharp.c ****           }
 2564              		.loc 1 850 20 view .LVU719
 2565 0140 6FF00300 		mvn	r0, #3
 2566 0144 FAE7     		b	.L181
 2567              	.L195:
 2568 0146 00BF     		.align	2
 2569              	.L194:
 2570 0148 00000000 		.word	etharp_cached_entry
 2571 014c 00000000 		.word	arp_table
 2572 0150 00000000 		.word	.LC0
 2573 0154 00000000 		.word	.LC6
 2574 0158 4C000000 		.word	.LC2
 2575 015c 00000000 		.word	.LC16
 2576 0160 00000000 		.word	.LC10
 2577 0164 00000000 		.word	ethbroadcast
 2578              	.LBE14:
 2579              		.cfi_endproc
 2580              	.LFE179:
 2582              		.section	.bss.etharp_cached_entry,"aw",%nobits
 2585              	etharp_cached_entry:
 2586 0000 00       		.space	1
 2587              		.section	.bss.arp_table,"aw",%nobits
 2588              		.align	2
 2591              	arp_table:
 2592 0000 00000000 		.space	240
 2592      00000000 
 2592      00000000 
 2592      00000000 
 2592      00000000 
 2593              		.text
 2594              	.Letext0:
 2595              		.file 2 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2596              		.file 3 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2597              		.file 4 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2598              		.file 5 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2599              		.file 6 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-too
 2600              		.file 7 "Middlewares/Third_Party/LwIP/src/include/lwip/arch.h"
 2601              		.file 8 "Middlewares/Third_Party/LwIP/src/include/lwip/err.h"
 2602              		.file 9 "Middlewares/Third_Party/LwIP/src/include/lwip/pbuf.h"
 2603              		.file 10 "Middlewares/Third_Party/LwIP/src/include/lwip/ip4_addr.h"
 2604              		.file 11 "Middlewares/Third_Party/LwIP/src/include/lwip/ip_addr.h"
 2605              		.file 12 "Middlewares/Third_Party/LwIP/src/include/lwip/memp.h"
 2606              		.file 13 "Middlewares/Third_Party/LwIP/src/include/lwip/netif.h"
 2607              		.file 14 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ethernet.h"
 2608              		.file 15 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/etharp.h"
 2609              		.file 16 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/ieee.h"
 2610              		.file 17 "Middlewares/Third_Party/LwIP/src/include/lwip/prot/iana.h"
 2611              		.file 18 "Middlewares/Third_Party/LwIP/src/include/netif/ethernet.h"
ARM GAS  /tmp/cchE5ny2.s 			page 78


 2612              		.file 19 "Middlewares/Third_Party/LwIP/src/include/lwip/def.h"
 2613              		.file 20 "/home/<USER>/st/stm32cubeide_1.19.0/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-to
ARM GAS  /tmp/cchE5ny2.s 			page 79


DEFINED SYMBOLS
                            *ABS*:00000000 etharp.c
     /tmp/cchE5ny2.s:20     .text.etharp_free_entry:00000000 $t
     /tmp/cchE5ny2.s:25     .text.etharp_free_entry:00000000 etharp_free_entry
     /tmp/cchE5ny2.s:74     .text.etharp_free_entry:00000030 $d
     /tmp/cchE5ny2.s:2591   .bss.arp_table:00000000 arp_table
     /tmp/cchE5ny2.s:79     .rodata.etharp_find_entry.str1.4:00000000 $d
     /tmp/cchE5ny2.s:96     .text.etharp_find_entry:00000000 $t
     /tmp/cchE5ny2.s:101    .text.etharp_find_entry:00000000 etharp_find_entry
     /tmp/cchE5ny2.s:489    .text.etharp_find_entry:00000198 $d
     /tmp/cchE5ny2.s:499    .rodata.etharp_update_arp_entry.str1.4:00000000 $d
     /tmp/cchE5ny2.s:503    .text.etharp_update_arp_entry:00000000 $t
     /tmp/cchE5ny2.s:508    .text.etharp_update_arp_entry:00000000 etharp_update_arp_entry
     /tmp/cchE5ny2.s:683    .text.etharp_update_arp_entry:000000b4 $d
     /tmp/cchE5ny2.s:691    .rodata.etharp_raw.str1.4:00000000 $d
     /tmp/cchE5ny2.s:702    .text.etharp_raw:00000000 $t
     /tmp/cchE5ny2.s:707    .text.etharp_raw:00000000 etharp_raw
     /tmp/cchE5ny2.s:908    .text.etharp_raw:000000d4 $d
     /tmp/cchE5ny2.s:917    .text.etharp_request_dst:00000000 $t
     /tmp/cchE5ny2.s:922    .text.etharp_request_dst:00000000 etharp_request_dst
     /tmp/cchE5ny2.s:967    .text.etharp_request_dst:00000020 $d
     /tmp/cchE5ny2.s:972    .text.etharp_cleanup_netif:00000000 $t
     /tmp/cchE5ny2.s:978    .text.etharp_cleanup_netif:00000000 etharp_cleanup_netif
     /tmp/cchE5ny2.s:1051   .text.etharp_cleanup_netif:00000034 $d
     /tmp/cchE5ny2.s:1056   .rodata.etharp_find_addr.str1.4:00000000 $d
     /tmp/cchE5ny2.s:1060   .text.etharp_find_addr:00000000 $t
     /tmp/cchE5ny2.s:1066   .text.etharp_find_addr:00000000 etharp_find_addr
     /tmp/cchE5ny2.s:1177   .text.etharp_find_addr:00000068 $d
     /tmp/cchE5ny2.s:1185   .rodata.etharp_get_entry.str1.4:00000000 $d
     /tmp/cchE5ny2.s:1192   .text.etharp_get_entry:00000000 $t
     /tmp/cchE5ny2.s:1198   .text.etharp_get_entry:00000000 etharp_get_entry
     /tmp/cchE5ny2.s:1326   .text.etharp_get_entry:0000007c $d
     /tmp/cchE5ny2.s:1336   .text.etharp_input:00000000 $t
     /tmp/cchE5ny2.s:1342   .text.etharp_input:00000000 etharp_input
     /tmp/cchE5ny2.s:1526   .text.etharp_input:000000ac $d
     /tmp/cchE5ny2.s:1533   .text.etharp_request:00000000 $t
     /tmp/cchE5ny2.s:1539   .text.etharp_request:00000000 etharp_request
     /tmp/cchE5ny2.s:1563   .text.etharp_request:0000000c $d
     /tmp/cchE5ny2.s:1568   .text.etharp_tmr:00000000 $t
     /tmp/cchE5ny2.s:1574   .text.etharp_tmr:00000000 etharp_tmr
     /tmp/cchE5ny2.s:1717   .text.etharp_tmr:00000084 $d
     /tmp/cchE5ny2.s:1722   .rodata.etharp_output_to_arp_index.str1.4:00000000 $d
     /tmp/cchE5ny2.s:1726   .text.etharp_output_to_arp_index:00000000 $t
     /tmp/cchE5ny2.s:1731   .text.etharp_output_to_arp_index:00000000 etharp_output_to_arp_index
     /tmp/cchE5ny2.s:1880   .text.etharp_output_to_arp_index:000000c8 $d
     /tmp/cchE5ny2.s:1888   .rodata.etharp_query.str1.4:00000000 $d
     /tmp/cchE5ny2.s:1898   .text.etharp_query:00000000 $t
     /tmp/cchE5ny2.s:1904   .text.etharp_query:00000000 etharp_query
     /tmp/cchE5ny2.s:2253   .text.etharp_query:0000017c $d
     /tmp/cchE5ny2.s:2585   .bss.etharp_cached_entry:00000000 etharp_cached_entry
     /tmp/cchE5ny2.s:2265   .rodata.etharp_output.str1.4:00000000 $d
     /tmp/cchE5ny2.s:2269   .text.etharp_output:00000000 $t
     /tmp/cchE5ny2.s:2275   .text.etharp_output:00000000 etharp_output
     /tmp/cchE5ny2.s:2570   .text.etharp_output:00000148 $d
     /tmp/cchE5ny2.s:2586   .bss.etharp_cached_entry:00000000 $d
     /tmp/cchE5ny2.s:2588   .bss.arp_table:00000000 $d

ARM GAS  /tmp/cchE5ny2.s 			page 80


UNDEFINED SYMBOLS
pbuf_free
printf
ip4_addr_isbroadcast_u32
ethernet_output
pbuf_alloc
lwip_htons
ethzero
ethbroadcast
pbuf_clone
pbuf_ref
